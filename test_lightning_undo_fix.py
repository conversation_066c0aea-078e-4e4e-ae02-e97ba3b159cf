#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاحات ميزة التراجع عن عملية البرق (Lightning Undo Fix)
Test Lightning Undo Feature Fixes

هذا الاختبار يتحقق من الإصلاحات التي تم تطبيقها:
1. تحسين منطق إظهار زر التراجع مع تسجيل مفصل
2. تحسين حفظ قائمة الكروت الناجحة مع معالجة الأخطاء
3. تحسين إرسال الرسالة مع زر التراجع
4. تحسين معالجة callback مع معالجة الأخطاء
5. تحسين التحقق من الشروط والتشخيص
"""

import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

class TestLightningUndoFix(unittest.TestCase):
    """اختبار إصلاحات ميزة التراجع عن عملية البرق"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء mock للتطبيق الرئيسي
        self.mock_app = Mock()
        
        # إعداد logger
        self.mock_app.logger = Mock()
        
        # إعداد بيانات التلجرام
        self.mock_app.telegram_bot_token = "test_bot_token"
        self.mock_app.telegram_chat_id = "123456789"
        
        # إعداد نوع النظام
        self.mock_app.system_type = 'hotspot'
        
        # إعداد إحصائيات البرق مع كروت ناجحة وفاشلة
        self.mock_app.last_send_stats = {
            'success': 8,
            'failed': 2,
            'duplicates': 1,
            'total': 10,
            'successful_usernames': [
                'user001', 'user002', 'user003', 'user004', 'user005',
                'user006', 'user007', 'user008'
            ]
        }

    def test_undo_button_conditions_detailed(self):
        """اختبار الشروط المفصلة لإظهار زر التراجع"""
        
        # إعداد المتغيرات
        success_count = 8
        failed_count = 2
        system_type = 'hotspot'
        has_successful_usernames = True
        
        # فحص الشروط الفردية
        has_successful_cards = success_count > 0
        has_failed_cards = failed_count > 0
        is_hotspot_system = system_type == 'hotspot'
        
        # فحص الشرط الإجمالي
        show_undo_button = (has_successful_cards and has_failed_cards and is_hotspot_system and has_successful_usernames)
        
        # التحقق من النتائج
        self.assertTrue(has_successful_cards, "يجب أن يكون هناك كروت ناجحة")
        self.assertTrue(has_failed_cards, "يجب أن يكون هناك كروت فاشلة")
        self.assertTrue(is_hotspot_system, "يجب أن يكون النظام هوت سبوت")
        self.assertTrue(has_successful_usernames, "يجب أن تكون هناك أسماء مستخدمين ناجحة")
        self.assertTrue(show_undo_button, "يجب إظهار زر التراجع")
        
        print("✅ اختبار الشروط المفصلة لإظهار زر التراجع نجح")

    def test_undo_button_conditions_no_failures(self):
        """اختبار عدم إظهار زر التراجع عند عدم وجود فشل"""
        
        # إعداد المتغيرات - بدون كروت فاشلة
        success_count = 10
        failed_count = 0
        system_type = 'hotspot'
        has_successful_usernames = True
        
        # فحص الشروط
        has_successful_cards = success_count > 0
        has_failed_cards = failed_count > 0
        is_hotspot_system = system_type == 'hotspot'
        
        show_undo_button = (has_successful_cards and has_failed_cards and is_hotspot_system and has_successful_usernames)
        
        # التحقق من النتائج
        self.assertTrue(has_successful_cards)
        self.assertFalse(has_failed_cards, "لا يجب أن يكون هناك كروت فاشلة")
        self.assertTrue(is_hotspot_system)
        self.assertFalse(show_undo_button, "لا يجب إظهار زر التراجع")
        
        print("✅ اختبار عدم إظهار زر التراجع بدون فشل نجح")

    def test_undo_button_conditions_user_manager(self):
        """اختبار عدم إظهار زر التراجع مع نظام User Manager"""
        
        # إعداد المتغيرات - نظام User Manager
        success_count = 8
        failed_count = 2
        system_type = 'user_manager'
        has_successful_usernames = True
        
        # فحص الشروط
        has_successful_cards = success_count > 0
        has_failed_cards = failed_count > 0
        is_hotspot_system = system_type == 'hotspot'
        
        show_undo_button = (has_successful_cards and has_failed_cards and is_hotspot_system and has_successful_usernames)
        
        # التحقق من النتائج
        self.assertTrue(has_successful_cards)
        self.assertTrue(has_failed_cards)
        self.assertFalse(is_hotspot_system, "النظام ليس هوت سبوت")
        self.assertFalse(show_undo_button, "لا يجب إظهار زر التراجع مع User Manager")
        
        print("✅ اختبار عدم إظهار زر التراجع مع User Manager نجح")

    def test_undo_button_conditions_no_successful_usernames(self):
        """اختبار عدم إظهار زر التراجع عند عدم وجود أسماء مستخدمين ناجحة"""
        
        # إعداد المتغيرات - بدون أسماء مستخدمين ناجحة
        success_count = 8
        failed_count = 2
        system_type = 'hotspot'
        has_successful_usernames = False  # قائمة فارغة
        
        # فحص الشروط
        has_successful_cards = success_count > 0
        has_failed_cards = failed_count > 0
        is_hotspot_system = system_type == 'hotspot'
        
        show_undo_button = (has_successful_cards and has_failed_cards and is_hotspot_system and has_successful_usernames)
        
        # التحقق من النتائج
        self.assertTrue(has_successful_cards)
        self.assertTrue(has_failed_cards)
        self.assertTrue(is_hotspot_system)
        self.assertFalse(has_successful_usernames, "لا توجد أسماء مستخدمين ناجحة")
        self.assertFalse(show_undo_button, "لا يجب إظهار زر التراجع بدون أسماء ناجحة")
        
        print("✅ اختبار عدم إظهار زر التراجع بدون أسماء ناجحة نجح")

    def test_save_successful_cards_with_data(self):
        """اختبار حفظ قائمة الكروت الناجحة مع وجود بيانات"""
        
        # محاكاة دالة save_lightning_successful_cards
        def save_lightning_successful_cards():
            successful_usernames = []
            
            if hasattr(self.mock_app, 'last_send_stats') and self.mock_app.last_send_stats:
                successful_usernames = self.mock_app.last_send_stats.get('successful_usernames', [])
            
            if not successful_usernames:
                self.mock_app.lightning_successful_cards = []
                return False
            
            self.mock_app.lightning_successful_cards = successful_usernames.copy()
            return True
        
        # تنفيذ الاختبار
        result = save_lightning_successful_cards()
        
        # التحقق من النتائج
        self.assertTrue(result, "يجب أن تنجح عملية الحفظ")
        self.assertTrue(hasattr(self.mock_app, 'lightning_successful_cards'))
        self.assertEqual(len(self.mock_app.lightning_successful_cards), 8)
        self.assertIn('user001', self.mock_app.lightning_successful_cards)
        
        print("✅ اختبار حفظ قائمة الكروت الناجحة مع وجود بيانات نجح")

    def test_save_successful_cards_no_data(self):
        """اختبار حفظ قائمة الكروت الناجحة بدون بيانات"""
        
        # إزالة البيانات
        self.mock_app.last_send_stats = {'successful_usernames': []}
        
        # محاكاة دالة save_lightning_successful_cards
        def save_lightning_successful_cards():
            successful_usernames = []
            
            if hasattr(self.mock_app, 'last_send_stats') and self.mock_app.last_send_stats:
                successful_usernames = self.mock_app.last_send_stats.get('successful_usernames', [])
            
            if not successful_usernames:
                self.mock_app.lightning_successful_cards = []
                return False
            
            self.mock_app.lightning_successful_cards = successful_usernames.copy()
            return True
        
        # تنفيذ الاختبار
        result = save_lightning_successful_cards()
        
        # التحقق من النتائج
        self.assertFalse(result, "يجب أن تفشل عملية الحفظ")
        self.assertEqual(len(self.mock_app.lightning_successful_cards), 0)
        
        print("✅ اختبار حفظ قائمة الكروت الناجحة بدون بيانات نجح")

    def test_callback_data_parsing(self):
        """اختبار تحليل callback data للتراجع"""
        
        # اختبار callback data مختلفة
        test_cases = [
            ("lightning_undo_8", "initial", 8),
            ("lightning_undo_confirm_8", "confirm", 8),
            ("lightning_undo_cancel", "cancel", None),
            ("lightning_undo_15", "initial", 15),
            ("lightning_undo_confirm_25", "confirm", 25)
        ]
        
        for callback_data, expected_type, expected_count in test_cases:
            if callback_data.startswith("lightning_undo_confirm_"):
                action_type = "confirm"
                try:
                    count = int(callback_data.replace("lightning_undo_confirm_", ""))
                except ValueError:
                    count = None
            elif callback_data == "lightning_undo_cancel":
                action_type = "cancel"
                count = None
            elif callback_data.startswith("lightning_undo_"):
                action_type = "initial"
                try:
                    count = int(callback_data.replace("lightning_undo_", ""))
                except ValueError:
                    count = None
            else:
                action_type = "unknown"
                count = None
            
            # التحقق من النتائج
            self.assertEqual(action_type, expected_type, f"نوع الإجراء خاطئ لـ {callback_data}")
            self.assertEqual(count, expected_count, f"العدد خاطئ لـ {callback_data}")
        
        print("✅ اختبار تحليل callback data للتراجع نجح")

    def test_error_handling_invalid_callback(self):
        """اختبار معالجة الأخطاء مع callback غير صالح"""
        
        # اختبار callback data غير صالحة
        invalid_callbacks = [
            "lightning_undo_abc",  # نص بدلاً من رقم
            "lightning_undo_confirm_xyz",  # نص بدلاً من رقم
            "lightning_undo_",  # فارغ
            "lightning_undo_confirm_"  # فارغ
        ]
        
        for callback_data in invalid_callbacks:
            try:
                if callback_data.startswith("lightning_undo_confirm_"):
                    count_str = callback_data.replace("lightning_undo_confirm_", "")
                    count = int(count_str)  # سيرمي ValueError
                elif callback_data.startswith("lightning_undo_"):
                    count_str = callback_data.replace("lightning_undo_", "")
                    count = int(count_str)  # سيرمي ValueError
                
                # إذا وصلنا هنا فالاختبار فشل
                self.fail(f"كان يجب أن يرمي خطأ لـ {callback_data}")
                
            except ValueError:
                # هذا متوقع
                pass
            except Exception as e:
                self.fail(f"خطأ غير متوقع لـ {callback_data}: {str(e)}")
        
        print("✅ اختبار معالجة الأخطاء مع callback غير صالح نجح")

    def test_system_type_validation(self):
        """اختبار التحقق من نوع النظام"""
        
        # اختبار أنواع أنظمة مختلفة
        test_systems = [
            ('hotspot', True),
            ('user_manager', False),
            ('', False),
            (None, False),
            ('invalid', False)
        ]
        
        for system_type, should_allow in test_systems:
            is_hotspot_system = system_type == 'hotspot'
            
            self.assertEqual(is_hotspot_system, should_allow, 
                           f"التحقق من النظام فشل لـ {system_type}")
        
        print("✅ اختبار التحقق من نوع النظام نجح")

    def test_comprehensive_conditions_matrix(self):
        """اختبار مصفوفة شاملة لجميع الشروط"""
        
        # مصفوفة اختبار شاملة
        test_matrix = [
            # (success_count, failed_count, system_type, has_usernames, expected_result)
            (8, 2, 'hotspot', True, True),      # جميع الشروط مستوفاة
            (0, 2, 'hotspot', True, False),     # لا توجد كروت ناجحة
            (8, 0, 'hotspot', True, False),     # لا توجد كروت فاشلة
            (8, 2, 'user_manager', True, False), # نظام خاطئ
            (8, 2, 'hotspot', False, False),    # لا توجد أسماء ناجحة
            (0, 0, 'hotspot', True, False),     # لا توجد كروت على الإطلاق
            (10, 5, 'hotspot', True, True),     # حالة نجاح أخرى
        ]
        
        for success_count, failed_count, system_type, has_usernames, expected in test_matrix:
            has_successful_cards = success_count > 0
            has_failed_cards = failed_count > 0
            is_hotspot_system = system_type == 'hotspot'
            
            show_undo_button = (has_successful_cards and has_failed_cards and 
                              is_hotspot_system and has_usernames)
            
            self.assertEqual(show_undo_button, expected, 
                           f"فشل الاختبار للحالة: success={success_count}, failed={failed_count}, "
                           f"system={system_type}, usernames={has_usernames}")
        
        print("✅ اختبار مصفوفة شاملة لجميع الشروط نجح")

if __name__ == '__main__':
    print("🔧 بدء اختبارات إصلاحات ميزة التراجع عن عملية البرق...")
    print("=" * 70)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 70)
    print("🎉 انتهت جميع اختبارات الإصلاحات بنجاح!")
