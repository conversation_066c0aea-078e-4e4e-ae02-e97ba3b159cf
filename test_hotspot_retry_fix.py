#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة إعادة المحاولة للكروت الفاشلة في نظام HotSpot
"""

import re
import os

def test_single_card_failed_cards_saving():
    """اختبار حفظ معلومات الكروت الفاشلة للكرت الواحد"""
    print("🔍 اختبار حفظ معلومات الكروت الفاشلة للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    if not os.path.exists(main_file):
        print(f"❌ الملف غير موجود: {main_file}")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ الكروت الفاشلة أثناء الإرسال
    failed_saving_patterns = [
        'if not hasattr(self, \'single_card_failed_cards\'):',
        'self.single_card_failed_cards = []',
        'self.single_card_failed_cards.append({',
        '\'username\': cred_username,',
        '\'password\': cred.get("password", ""),',
        '\'profile\': cred.get("profile", ""),',
        '\'error\': str(user_error)'
    ]
    
    for pattern in failed_saving_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ الكروت الفاشلة غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ الكروت الفاشلة موجود: {pattern}")
    
    # التحقق من حفظ failed_cards_info
    failed_info_patterns = [
        'if failed_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\':',
        'if hasattr(self, \'single_card_failed_cards\') and self.single_card_failed_cards:',
        'self.failed_cards_info = {',
        '\'card_type\': \'single\',',
        '\'failed_cards\': self.single_card_failed_cards.copy(),',
        '\'operation_type\': \'single_card\'',
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد'
    ]
    
    for pattern in failed_info_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ failed_cards_info غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ failed_cards_info موجود: {pattern}")
    
    return True

def test_retry_button_display():
    """اختبار إظهار زر إعادة المحاولة"""
    print("\n🔍 اختبار إظهار زر إعادة المحاولة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من شروط إظهار زر إعادة المحاولة
    retry_button_patterns = [
        'show_retry_failed_button = (',
        'failed_count > 0 and',
        'hasattr(self, \'failed_cards_info\') and',
        'bool(self.failed_cards_info.get(\'failed_cards\', []))',
        'if show_retry_failed_button:',
        'إعادة المحاولة للكروت الفاشلة:',
        'retry_failed_cards_single_'
    ]
    
    for pattern in retry_button_patterns:
        if pattern not in func_code:
            print(f"❌ نمط زر إعادة المحاولة غير موجود: {pattern}")
            return False
        print(f"✅ نمط زر إعادة المحاولة موجود: {pattern}")
    
    return True

def test_callback_handling():
    """اختبار معالجة callback لإعادة المحاولة"""
    print("\n🔍 اختبار معالجة callback لإعادة المحاولة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback
    callback_patterns = [
        'elif callback_data.startswith("retry_failed_cards_"):',
        'self.handle_retry_failed_cards(bot_token, chat_id, callback_data)',
        'elif callback_data.startswith("confirm_retry_"):',
        'self.execute_retry_failed_cards(bot_token, chat_id, callback_data)',
        'elif callback_data == "cancel_retry_failed":'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback موجود: {pattern}")
    
    return True

def test_handle_retry_failed_cards():
    """اختبار دالة handle_retry_failed_cards"""
    print("\n🔍 اختبار دالة handle_retry_failed_cards...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_retry_failed_cards
    func_match = re.search(r'def handle_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التشخيص والتحقق من البيانات
    diagnostic_patterns = [
        'has_failed_info = hasattr(self, \'failed_cards_info\')',
        'failed_info_exists = has_failed_info and bool(self.failed_cards_info)',
        'if not has_failed_info or not failed_info_exists:',
        'لا توجد معلومات محفوظة عن الكروت الفاشلة',
        'failed_cards = self.failed_cards_info.get(\'failed_cards\', [])',
        'if not failed_cards:'
    ]
    
    for pattern in diagnostic_patterns:
        if pattern not in func_code:
            print(f"❌ نمط التشخيص غير موجود: {pattern}")
            return False
        print(f"✅ نمط التشخيص موجود: {pattern}")
    
    return True

def test_execute_retry_failed_cards():
    """اختبار دالة execute_retry_failed_cards"""
    print("\n🔍 اختبار دالة execute_retry_failed_cards...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة execute_retry_failed_cards
    func_match = re.search(r'def execute_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التنفيذ
    execution_patterns = [
        'if not hasattr(self, \'failed_cards_info\') or not self.failed_cards_info:',
        'failed_cards = self.failed_cards_info.get(\'failed_cards\', [])',
        'template_name = self.failed_cards_info.get(\'template_name\', \'\')',
        'if card_type == \'single\':',
        'success = self.retry_single_card_failed_cards(failed_cards)'
    ]
    
    for pattern in execution_patterns:
        if pattern not in func_code:
            print(f"❌ نمط التنفيذ غير موجود: {pattern}")
            return False
        print(f"✅ نمط التنفيذ موجود: {pattern}")
    
    return True

def test_retry_single_card_failed_cards():
    """اختبار دالة retry_single_card_failed_cards"""
    print("\n🔍 اختبار دالة retry_single_card_failed_cards...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدالة
    if 'def retry_single_card_failed_cards(' not in content:
        print("❌ دالة retry_single_card_failed_cards غير موجودة")
        return False
    
    print("✅ دالة retry_single_card_failed_cards موجودة")
    
    # البحث عن الدالة
    func_match = re.search(r'def retry_single_card_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على محتوى دالة retry_single_card_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من إعادة إنشاء الكروت
    retry_patterns = [
        'retry_credentials = []',
        'for failed_card in failed_cards:',
        'retry_cred = {',
        '\'username\': failed_card.get(\'username\', \'\')',
        'original_credentials = self.generated_credentials.copy()',
        'self.generated_credentials = retry_credentials',
        'success = self.send_single_card_to_mikrotik_silent()',
        'self.generated_credentials = original_credentials'
    ]
    
    for pattern in retry_patterns:
        if pattern not in func_code:
            print(f"❌ نمط إعادة المحاولة غير موجود: {pattern}")
            return False
        print(f"✅ نمط إعادة المحاولة موجود: {pattern}")
    
    return True

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاح مشكلة إعادة المحاولة للكروت الفاشلة في HotSpot\n")
    
    tests = [
        test_single_card_failed_cards_saving,
        test_retry_button_display,
        test_callback_handling,
        test_handle_retry_failed_cards,
        test_execute_retry_failed_cards,
        test_retry_single_card_failed_cards
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج إصلاح")
        return False

if __name__ == "__main__":
    run_all_tests()
