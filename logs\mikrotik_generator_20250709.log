2025-07-09 00:02:02,313 - INFO - تم بدء تشغيل التطبيق
2025-07-09 00:02:02,323 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 00:02:02,372 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_000202.db
2025-07-09 00:02:02,384 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 00:02:02,841 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 00:02:02,842 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 00:02:04,848 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 00:02:05,423 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 00:02:05,424 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 00:02:08,427 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 00:02:08,493 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 00:02:09,963 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:02:09,964 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 00:02:13,781 - INFO - معالجة الأمر: /sethost
2025-07-09 00:02:13,782 - INFO - طلب تعديل Host من التلجرام: /sethost
2025-07-09 00:02:32,243 - INFO - تم اختيار النظام: hotspot
2025-07-09 00:02:32,606 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 00:02:32,850 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 00:02:35,339 - INFO - معالجة الأمر: /sethost *******
2025-07-09 00:02:35,339 - INFO - طلب تعديل Host من التلجرام: /sethost *******
2025-07-09 00:02:35,697 - INFO - تم تحديث Host من التلجرام: *********** → *******
2025-07-09 00:02:37,690 - INFO - تم حفظ القالب: 10
2025-07-09 00:02:53,051 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 00:02:53,052 - INFO - محاولة الاتصال بـ *******:8728 (عادي)
2025-07-09 00:03:06,891 - INFO - معالجة الأمر: /connection
2025-07-09 00:03:06,892 - INFO - طلب فحص الاتصال من التلجرام
2025-07-09 00:03:07,322 - INFO - بدء فحص الاتصال من بوت التلجرام
2025-07-09 00:03:13,054 - ERROR - خطأ في الاتصال - النوع: RouterOsApiConnectionError, الرسالة: timed out
2025-07-09 00:03:13,056 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 00:03:13,096 - INFO - محاولة الاتصال بـ *******:8728 (عادي)
2025-07-09 00:03:21,325 - INFO - تم تسجيل العملية: اختبار الاتصال - فشل الاتصال
2025-07-09 00:03:28,293 - INFO - معالجة الأمر: /start
2025-07-09 00:03:28,966 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:03:30,661 - INFO - معالجة callback: select_system_hs
2025-07-09 00:03:32,452 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:03:33,090 - ERROR - خطأ في الاتصال - النوع: RouterOsApiConnectionError, الرسالة: timed out
2025-07-09 00:03:33,092 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 00:03:33,799 - INFO - تم تسجيل العملية: فحص الاتصال من التلجرام - فشل الاتصال
2025-07-09 00:03:33,800 - WARNING - فشل فحص الاتصال من التلجرام - العنوان: *******:8728
2025-07-09 00:03:48,632 - INFO - معالجة الأمر: /start
2025-07-09 00:03:49,010 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:03:50,727 - INFO - معالجة callback: select_system_hs
2025-07-09 00:03:51,431 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:03:58,734 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 00:03:59,665 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 00:03:59,668 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 00:03:59,669 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:03:59,709 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:03:59,710 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 00:03:59,715 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 00:03:59,719 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:03:59,724 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 00:03:59,725 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 00:04:00,605 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 00:04:13,964 - INFO - تم حفظ القالب: 10
2025-07-09 00:04:38,191 - INFO - تم حفظ القالب: 10
2025-07-09 00:04:49,639 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 00:04:51,459 - INFO - معالجة callback: independent_count_hs_10_lightning_5
2025-07-09 00:04:52,128 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 00:04:52,129 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:04:52,171 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:04:52,172 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 00:04:52,177 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 00:04:52,180 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:04:52,805 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 00:04:52,806 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:04:52,841 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:04:52,842 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 00:04:52,847 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 00:04:52,850 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:04:52,851 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 5
2025-07-09 00:04:52,852 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 00:04:52,853 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-09 00:04:52,856 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-09 00:04:52,856 - INFO - ⚡ البرق الموحد: العدد المطلوب 5 حساب لنظام Hotspot
2025-07-09 00:04:52,858 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-09 00:04:52,860 - INFO - ⚡ البرق الموحد: تم توليد 5 حساب لنظام Hotspot
2025-07-09 00:04:52,955 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 00:04:53,101 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-5 كارت-09-07-2025-00-04-52-hotspot.pdf
2025-07-09 00:04:53,102 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-5 كارت-09-07-2025-00-04-52-hotspot.pdf
2025-07-09 00:04:53,186 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-5 كارت-09-07-2025-00-04-52-hotspot.pdf
2025-07-09 00:04:53,231 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-5 كارت-09-07-2025-00-04-52-hotspot.rsc
2025-07-09 00:04:54,102 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-00-04-52-hotspot.pdf
2025-07-09 00:04:54,105 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-5 كارت-09-07-2025-00-04-52-hotspot.pdf
2025-07-09 00:04:54,428 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-09 00:04:54,430 - INFO - محاولة الاتصال بـ *******:8728 (عادي)
2025-07-09 00:05:14,425 - ERROR - خطأ في الاتصال - النوع: RouterOsApiConnectionError, الرسالة: timed out
2025-07-09 00:05:14,427 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 00:05:14,429 - ERROR - فشل الاتصال بـ MikroTik
2025-07-09 00:05:14,429 - WARNING - ⚡ البرق الموحد: تم إنشاء 5 كارت وحفظ PDF، لكن فشل إرسال الكروت إلى MikroTik
2025-07-09 00:05:14,439 - INFO - تم إرسال 5 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-09 00:06:03,387 - INFO - بدء إغلاق التطبيق
2025-07-09 00:06:03,409 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_000603.db
2025-07-09 00:06:03,409 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_000603.db
2025-07-09 00:06:03,580 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 00:06:03,581 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 00:11:41,747 - INFO - تم بدء تشغيل التطبيق
2025-07-09 00:11:41,762 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 00:11:41,785 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_001141.db
2025-07-09 00:11:41,799 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 00:11:42,286 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 00:11:42,287 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 00:11:44,304 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 00:11:44,685 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 00:11:44,686 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 00:11:47,692 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 00:11:47,710 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 00:11:48,400 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:11:48,401 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 00:12:25,467 - INFO - تم اختيار النظام: hotspot
2025-07-09 00:12:25,830 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 00:12:26,086 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 00:13:19,015 - INFO - معالجة callback: select_system_hs
2025-07-09 00:13:21,112 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:13:23,508 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 00:13:24,361 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 00:13:24,364 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 00:13:24,364 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:13:24,403 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:13:24,405 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 00:13:24,410 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 00:13:24,413 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:13:24,418 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 00:13:24,419 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 00:13:24,800 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 00:13:29,294 - INFO - معالجة callback: refresh_templates
2025-07-09 00:13:30,017 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:13:31,570 - INFO - معالجة callback: select_system_hs
2025-07-09 00:13:32,191 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:13:33,740 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 00:13:34,433 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 00:13:34,437 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 00:13:34,437 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:13:34,481 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:13:34,482 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 00:13:34,487 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 00:13:34,490 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:13:34,494 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 00:13:34,496 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 00:13:34,914 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 00:13:38,465 - INFO - معالجة callback: refresh_templates
2025-07-09 00:13:39,053 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:13:41,480 - INFO - معالجة callback: select_system_hs
2025-07-09 00:13:42,020 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:13:43,352 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 00:13:44,424 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 00:13:44,428 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 00:13:44,429 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:13:44,470 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:13:44,472 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 00:13:44,477 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 00:13:44,482 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:13:44,487 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 00:13:44,489 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 00:13:45,478 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 00:13:47,678 - INFO - معالجة callback: refresh_templates
2025-07-09 00:13:48,848 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:13:50,279 - INFO - معالجة callback: select_system_hs
2025-07-09 00:13:50,818 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:13:53,603 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 00:13:54,771 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 00:13:54,775 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 00:13:54,775 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:13:54,806 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:13:54,807 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 00:13:54,811 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 00:13:54,815 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:13:54,820 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 00:13:54,822 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 00:13:55,130 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 00:13:58,796 - INFO - معالجة callback: back_to_system_selection
2025-07-09 00:13:59,782 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:14:02,047 - INFO - معالجة callback: select_system_hs
2025-07-09 00:14:03,274 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:14:04,700 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 00:14:05,399 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 00:14:05,403 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 00:14:05,404 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:14:05,442 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:14:05,443 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 00:14:05,448 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 00:14:05,452 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:14:05,457 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 00:14:05,458 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 00:14:05,866 - INFO - ✅ تمت المزامنة الشاملة للقالب '10-marawan' بنجاح
2025-07-09 00:14:14,953 - INFO - معالجة الأمر: /restart
2025-07-09 00:14:14,954 - INFO - طلب إعادة تشغيل البرنامج من التلجرام
2025-07-09 00:14:16,574 - INFO - معالجة callback: confirm_restart
2025-07-09 00:14:16,851 - INFO - تم تأكيد إعادة تشغيل البرنامج من التلجرام
2025-07-09 00:14:16,852 - INFO - بدء عملية إعادة تشغيل البرنامج من التلجرام
2025-07-09 00:14:23,970 - INFO - تم تسجيل العملية: إعادة تشغيل من التلجرام - بدء عملية إعادة التشغيل
2025-07-09 00:14:44,977 - INFO - تم بدء تشغيل التطبيق
2025-07-09 00:14:44,989 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 00:14:45,011 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_001445.db
2025-07-09 00:14:45,024 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 00:14:45,474 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 00:14:45,475 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 00:14:47,283 - INFO - تم اختيار النظام: hotspot
2025-07-09 00:14:47,646 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 00:14:47,928 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 00:14:47,930 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 00:14:48,416 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 00:14:48,439 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 00:14:51,443 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 00:14:51,667 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 00:14:51,974 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:14:51,975 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 00:15:02,751 - INFO - تم حفظ القالب: 30
2025-07-09 00:15:16,969 - INFO - معالجة callback: select_system_hs
2025-07-09 00:15:17,643 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:15:19,854 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 00:15:21,485 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 00:15:21,487 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 00:15:21,488 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:15:21,525 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:15:21,526 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 00:15:21,531 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 00:15:21,534 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:15:21,539 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 00:15:21,540 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 00:15:21,859 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 00:15:24,920 - INFO - معالجة callback: refresh_templates
2025-07-09 00:15:25,470 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:15:26,809 - INFO - معالجة callback: select_system_hs
2025-07-09 00:15:27,624 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:15:29,131 - INFO - معالجة callback: independent_template_hs_30
2025-07-09 00:15:29,811 - INFO - 🔄 بدء مزامنة القالب '30' مع البرنامج الرئيسي
2025-07-09 00:15:29,814 - INFO - 🔄 بدء المزامنة الشاملة للقالب '30'...
2025-07-09 00:15:29,815 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:15:29,848 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:15:29,849 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '30' بمزامنة شاملة
2025-07-09 00:15:29,854 - INFO - ✅ تم تحديد القالب '30' في قائمة القوالب
2025-07-09 00:15:29,856 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:15:29,862 - INFO - ✅ تم تحديد القالب '30' في قائمة القوالب
2025-07-09 00:15:29,863 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 30
2025-07-09 00:15:30,423 - INFO - ✅ تمت المزامنة الشاملة للقالب '30' بنجاح
2025-07-09 00:15:40,250 - INFO - معالجة callback: refresh_templates
2025-07-09 00:15:41,027 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 00:15:42,574 - INFO - معالجة callback: select_system_hs
2025-07-09 00:15:43,182 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 00:15:44,854 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 00:15:45,814 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 00:15:45,851 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 00:15:45,851 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 00:15:45,885 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 00:15:45,886 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 00:15:45,891 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 00:15:45,895 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 00:15:45,899 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 00:15:45,901 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 00:15:46,378 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 00:15:58,403 - INFO - بدء إغلاق التطبيق
2025-07-09 00:15:58,519 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_001558.db
2025-07-09 00:15:58,520 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_001558.db
2025-07-09 00:15:58,603 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 00:15:58,604 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 03:08:08,544 - INFO - تم بدء تشغيل التطبيق
2025-07-09 03:08:08,617 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 03:08:08,619 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 03:08:08,619 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:08:08,769 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_030808.db
2025-07-09 03:08:08,774 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 03:08:09,437 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 03:08:09,439 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 03:08:11,453 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 03:08:11,740 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 03:08:11,741 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 03:08:14,752 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 03:08:14,753 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 03:08:15,020 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 03:08:15,021 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 03:08:24,578 - INFO - معالجة الأمر: /quick
2025-07-09 03:08:24,578 - INFO - طلب كروت سريعة: /quick
2025-07-09 03:08:33,241 - INFO - معالجة الأمر: /start
2025-07-09 03:08:33,499 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 03:08:35,896 - INFO - معالجة الأمر: /cards
2025-07-09 03:08:35,897 - INFO - طلب كروت: /cards
2025-07-09 03:08:36,151 - ERROR - خطأ في إنشاء الكروت بالطريقة المحددة: 'MikroTikCardGenerator' object has no attribute 'profile_combo'
2025-07-09 03:08:43,332 - INFO - معالجة callback: select_system_hs
2025-07-09 03:08:43,911 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 03:08:45,909 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 03:08:46,440 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 03:08:46,440 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 03:08:46,684 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 03:08:46,933 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:08:47,034 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 03:08:47,037 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 03:08:47,037 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 03:08:47,040 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 03:08:47,044 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:08:47,531 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 03:08:47,641 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 03:08:47,646 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:08:47,766 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 03:08:47,769 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 03:08:47,776 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 03:08:47,782 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 03:08:47,784 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 03:08:47,785 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 03:08:47,915 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 03:08:48,396 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 03:08:48,411 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 03:08:48,426 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 03:08:48,429 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 03:08:48,443 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 03:08:48,445 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 03:08:48,718 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 03:08:49,169 - INFO - معالجة الأمر: /quick
2025-07-09 03:08:49,171 - INFO - طلب كروت سريعة: /quick
2025-07-09 03:08:49,419 - INFO - 🚀 بدء إنشاء الكروت السريعة - العدد: 3
2025-07-09 03:08:49,553 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 03:08:49,579 - INFO - تم إنشاء PDF بنجاح: exports/كروت_سريعة-5_كارت-09-07-2025-03-08-49-hotspot.pdf
2025-07-09 03:08:49,586 - INFO - ✅ تم إنشاء PDF للكروت السريعة: exports/كروت_سريعة-5_كارت-09-07-2025-03-08-49-hotspot.pdf
2025-07-09 03:08:49,587 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 03:08:49,626 - INFO - نجح الاتصال مع ***********
2025-07-09 03:08:49,676 - INFO - ✅ تم إضافة المستخدم: 2008995179
2025-07-09 03:08:49,714 - INFO - ✅ تم إضافة المستخدم: 2008277164
2025-07-09 03:08:49,754 - INFO - ✅ تم إضافة المستخدم: 2068775801
2025-07-09 03:08:49,804 - INFO - ✅ تم إضافة المستخدم: 2006423560
2025-07-09 03:08:49,844 - INFO - ✅ تم إضافة المستخدم: 2046953673
2025-07-09 03:08:49,846 - ERROR - خطأ في إرسال الكروت السريعة إلى MikroTik: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 03:08:49,847 - ERROR - فشل في إرسال الكروت السريعة إلى MikroTik
2025-07-09 03:08:58,742 - INFO - معالجة الأمر: /cards
2025-07-09 03:08:58,744 - INFO - طلب كروت: /cards
2025-07-09 03:08:59,021 - INFO - تم توليد 5 حساب
2025-07-09 03:08:59,024 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 03:09:02,021 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 03:09:02,038 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(افتراضي)_ب20-5 كارت-09-07-2025-03-09-01-hotspot.pdf
2025-07-09 03:09:02,613 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-09 03:09:02,868 - INFO - استخدام الاتصال الحالي
2025-07-09 03:09:02,869 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 03:09:02,906 - INFO - تم إرسال المستخدم 2057804156 بنجاح (1/5)
2025-07-09 03:09:03,054 - INFO - تم إرسال المستخدم 2072684172 بنجاح (2/5)
2025-07-09 03:09:03,194 - INFO - تم إرسال المستخدم 2038901221 بنجاح (3/5)
2025-07-09 03:09:03,334 - INFO - تم إرسال المستخدم 2041355376 بنجاح (4/5)
2025-07-09 03:09:03,474 - INFO - تم إرسال المستخدم 2091882397 بنجاح (5/5)
2025-07-09 03:09:03,577 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 03:09:03,579 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 03:09:31,901 - INFO - بدء إغلاق التطبيق
2025-07-09 03:09:31,937 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_030931.db
2025-07-09 03:09:31,940 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_030931.db
2025-07-09 03:09:31,947 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 03:09:31,966 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 03:09:31,968 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 03:10:34,361 - INFO - تم بدء تشغيل التطبيق
2025-07-09 03:10:34,397 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 03:10:34,410 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 03:10:34,411 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:10:34,526 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_031034.db
2025-07-09 03:10:34,540 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 03:10:35,061 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 03:10:35,062 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 03:10:37,005 - INFO - تم اختيار النظام: hotspot
2025-07-09 03:10:37,311 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 03:10:37,412 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 03:10:37,412 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:10:37,532 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 03:10:37,534 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 03:10:37,816 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 03:10:37,825 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 03:10:40,832 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 03:10:40,834 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 03:10:41,095 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 03:10:41,098 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 03:11:34,066 - INFO - معالجة callback: select_system_hs
2025-07-09 03:11:35,264 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 03:11:40,777 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 03:11:41,634 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 03:11:41,635 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 03:11:41,635 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 03:11:41,674 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 03:11:41,674 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 03:11:41,679 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 03:11:41,681 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 03:11:41,685 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 03:11:41,686 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 03:11:41,945 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 03:12:49,923 - INFO - تم بدء تشغيل التطبيق
2025-07-09 03:12:49,988 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 03:12:50,015 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 03:12:50,015 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:12:50,131 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_031250.db
2025-07-09 03:12:50,212 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 03:12:53,203 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 03:12:53,215 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 03:12:55,062 - INFO - تم اختيار النظام: hotspot
2025-07-09 03:12:55,370 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 03:12:55,465 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 03:12:55,466 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 03:12:55,597 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 03:12:55,600 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 03:12:55,930 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 03:12:55,941 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 03:12:58,955 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 03:12:58,978 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 03:12:59,311 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 03:12:59,311 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 06:32:43,973 - INFO - تم بدء تشغيل التطبيق
2025-07-09 06:32:44,021 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 06:32:44,052 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 06:32:44,119 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:32:44,269 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_063244.db
2025-07-09 06:32:44,275 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 06:32:51,842 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 06:32:51,843 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 06:32:53,844 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 06:32:54,188 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 06:32:54,189 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 06:32:57,198 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 06:32:57,245 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 06:32:57,579 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 06:32:57,581 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 06:33:14,691 - INFO - تم اختيار النظام: hotspot
2025-07-09 06:33:15,192 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 06:33:15,287 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 06:33:15,301 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:33:15,474 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 06:33:41,227 - INFO - معالجة الأمر: /cards
2025-07-09 06:33:41,228 - INFO - طلب كروت: /cards
2025-07-09 06:33:41,544 - INFO - تم توليد 5 حساب
2025-07-09 06:33:41,546 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 06:33:44,704 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 06:33:44,784 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(افتراضي)_ب20-5 كارت-09-07-2025-06-33-44-hotspot.pdf
2025-07-09 06:33:46,189 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-09 06:33:46,503 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 06:33:46,559 - INFO - نجح الاتصال مع ***********
2025-07-09 06:33:46,562 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 06:33:46,602 - INFO - تم إرسال المستخدم 2021133201 بنجاح (1/5)
2025-07-09 06:33:46,739 - INFO - تم إرسال المستخدم 2044078381 بنجاح (2/5)
2025-07-09 06:33:46,879 - INFO - تم إرسال المستخدم 2096832109 بنجاح (3/5)
2025-07-09 06:33:47,019 - INFO - تم إرسال المستخدم 2076333391 بنجاح (4/5)
2025-07-09 06:33:47,159 - INFO - تم إرسال المستخدم 2020334744 بنجاح (5/5)
2025-07-09 06:33:47,261 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 06:33:47,263 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 06:34:04,241 - INFO - بدء إغلاق التطبيق
2025-07-09 06:34:04,253 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_063404.db
2025-07-09 06:34:04,254 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_063404.db
2025-07-09 06:34:04,260 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 06:34:04,277 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 06:34:04,279 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 06:34:09,510 - INFO - تم بدء تشغيل التطبيق
2025-07-09 06:34:09,582 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 06:34:09,585 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 06:34:09,586 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:34:09,695 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_063409.db
2025-07-09 06:34:09,715 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 06:34:10,142 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 06:34:10,143 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 06:34:12,145 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 06:34:12,542 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 06:34:12,543 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 06:39:42,485 - INFO - تم بدء تشغيل التطبيق
2025-07-09 06:39:42,558 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 06:39:42,592 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 06:39:42,685 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:39:42,800 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_063942.db
2025-07-09 06:39:42,805 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 06:39:47,167 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 06:39:47,178 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 06:39:49,216 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 06:39:49,730 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 06:39:49,736 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 06:39:52,740 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 06:39:52,762 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 06:39:53,017 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 06:39:53,040 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 06:40:00,262 - INFO - معالجة callback: select_system_hs
2025-07-09 06:40:00,722 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 06:40:01,806 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 06:40:02,465 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 06:40:02,466 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 06:40:02,713 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 06:40:02,801 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:40:02,902 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 06:40:02,904 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 06:40:02,905 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 06:40:02,908 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 06:40:02,912 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:40:03,531 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 06:40:03,645 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 06:40:03,649 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 06:40:03,807 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 06:40:03,810 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 06:40:03,821 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 06:40:03,823 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 06:40:03,824 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:40:03,824 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:40:03,909 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 06:40:04,402 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:40:04,404 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:40:04,411 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:40:04,414 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:40:04,418 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:40:04,419 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 06:40:04,707 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 06:40:06,627 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 06:40:09,138 - INFO - معالجة callback: independent_custom_hs_10_normal
2025-07-09 06:40:14,601 - INFO - معالجة الأمر: 1
2025-07-09 06:40:14,876 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:40:14,878 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:40:14,893 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:40:14,903 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:40:14,904 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:40:14,907 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:40:15,171 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:40:15,172 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:40:15,189 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:40:15,199 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:40:15,200 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:40:15,203 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:40:15,203 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 06:40:15,208 - INFO - تم توليد 5 حساب
2025-07-09 06:40:15,210 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 06:40:18,312 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 06:40:18,359 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-06-40-18-hotspot.pdf
2025-07-09 06:40:18,376 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-06-40-18-hotspot.rsc
2025-07-09 06:40:19,649 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-06-40-18-hotspot.pdf
2025-07-09 06:40:19,910 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 06:40:20,180 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 06:40:20,225 - INFO - نجح الاتصال مع ***********
2025-07-09 06:40:20,229 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 06:40:20,266 - INFO - تم إرسال المستخدم 2007875198 بنجاح (1/5)
2025-07-09 06:40:20,424 - INFO - تم إرسال المستخدم 2099164637 بنجاح (2/5)
2025-07-09 06:40:20,564 - INFO - تم إرسال المستخدم 2047310938 بنجاح (3/5)
2025-07-09 06:40:20,704 - INFO - تم إرسال المستخدم 2058679566 بنجاح (4/5)
2025-07-09 06:40:20,844 - INFO - تم إرسال المستخدم 2018927722 بنجاح (5/5)
2025-07-09 06:40:20,946 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 06:40:20,948 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 06:41:29,831 - INFO - معالجة الأمر: /start
2025-07-09 06:41:30,165 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 06:41:31,558 - INFO - معالجة callback: select_system_hs
2025-07-09 06:41:32,791 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 06:41:34,205 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 06:41:35,460 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 06:41:36,273 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:41:36,274 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:41:36,308 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:41:36,309 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:41:36,312 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:41:36,314 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:41:36,319 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:41:36,322 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 06:41:36,581 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 06:41:38,708 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 06:41:41,108 - INFO - معالجة callback: independent_custom_hs_10_normal
2025-07-09 06:41:45,023 - INFO - معالجة الأمر: 1
2025-07-09 06:41:45,286 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:41:45,288 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:41:45,319 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:41:45,320 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:41:45,324 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:41:45,326 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:41:45,617 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:41:45,619 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:41:45,651 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:41:45,653 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:41:45,656 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:41:45,659 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:41:45,660 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 06:41:47,741 - INFO - تم توليد 5 حساب
2025-07-09 06:41:47,743 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 06:41:48,878 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 06:41:49,117 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-06-41-48-hotspot.pdf
2025-07-09 06:41:49,123 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-06-41-48-hotspot.rsc
2025-07-09 06:41:56,311 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-06-41-48-hotspot.pdf
2025-07-09 06:41:56,553 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 06:41:56,815 - INFO - استخدام الاتصال الحالي
2025-07-09 06:41:56,822 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 06:41:56,858 - INFO - تم إرسال المستخدم 2028664402 بنجاح (1/5)
2025-07-09 06:41:57,006 - INFO - تم إرسال المستخدم 2087258428 بنجاح (2/5)
2025-07-09 06:41:57,146 - INFO - تم إرسال المستخدم 2004961524 بنجاح (3/5)
2025-07-09 06:41:57,286 - INFO - تم إرسال المستخدم 2050097670 بنجاح (4/5)
2025-07-09 06:41:57,426 - INFO - تم إرسال المستخدم 2074585284 بنجاح (5/5)
2025-07-09 06:41:57,528 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 06:41:57,531 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 06:42:08,077 - INFO - معالجة الأمر: /start
2025-07-09 06:42:08,321 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 06:42:10,351 - INFO - معالجة callback: select_system_hs
2025-07-09 06:42:10,855 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 06:42:13,489 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 06:42:14,020 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 06:42:14,023 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:42:14,024 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:42:14,040 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:42:14,049 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:42:14,050 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:42:14,053 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:42:14,054 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:42:14,057 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 06:42:14,390 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 06:42:16,126 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 06:42:19,833 - INFO - معالجة callback: independent_count_hs_10_normal_20
2025-07-09 06:42:20,389 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:42:20,391 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:42:20,417 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:42:20,419 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:42:20,420 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:42:20,423 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:42:20,673 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 06:42:20,675 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 06:42:20,689 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 06:42:20,698 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 06:42:20,699 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 06:42:20,702 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 06:42:20,702 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 06:42:20,708 - INFO - تم توليد 5 حساب
2025-07-09 06:42:20,710 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 06:42:43,746 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 06:42:43,763 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-06-42-43-hotspot.pdf
2025-07-09 06:42:43,767 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-06-42-43-hotspot.rsc
2025-07-09 06:42:44,471 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-06-42-43-hotspot.pdf
2025-07-09 06:42:44,720 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 06:42:44,961 - INFO - استخدام الاتصال الحالي
2025-07-09 06:42:44,963 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 06:42:44,999 - INFO - تم إرسال المستخدم 2059764902 بنجاح (1/5)
2025-07-09 06:42:45,136 - INFO - تم إرسال المستخدم 2058769023 بنجاح (2/5)
2025-07-09 06:42:45,276 - INFO - تم إرسال المستخدم 2050992806 بنجاح (3/5)
2025-07-09 06:42:45,416 - INFO - تم إرسال المستخدم 2037415587 بنجاح (4/5)
2025-07-09 06:42:45,566 - INFO - تم إرسال المستخدم 2050397475 بنجاح (5/5)
2025-07-09 06:42:45,669 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 06:42:45,671 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 06:44:05,061 - INFO - استخدام الاتصال الحالي
2025-07-09 06:44:05,063 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 06:44:05,110 - ERROR - فشل في إرسال المستخدم 2059764902: ('Error "failure: already have user with this name for this server" executing command b\'/ip/hotspot/user/add =name=2059764902 =password=40720953 =profile=CARD=ALLLLLLL1 =server=all =limit-bytes-total=6442450944 =email=<EMAIL> .tag=21\'', b'failure: already have user with this name for this server')
2025-07-09 06:44:05,158 - ERROR - فشل في إرسال المستخدم 2058769023: ('Error "failure: already have user with this name for this server" executing command b\'/ip/hotspot/user/add =name=2058769023 =password=39172519 =profile=CARD=ALLLLLLL1 =server=all =limit-bytes-total=6442450944 =email=<EMAIL> .tag=22\'', b'failure: already have user with this name for this server')
2025-07-09 06:44:05,202 - ERROR - فشل في إرسال المستخدم 2050992806: ('Error "failure: already have user with this name for this server" executing command b\'/ip/hotspot/user/add =name=2050992806 =password=41278558 =profile=CARD=ALLLLLLL1 =server=all =limit-bytes-total=6442450944 =email=<EMAIL> .tag=23\'', b'failure: already have user with this name for this server')
2025-07-09 06:44:05,238 - ERROR - فشل في إرسال المستخدم 2037415587: ('Error "failure: already have user with this name for this server" executing command b\'/ip/hotspot/user/add =name=2037415587 =password=65782781 =profile=CARD=ALLLLLLL1 =server=all =limit-bytes-total=6442450944 =email=<EMAIL> .tag=24\'', b'failure: already have user with this name for this server')
2025-07-09 06:44:05,288 - ERROR - فشل في إرسال المستخدم 2050397475: ('Error "failure: already have user with this name for this server" executing command b\'/ip/hotspot/user/add =name=2050397475 =password=90626468 =profile=CARD=ALLLLLLL1 =server=all =limit-bytes-total=6442450944 =email=<EMAIL> .tag=25\'', b'failure: already have user with this name for this server')
2025-07-09 06:44:05,297 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 06:44:25,974 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 06:44:25,990 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-5 كارت-09-07-2025-06-44-25-hotspot.pdf
2025-07-09 06:44:25,994 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-5 كارت-09-07-2025-06-44-25-hotspot.pdf
2025-07-09 06:44:28,700 - INFO - استخدام الاتصال الحالي
2025-07-09 17:39:13,162 - INFO - تم بدء تشغيل التطبيق
2025-07-09 17:39:13,174 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 17:39:13,212 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 17:39:13,215 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:39:13,489 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_173913.db
2025-07-09 17:39:13,517 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 17:39:13,985 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 17:39:13,986 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 17:39:15,997 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 17:39:16,359 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 17:39:16,361 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 07:39:16,810 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:19,370 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 17:39:19,412 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 17:39:19,707 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:39:19,708 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 17:39:20,031 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:20,325 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:23,460 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:23,755 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:26,892 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:27,247 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:30,405 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:30,697 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:33,829 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:34,167 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:37,302 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:37,620 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:39:40,766 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:41,059 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:42,731 - INFO - معالجة callback: select_system_hs
2025-07-09 07:39:43,492 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 17:39:44,202 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:44,501 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:46,828 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 07:39:47,535 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 07:39:47,536 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:39:47,536 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:39:47,698 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:39:47,700 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:39:47,704 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:39:47,706 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:39:47,710 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:39:47,631 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:47,717 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 17:39:47,868 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 07:39:47,989 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 17:39:48,475 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 17:39:48,477 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 17:39:48,767 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 17:39:48,897 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:39:48,999 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 17:39:49,000 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 17:39:49,002 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 17:39:49,003 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 17:39:49,012 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:39:49,464 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 17:39:49,636 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 17:39:49,637 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:39:49,973 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 17:39:49,974 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 17:39:50,049 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 17:39:50,085 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 17:39:50,089 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 17:39:50,096 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 17:39:50,097 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:39:50,295 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 17:39:50,297 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 17:39:50,305 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:39:50,308 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:39:50,316 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:39:50,318 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 17:39:50,590 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 07:39:51,151 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:53,052 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 17:39:54,295 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:54,664 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:56,743 - INFO - معالجة callback: independent_custom_hs_10_normal
2025-07-09 17:39:57,799 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:39:58,091 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:00,893 - INFO - معالجة الأمر: 1
2025-07-09 07:40:01,284 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:40:01,285 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:40:01,317 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:40:01,317 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:40:01,321 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:40:01,323 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:40:01,220 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:02,282 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:40:02,296 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:40:02,327 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:40:02,328 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:40:02,332 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:40:02,334 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:40:02,334 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 07:40:02,428 - INFO - تم توليد 5 حساب
2025-07-09 07:40:02,435 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 07:40:03,312 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:05,456 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:40:05,635 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-40-05-hotspot.pdf
2025-07-09 07:40:05,650 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-40-05-hotspot.rsc
2025-07-09 17:40:06,517 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:06,807 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:07,279 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-07-40-05-hotspot.pdf
2025-07-09 07:40:07,558 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 07:40:07,863 - INFO - استخدام الاتصال الحالي
2025-07-09 07:40:07,864 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 07:40:07,905 - INFO - تم إرسال المستخدم 2006332626 بنجاح (1/5)
2025-07-09 07:40:08,043 - INFO - تم إرسال المستخدم 2028294405 بنجاح (2/5)
2025-07-09 07:40:08,183 - INFO - تم إرسال المستخدم 2046160638 بنجاح (3/5)
2025-07-09 07:40:08,323 - INFO - تم إرسال المستخدم 2071605902 بنجاح (4/5)
2025-07-09 07:40:08,463 - INFO - تم إرسال المستخدم 2090986687 بنجاح (5/5)
2025-07-09 07:40:08,572 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 07:40:08,573 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 17:40:09,933 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:10,235 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:13,392 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:13,763 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:16,911 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:17,261 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:18,842 - INFO - معالجة الأمر: /cards
2025-07-09 07:40:18,843 - INFO - طلب كروت: /cards
2025-07-09 07:40:19,134 - INFO - تم توليد 5 حساب
2025-07-09 07:40:19,136 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 17:40:20,426 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:20,718 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:22,134 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:40:22,152 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-40-22-hotspot.pdf
2025-07-09 07:40:22,718 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-09 07:40:22,973 - INFO - استخدام الاتصال الحالي
2025-07-09 07:40:22,974 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 07:40:23,016 - INFO - تم إرسال المستخدم 2056771373 بنجاح (1/5)
2025-07-09 07:40:23,153 - INFO - تم إرسال المستخدم 2049450078 بنجاح (2/5)
2025-07-09 07:40:23,293 - INFO - تم إرسال المستخدم 2090832257 بنجاح (3/5)
2025-07-09 07:40:23,434 - INFO - تم إرسال المستخدم 2002800263 بنجاح (4/5)
2025-07-09 07:40:23,573 - INFO - تم إرسال المستخدم 2082791060 بنجاح (5/5)
2025-07-09 07:40:23,675 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 07:40:23,676 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 17:40:23,845 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:24,142 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:25,487 - INFO - معالجة الأمر: /cards
2025-07-09 07:40:25,488 - INFO - طلب كروت: /cards
2025-07-09 07:40:25,762 - INFO - تم توليد 5 حساب
2025-07-09 07:40:25,763 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 07:40:27,176 - INFO - معالجة الأمر: /start
2025-07-09 17:40:27,266 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:27,475 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:40:27,481 - INFO - معالجة الأمر: /start
2025-07-09 17:40:27,907 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 07:40:28,219 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:28,800 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:40:28,819 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-40-28-hotspot.pdf
2025-07-09 07:40:29,540 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-09 07:40:29,833 - INFO - استخدام الاتصال الحالي
2025-07-09 07:40:29,853 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 07:40:29,903 - INFO - تم إرسال المستخدم 2048665747 بنجاح (1/5)
2025-07-09 07:40:30,043 - INFO - تم إرسال المستخدم 2036959363 بنجاح (2/5)
2025-07-09 07:40:30,183 - INFO - تم إرسال المستخدم 2065433862 بنجاح (3/5)
2025-07-09 07:40:30,333 - INFO - تم إرسال المستخدم 2096066305 بنجاح (4/5)
2025-07-09 07:40:30,473 - INFO - تم إرسال المستخدم 2087347950 بنجاح (5/5)
2025-07-09 07:40:30,575 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 07:40:30,576 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 17:40:31,344 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:31,647 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:34,779 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:35,084 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:36,647 - INFO - معالجة callback: select_system_hs
2025-07-09 07:40:37,196 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 17:40:38,268 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:38,599 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:41,046 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 07:40:41,564 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 07:40:41,569 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:40:41,570 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:40:41,602 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:40:41,603 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:40:41,606 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:40:41,609 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:40:41,613 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:40:41,614 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 17:40:41,726 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:41,884 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 17:40:41,943 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 17:40:42,528 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 17:40:42,534 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 17:40:42,535 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:40:42,570 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 17:40:42,571 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 17:40:42,577 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:40:42,580 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:40:42,587 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:40:42,589 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 17:40:42,870 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 07:40:43,437 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:46,566 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:46,863 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:49,993 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:50,290 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:53,419 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:53,714 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:56,447 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 17:40:56,843 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:40:57,074 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 07:40:57,865 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:40:59,493 - INFO - معالجة callback: independent_count_hs_10_normal_10
2025-07-09 07:41:00,074 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:41:00,074 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:41:00,106 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:41:00,106 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:41:00,110 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:41:00,112 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:41:00,351 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:41:00,352 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:41:00,381 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:41:00,382 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:41:00,385 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:41:00,387 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:41:00,388 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 07:41:00,399 - INFO - تم توليد 5 حساب
2025-07-09 07:41:00,400 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 17:41:01,011 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:41:01,316 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:41:04,455 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:41:04,751 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:41:07,910 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:41:08,294 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:41:08,978 - INFO - بدء إغلاق التطبيق
2025-07-09 17:41:09,119 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_174109.db
2025-07-09 17:41:09,121 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_174109.db
2025-07-09 17:41:09,221 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 17:41:09,222 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 07:41:40,488 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:41:42,059 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-41-39-hotspot.pdf
2025-07-09 07:41:44,213 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-41-39-hotspot.rsc
2025-07-09 07:41:45,822 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-07-41-39-hotspot.pdf
2025-07-09 07:41:46,338 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 07:41:46,966 - INFO - استخدام الاتصال الحالي
2025-07-09 07:41:47,493 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 07:41:47,539 - INFO - تم إرسال المستخدم 2082499469 بنجاح (1/5)
2025-07-09 07:41:48,864 - INFO - تم إرسال المستخدم 2042915595 بنجاح (2/5)
2025-07-09 07:41:49,584 - INFO - تم إرسال المستخدم 2094841035 بنجاح (3/5)
2025-07-09 07:41:50,234 - INFO - تم إرسال المستخدم 2042152662 بنجاح (4/5)
2025-07-09 07:41:50,784 - INFO - تم إرسال المستخدم 2043217726 بنجاح (5/5)
2025-07-09 07:41:51,208 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 07:41:51,457 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 17:42:31,909 - INFO - تم بدء تشغيل التطبيق
2025-07-09 17:42:31,920 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 17:42:31,936 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 17:42:31,937 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:42:32,088 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_174232.db
2025-07-09 17:42:32,103 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 17:42:32,542 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 17:42:32,543 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 17:42:34,549 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 17:42:34,781 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 17:42:34,782 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 07:42:35,082 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:35,645 - INFO - معالجة الأمر: /start
2025-07-09 07:42:35,918 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 07:42:37,444 - INFO - معالجة callback: select_system_hs
2025-07-09 17:42:37,782 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 17:42:37,795 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 07:42:37,936 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 17:42:38,215 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:42:38,352 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:42:38,353 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 07:42:38,536 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:40,916 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 07:42:41,395 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 07:42:41,396 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:42:41,396 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:42:41,428 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:42:41,429 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:42:41,433 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:42:41,435 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:42:41,439 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:42:41,440 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 07:42:41,702 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 17:42:41,657 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:42:41,871 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 17:42:42,660 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 17:42:42,661 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 17:42:42,942 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 17:42:42,973 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:42:43,072 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 17:42:43,074 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 17:42:43,075 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 17:42:43,077 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 17:42:43,086 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:42:43,537 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 17:42:43,700 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 17:42:43,702 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:42:43,925 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 17:42:43,927 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 17:42:43,999 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 17:42:44,029 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 17:42:44,033 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 17:42:44,037 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 17:42:44,038 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:42:44,248 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 17:42:44,250 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 17:42:44,256 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:42:44,259 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:42:44,266 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:42:44,268 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 17:42:44,559 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 07:42:45,163 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:46,836 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 17:42:48,286 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:48,585 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:49,701 - INFO - معالجة callback: independent_count_hs_10_normal_5
2025-07-09 07:42:50,172 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:42:50,173 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:42:50,203 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:42:50,204 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:42:50,207 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:42:50,210 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:42:50,470 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:42:50,471 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:42:50,502 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:42:50,502 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:42:50,506 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:42:50,508 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:42:50,525 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 07:42:50,564 - INFO - تم توليد 5 حساب
2025-07-09 07:42:50,565 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 17:42:51,708 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:52,015 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:53,552 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:42:53,569 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-42-53-hotspot.pdf
2025-07-09 07:42:53,592 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-07-42-53-hotspot.rsc
2025-07-09 07:42:54,184 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-07-42-53-hotspot.pdf
2025-07-09 07:42:54,465 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 07:42:54,755 - INFO - استخدام الاتصال الحالي
2025-07-09 07:42:54,755 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 07:42:54,797 - INFO - تم إرسال المستخدم 2068840628 بنجاح (1/5)
2025-07-09 07:42:54,935 - INFO - تم إرسال المستخدم 2000771033 بنجاح (2/5)
2025-07-09 07:42:55,075 - INFO - تم إرسال المستخدم 2087880640 بنجاح (3/5)
2025-07-09 07:42:55,215 - INFO - تم إرسال المستخدم 2075238147 بنجاح (4/5)
2025-07-09 17:42:55,143 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:55,355 - INFO - تم إرسال المستخدم 2008651034 بنجاح (5/5)
2025-07-09 07:42:55,442 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:55,457 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 07:42:55,457 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 17:42:58,559 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:42:58,858 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:01,212 - INFO - معالجة الأمر: /start
2025-07-09 07:43:01,466 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:43:02,009 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:02,313 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:02,879 - INFO - معالجة callback: select_system_hs
2025-07-09 07:43:03,362 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 07:43:05,236 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 17:43:05,463 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:05,744 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 07:43:05,745 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 07:43:05,745 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:43:05,679 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 07:43:05,850 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:43:05,851 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 07:43:05,855 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:43:05,857 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:43:05,860 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:43:05,861 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 07:43:06,116 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 17:43:06,219 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 17:43:06,224 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 17:43:06,225 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:43:06,282 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 17:43:06,284 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 17:43:06,309 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 17:43:06,312 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:43:06,317 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 17:43:06,319 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 17:43:06,604 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 07:43:07,173 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:43:10,296 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:10,599 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:43:10,588 - INFO - معالجة callback: independent_create_hs_20_normal
2025-07-09 07:43:10,808 - INFO - معالجة callback: independent_create_hs_20_normal
2025-07-09 07:43:12,890 - INFO - معالجة callback: independent_custom_hs_20_normal
2025-07-09 17:43:14,380 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:14,695 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:17,197 - INFO - معالجة الأمر: 3
2025-07-09 07:43:17,523 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 07:43:17,524 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:43:17,555 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:43:17,555 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 07:43:17,559 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:43:17,561 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:43:17,830 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 07:43:17,831 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:43:17,863 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:43:17,863 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 07:43:17,867 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:43:17,869 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:43:17,869 - INFO - تم تطبيق إعدادات القالب '20' بنجاح
2025-07-09 17:43:17,835 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:17,926 - INFO - تم توليد 5 حساب
2025-07-09 07:43:17,928 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 07:43:18,134 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:20,896 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:43:20,911 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(20)_ب20-5 كارت-09-07-2025-07-43-20-hotspot.pdf
2025-07-09 07:43:20,918 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(20)_ب20-5 كارت-09-07-2025-07-43-20-hotspot.rsc
2025-07-09 17:43:21,264 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:21,564 - INFO - تم إرسال الملف بنجاح: كروت_(20)_ب20-5 كارت-09-07-2025-07-43-20-hotspot.pdf
2025-07-09 07:43:21,580 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:21,874 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 20
2025-07-09 17:43:24,701 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:28,667 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:28,865 - INFO - استخدام الاتصال الحالي
2025-07-09 07:43:29,685 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 07:43:29,728 - INFO - تم إرسال المستخدم 0208650675 بنجاح (1/5)
2025-07-09 07:43:30,035 - INFO - تم إرسال المستخدم 0216459780 بنجاح (2/5)
2025-07-09 07:43:30,235 - INFO - تم إرسال المستخدم 0261155899 بنجاح (3/5)
2025-07-09 07:43:30,375 - INFO - تم إرسال المستخدم 0272953302 بنجاح (4/5)
2025-07-09 07:43:30,515 - INFO - تم إرسال المستخدم 0277397619 بنجاح (5/5)
2025-07-09 07:43:30,657 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 07:43:30,693 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 17:43:32,824 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:43:33,122 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:43:33,562 - INFO - بدء إغلاق التطبيق
2025-07-09 17:43:33,607 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_174333.db
2025-07-09 17:43:33,608 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_174333.db
2025-07-09 17:43:33,652 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 17:43:33,654 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 17:44:49,291 - INFO - تم بدء تشغيل التطبيق
2025-07-09 17:44:49,301 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 17:44:49,314 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 17:44:49,315 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:44:49,445 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_174449.db
2025-07-09 17:44:49,460 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 17:44:49,903 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 17:44:49,904 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 17:44:51,170 - INFO - تم اختيار النظام: hotspot
2025-07-09 17:44:51,527 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 17:44:51,677 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 17:44:51,678 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:44:51,900 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 17:44:51,907 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 17:44:52,246 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 17:44:52,270 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 07:44:52,580 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:44:55,267 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 17:44:55,277 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 17:44:55,590 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:44:55,591 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 17:44:55,727 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:44:56,035 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:44:59,237 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:44:59,543 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:02,664 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:03,040 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:06,162 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:06,518 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:09,659 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:10,088 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:13,208 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:13,523 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:16,657 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:23,117 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:26,463 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:26,792 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:29,933 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:30,239 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:33,362 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:33,676 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:36,806 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:37,130 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:40,257 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:40,564 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:43,691 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:44,219 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:47,969 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:51,540 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:45:54,700 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:45:58,557 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:01,709 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:02,046 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:05,167 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:05,488 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:08,603 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:08,910 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:12,061 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:12,371 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:15,540 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:15,852 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:18,968 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:19,332 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:22,460 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:28,280 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:31,637 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:31,964 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:35,103 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:35,454 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:38,568 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:46:39,296 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:46:41,130 - INFO - بدء إغلاق التطبيق
2025-07-09 17:46:41,734 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_174641.db
2025-07-09 17:46:41,736 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_174641.db
2025-07-09 17:46:41,921 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 17:46:41,927 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 17:58:28,670 - INFO - تم بدء تشغيل التطبيق
2025-07-09 17:58:28,685 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 17:58:28,699 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 17:58:28,700 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:58:28,959 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_175828.db
2025-07-09 17:58:28,980 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 17:58:29,399 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 17:58:29,400 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 17:58:31,409 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 17:58:31,655 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 17:58:31,656 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 07:58:32,027 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:58:34,655 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 17:58:34,686 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 17:58:34,961 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:58:34,962 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 17:58:35,145 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:35,489 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:37,778 - INFO - معالجة callback: select_system_hs
2025-07-09 07:58:38,459 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 17:58:38,573 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:38,921 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:39,926 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 07:58:40,512 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 07:58:40,533 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:58:40,534 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:58:40,824 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:58:40,825 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:58:40,829 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:58:40,833 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:58:40,837 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:58:40,839 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 07:58:41,099 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 17:58:42,009 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:42,353 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:58:45,437 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:45,774 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:48,172 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 17:58:48,868 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:49,214 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:50,138 - INFO - معالجة callback: independent_count_hs_10_lightning_5
2025-07-09 07:58:50,661 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:58:50,663 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:58:50,698 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:58:50,699 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:58:50,703 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:58:50,705 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:58:51,092 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:58:51,092 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:58:51,127 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:58:51,128 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:58:51,132 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:58:51,134 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:58:51,136 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 5
2025-07-09 07:58:51,177 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 07:58:51,178 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-09 07:58:51,301 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-09 07:58:51,302 - INFO - ⚡ البرق الموحد: العدد المطلوب 5 حساب لنظام Hotspot
2025-07-09 07:58:51,303 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-09 07:58:51,305 - INFO - ⚡ البرق الموحد: تم توليد 5 حساب لنظام Hotspot
2025-07-09 07:58:51,607 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:58:51,934 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-5 كارت-09-07-2025-07-58-51-hotspot.pdf
2025-07-09 07:58:51,935 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-5 كارت-09-07-2025-07-58-51-hotspot.pdf
2025-07-09 17:58:52,300 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:52,641 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:54,174 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-5 كارت-09-07-2025-07-58-51-hotspot.pdf
2025-07-09 07:58:54,240 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-5 كارت-09-07-2025-07-58-51-hotspot.rsc
2025-07-09 07:58:55,006 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-07-58-51-hotspot.pdf
2025-07-09 07:58:55,008 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-5 كارت-09-07-2025-07-58-51-hotspot.pdf
2025-07-09 07:58:55,298 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-09 07:58:55,302 - INFO - استخدام الاتصال الحالي
2025-07-09 07:58:55,732 - INFO - ⚡ البرق الموحد: بدء إرسال 5 مستخدم إلى MikroTik
2025-07-09 17:58:55,725 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:55,968 - INFO - ⚡ البرق الموحد: النتائج النهائية - نجح: 5, فشل: 0, مكرر: 0
2025-07-09 07:58:55,985 - INFO - ⚡ البرق الموحد مكتمل: تم إنشاء وإرسال 5 كارت بنجاح إلى MikroTik
2025-07-09 07:58:56,065 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:56,259 - INFO - تم إرسال 5 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-09 17:58:59,153 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:58:59,498 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:02,578 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:02,711 - INFO - معالجة الأمر: /start
2025-07-09 17:59:02,808 - INFO - معالجة الأمر: /start
2025-07-09 07:59:03,001 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:59:03,087 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 07:59:03,426 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:04,698 - INFO - معالجة callback: select_system_hs
2025-07-09 07:59:05,333 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 07:59:06,633 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 17:59:06,515 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:06,730 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 07:59:07,229 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 07:59:07,230 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 07:59:07,231 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:59:07,286 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:59:07,288 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 07:59:07,292 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:59:07,295 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:59:07,298 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:59:07,301 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 17:59:07,366 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 17:59:07,367 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 07:59:07,618 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 17:59:07,704 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 17:59:07,813 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:07,909 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 17:59:07,911 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 17:59:07,913 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 17:59:07,914 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 17:59:07,923 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:08,373 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 17:59:08,538 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 17:59:08,540 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:08,724 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 17:59:08,726 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 17:59:08,765 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 17:59:08,770 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 17:59:08,775 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 17:59:08,777 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:59:08,790 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 17:59:09,065 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 17:59:09,068 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 17:59:09,074 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 17:59:09,078 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:59:09,085 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 17:59:09,086 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 17:59:09,380 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 07:59:10,006 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:13,200 - INFO - معالجة callback: independent_create_hs_20_lightning
2025-07-09 17:59:13,096 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:13,323 - INFO - معالجة callback: independent_create_hs_20_lightning
2025-07-09 07:59:14,144 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:15,542 - INFO - معالجة callback: independent_count_hs_20_lightning_5
2025-07-09 07:59:16,015 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 07:59:16,016 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:59:16,051 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:59:16,053 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 07:59:16,057 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:59:16,059 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:59:16,289 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 07:59:16,290 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:59:16,325 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 07:59:16,326 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 07:59:16,329 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 07:59:16,332 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:59:16,334 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 5
2025-07-09 07:59:16,334 - INFO - تم تطبيق إعدادات القالب '20' بنجاح
2025-07-09 07:59:16,335 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-09 07:59:16,342 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-09 07:59:16,342 - INFO - ⚡ البرق الموحد: العدد المطلوب 5 حساب لنظام Hotspot
2025-07-09 07:59:16,343 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-09 07:59:16,343 - INFO - ⚡ البرق الموحد: تم توليد 5 حساب لنظام Hotspot
2025-07-09 07:59:16,381 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 07:59:16,497 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(20)_ب20-5 كارت-09-07-2025-07-59-16-hotspot.pdf
2025-07-09 07:59:16,499 - INFO - تم حفظ ملف PDF: exports\كروت_(20)_ب20-5 كارت-09-07-2025-07-59-16-hotspot.pdf
2025-07-09 07:59:16,658 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(20)_ب20-5 كارت-09-07-2025-07-59-16-hotspot.pdf
2025-07-09 07:59:16,688 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(20)_ب20-5 كارت-09-07-2025-07-59-16-hotspot.rsc
2025-07-09 07:59:17,304 - INFO - تم إرسال الملف بنجاح: كروت_(20)_ب20-5 كارت-09-07-2025-07-59-16-hotspot.pdf
2025-07-09 07:59:17,306 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(20)_ب20-5 كارت-09-07-2025-07-59-16-hotspot.pdf
2025-07-09 17:59:17,237 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:17,573 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:17,578 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-09 07:59:17,612 - INFO - استخدام الاتصال الحالي
2025-07-09 07:59:17,985 - INFO - ⚡ البرق الموحد: بدء إرسال 5 مستخدم إلى MikroTik
2025-07-09 07:59:18,187 - INFO - ⚡ البرق الموحد: النتائج النهائية - نجح: 5, فشل: 0, مكرر: 0
2025-07-09 07:59:18,189 - INFO - ⚡ البرق الموحد مكتمل: تم إنشاء وإرسال 5 كارت بنجاح إلى MikroTik
2025-07-09 17:59:20,665 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:21,007 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:21,337 - INFO - تم إرسال 5 كرت عبر البرق الموحد باستخدام قالب 20
2025-07-09 17:59:24,093 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:24,432 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:27,525 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:27,920 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:31,039 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:31,378 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:32,842 - INFO - معالجة الأمر: /start
2025-07-09 07:59:33,152 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:59:34,458 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:34,807 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:35,438 - INFO - معالجة callback: select_system_um
2025-07-09 07:59:35,908 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-09 07:59:37,091 - INFO - معالجة callback: independent_template_um_10
2025-07-09 07:59:37,549 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-09 07:59:37,550 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-09 07:59:37,807 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 07:59:37,808 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-09 07:59:37,831 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 07:59:37,936 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-09 07:59:37,937 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-09 07:59:37,938 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-09 07:59:37,940 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 07:59:37,942 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:37,892 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:38,118 - INFO - معالجة callback: independent_template_um_10
2025-07-09 17:59:38,667 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-09 17:59:38,719 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-09 17:59:38,988 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 17:59:39,026 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 07:59:39,205 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-09 17:59:39,143 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-09 17:59:39,145 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-09 17:59:39,147 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-09 17:59:39,148 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 17:59:39,150 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 07:59:39,333 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 07:59:39,338 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:39,375 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-09 17:59:39,403 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 17:59:39,404 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:39,602 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-09 17:59:39,604 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 17:59:39,766 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-09 17:59:39,811 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-09 17:59:39,816 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 17:59:39,827 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 17:59:39,829 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 17:59:39,892 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-09 17:59:39,894 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 17:59:39,903 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:59:39,906 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 17:59:39,914 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 17:59:39,916 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-09 17:59:40,186 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 07:59:40,426 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-09 07:59:40,428 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 07:59:40,458 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-09 07:59:40,461 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 07:59:40,462 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 07:59:40,462 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 07:59:40,536 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-09 07:59:40,551 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-09 07:59:40,557 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 07:59:41,116 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:59:41,134 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 07:59:41,150 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 07:59:41,152 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-09 07:59:41,499 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 17:59:41,857 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:45,202 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:45,290 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:46,122 - INFO - بدء إغلاق التطبيق
2025-07-09 17:59:46,350 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_175946.db
2025-07-09 17:59:46,351 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_175946.db
2025-07-09 17:59:46,410 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 17:59:46,411 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 07:59:48,628 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:50,679 - INFO - تم بدء تشغيل التطبيق
2025-07-09 17:59:50,693 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 17:59:50,705 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 17:59:50,707 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 17:59:50,839 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_175950.db
2025-07-09 17:59:50,856 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 17:59:51,293 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 17:59:51,294 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 17:59:53,302 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 17:59:53,589 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 17:59:53,590 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 07:59:53,934 - ERROR - خطأ في API التلجرام: 409
2025-07-09 17:59:56,591 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 17:59:56,605 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 17:59:56,855 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 17:59:56,856 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 17:59:57,016 - ERROR - خطأ في API التلجرام: 409
2025-07-09 07:59:57,355 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:00,438 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:00,777 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:03,861 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:04,241 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:07,321 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:07,729 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:10,602 - INFO - معالجة callback: select_system_hs
2025-07-09 18:00:10,840 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:11,122 - INFO - معالجة callback: select_system_hs
2025-07-09 08:00:11,275 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 18:00:11,675 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 08:00:12,085 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:15,335 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:15,788 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:17,612 - INFO - معالجة الأمر: /start
2025-07-09 08:00:17,913 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:00:19,016 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:19,175 - INFO - معالجة callback: select_system_hs
2025-07-09 18:00:19,286 - INFO - معالجة callback: select_system_hs
2025-07-09 08:00:19,683 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 18:00:19,841 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 08:00:20,228 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:21,326 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 08:00:22,138 - INFO - 🔄 تبديل النظام تلقائياً من user_manager إلى hotspot
2025-07-09 08:00:22,139 - INFO - 🔄 طلب تبديل النظام من التلجرام: user_manager → hotspot
2025-07-09 08:00:22,457 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 08:00:22,460 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 08:00:22,569 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-09 08:00:22,570 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 08:00:22,571 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 08:00:22,573 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 08:00:22,575 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 08:00:22,872 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 08:00:22,876 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 08:00:22,905 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 08:00:23,029 - ERROR - خطأ في إعادة بناء الواجهة: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 08:00:23,054 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 08:00:23,076 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 08:00:23,186 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من user_manager إلى hotspot
2025-07-09 08:00:23,187 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:00:23,367 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:00:23,399 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:00:23,318 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:23,635 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:00:23,675 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:00:23,745 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:00:23,752 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:00:23,757 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:00:23,759 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 08:00:24,153 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:00:24,171 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:00:24,732 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:00:24,733 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 18:00:25,047 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:00:25,156 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:00:25,265 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:00:25,267 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:00:25,269 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:00:25,271 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:00:25,279 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:00:25,743 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:00:25,909 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:00:25,911 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:00:26,139 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:00:26,141 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:00:26,214 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:00:26,245 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:00:26,251 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:00:26,257 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:00:26,258 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:00:26,478 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:00:26,479 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:00:26,487 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:00:26,491 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:00:26,497 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:00:26,499 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:00:26,877 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:00:27,646 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:30,794 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:31,161 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:34,503 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:34,858 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:37,946 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:38,310 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:41,560 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:00:42,144 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:44,794 - INFO - بدء إغلاق التطبيق
2025-07-09 18:00:49,719 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:50,878 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180050.db
2025-07-09 18:00:50,880 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_180050.db
2025-07-09 08:00:53,121 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:00:57,620 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:00:57,634 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:00:57,649 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:00:57,649 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:00:57,766 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180057.db
2025-07-09 18:00:57,789 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:00:58,231 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:00:58,232 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:01:00,230 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:01:00,640 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:01:00,642 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 08:01:00,995 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:03,650 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:01:03,663 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:01:03,926 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:01:03,927 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:01:04,074 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:04,420 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:06,172 - INFO - معالجة callback: select_system_hs
2025-07-09 08:01:06,717 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 18:01:07,514 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:07,652 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:01:07,741 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 08:01:08,252 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:01:08,254 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:01:08,255 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:01:08,288 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:01:08,289 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:01:08,293 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:01:08,295 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:01:08,299 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:01:08,301 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:01:08,296 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:01:08,297 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 08:01:08,553 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:01:08,571 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:01:08,679 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:01:08,788 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:01:08,790 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:01:08,801 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:01:08,803 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:01:08,812 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:01:09,278 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:01:09,441 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:01:09,442 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:01:09,609 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:01:09,611 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:01:09,673 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:01:09,714 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:01:09,719 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:01:09,723 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:01:09,724 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:01:09,921 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:01:09,923 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:01:09,932 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:01:09,935 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:01:09,941 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:01:09,942 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:01:10,216 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:01:10,824 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:13,948 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:14,340 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:16,104 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 18:01:17,420 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:17,762 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:20,158 - INFO - معالجة callback: independent_count_hs_10_normal_5
2025-07-09 08:01:20,642 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:01:20,644 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:01:20,682 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:01:20,683 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:01:20,687 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:01:20,689 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:01:20,935 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:01:20,936 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:01:20,971 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:01:20,971 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:01:20,976 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:01:20,846 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:20,979 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:01:20,980 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 08:01:20,991 - INFO - تم توليد 5 حساب
2025-07-09 08:01:20,992 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 08:01:20,994 - ERROR - خطأ في إنشاء الكروت بالطريقة المحددة: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 08:01:21,189 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:24,008 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 08:01:24,011 - WARNING - فشل في حفظ آخر رقم تسلسلي: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 08:01:24,026 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-08-01-23-hotspot.pdf
2025-07-09 08:01:24,033 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-08-01-23-hotspot.rsc
2025-07-09 18:01:24,269 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:24,583 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-08-01-23-hotspot.pdf
2025-07-09 08:01:24,641 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:24,834 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 08:01:25,127 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 08:01:25,171 - INFO - نجح الاتصال مع ***********
2025-07-09 08:01:25,172 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 08:01:25,211 - INFO - تم إرسال المستخدم 2034603041 بنجاح (1/5)
2025-07-09 08:01:25,363 - INFO - تم إرسال المستخدم 2087401463 بنجاح (2/5)
2025-07-09 08:01:25,509 - INFO - تم إرسال المستخدم 2039157577 بنجاح (3/5)
2025-07-09 08:01:25,649 - INFO - تم إرسال المستخدم 2085276490 بنجاح (4/5)
2025-07-09 08:01:25,789 - INFO - تم إرسال المستخدم 2017667281 بنجاح (5/5)
2025-07-09 08:01:25,908 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 08:01:25,910 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 18:01:27,719 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:28,063 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:31,143 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:31,493 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:34,585 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:34,928 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:38,014 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:38,361 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:41,441 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:41,845 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:44,929 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:45,274 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:48,373 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:48,744 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:01:54,986 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:01:59,888 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:02,989 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:03,359 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:06,459 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:06,852 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:09,970 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:10,314 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:13,409 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:13,752 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:16,849 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:17,263 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:20,340 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:20,710 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:23,791 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:24,142 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:27,220 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:27,590 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:30,670 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:31,015 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:34,095 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:34,443 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:37,539 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:37,942 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:41,057 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:41,410 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:44,493 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:44,843 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:46,706 - INFO - معالجة الأمر: /start
2025-07-09 08:02:47,084 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:02:47,927 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:48,276 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:49,319 - INFO - معالجة callback: select_system_hs
2025-07-09 08:02:49,895 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 08:02:51,134 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:02:51,353 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:51,660 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:02:51,662 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:02:51,662 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:02:51,698 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:02:51,698 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:02:51,702 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:02:51,704 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:02:51,573 - INFO - معالجة callback: independent_template_hs_10
ة القوالب
2025-07-09 08:02:51,711 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 08:02:51,979 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:02:52,146 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:02:52,151 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:02:52,152 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:02:52,196 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:02:52,198 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:02:52,206 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:02:52,211 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:02:52,216 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:02:52,218 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:02:52,502 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:02:53,142 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:02:56,225 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:02:56,577 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:04,204 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 18:02:59,654 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:05,104 - INFO - معالجة callback: independent_create_hs_10_normal
2025-07-09 08:03:06,036 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:08,337 - INFO - معالجة callback: independent_count_hs_10_normal_5
2025-07-09 08:03:08,814 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:03:08,815 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:03:08,853 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:03:08,854 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:03:08,858 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:03:08,860 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:03:09,146 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:03:09,147 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:03:09,184 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:03:09,185 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:03:09,189 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:03:09,192 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:03:09,192 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 08:03:09,207 - INFO - تم توليد 5 حساب
2025-07-09 08:03:09,208 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 08:03:09,210 - ERROR - خطأ في إنشاء الكروت بالطريقة المحددة: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 18:03:09,118 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:09,504 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:12,222 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-09 08:03:12,226 - WARNING - فشل في حفظ آخر رقم تسلسلي: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 08:03:12,241 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-09-07-2025-08-03-12-hotspot.pdf
2025-07-09 08:03:12,245 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-09-07-2025-08-03-12-hotspot.rsc
2025-07-09 18:03:12,583 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:12,943 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-09-07-2025-08-03-12-hotspot.pdf
2025-07-09 08:03:12,958 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:13,259 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-09 08:03:13,651 - INFO - استخدام الاتصال الحالي
2025-07-09 08:03:13,652 - INFO - بدء إرسال 5 كرت هوت سبوت مباشرة عبر API
2025-07-09 08:03:13,692 - INFO - تم إرسال المستخدم 2019346127 بنجاح (1/5)
2025-07-09 08:03:13,830 - INFO - تم إرسال المستخدم 2045220429 بنجاح (2/5)
2025-07-09 08:03:13,972 - INFO - تم إرسال المستخدم 2089848037 بنجاح (3/5)
2025-07-09 08:03:14,110 - INFO - تم إرسال المستخدم 2049099499 بنجاح (4/5)
2025-07-09 08:03:14,250 - INFO - تم إرسال المستخدم 2083621551 بنجاح (5/5)
2025-07-09 08:03:14,352 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 08:03:14,352 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 18:03:16,051 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:16,400 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:19,478 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:19,832 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:22,910 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:23,252 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:26,326 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:26,670 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:29,745 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:30,133 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:33,214 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:33,556 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:36,641 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:37,063 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:40,141 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:40,488 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:42,511 - INFO - تم حفظ القالب: 10
2025-07-09 18:03:43,585 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:43,934 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:47,079 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:47,605 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:50,431 - INFO - تم حفظ القالب: 10
2025-07-09 18:03:50,683 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:51,030 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:54,121 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:54,469 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:57,544 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:03:57,890 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:03:58,370 - INFO - تم حفظ القالب: 20
2025-07-09 18:04:00,964 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:01,307 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:04,382 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:04,728 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:07,810 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:08,228 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:11,308 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:11,664 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:16,159 - INFO - معالجة الأمر: /start
2025-07-09 08:04:17,609 - INFO - معالجة الأمر: /start
2025-07-09 18:04:17,598 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 08:04:17,888 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:04:17,991 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:21,338 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:21,434 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:24,782 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:24,881 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:28,242 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:28,364 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:29,901 - INFO - بدء إغلاق التطبيق
2025-07-09 18:04:29,954 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180429.db
2025-07-09 18:04:29,956 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_180429.db
2025-07-09 18:04:30,055 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:04:30,059 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:04:30,859 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:04:30,872 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:04:30,883 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:04:30,884 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:04:31,059 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180431.db
2025-07-09 18:04:31,077 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:04:31,516 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:04:31,517 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 08:04:31,748 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:33,523 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:04:33,777 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:04:33,779 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 18:04:36,782 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:04:36,795 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 08:04:37,136 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:37,087 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:04:37,106 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:04:37,226 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:38,812 - INFO - معالجة callback: select_system_hs
2025-07-09 18:04:39,399 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 08:04:40,586 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:40,658 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:40,869 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:04:40,869 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 08:04:41,627 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:04:41,630 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:04:41,631 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:04:41,668 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:04:41,669 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:04:41,673 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:04:41,675 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:04:41,680 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:04:41,682 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:04:41,655 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:04:41,657 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 08:04:41,942 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:04:42,002 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:04:42,107 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:04:42,212 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:04:42,214 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:04:42,216 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:04:42,218 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:04:42,227 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:04:42,708 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:04:42,871 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:04:42,872 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:04:43,057 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:04:43,059 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:04:43,087 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:04:43,091 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:04:43,095 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:04:43,097 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:04:43,122 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:04:43,359 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:04:43,361 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:04:43,367 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:04:43,372 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:04:43,378 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:04:43,380 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:04:43,684 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:04:44,305 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:47,390 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:47,741 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:50,823 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:51,182 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:54,265 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:54,628 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:04:54,558 - INFO - معالجة الأمر: /start
2025-07-09 08:04:54,849 - INFO - معالجة الأمر: /start
2025-07-09 18:04:54,854 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 08:04:55,093 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:04:58,178 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:04:58,542 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:01,622 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:02,023 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:05,094 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:05,450 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:07,308 - INFO - بدء إغلاق التطبيق
2025-07-09 18:05:07,343 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180507.db
2025-07-09 18:05:07,345 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_180507.db
2025-07-09 18:05:07,427 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:05:07,428 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:05:15,124 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:05:15,138 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:05:15,152 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:05:15,153 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:05:15,282 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180515.db
2025-07-09 18:05:15,308 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:05:15,748 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:05:15,749 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:05:17,765 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:05:17,995 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:05:17,997 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 08:05:18,348 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:21,006 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:05:21,020 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:05:21,306 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:05:21,307 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:05:21,418 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:21,777 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:24,645 - INFO - معالجة الأمر: /start
2025-07-09 18:05:24,854 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:29,916 - INFO - معالجة الأمر: /start
2025-07-09 08:05:30,147 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:05:30,181 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 08:05:30,535 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:34,086 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:34,440 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:35,663 - INFO - معالجة callback: select_system_hs
2025-07-09 08:05:36,179 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 08:05:37,441 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:05:37,578 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:37,919 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:05:37,920 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:05:37,921 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:05:37,794 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 08:05:37,957 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:05:37,959 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:05:37,969 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:05:37,972 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:05:37,977 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:05:37,978 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 08:05:38,240 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:05:38,281 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:05:38,283 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 18:05:38,563 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:05:38,691 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:05:38,792 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:05:38,793 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:05:38,795 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:05:38,797 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:05:38,806 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:05:39,284 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:05:39,454 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:05:39,455 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:05:39,656 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:05:39,659 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:05:39,733 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:05:39,780 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:05:39,784 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:05:39,788 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:05:39,789 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:05:39,988 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:05:39,989 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:05:39,996 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:05:40,003 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:05:40,013 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:05:40,017 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:05:40,285 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:05:40,931 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:43,997 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:44,356 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:47,424 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:47,775 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:50,852 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:51,204 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:54,308 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:54,735 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:05:57,815 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:05:58,166 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:01,248 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:01,613 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:03,801 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 18:06:04,685 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:05,036 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:08,111 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:08,462 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:11,545 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:11,937 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:15,032 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:15,390 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:16,266 - INFO - بدء إغلاق التطبيق
2025-07-09 18:06:16,288 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180616.db
2025-07-09 18:06:16,290 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_180616.db
2025-07-09 18:06:16,346 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:06:16,349 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:06:33,985 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:06:33,997 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:06:34,012 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:06:34,012 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:06:34,128 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180634.db
2025-07-09 18:06:34,142 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:06:34,585 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:06:34,587 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:06:43,392 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:06:43,673 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:06:43,674 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 08:06:44,034 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:46,688 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:06:46,700 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:06:46,982 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:06:46,984 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:06:47,116 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:47,467 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:50,537 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:50,890 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:06:51,558 - INFO - معالجة callback: select_system_hs
2025-07-09 08:06:52,054 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 08:06:53,979 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:06:53,960 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:06:54,178 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 08:06:54,547 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:06:54,549 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:06:54,549 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:06:54,586 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:06:54,587 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:06:54,591 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:06:54,593 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:06:54,597 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:06:54,599 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:06:54,673 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:06:54,674 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 08:06:54,864 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:06:54,957 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:06:55,062 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:06:55,162 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:06:55,163 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:06:55,165 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:06:55,166 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:06:55,171 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:06:55,627 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:06:55,791 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:06:55,793 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:06:56,000 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:06:56,001 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:06:56,063 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:06:56,119 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:06:56,122 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:06:56,127 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:06:56,128 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:06:56,313 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:06:56,314 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:06:56,323 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:06:56,326 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:06:56,332 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:06:56,333 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:06:56,610 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:06:57,200 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:07:00,270 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:00,632 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:03,855 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 18:07:03,707 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:07:03,947 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 08:07:04,829 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:06,267 - INFO - معالجة callback: independent_custom_hs_10_lightning
2025-07-09 18:07:07,897 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:08,269 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:10,180 - INFO - معالجة الأمر: 1
2025-07-09 08:07:10,502 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:07:10,503 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:07:10,535 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:07:10,536 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:07:10,540 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:07:10,542 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:07:10,782 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:07:10,786 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:07:10,816 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:07:10,817 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:07:10,821 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:07:10,823 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:07:10,824 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 5
2025-07-09 08:07:10,825 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-09 08:07:10,825 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-09 08:07:10,833 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 1
2025-07-09 08:07:10,834 - INFO - ⚡ البرق الموحد: العدد المطلوب 1 حساب لنظام Hotspot
2025-07-09 08:07:10,835 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 1
2025-07-09 08:07:10,835 - INFO - ⚡ البرق الموحد: تم توليد 1 حساب لنظام Hotspot
2025-07-09 08:07:10,870 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 1
2025-07-09 08:07:10,871 - WARNING - فشل في حفظ آخر رقم تسلسلي: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 08:07:10,885 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-1 كارت-09-07-2025-08-07-10-hotspot.pdf
2025-07-09 08:07:10,887 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-1 كارت-09-07-2025-08-07-10-hotspot.pdf
2025-07-09 08:07:11,218 - ERROR - حدث خطأ أثناء حفظ PDF: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-09 08:07:11,231 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-1 كارت-09-07-2025-08-07-10-hotspot.pdf
2025-07-09 08:07:11,233 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-1 كارت-09-07-2025-08-07-10-hotspot.rsc
2025-07-09 18:07:11,350 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:11,788 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:11,819 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-1 كارت-09-07-2025-08-07-10-hotspot.pdf
2025-07-09 08:07:11,820 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-1 كارت-09-07-2025-08-07-10-hotspot.pdf
2025-07-09 08:07:12,145 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-09 08:07:12,148 - INFO - استخدام الاتصال الحالي
2025-07-09 08:07:12,549 - INFO - ⚡ البرق الموحد: بدء إرسال 1 مستخدم إلى MikroTik
2025-07-09 08:07:12,593 - INFO - ⚡ البرق الموحد: النتائج النهائية - نجح: 1, فشل: 0, مكرر: 0
2025-07-09 08:07:12,595 - INFO - ⚡ البرق الموحد مكتمل: تم إنشاء وإرسال 1 كارت بنجاح إلى MikroTik
2025-07-09 18:07:14,866 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:15,221 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:15,826 - INFO - تم إرسال 1 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-09 18:07:18,292 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:18,649 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:07:21,715 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:22,069 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:07:25,138 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:07:25,544 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:07:26,445 - INFO - بدء إغلاق التطبيق
2025-07-09 18:07:26,468 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_180726.db
2025-07-09 18:07:26,469 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_180726.db
2025-07-09 18:07:26,768 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:07:26,770 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:13:44,076 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:13:44,090 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:13:44,104 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:13:44,105 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:13:44,260 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_181344.db
2025-07-09 18:13:44,276 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:13:44,716 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:13:44,717 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:13:46,711 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:13:46,987 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:13:46,989 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 08:13:47,372 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:13:49,989 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:13:50,021 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:13:50,371 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:13:50,386 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:13:50,430 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:13:50,819 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:13:53,916 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:13:54,290 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:13:57,346 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:13:57,725 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:00,050 - INFO - معالجة callback: select_system_hs
2025-07-09 08:14:00,572 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 18:14:00,784 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:01,157 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:03,845 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:14:04,212 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:04,510 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 08:14:04,512 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 08:14:04,512 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 08:14:04,545 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 08:14:04,546 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 08:14:04,550 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:14:04,553 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 08:14:04,557 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 08:14:04,558 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:14:04,427 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 08:14:04,812 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:14:04,942 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:14:04,943 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 18:14:05,229 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:14:05,338 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:14:05,441 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:14:05,443 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:14:05,445 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:14:05,447 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:14:05,456 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:14:05,955 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:14:06,122 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:14:06,123 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:14:06,333 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:14:06,335 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:14:06,405 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:14:06,449 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:14:06,453 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:14:06,457 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:14:06,459 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:14:06,649 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:14:06,651 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:14:06,657 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:14:06,660 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:14:06,667 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:14:06,669 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:14:07,011 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 08:14:07,735 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:10,789 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:11,226 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:14,277 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:14,646 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:17,720 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:18,104 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:20,889 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 18:14:21,161 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:21,372 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 08:14:22,217 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:25,310 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:25,681 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:28,733 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:29,103 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:32,226 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:32,619 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:35,822 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:36,271 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:39,369 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:39,742 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:42,794 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:43,373 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:46,438 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:46,868 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:14:49,951 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:14:57,779 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:00,836 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:01,290 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:04,346 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:04,726 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:07,775 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:08,239 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:11,399 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:11,784 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:14,861 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:15,232 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:18,317 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:18,740 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:21,869 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:22,567 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:25,697 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:26,263 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:29,472 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:30,052 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:33,166 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:44,408 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:47,850 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:47,931 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:51,360 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:51,431 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:54,886 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:54,966 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:15:58,404 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:15:58,460 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:01,891 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:01,957 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:05,397 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:05,559 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:08,988 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:09,047 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:12,489 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:12,548 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:15,998 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:16,072 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:19,535 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:19,618 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:23,052 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:23,126 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:26,563 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:26,634 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:30,082 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:30,167 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:33,680 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:39,600 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:43,024 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:47,982 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:51,456 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:51,503 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:54,970 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:55,025 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:16:58,487 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:16:58,536 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:01,991 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:02,051 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:05,500 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:05,642 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:09,100 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:09,165 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:12,602 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:12,655 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:16,100 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:16,255 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:19,711 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:19,757 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:23,174 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:23,264 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:26,690 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:26,749 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:30,229 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:30,313 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:33,756 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:33,846 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:37,274 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:37,325 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:40,777 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:40,843 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:44,278 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:44,341 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:47,815 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:47,895 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:51,329 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:17:51,402 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:17:59,363 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:02,482 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:02,882 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:05,929 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:06,445 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:09,539 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:09,965 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:13,054 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:13,974 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:17,053 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:17,468 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:20,525 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:20,946 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:24,011 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:24,448 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:27,512 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:27,925 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:30,997 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:31,430 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:34,492 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:34,929 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:37,982 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:38,446 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:41,499 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:41,977 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:45,108 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:45,519 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:48,587 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:49,017 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:52,111 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:52,560 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:55,631 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:56,032 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:18:59,103 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:18:59,525 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:02,590 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:03,085 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:06,495 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:10,230 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:13,484 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:13,886 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:17,024 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:17,405 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:20,447 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:20,839 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:23,879 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:24,269 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:27,308 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:27,694 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:30,740 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:31,138 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:34,178 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:34,569 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:37,708 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:38,123 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:41,167 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:41,549 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:44,605 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:45,056 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:48,095 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:48,485 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:51,558 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:52,010 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:55,045 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:55,706 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:19:58,745 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:19:59,162 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:02,233 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:02,653 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:05,729 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:06,121 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:09,602 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:09,992 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:13,050 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:13,443 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:16,517 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:17,511 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:20,596 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:21,042 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:24,093 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:24,484 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:27,533 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:27,920 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:30,973 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:31,368 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:34,408 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:34,855 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:37,902 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:38,292 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:41,332 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:41,735 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:44,789 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:45,214 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:48,257 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:48,733 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:51,787 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:52,182 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:55,275 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:55,712 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:20:58,773 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:20:59,169 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:02,221 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:02,622 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:05,677 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:06,090 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:09,148 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:09,543 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:12,604 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:18,262 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:23,157 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:23,587 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:26,660 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:27,060 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:30,142 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:30,543 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:33,613 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:34,018 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:37,081 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:37,942 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:41,751 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:46,154 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:49,189 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:49,602 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:52,645 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:53,050 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:56,135 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:56,522 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:21:59,552 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:21:59,944 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:02,999 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:03,395 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:06,454 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:06,910 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:09,979 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:10,367 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:13,407 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:13,813 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:17,212 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:23,555 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:27,073 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:30,656 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:30,752 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:34,273 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:34,943 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:38,337 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:38,656 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:42,105 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:47,874 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:52,054 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:54,781 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:22:58,287 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:22:58,373 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:23:01,769 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:23:01,804 - ERROR - خطأ في API التلجرام: 409
2025-07-09 08:23:05,230 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:23:05,320 - ERROR - خطأ في API التلجرام: 409
2025-07-09 18:23:13,643 - INFO - بدء إغلاق التطبيق
2025-07-09 18:23:17,610 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_182317.db
2025-07-09 18:23:17,612 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_182317.db
2025-07-09 18:23:18,257 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:23:18,259 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:23:25,160 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:23:25,179 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:23:25,196 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:23:25,197 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:23:25,321 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_182325.db
2025-07-09 18:23:25,338 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:23:25,777 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:23:25,778 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:23:27,802 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:23:28,063 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:23:28,065 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 18:23:31,079 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:23:31,125 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:23:31,435 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:23:31,435 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:23:35,556 - INFO - معالجة callback: select_system_hs
2025-07-09 18:23:43,264 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 18:23:48,851 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:23:49,493 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:23:49,494 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 18:23:49,822 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:23:49,930 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:23:50,178 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:23:50,180 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:23:50,181 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:23:50,211 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:23:50,220 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:23:50,685 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:23:55,787 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:23:55,788 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:23:55,992 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:23:55,993 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:23:56,201 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:23:56,261 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:23:56,265 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:23:56,269 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:23:56,275 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:23:56,327 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:23:56,328 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:23:56,336 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:23:56,340 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:23:56,345 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:23:56,347 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:23:56,658 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:24:08,300 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 18:24:14,403 - INFO - بدء إغلاق التطبيق
2025-07-09 18:24:15,384 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_182414.db
2025-07-09 18:24:15,385 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_182414.db
2025-07-09 18:24:15,811 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:24:15,877 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:53:15,574 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:53:15,607 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:53:15,649 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:53:15,650 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:53:15,871 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_185315.db
2025-07-09 18:53:15,887 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:53:16,310 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:53:16,311 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:53:18,351 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:53:18,636 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:53:18,656 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 18:53:21,659 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:53:21,731 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:53:22,075 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:53:22,076 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:53:23,436 - INFO - معالجة callback: select_system_hs
2025-07-09 18:53:24,433 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 18:53:25,611 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 18:53:26,155 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 18:53:26,156 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 18:53:26,447 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 18:53:26,555 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:53:26,661 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:53:26,662 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 18:53:26,663 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 18:53:26,665 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 18:53:26,674 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:53:27,128 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:53:27,306 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:53:27,307 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:53:27,514 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 18:53:27,515 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 18:53:27,576 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 18:53:27,619 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 18:53:27,623 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 18:53:27,626 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 18:53:27,627 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 18:53:27,818 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 18:53:27,822 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 18:53:27,829 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:53:27,832 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 18:53:27,839 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 18:53:27,840 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 18:53:28,162 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 18:53:29,873 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-09 18:53:41,379 - INFO - بدء إغلاق التطبيق
2025-07-09 18:53:41,504 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_185341.db
2025-07-09 18:53:41,505 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_185341.db
2025-07-09 18:53:41,549 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 18:53:41,550 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 18:54:25,483 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:54:25,500 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:54:25,515 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:54:25,516 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:54:25,975 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_185425.db
2025-07-09 18:54:25,994 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:54:26,412 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:54:26,412 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:54:28,434 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:54:29,135 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:54:29,137 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 18:54:32,142 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:54:32,170 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:54:32,590 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:54:32,591 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:54:46,718 - INFO - بدء إغلاق التطبيق
2025-07-09 18:54:46,719 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 18:57:02,319 - INFO - تم بدء تشغيل التطبيق
2025-07-09 18:57:02,795 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 18:57:02,808 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 18:57:02,809 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:57:03,925 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_185702.db
2025-07-09 18:57:03,945 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 18:57:04,359 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 18:57:04,367 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 18:57:06,383 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 18:57:06,758 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 18:57:06,760 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 18:57:06,987 - INFO - معالجة callback: refresh_system_selection
2025-07-09 18:57:07,286 - ERROR - خطأ في الرد على callback query: HTTP Error 400: Bad Request
2025-07-09 18:57:07,741 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:57:09,769 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 18:57:09,776 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 18:57:10,189 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 18:57:10,191 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 18:57:35,491 - INFO - تم اختيار النظام: hotspot
2025-07-09 18:57:35,849 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 18:57:36,013 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 18:57:36,015 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 18:57:37,185 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 18:57:49,403 - INFO - تم توليد 1 حساب
2025-07-09 18:57:49,404 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-09 18:57:50,233 - INFO - تم توليد 1 حساب
2025-07-09 18:57:50,234 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 7 (من generate_all)
2025-07-09 19:00:59,467 - INFO - بدء إغلاق التطبيق
2025-07-09 19:01:00,879 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_190059.db
2025-07-09 19:01:00,989 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_190059.db
2025-07-09 19:01:02,185 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 19:01:02,187 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 19:43:56,081 - INFO - تم بدء تشغيل التطبيق
2025-07-09 19:43:56,100 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 19:43:56,112 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 19:43:56,113 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 19:43:56,304 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_194356.db
2025-07-09 19:43:56,320 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 19:43:56,754 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 19:43:56,755 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 19:43:58,769 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 19:43:59,056 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 19:43:59,057 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 19:44:02,061 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 19:44:02,097 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 19:44:02,372 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 19:44:02,373 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 19:44:16,481 - INFO - معالجة callback: single_card
2025-07-09 19:44:16,736 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 19:44:17,082 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 19:44:21,890 - INFO - معالجة callback: single_card_template_10
2025-07-09 19:44:22,109 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10
2025-07-09 19:44:22,390 - INFO - 🔄 تفعيل نظام Hotspot...
2025-07-09 19:44:22,392 - INFO - 📋 تطبيق القالب: 10
2025-07-09 19:44:22,393 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: 'MikroTikCardGenerator' object has no attribute 'apply_hotspot_template'
2025-07-09 19:45:01,363 - INFO - بدء إغلاق التطبيق
2025-07-09 19:45:01,364 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 09:46:03,615 - INFO - تم بدء تشغيل التطبيق
2025-07-09 09:46:03,668 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 09:46:03,687 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 09:46:03,713 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 09:46:03,858 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_094603.db
2025-07-09 09:46:03,863 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 09:46:15,271 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 09:46:15,273 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 09:46:17,285 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 09:46:17,737 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 09:46:17,738 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 09:46:20,748 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 09:46:20,763 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 09:46:21,060 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 09:46:21,061 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 09:46:25,447 - INFO - معالجة callback: single_card
2025-07-09 09:46:25,664 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 09:46:25,904 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 09:46:27,729 - INFO - معالجة callback: single_card_template_10
2025-07-09 09:46:27,948 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10
2025-07-09 09:46:28,246 - INFO - 🔄 تفعيل نظام Hotspot...
2025-07-09 09:46:28,248 - INFO - 📋 تطبيق القالب: 10
2025-07-09 09:46:28,248 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: 'MikroTikCardGenerator' object has no attribute 'apply_hotspot_template'
2025-07-09 09:47:25,528 - INFO - بدء إغلاق التطبيق
2025-07-09 09:47:25,842 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 10:33:37,765 - INFO - تم بدء تشغيل التطبيق
2025-07-09 10:33:37,798 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 10:33:37,805 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 10:33:37,888 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 10:33:38,067 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_103338.db
2025-07-09 10:33:38,071 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 10:33:40,015 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 10:33:40,016 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 10:33:42,045 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 10:33:42,548 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 10:33:42,557 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 10:33:45,567 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 10:33:45,601 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 10:33:45,933 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 10:33:45,934 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 10:33:54,715 - INFO - معالجة callback: single_card
2025-07-09 10:33:55,028 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 10:33:55,320 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 10:33:56,473 - INFO - معالجة callback: single_card_template_10
2025-07-09 10:33:56,787 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10
2025-07-09 10:33:57,097 - INFO - 🔄 تفعيل نظام Hotspot...
2025-07-09 10:33:57,099 - INFO - 📋 تطبيق القالب: 10
2025-07-09 10:33:57,100 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 10:33:57,100 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 10:33:57,107 - ERROR - ❌ خطأ في تحديث الواجهة المرئية: 'MikroTikCardGenerator' object has no attribute 'preview_canvas'
2025-07-09 10:33:57,108 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 3 إعدادات
2025-07-09 10:33:57,108 - INFO - ✅ تم تطبيق 3 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 10:33:57,108 - WARNING - ⚠️ تعذر تحديث المعاينة: 'MikroTikCardGenerator' object has no attribute 'preview_canvas'
2025-07-09 10:33:57,109 - INFO - 🔢 تعيين عدد الكروت إلى 1
2025-07-09 10:33:57,112 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 10:33:57,113 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: 'MikroTikCardGenerator' object has no attribute 'profile_combo'
2025-07-09 22:09:58,135 - INFO - تم بدء تشغيل التطبيق
2025-07-09 22:09:58,159 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 22:09:58,185 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 22:09:58,186 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 22:09:58,399 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_220958.db
2025-07-09 22:09:58,434 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 22:09:58,860 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 22:09:58,861 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 22:10:00,872 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 22:10:01,159 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 22:10:01,160 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 22:10:04,167 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 22:10:04,241 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 22:10:04,560 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 22:10:04,561 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 22:10:15,940 - INFO - معالجة callback: single_card
2025-07-09 22:10:16,171 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 22:10:16,450 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 22:10:19,276 - INFO - معالجة callback: single_card_template_10
2025-07-09 22:10:19,534 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10
2025-07-09 22:10:19,905 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 22:10:19,907 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 22:10:20,017 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 22:10:20,127 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 22:10:20,129 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 22:10:20,130 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 22:10:20,130 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-09 22:10:20,131 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 22:10:20,141 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 22:10:20,606 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 22:10:20,772 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 22:10:20,773 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 22:10:21,040 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 22:10:21,041 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 22:10:21,045 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 22:10:21,373 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-09 22:10:21,378 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 22:10:21,381 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 22:10:21,417 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 22:10:21,419 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 22:10:21,424 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 22:10:21,427 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 22:10:21,428 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-09 22:10:21,432 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 22:10:21,435 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 22:10:21,441 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 22:10:21,449 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 22:10:21,450 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 22:10:21,452 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 22:10:21,452 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 22:10:21,453 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 22:10:21,500 - INFO - تم توليد 1 حساب
2025-07-09 22:10:21,501 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 8 (من generate_all)
2025-07-09 22:10:21,600 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 22:10:21,602 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 22:10:21,646 - INFO - نجح الاتصال مع ***********
2025-07-09 22:10:22,416 - INFO - ✅ تم إرسال المستخدم: 0149096903
2025-07-09 22:10:22,418 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 22:10:22,422 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 22:10:22,679 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 22:10:45,600 - INFO - معالجة الأمر: /start
2025-07-09 22:10:46,022 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 22:10:47,145 - INFO - معالجة callback: single_card
2025-07-09 22:10:47,432 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 22:10:47,734 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 22:10:49,455 - INFO - معالجة callback: single_card_template_20
2025-07-09 22:10:49,834 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 20
2025-07-09 22:10:50,391 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 22:10:50,393 - INFO - النظام hotspot مفعل بالفعل
2025-07-09 22:10:50,394 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-09 22:10:50,397 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 22:10:50,419 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 20
2025-07-09 22:10:50,424 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 22:10:50,425 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 22:10:50,481 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 22:10:50,482 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 22:10:50,488 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 22:10:50,493 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 22:10:50,494 - INFO - ✅ تم تطبيق القالب '20' بنجاح
2025-07-09 22:10:50,497 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 22:10:50,499 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 22:10:50,508 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 22:10:50,514 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 22:10:50,515 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 22:10:50,517 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 22:10:50,518 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 22:10:50,519 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 22:10:50,577 - INFO - تم توليد 1 حساب
2025-07-09 22:10:50,579 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 9 (من generate_all)
2025-07-09 22:10:50,605 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 22:10:50,645 - INFO - استخدام الاتصال الحالي
2025-07-09 22:10:51,395 - INFO - ✅ تم إرسال المستخدم: 0227883832
2025-07-09 22:10:51,397 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 22:10:51,400 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 22:10:51,678 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 22:14:23,956 - INFO - بدء إغلاق التطبيق
2025-07-09 22:14:23,984 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_221423.db
2025-07-09 22:14:23,985 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_221423.db
2025-07-09 22:14:23,986 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 22:14:24,053 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 22:14:24,055 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 23:08:28,450 - INFO - تم بدء تشغيل التطبيق
2025-07-09 23:08:28,940 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 23:08:28,955 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 23:08:28,956 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:08:29,118 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_230829.db
2025-07-09 23:08:29,133 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 23:08:29,556 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 23:08:29,645 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 23:08:32,505 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 23:08:32,797 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 23:08:32,799 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 23:08:35,816 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 23:08:35,828 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 23:08:36,165 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:08:36,166 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 23:09:11,344 - INFO - معالجة callback: single_card
2025-07-09 23:09:11,627 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 23:09:11,946 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 23:09:15,467 - INFO - معالجة callback: single_card_template_10
2025-07-09 23:09:15,723 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10
2025-07-09 23:09:16,000 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 23:09:16,001 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 23:09:16,112 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:09:16,208 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 23:09:16,209 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 23:09:16,210 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 23:09:16,210 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-09 23:09:16,211 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 23:09:16,219 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:09:16,686 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 23:09:16,850 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 23:09:16,851 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:09:17,096 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 23:09:17,097 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 23:09:17,098 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:09:17,314 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-09 23:09:17,318 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:09:17,319 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:09:17,440 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:09:17,442 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:09:17,449 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:09:17,452 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:09:17,453 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-09 23:09:17,454 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:09:17,457 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 23:09:17,467 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 23:09:17,473 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:09:17,474 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 23:09:17,475 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 23:09:17,476 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 23:09:17,477 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 23:09:17,537 - INFO - تم توليد 1 حساب
2025-07-09 23:09:17,538 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10 (من generate_all)
2025-07-09 23:09:17,562 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 23:09:17,563 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 23:09:17,607 - INFO - نجح الاتصال مع ***********
2025-07-09 23:09:18,357 - INFO - ✅ تم إرسال المستخدم: 0103545535
2025-07-09 23:09:18,358 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 23:09:18,361 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 23:09:18,661 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 23:10:02,020 - INFO - معالجة callback: single_card_template_20
2025-07-09 23:10:02,253 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 20
2025-07-09 23:10:02,555 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 23:10:02,557 - INFO - النظام hotspot مفعل بالفعل
2025-07-09 23:10:02,558 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-09 23:10:02,559 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:10:02,576 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 20
2025-07-09 23:10:02,594 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 23:10:02,595 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:10:02,630 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:10:02,631 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 23:10:02,639 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:10:02,642 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:10:02,643 - INFO - ✅ تم تطبيق القالب '20' بنجاح
2025-07-09 23:10:02,644 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 23:10:02,647 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 23:10:02,658 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 23:10:02,665 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:10:02,668 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 23:10:02,671 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 23:10:02,672 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 23:10:02,673 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 23:10:02,727 - INFO - تم توليد 1 حساب
2025-07-09 23:10:02,729 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 11 (من generate_all)
2025-07-09 23:10:02,801 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 23:10:02,836 - INFO - استخدام الاتصال الحالي
2025-07-09 23:10:03,586 - INFO - ✅ تم إرسال المستخدم: 0209735085
2025-07-09 23:10:03,587 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 23:10:03,591 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 23:10:03,932 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 23:10:36,882 - INFO - تم حفظ القالب: 20
2025-07-09 23:10:43,321 - INFO - معالجة callback: single_card_template_20
2025-07-09 23:10:43,651 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 20
2025-07-09 23:10:43,920 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 23:10:43,922 - INFO - النظام hotspot مفعل بالفعل
2025-07-09 23:10:43,923 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-09 23:10:43,924 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:10:43,974 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 20
2025-07-09 23:10:43,978 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 23:10:43,979 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:10:44,019 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:10:44,021 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 23:10:44,027 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:10:44,033 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:10:44,034 - INFO - ✅ تم تطبيق القالب '20' بنجاح
2025-07-09 23:10:44,035 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 23:10:44,038 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 23:10:44,048 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 23:10:44,053 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:10:44,055 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 23:10:44,057 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 23:10:44,058 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 23:10:44,059 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 23:10:44,109 - INFO - تم توليد 1 حساب
2025-07-09 23:10:44,111 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 12 (من generate_all)
2025-07-09 23:10:44,146 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 23:10:44,185 - INFO - استخدام الاتصال الحالي
2025-07-09 23:10:44,935 - INFO - ✅ تم إرسال المستخدم: 0247525256
2025-07-09 23:10:44,936 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 23:10:44,939 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 23:10:45,289 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 23:11:17,021 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:11:17,022 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-09 23:11:17,024 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:11:17,125 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:11:19,149 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:11:19,150 - INFO - محاولة الاتصال بـ ***********:8729 (SSL)
2025-07-09 23:11:19,444 - ERROR - خطأ في الاتصال - النوع: RouterOsApiCommunicationError, الرسالة: ('Error "invalid user name or password (6)" executing command b\'/login =name=saye =response=003be739ec9dc21749e19c97eeffc2527f .tag=2\'', b'invalid user name or password (6)')
2025-07-09 23:11:19,446 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 23:11:20,764 - INFO - تم تسجيل العملية: اختبار الاتصال - فشل الاتصال
2025-07-09 23:12:15,869 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:12:15,870 - INFO - محاولة الاتصال بـ ***********:8729 (SSL)
2025-07-09 23:12:16,062 - ERROR - خطأ في الاتصال - النوع: RouterOsApiCommunicationError, الرسالة: ('Error "invalid user name or password (6)" executing command b\'/login =name=saye =response=002a7f7eebd702df953ff1ea319d8e44bb .tag=2\'', b'invalid user name or password (6)')
2025-07-09 23:12:16,065 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 23:12:17,367 - INFO - تم تسجيل العملية: اختبار الاتصال - فشل الاتصال
2025-07-09 23:12:33,365 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:33,367 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:33,463 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:12:36,797 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:12:36,798 - INFO - محاولة الاتصال بـ ***********:8729 (عادي)
2025-07-09 23:12:36,801 - ERROR - خطأ في الاتصال - النوع: RouterOsApiConnectionClosedError, الرسالة: 
2025-07-09 23:12:36,802 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 23:12:38,378 - INFO - تم تسجيل العملية: اختبار الاتصال - فشل الاتصال
2025-07-09 23:12:47,869 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:47,871 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:47,968 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:12:48,957 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:48,958 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:49,062 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:12:49,933 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:49,935 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:50,031 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:12:50,709 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:50,710 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:50,813 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:12:51,501 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:51,502 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:51,610 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:12:53,021 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:53,022 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:53,128 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:12:53,813 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:53,814 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:53,909 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:12:53,915 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:53,917 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:54,019 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:12:54,901 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:54,902 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:55,003 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:12:55,837 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:55,838 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:55,941 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:12:57,109 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:12:57,110 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:12:57,207 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:13:00,741 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:13:00,742 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:13:00,848 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:13:03,365 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:13:03,366 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:13:03,475 - INFO - تم تفعيل SSL - المنفذ الافتراضي: 8729
2025-07-09 23:13:06,205 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-09 23:13:06,206 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:13:06,304 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-09 23:13:08,797 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:13:08,798 - INFO - محاولة الاتصال بـ ***********:8728 (عادي)
2025-07-09 23:13:08,842 - INFO - نجح الاتصال مع ***********
2025-07-09 23:13:09,971 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع www.TEDATA.com
2025-07-09 23:14:44,509 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:14:44,549 - INFO - استخدام الاتصال الحالي
2025-07-09 23:14:45,663 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع www.TEDATA.com
2025-07-09 23:15:03,651 - INFO - معالجة الأمر: /start
2025-07-09 23:15:04,156 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:15:05,492 - INFO - معالجة callback: single_card
2025-07-09 23:15:05,749 - INFO - 🎴 بدء معالجة طلب كرت واحد
2025-07-09 23:15:06,030 - INFO - ✅ تم إرسال قائمة قوالب كرت واحد - 5 قالب
2025-07-09 23:15:11,629 - INFO - معالجة callback: single_card_template_10
2025-07-09 23:15:11,887 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10
2025-07-09 23:15:12,186 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 23:15:12,188 - INFO - النظام hotspot مفعل بالفعل
2025-07-09 23:15:12,189 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-09 23:15:12,190 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:15:12,207 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-09 23:15:12,212 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:15:12,216 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:15:12,247 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:15:12,249 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:15:12,254 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:15:12,257 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:15:12,257 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-09 23:15:12,258 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:15:12,261 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 23:15:12,268 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 23:15:12,273 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:15:12,274 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 23:15:12,275 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 23:15:12,276 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 23:15:12,277 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 23:15:12,317 - INFO - تم توليد 1 حساب
2025-07-09 23:15:12,318 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 13 (من generate_all)
2025-07-09 23:15:12,357 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 23:15:12,398 - INFO - استخدام الاتصال الحالي
2025-07-09 23:15:13,179 - INFO - ✅ تم إرسال المستخدم: 0118082251
2025-07-09 23:15:13,180 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 23:15:13,184 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 23:15:13,580 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 23:15:24,729 - INFO - معالجة callback: single_card_template_10-marawan
2025-07-09 23:15:24,971 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 10-marawan
2025-07-09 23:15:25,284 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 23:15:25,286 - INFO - النظام hotspot مفعل بالفعل
2025-07-09 23:15:25,287 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-09 23:15:25,288 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:15:25,304 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10-marawan
2025-07-09 23:15:25,315 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 23:15:25,316 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:15:25,350 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:15:25,351 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 23:15:25,360 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:15:25,363 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:15:25,364 - INFO - ✅ تم تطبيق القالب '10-marawan' بنجاح
2025-07-09 23:15:25,365 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 23:15:25,368 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 23:15:25,379 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 23:15:25,388 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:15:25,389 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 23:15:25,391 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 23:15:25,393 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 23:15:25,393 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 23:15:25,435 - INFO - تم توليد 1 حساب
2025-07-09 23:15:25,436 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 14 (من generate_all)
2025-07-09 23:15:25,457 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 23:15:25,498 - INFO - استخدام الاتصال الحالي
2025-07-09 23:15:26,248 - INFO - ✅ تم إرسال المستخدم: 1079046828
2025-07-09 23:15:26,250 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 23:15:26,253 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 23:15:26,532 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 23:15:41,805 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:15:41,838 - INFO - استخدام الاتصال الحالي
2025-07-09 23:15:43,552 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع www.TEDATA.com
2025-07-09 23:15:45,221 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:15:45,268 - INFO - استخدام الاتصال الحالي
2025-07-09 23:15:53,473 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع www.TEDATA.com
2025-07-09 23:15:56,109 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:15:56,147 - INFO - استخدام الاتصال الحالي
2025-07-09 23:15:58,028 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع www.TEDATA.com
2025-07-09 23:16:00,909 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-09 23:16:00,947 - INFO - استخدام الاتصال الحالي
2025-07-09 23:16:02,243 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع www.TEDATA.com
2025-07-09 23:16:09,957 - INFO - بدء إغلاق التطبيق
2025-07-09 23:16:09,979 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_231609.db
2025-07-09 23:16:09,980 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_231609.db
2025-07-09 23:16:09,981 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 23:16:10,056 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 23:16:10,057 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 23:16:14,580 - INFO - تم بدء تشغيل التطبيق
2025-07-09 23:16:14,595 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 23:16:14,606 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 23:16:14,607 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:16:14,732 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_231614.db
2025-07-09 23:16:14,751 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 23:16:15,203 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 23:16:15,204 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 23:16:17,220 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 23:16:17,474 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 23:16:17,476 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 23:16:20,473 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 23:16:20,487 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 23:16:20,743 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:16:20,743 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 23:16:21,396 - INFO - معالجة callback: single_card_template_20-marawan
2025-07-09 23:16:21,625 - INFO - 🎴 بدء إنشاء كرت واحد باستخدام القالب: 20-marawan
2025-07-09 23:16:21,931 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-09 23:16:21,933 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 23:16:21,962 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:16:22,061 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 23:16:22,063 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 23:16:22,064 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 23:16:22,064 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-09 23:16:22,065 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 23:16:22,073 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:16:22,554 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 23:16:22,721 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 23:16:22,722 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:16:22,946 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 23:16:22,947 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 23:16:22,951 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:16:23,211 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 20-marawan
2025-07-09 23:16:23,215 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 23:16:23,217 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:16:23,326 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:16:23,327 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 23:16:23,334 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:16:23,337 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:16:23,338 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح
2025-07-09 23:16:23,339 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 23:16:23,342 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-09 23:16:23,351 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-09 23:16:23,356 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:16:23,357 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-09 23:16:23,359 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-09 23:16:23,359 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-09 23:16:23,361 - INFO - 🏭 بدء توليد الكرت...
2025-07-09 23:16:23,406 - INFO - تم توليد 1 حساب
2025-07-09 23:16:23,408 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 15 (من generate_all)
2025-07-09 23:16:23,446 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-09 23:16:23,447 - INFO - محاولة الاتصال بـ *******:8728 (عادي)
2025-07-09 23:16:23,843 - INFO - نجح الاتصال مع *******
2025-07-09 23:16:30,340 - INFO - ✅ تم إرسال المستخدم: 2052817314
2025-07-09 23:16:30,342 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 23:16:30,345 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-09 23:16:30,613 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-09 23:17:40,712 - INFO - معالجة الأمر: /start
2025-07-09 23:17:41,071 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:17:43,698 - INFO - معالجة callback: system_control
2025-07-09 23:17:49,004 - INFO - معالجة callback: method_control_lightning
2025-07-09 23:17:51,926 - INFO - معالجة callback: count_control_lightning_10
2025-07-09 23:17:52,473 - INFO - تم توليد 1 حساب
2025-07-09 23:17:52,474 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 16 (من generate_all)
2025-07-09 23:17:55,527 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 1
2025-07-09 23:17:55,667 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(افتراضي)_ب20-1 كارت-09-07-2025-23-17-55-um.pdf
2025-07-09 23:17:56,430 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-09 23:18:04,879 - INFO - معالجة الأمر: /start
2025-07-09 23:18:05,226 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:18:16,155 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 23:18:29,357 - INFO - بدء إغلاق التطبيق
2025-07-09 23:18:29,393 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_231829.db
2025-07-09 23:18:29,394 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_231829.db
2025-07-09 23:18:29,395 - INFO - تم قطع الاتصال مع MikroTik
2025-07-09 23:18:29,472 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 23:18:29,473 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 23:18:34,462 - INFO - تم بدء تشغيل التطبيق
2025-07-09 23:18:34,481 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 23:18:34,508 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 23:18:34,512 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:18:34,629 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_231834.db
2025-07-09 23:18:34,646 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 23:18:35,080 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 23:18:35,081 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 23:18:37,085 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 23:18:37,426 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 23:18:37,427 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 23:18:40,424 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 23:18:40,438 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 23:18:40,782 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:18:40,783 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 23:18:47,457 - INFO - معالجة callback: select_system_hs
2025-07-09 23:18:48,076 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:18:50,238 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:18:50,807 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 23:18:50,808 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 23:18:51,084 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 23:18:51,192 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:18:51,292 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 23:18:51,293 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 23:18:51,294 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 23:18:51,296 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 23:18:51,305 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:18:51,758 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 23:18:51,922 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 23:18:51,924 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:18:52,112 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 23:18:52,113 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 23:18:52,184 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:18:52,218 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 23:18:52,222 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:18:52,226 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:18:52,227 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:18:52,429 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:18:52,430 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:18:52,435 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:18:52,440 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:18:52,446 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:18:52,448 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:18:52,746 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:18:59,356 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 23:19:00,038 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 23:19:00,042 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 23:19:00,042 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:19:00,082 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:19:00,083 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 23:19:00,088 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:19:00,092 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:19:00,098 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:19:00,099 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 23:19:00,393 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 23:19:08,616 - INFO - تم حفظ القالب: 20-marawan
2025-07-09 23:19:15,635 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 23:19:16,168 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 23:19:16,172 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 23:19:16,173 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:19:16,214 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:19:16,215 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 23:19:16,221 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:19:16,224 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:19:16,230 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:19:16,231 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 23:19:16,554 - INFO - ✅ تمت المزامنة الشاملة للقالب '10-marawan' بنجاح
2025-07-09 23:19:18,576 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:19:19,553 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:19:19,557 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:19:19,558 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:19:19,598 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:19:19,599 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:19:19,605 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:19:19,608 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:19:19,614 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:19:19,615 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:19:19,933 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:19:27,168 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 23:19:27,708 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 23:19:27,712 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 23:19:27,713 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:19:27,768 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:19:27,769 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 23:19:27,775 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:19:27,778 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:19:27,783 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:19:27,784 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 23:19:28,092 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 23:19:36,652 - INFO - معالجة الأمر: /start
2025-07-09 23:19:36,947 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:19:39,213 - INFO - معالجة callback: select_system_hs
2025-07-09 23:19:39,758 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:19:41,933 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:19:42,458 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:19:42,462 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:19:42,463 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:19:42,501 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:19:42,502 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:19:42,507 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:19:42,511 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:19:42,516 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:19:42,518 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:19:42,840 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:19:46,677 - INFO - بدء إغلاق التطبيق
2025-07-09 23:19:49,353 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_231949.db
2025-07-09 23:19:49,354 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_231949.db
2025-07-09 23:19:49,434 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 23:19:49,435 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 23:19:53,942 - INFO - تم بدء تشغيل التطبيق
2025-07-09 23:19:53,954 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 23:19:53,968 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 23:19:53,969 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:19:54,090 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_231954.db
2025-07-09 23:19:54,107 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 23:19:54,638 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 23:19:54,639 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 23:19:56,638 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 23:19:56,874 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 23:19:56,875 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 23:19:59,884 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 23:19:59,897 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 23:20:00,194 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:20:00,195 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 23:20:02,155 - INFO - معالجة callback: select_system_hs
2025-07-09 23:20:02,767 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:20:04,018 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:20:04,544 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 23:20:04,545 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 23:20:04,871 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 23:20:04,900 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:20:04,996 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 23:20:04,998 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 23:20:04,999 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 23:20:05,001 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 23:20:05,009 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:20:05,464 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 23:20:05,627 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 23:20:05,629 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:20:05,814 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 23:20:05,816 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 23:20:05,876 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:20:05,912 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 23:20:05,915 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:20:05,919 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:20:05,921 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:20:06,124 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:20:06,126 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:20:06,131 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:20:06,134 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:20:06,140 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:20:06,144 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:20:06,661 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:20:16,436 - INFO - تم حفظ القالب: 10
2025-07-09 23:20:33,086 - INFO - معالجة الأمر: /start
2025-07-09 23:20:33,383 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:20:34,666 - INFO - معالجة callback: select_system_hs
2025-07-09 23:20:35,178 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:20:37,680 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 23:20:38,283 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 23:20:38,287 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 23:20:38,288 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:20:38,324 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:20:38,325 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 23:20:38,331 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:20:38,334 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:20:38,339 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:20:38,342 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 23:20:38,722 - INFO - ✅ تمت المزامنة الشاملة للقالب '10-marawan' بنجاح
2025-07-09 23:20:54,385 - INFO - تم حفظ القالب: 10-marawan
2025-07-09 23:20:59,367 - INFO - معالجة الأمر: /start
2025-07-09 23:20:59,648 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:21:00,876 - INFO - معالجة callback: select_system_hs
2025-07-09 23:21:01,399 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:21:03,031 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 23:21:03,570 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 23:21:03,574 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 23:21:03,575 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:21:03,611 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:21:03,612 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 23:21:03,618 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:21:03,621 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:21:03,627 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:21:03,628 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 23:21:03,930 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 23:21:05,855 - INFO - تم حفظ القالب: 20-marawan
2025-07-09 23:21:10,339 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:21:11,116 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:21:11,120 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:21:11,121 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:21:11,159 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:21:11,160 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:21:11,165 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:21:11,169 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:21:11,174 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:21:11,176 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:21:11,490 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:21:17,121 - INFO - تم حفظ القالب: 10
2025-07-09 23:21:18,831 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 23:21:19,474 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 23:21:19,478 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 23:21:19,479 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:21:19,515 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:21:19,516 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 23:21:19,522 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:21:19,525 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:21:19,531 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:21:19,532 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 23:21:19,887 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 23:21:20,834 - INFO - تم حفظ القالب: 20
2025-07-09 23:21:22,280 - INFO - تم حفظ القالب: 10
2025-07-09 23:21:24,333 - INFO - بدء إغلاق التطبيق
2025-07-09 23:21:24,375 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_232124.db
2025-07-09 23:21:24,375 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_232124.db
2025-07-09 23:21:24,426 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 23:21:24,428 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 23:21:29,614 - INFO - تم بدء تشغيل التطبيق
2025-07-09 23:21:29,627 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 23:21:29,640 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 23:21:29,641 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:21:29,757 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_232129.db
2025-07-09 23:21:29,774 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 23:21:30,218 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 23:21:30,219 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 23:21:32,230 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 23:21:32,471 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 23:21:32,472 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 23:21:35,077 - INFO - معالجة الأمر: /start
2025-07-09 23:21:35,429 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:21:35,480 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 23:21:35,486 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 23:21:35,789 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 23:21:35,790 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 23:21:37,303 - INFO - معالجة callback: select_system_hs
2025-07-09 23:21:38,003 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:21:41,261 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:21:41,800 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 23:21:41,801 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 23:21:42,124 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 23:21:42,234 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:21:42,344 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 23:21:42,345 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 23:21:42,346 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 23:21:42,348 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 23:21:42,357 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:21:42,811 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 23:21:42,974 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 23:21:42,975 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 23:21:43,195 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 23:21:43,196 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 23:21:43,265 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 23:21:43,300 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 23:21:43,304 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:21:43,308 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:21:43,308 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:21:43,507 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:21:43,508 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:21:43,514 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:21:43,517 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:21:43,523 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:21:43,524 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:21:43,821 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:21:52,367 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 23:21:52,962 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 23:21:52,966 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 23:21:52,967 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:21:53,027 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:21:53,029 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 23:21:53,034 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:21:53,037 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:21:53,043 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:21:53,045 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 23:21:53,345 - INFO - ✅ تمت المزامنة الشاملة للقالب '10-marawan' بنجاح
2025-07-09 23:21:55,188 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 23:21:55,830 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 23:21:55,834 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 23:21:55,834 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:21:55,867 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:21:55,869 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 23:21:55,874 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:21:55,877 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:21:55,883 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:21:55,884 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 23:21:56,178 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 23:21:59,735 - INFO - معالجة callback: independent_template_hs_30
2025-07-09 23:22:00,336 - INFO - 🔄 بدء مزامنة القالب '30' مع البرنامج الرئيسي
2025-07-09 23:22:00,340 - INFO - 🔄 بدء المزامنة الشاملة للقالب '30'...
2025-07-09 23:22:00,340 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:22:00,401 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:22:00,403 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '30' بمزامنة شاملة
2025-07-09 23:22:00,408 - INFO - ✅ تم تحديد القالب '30' في قائمة القوالب
2025-07-09 23:22:00,411 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:22:00,417 - INFO - ✅ تم تحديد القالب '30' في قائمة القوالب
2025-07-09 23:22:00,418 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 30
2025-07-09 23:22:00,734 - INFO - ✅ تمت المزامنة الشاملة للقالب '30' بنجاح
2025-07-09 23:22:03,374 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:22:03,976 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:22:03,980 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:22:03,981 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:22:04,020 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:22:04,021 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:22:04,026 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:22:04,029 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:22:04,035 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:22:04,036 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:22:04,373 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:22:06,252 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 23:22:07,141 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 23:22:07,145 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 23:22:07,146 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:22:07,188 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:22:07,189 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 23:22:07,196 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:22:07,200 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:22:07,206 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 23:22:07,208 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 23:22:07,504 - INFO - ✅ تمت المزامنة الشاملة للقالب '10-marawan' بنجاح
2025-07-09 23:23:09,515 - INFO - معالجة callback: refresh_filtered_hs
2025-07-09 23:23:10,055 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:23:15,136 - INFO - معالجة callback: refresh_filtered_hs
2025-07-09 23:23:15,636 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 23:23:17,986 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 23:23:18,547 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 23:23:18,551 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-09 23:23:18,552 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:23:18,587 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:23:18,588 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-09 23:23:18,593 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:23:18,596 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:23:18,601 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 23:23:18,604 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-09 23:23:18,922 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-09 23:23:22,095 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 23:23:22,961 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 23:23:22,965 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 23:23:22,966 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:23:23,005 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:23:23,006 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 23:23:23,012 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:23:23,015 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:23:23,020 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 23:23:23,022 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 23:23:23,305 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 23:23:25,386 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 23:23:25,931 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 23:23:25,935 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 23:23:25,935 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 23:23:25,973 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 23:23:25,974 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 23:23:25,980 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:23:25,987 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 23:23:25,992 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 23:23:25,994 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 23:23:26,309 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 23:23:49,413 - INFO - بدء إغلاق التطبيق
2025-07-09 23:23:49,450 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_232349.db
2025-07-09 23:23:49,451 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_232349.db
2025-07-09 23:23:49,519 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 23:23:49,521 - INFO - تم إغلاق التطبيق بنجاح
2025-07-09 15:13:28,555 - INFO - تم بدء تشغيل التطبيق
2025-07-09 15:13:28,588 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 15:13:28,609 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 15:13:28,738 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 15:13:28,984 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_151328.db
2025-07-09 15:13:29,305 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 15:13:34,664 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 15:13:34,665 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 15:13:36,673 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 15:13:37,213 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 15:13:37,214 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 15:13:40,229 - INFO - 🔄 بدء مزامنة القوالب الشاملة مع بوت التلجرام...
2025-07-09 15:13:40,229 - ERROR - خطأ في مزامنة القوالب: name 'time' is not defined
2025-07-09 15:13:40,230 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:13:40,230 - INFO - ✅ تم تنفيذ المزامنة الشاملة باستخدام مدير المزامنة المحسن
2025-07-09 15:13:41,216 - ERROR - خطأ في بدء مراقبة الملفات: name 'WATCHDOG_AVAILABLE' is not defined
2025-07-09 15:13:54,087 - INFO - معالجة الأمر: /start
2025-07-09 15:13:54,122 - ERROR - خطأ في إرسال قائمة اختيار النظام: 'MikroTikCardGenerator' object has no attribute 'template_combo'
2025-07-09 15:14:03,405 - INFO - تم اختيار النظام: hotspot
2025-07-09 15:14:03,932 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 15:14:04,049 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 15:14:04,050 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 15:14:04,227 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 15:14:18,802 - INFO - معالجة الأمر: /start
2025-07-09 15:14:19,255 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:14:21,112 - INFO - معالجة callback: select_system_hs
2025-07-09 15:14:21,612 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:14:23,967 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 15:14:24,511 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 15:14:24,513 - INFO - 🔄 تطبيق القالب الكامل: 10
2025-07-09 15:14:24,572 - INFO - نتائج التحقق من القالب: 4/4 (100.0%)
2025-07-09 15:14:24,575 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:14:24,582 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 15:14:24,900 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-09 15:14:31,712 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 15:14:32,286 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 15:14:32,288 - INFO - 🔄 تطبيق القالب الكامل: 20-marawan
2025-07-09 15:14:32,303 - WARNING - عدم تطابق Host: متوقع '*******', موجود '***********'
2025-07-09 15:14:32,314 - INFO - نتائج التحقق من القالب: 3/4 (75.0%)
2025-07-09 15:14:32,314 - WARNING - ⚠️ تم تطبيق القالب '20-marawan' جزئياً
2025-07-09 15:14:32,316 - WARNING - ⚠️ فشل في تطبيق القالب '20-marawan' بالكامل
2025-07-09 15:14:49,380 - INFO - معالجة الأمر: /start
2025-07-09 15:14:49,632 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:14:51,091 - INFO - معالجة callback: select_system_hs
2025-07-09 15:14:51,592 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:14:53,174 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 15:14:53,804 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 15:14:53,806 - INFO - 🔄 تطبيق القالب الكامل: 20-marawan
2025-07-09 15:14:53,822 - WARNING - عدم تطابق Host: متوقع '*******', موجود '***********'
2025-07-09 15:14:53,834 - INFO - نتائج التحقق من القالب: 3/4 (75.0%)
2025-07-09 15:14:53,835 - WARNING - ⚠️ تم تطبيق القالب '20-marawan' جزئياً
2025-07-09 15:14:53,835 - WARNING - ⚠️ فشل في تطبيق القالب '20-marawan' بالكامل
2025-07-09 15:15:08,888 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 15:15:09,607 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 15:15:09,609 - INFO - 🔄 تطبيق القالب الكامل: 10-marawan
2025-07-09 15:15:09,641 - WARNING - عدم تطابق Host: متوقع '*******', موجود '***********'
2025-07-09 15:15:09,642 - INFO - نتائج التحقق من القالب: 3/4 (75.0%)
2025-07-09 15:15:09,642 - WARNING - ⚠️ تم تطبيق القالب '10-marawan' جزئياً
2025-07-09 15:15:09,643 - WARNING - ⚠️ فشل في تطبيق القالب '10-marawan' بالكامل
2025-07-09 15:15:44,027 - INFO - معالجة callback: refresh_templates
2025-07-09 15:15:44,851 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:15:46,332 - INFO - معالجة callback: select_system_hs
2025-07-09 15:15:48,445 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:15:49,589 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 15:15:50,412 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 15:15:50,414 - INFO - 🔄 تطبيق القالب الكامل: 10
2025-07-09 15:15:50,430 - INFO - نتائج التحقق من القالب: 4/4 (100.0%)
2025-07-09 15:15:50,450 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:15:50,452 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 15:15:50,785 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-09 15:16:02,444 - INFO - معالجة callback: refresh_templates
2025-07-09 15:16:02,942 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:16:04,339 - INFO - معالجة callback: select_system_hs
2025-07-09 15:16:05,125 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:16:08,361 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 15:16:09,109 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 15:16:09,111 - INFO - 🔄 تطبيق القالب الكامل: 20-marawan
2025-07-09 15:16:09,125 - WARNING - عدم تطابق Host: متوقع '*******', موجود '***********'
2025-07-09 15:16:09,135 - INFO - نتائج التحقق من القالب: 3/4 (75.0%)
2025-07-09 15:16:09,136 - WARNING - ⚠️ تم تطبيق القالب '20-marawan' جزئياً
2025-07-09 15:16:09,136 - WARNING - ⚠️ فشل في تطبيق القالب '20-marawan' بالكامل
2025-07-09 15:37:28,171 - INFO - تم بدء تشغيل التطبيق
2025-07-09 15:37:28,260 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 15:37:28,294 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 15:37:28,398 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 15:37:28,686 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_153728.db
2025-07-09 15:37:28,741 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 15:37:31,173 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 15:37:31,174 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 15:37:33,213 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 15:37:34,972 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 15:37:34,973 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 15:37:37,988 - INFO - 🔄 بدء مزامنة القوالب الشاملة مع بوت التلجرام...
2025-07-09 15:37:37,989 - ERROR - خطأ في مزامنة القوالب: name 'time' is not defined
2025-07-09 15:37:37,989 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:37:37,989 - INFO - ✅ تم تنفيذ المزامنة الشاملة باستخدام مدير المزامنة المحسن
2025-07-09 15:37:38,980 - ERROR - خطأ في بدء مراقبة الملفات: name 'WATCHDOG_AVAILABLE' is not defined
2025-07-09 15:37:50,467 - INFO - معالجة الأمر: /start
2025-07-09 15:37:50,488 - ERROR - خطأ في إرسال قائمة اختيار النظام: 'MikroTikCardGenerator' object has no attribute 'template_combo'
2025-07-09 15:38:05,596 - INFO - معالجة الأمر: /start
2025-07-09 15:38:05,597 - ERROR - خطأ في إرسال قائمة اختيار النظام: 'MikroTikCardGenerator' object has no attribute 'template_combo'
2025-07-09 15:38:23,396 - INFO - تم اختيار النظام: hotspot
2025-07-09 15:38:23,806 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 15:38:23,909 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 15:38:23,910 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 15:38:24,088 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-09 15:38:28,389 - INFO - معالجة الأمر: /start
2025-07-09 15:38:28,699 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:38:29,938 - INFO - معالجة callback: select_system_hs
2025-07-09 15:38:30,671 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:38:32,421 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 15:38:33,302 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 15:38:33,305 - INFO - 🔄 تطبيق القالب الكامل: 10
2025-07-09 15:38:33,305 - INFO - 🔄 تطبيق القالب مباشرة: 10
2025-07-09 15:38:33,306 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 15:38:33,319 - INFO - ✅ تم تطبيق جميع إعدادات القالب '10' بنجاح
2025-07-09 15:38:33,329 - INFO - نتائج التحقق من القالب '10':
2025-07-09 15:38:33,330 - INFO -   ✅ Host: ***********
2025-07-09 15:38:33,330 - INFO -   ✅ Username: saye
2025-07-09 15:38:33,331 - INFO -   ✅ Port: 8728
2025-07-09 15:38:33,332 - INFO -   ✅ SSL: معطل
2025-07-09 15:38:33,332 - INFO -   ✅ اختيار القالب: 10
2025-07-09 15:38:33,333 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 15:38:33,333 - INFO - ✅ تم التحقق من تطبيق القالب '10' بنجاح
2025-07-09 15:38:33,334 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:38:33,335 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 15:38:33,694 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 15:38:39,533 - INFO - معالجة callback: refresh_templates
2025-07-09 15:38:40,307 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:38:41,505 - INFO - معالجة callback: select_system_hs
2025-07-09 15:38:43,425 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:38:45,288 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 15:38:45,914 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 15:38:45,916 - INFO - 🔄 تطبيق القالب الكامل: 20-marawan
2025-07-09 15:38:45,916 - INFO - 🔄 تطبيق القالب مباشرة: 20-marawan
2025-07-09 15:38:45,917 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 15:38:45,938 - INFO - ✅ تم تطبيق جميع إعدادات القالب '20-marawan' بنجاح
2025-07-09 15:38:45,958 - INFO - نتائج التحقق من القالب '20-marawan':
2025-07-09 15:38:45,959 - INFO -   ✅ Host: *******
2025-07-09 15:38:45,959 - INFO -   ✅ Username: saye
2025-07-09 15:38:45,960 - INFO -   ✅ Port: 8728
2025-07-09 15:38:45,960 - INFO -   ✅ SSL: معطل
2025-07-09 15:38:45,961 - INFO -   ✅ اختيار القالب: 20-marawan
2025-07-09 15:38:45,962 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 15:38:45,962 - INFO - ✅ تم التحقق من تطبيق القالب '20-marawan' بنجاح
2025-07-09 15:38:45,963 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:38:45,965 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح مع جميع الإعدادات
2025-07-09 15:38:47,271 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح مع جميع الإعدادات
2025-07-09 15:38:57,490 - INFO - معالجة callback: refresh_templates
2025-07-09 15:38:57,974 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 15:38:59,524 - INFO - معالجة callback: select_system_hs
2025-07-09 15:39:00,066 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 15:39:01,866 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 15:39:02,376 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 15:39:02,378 - INFO - 🔄 تطبيق القالب الكامل: 10-marawan
2025-07-09 15:39:02,378 - INFO - 🔄 تطبيق القالب مباشرة: 10-marawan
2025-07-09 15:39:02,379 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 15:39:02,391 - INFO - ✅ تم تطبيق جميع إعدادات القالب '10-marawan' بنجاح
2025-07-09 15:39:02,406 - INFO - نتائج التحقق من القالب '10-marawan':
2025-07-09 15:39:02,407 - INFO -   ✅ Host: *******
2025-07-09 15:39:02,407 - INFO -   ✅ Username: saye
2025-07-09 15:39:02,408 - INFO -   ✅ Port: 8728
2025-07-09 15:39:02,408 - INFO -   ✅ SSL: معطل
2025-07-09 15:39:02,409 - INFO -   ✅ اختيار القالب: 10-marawan
2025-07-09 15:39:02,409 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 15:39:02,410 - INFO - ✅ تم التحقق من تطبيق القالب '10-marawan' بنجاح
2025-07-09 15:39:02,410 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:39:02,412 - INFO - ✅ تم تطبيق القالب '10-marawan' بنجاح مع جميع الإعدادات
2025-07-09 15:39:02,667 - INFO - ✅ تم تطبيق القالب '10-marawan' بنجاح مع جميع الإعدادات
2025-07-09 15:39:10,086 - INFO - معالجة callback: independent_template_hs_30
2025-07-09 15:39:10,657 - INFO - 🔄 بدء مزامنة القالب '30' مع البرنامج الرئيسي
2025-07-09 15:39:10,659 - INFO - 🔄 تطبيق القالب الكامل: 30
2025-07-09 15:39:10,660 - INFO - 🔄 تطبيق القالب مباشرة: 30
2025-07-09 15:39:10,661 - INFO - ✅ تم تحديد القالب '30' في قائمة القوالب
2025-07-09 15:39:10,676 - INFO - ✅ تم تطبيق جميع إعدادات القالب '30' بنجاح
2025-07-09 15:39:10,699 - INFO - نتائج التحقق من القالب '30':
2025-07-09 15:39:10,699 - INFO -   ✅ Host: ***********
2025-07-09 15:39:10,700 - INFO -   ✅ Username: saye
2025-07-09 15:39:10,700 - INFO -   ✅ Port: 8728
2025-07-09 15:39:10,701 - INFO -   ✅ SSL: معطل
2025-07-09 15:39:10,701 - INFO -   ✅ اختيار القالب: 30
2025-07-09 15:39:10,702 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 15:39:10,702 - INFO - ✅ تم التحقق من تطبيق القالب '30' بنجاح
2025-07-09 15:39:10,703 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 15:39:10,705 - INFO - ✅ تم تطبيق القالب '30' بنجاح مع جميع الإعدادات
2025-07-09 15:39:10,970 - INFO - ✅ تم تطبيق القالب '30' بنجاح مع جميع الإعدادات
2025-07-09 16:29:05,477 - INFO - معالجة الأمر: /start
2025-07-09 16:29:05,804 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:29:07,937 - INFO - معالجة callback: select_system_hs
2025-07-09 16:29:08,466 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:30:41,496 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 16:30:42,011 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 16:30:42,013 - INFO - 🔄 تطبيق القالب الكامل: 20-marawan
2025-07-09 16:30:42,014 - INFO - 🔄 تطبيق القالب مباشرة: 20-marawan
2025-07-09 16:30:42,017 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 16:30:42,056 - INFO - ✅ تم تطبيق جميع إعدادات القالب '20-marawan' بنجاح
2025-07-09 16:30:42,061 - INFO - نتائج التحقق من القالب '20-marawan':
2025-07-09 16:30:42,061 - INFO -   ✅ Host: *******
2025-07-09 16:30:42,062 - INFO -   ✅ Username: saye
2025-07-09 16:30:42,062 - INFO -   ✅ Port: 8728
2025-07-09 16:30:42,063 - INFO -   ✅ SSL: معطل
2025-07-09 16:30:42,063 - INFO -   ✅ اختيار القالب: 20-marawan
2025-07-09 16:30:42,064 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:30:42,064 - INFO - ✅ تم التحقق من تطبيق القالب '20-marawan' بنجاح
2025-07-09 16:30:42,065 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:30:42,067 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح مع جميع الإعدادات
2025-07-09 16:30:42,318 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح مع جميع الإعدادات
2025-07-09 16:30:48,244 - INFO - معالجة callback: refresh_templates
2025-07-09 16:30:48,710 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:30:50,911 - INFO - معالجة callback: select_system_hs
2025-07-09 16:30:51,374 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:30:52,513 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 16:30:53,043 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 16:30:53,045 - INFO - 🔄 تطبيق القالب الكامل: 10
2025-07-09 16:30:53,045 - INFO - 🔄 تطبيق القالب مباشرة: 10
2025-07-09 16:30:53,047 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 16:30:53,061 - INFO - ✅ تم تطبيق جميع إعدادات القالب '10' بنجاح
2025-07-09 16:30:53,082 - INFO - نتائج التحقق من القالب '10':
2025-07-09 16:30:53,083 - INFO -   ✅ Host: ***********
2025-07-09 16:30:53,083 - INFO -   ✅ Username: saye
2025-07-09 16:30:53,084 - INFO -   ✅ Port: 8728
2025-07-09 16:30:53,084 - INFO -   ✅ SSL: معطل
2025-07-09 16:30:53,085 - INFO -   ✅ اختيار القالب: 10
2025-07-09 16:30:53,087 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:30:53,088 - INFO - ✅ تم التحقق من تطبيق القالب '10' بنجاح
2025-07-09 16:30:53,089 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:30:53,091 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 16:30:53,345 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 16:30:58,010 - INFO - معالجة callback: refresh_templates
2025-07-09 16:30:58,494 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:31:00,316 - INFO - معالجة callback: select_system_hs
2025-07-09 16:31:00,807 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:31:02,505 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 16:31:02,958 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 16:31:02,960 - INFO - 🔄 تطبيق القالب الكامل: 20-marawan
2025-07-09 16:31:02,961 - INFO - 🔄 تطبيق القالب مباشرة: 20-marawan
2025-07-09 16:31:02,962 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 16:31:02,974 - INFO - ✅ تم تطبيق جميع إعدادات القالب '20-marawan' بنجاح
2025-07-09 16:31:02,995 - INFO - نتائج التحقق من القالب '20-marawan':
2025-07-09 16:31:02,995 - INFO -   ✅ Host: *******
2025-07-09 16:31:02,996 - INFO -   ✅ Username: saye
2025-07-09 16:31:02,996 - INFO -   ✅ Port: 8728
2025-07-09 16:31:02,997 - INFO -   ✅ SSL: معطل
2025-07-09 16:31:02,997 - INFO -   ✅ اختيار القالب: 20-marawan
2025-07-09 16:31:02,998 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:31:02,998 - INFO - ✅ تم التحقق من تطبيق القالب '20-marawan' بنجاح
2025-07-09 16:31:02,999 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:03,001 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح مع جميع الإعدادات
2025-07-09 16:31:03,252 - INFO - ✅ تم تطبيق القالب '20-marawan' بنجاح مع جميع الإعدادات
2025-07-09 16:31:05,738 - INFO - معالجة callback: refresh_templates
2025-07-09 16:31:06,280 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:31:07,491 - INFO - معالجة callback: select_system_hs
2025-07-09 16:31:08,050 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:31:14,148 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 16:31:14,640 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 16:31:14,642 - INFO - 🔄 تطبيق القالب الكامل: 10
2025-07-09 16:31:14,643 - INFO - 🔄 تطبيق القالب مباشرة: 10
2025-07-09 16:31:14,644 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 16:31:14,657 - INFO - ✅ تم تطبيق جميع إعدادات القالب '10' بنجاح
2025-07-09 16:31:14,679 - INFO - نتائج التحقق من القالب '10':
2025-07-09 16:31:14,680 - INFO -   ✅ Host: ***********
2025-07-09 16:31:14,681 - INFO -   ✅ Username: saye
2025-07-09 16:31:14,681 - INFO -   ✅ Port: 8728
2025-07-09 16:31:14,682 - INFO -   ✅ SSL: معطل
2025-07-09 16:31:14,682 - INFO -   ✅ اختيار القالب: 10
2025-07-09 16:31:14,683 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:31:14,683 - INFO - ✅ تم التحقق من تطبيق القالب '10' بنجاح
2025-07-09 16:31:14,684 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:14,686 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 16:31:14,936 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 16:31:19,187 - INFO - معالجة callback: sync_status_hs_10
2025-07-09 16:31:22,180 - INFO - معالجة callback: independent_template_hs_10
2025-07-09 16:31:22,653 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-09 16:31:22,655 - INFO - 🔄 تطبيق القالب الكامل: 10
2025-07-09 16:31:22,656 - INFO - 🔄 تطبيق القالب مباشرة: 10
2025-07-09 16:31:22,657 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-09 16:31:22,669 - INFO - ✅ تم تطبيق جميع إعدادات القالب '10' بنجاح
2025-07-09 16:31:22,681 - INFO - نتائج التحقق من القالب '10':
2025-07-09 16:31:22,681 - INFO -   ✅ Host: ***********
2025-07-09 16:31:22,682 - INFO -   ✅ Username: saye
2025-07-09 16:31:22,683 - INFO -   ✅ Port: 8728
2025-07-09 16:31:22,683 - INFO -   ✅ SSL: معطل
2025-07-09 16:31:22,684 - INFO -   ✅ اختيار القالب: 10
2025-07-09 16:31:22,684 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:31:22,685 - INFO - ✅ تم التحقق من تطبيق القالب '10' بنجاح
2025-07-09 16:31:22,685 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:22,687 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 16:31:22,946 - INFO - ✅ تم تطبيق القالب '10' بنجاح مع جميع الإعدادات
2025-07-09 16:31:25,291 - INFO - معالجة callback: refresh_templates
2025-07-09 16:31:25,745 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:31:26,917 - INFO - معالجة callback: select_system_hs
2025-07-09 16:31:27,371 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:31:29,135 - INFO - معالجة callback: independent_template_hs_30
2025-07-09 16:31:29,715 - INFO - 🔄 بدء مزامنة القالب '30' مع البرنامج الرئيسي
2025-07-09 16:31:29,717 - INFO - 🔄 تطبيق القالب الكامل: 30
2025-07-09 16:31:29,718 - INFO - 🔄 تطبيق القالب مباشرة: 30
2025-07-09 16:31:29,719 - INFO - ✅ تم تحديد القالب '30' في قائمة القوالب
2025-07-09 16:31:29,732 - INFO - ✅ تم تطبيق جميع إعدادات القالب '30' بنجاح
2025-07-09 16:31:29,744 - INFO - نتائج التحقق من القالب '30':
2025-07-09 16:31:29,745 - INFO -   ✅ Host: ***********
2025-07-09 16:31:29,746 - INFO -   ✅ Username: saye
2025-07-09 16:31:29,746 - INFO -   ✅ Port: 8728
2025-07-09 16:31:29,747 - INFO -   ✅ SSL: معطل
2025-07-09 16:31:29,747 - INFO -   ✅ اختيار القالب: 30
2025-07-09 16:31:29,748 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:31:29,748 - INFO - ✅ تم التحقق من تطبيق القالب '30' بنجاح
2025-07-09 16:31:29,749 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:29,751 - INFO - ✅ تم تطبيق القالب '30' بنجاح مع جميع الإعدادات
2025-07-09 16:31:30,005 - INFO - ✅ تم تطبيق القالب '30' بنجاح مع جميع الإعدادات
2025-07-09 16:31:36,663 - INFO - معالجة callback: refresh_templates
2025-07-09 16:31:37,190 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:31:38,371 - INFO - معالجة callback: select_system_hs
2025-07-09 16:31:38,871 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:31:42,375 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 16:31:42,938 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 16:31:42,940 - INFO - 🔄 تطبيق القالب الكامل: 10-marawan
2025-07-09 16:31:42,941 - INFO - 🔄 تطبيق القالب مباشرة: 10-marawan
2025-07-09 16:31:42,942 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 16:31:42,956 - INFO - ✅ تم تطبيق جميع إعدادات القالب '10-marawan' بنجاح
2025-07-09 16:31:42,977 - INFO - نتائج التحقق من القالب '10-marawan':
2025-07-09 16:31:42,977 - INFO -   ✅ Host: *******
2025-07-09 16:31:42,978 - INFO -   ✅ Username: saye
2025-07-09 16:31:42,979 - INFO -   ✅ Port: 8728
2025-07-09 16:31:42,979 - INFO -   ✅ SSL: معطل
2025-07-09 16:31:42,980 - INFO -   ✅ اختيار القالب: 10-marawan
2025-07-09 16:31:42,980 - INFO - إجمالي النجاح: 5/5 (100.0%)
2025-07-09 16:31:42,981 - INFO - ✅ تم التحقق من تطبيق القالب '10-marawan' بنجاح
2025-07-09 16:31:42,981 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:42,985 - INFO - ✅ تم تطبيق القالب '10-marawan' بنجاح مع جميع الإعدادات
2025-07-09 16:31:43,300 - INFO - ✅ تم تطبيق القالب '10-marawan' بنجاح مع جميع الإعدادات
2025-07-09 16:31:51,966 - INFO - معالجة الأمر: /cards
2025-07-09 16:31:51,968 - INFO - طلب كروت: /cards
2025-07-09 16:31:52,314 - INFO - تم توليد 8 حساب
2025-07-09 16:31:52,321 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 9 (من generate_all)
2025-07-09 16:31:52,637 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:55,396 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 8
2025-07-09 16:31:55,493 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(افتراضي)_ب30-8 كارت-09-07-2025-16-31-55-hotspot.pdf
2025-07-09 16:31:55,715 - ERROR - خطأ في مزامنة معلومات الهوست: name 'time' is not defined
2025-07-09 16:31:56,211 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-09 16:31:56,463 - INFO - محاولة الاتصال بـ *******:8728 (عادي)
2025-07-09 16:31:56,686 - INFO - معالجة الأمر: /restart
2025-07-09 16:31:56,687 - INFO - طلب إعادة تشغيل البرنامج من التلجرام
2025-07-09 16:31:56,756 - INFO - نجح الاتصال مع *******
2025-07-09 16:31:56,783 - INFO - بدء إرسال 8 كرت هوت سبوت مباشرة عبر API
2025-07-09 16:31:56,914 - INFO - تم إرسال المستخدم 1085156929 بنجاح (1/8)
2025-07-09 16:31:57,147 - INFO - تم إرسال المستخدم 1010742391 بنجاح (2/8)
2025-07-09 16:31:57,395 - INFO - تم إرسال المستخدم 1024490829 بنجاح (3/8)
2025-07-09 16:31:57,715 - INFO - تم إرسال المستخدم 1068064047 بنجاح (4/8)
2025-07-09 16:31:57,954 - INFO - تم إرسال المستخدم 1012154518 بنجاح (5/8)
2025-07-09 16:31:58,184 - INFO - تم إرسال المستخدم 1066799114 بنجاح (6/8)
2025-07-09 16:31:58,413 - INFO - تم إرسال المستخدم 1037626269 بنجاح (7/8)
2025-07-09 16:31:58,646 - INFO - تم إرسال المستخدم 1085702669 بنجاح (8/8)
2025-07-09 16:31:58,749 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-09 16:31:58,750 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-09 16:32:02,383 - INFO - معالجة callback: confirm_restart
2025-07-09 16:32:02,594 - INFO - تم تأكيد إعادة تشغيل البرنامج من التلجرام
2025-07-09 16:32:02,596 - INFO - بدء عملية إعادة تشغيل البرنامج من التلجرام
2025-07-09 16:32:08,335 - INFO - تم تسجيل العملية: إعادة تشغيل من التلجرام - بدء عملية إعادة التشغيل
2025-07-09 16:32:19,162 - INFO - تم بدء تشغيل التطبيق
2025-07-09 16:32:19,211 - INFO - تم إنشاء المجلدات الأساسية
2025-07-09 16:32:19,213 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-09 16:32:19,213 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-09 16:32:19,316 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_163219.db
2025-07-09 16:32:19,319 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-09 16:32:20,520 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-09 16:32:20,537 - INFO - تم إعداد التطبيق بنجاح
2025-07-09 16:32:22,540 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-09 16:32:22,770 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-09 16:32:22,771 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-09 16:32:25,783 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-09 16:32:25,974 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-09 16:32:26,260 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:32:26,351 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-09 16:32:27,835 - INFO - معالجة callback: select_system_hs
2025-07-09 16:32:28,442 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:32:31,614 - INFO - معالجة callback: independent_template_hs_20-marawan
2025-07-09 16:32:32,212 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-09 16:32:32,238 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-09 16:32:32,481 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-09 16:32:33,098 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-09 16:32:33,199 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-09 16:32:33,398 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-09 16:32:33,399 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-09 16:32:33,400 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-09 16:32:33,404 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-09 16:32:35,162 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-09 16:32:35,579 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-09 16:32:35,740 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-09 16:32:35,860 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-09 16:32:36,108 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-09 16:32:36,115 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-09 16:32:36,144 - INFO - 🔄 بدء مزامنة القالب '20-marawan' مع البرنامج الرئيسي
2025-07-09 16:32:36,145 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20-marawan'...
2025-07-09 16:32:36,146 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 16:32:36,693 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-09 16:32:37,374 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-09 16:32:37,671 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-09 16:32:37,918 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-09 16:32:38,128 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '20-marawan' بمزامنة شاملة
2025-07-09 16:32:38,132 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 16:32:38,134 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 16:32:38,138 - INFO - ✅ تم تحديد القالب '20-marawan' في قائمة القوالب
2025-07-09 16:32:38,139 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20-marawan
2025-07-09 16:32:38,386 - INFO - ✅ تمت المزامنة الشاملة للقالب '20-marawan' بنجاح
2025-07-09 16:35:37,168 - INFO - معالجة الأمر: /start
2025-07-09 16:35:37,475 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:35:38,759 - INFO - معالجة callback: select_system_hs
2025-07-09 16:35:39,212 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:35:41,449 - INFO - معالجة callback: independent_template_hs_20
2025-07-09 16:35:41,971 - INFO - 🔄 بدء مزامنة القالب '20' مع البرنامج الرئيسي
2025-07-09 16:35:41,973 - INFO - 🔄 بدء المزامنة الشاملة للقالب '20'...
2025-07-09 16:35:41,974 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 16:35:41,977 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-09 16:35:41,985 - INFO - ✅ تم تحديث Host: ******* → ***********
2025-07-09 16:35:41,987 - INFO - 🔄 تم جدولة تحديث معلومات الخادم في البوت
2025-07-09 16:35:41,988 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 1 إعدادات
2025-07-09 16:35:42,009 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 73 إعدادات
2025-07-09 16:35:42,009 - INFO - ✅ تم تطبيق 73 إعدادات من القالب '20' بمزامنة شاملة
2025-07-09 16:35:42,013 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 16:35:42,015 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 16:35:42,023 - INFO - ✅ تم تحديد القالب '20' في قائمة القوالب
2025-07-09 16:35:42,025 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 20
2025-07-09 16:35:42,303 - INFO - ✅ تمت المزامنة الشاملة للقالب '20' بنجاح
2025-07-09 16:35:54,472 - INFO - معالجة callback: refresh_templates
2025-07-09 16:35:54,954 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-09 16:35:56,119 - INFO - معالجة callback: select_system_hs
2025-07-09 16:35:56,701 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-09 16:35:58,626 - INFO - معالجة callback: independent_template_hs_10-marawan
2025-07-09 16:35:59,089 - INFO - 🔄 بدء مزامنة القالب '10-marawan' مع البرنامج الرئيسي
2025-07-09 16:35:59,091 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10-marawan'...
2025-07-09 16:35:59,092 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-09 16:35:59,095 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-09 16:35:59,105 - INFO - ✅ تم تحديث Host: *********** → *******
2025-07-09 16:35:59,106 - INFO - 🔄 تم جدولة تحديث معلومات الخادم في البوت
2025-07-09 16:35:59,106 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 1 إعدادات
2025-07-09 16:35:59,125 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 73 إعدادات
2025-07-09 16:35:59,126 - INFO - ✅ تم تطبيق 73 إعدادات من القالب '10-marawan' بمزامنة شاملة
2025-07-09 16:35:59,128 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 16:35:59,137 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-09 16:35:59,139 - INFO - ✅ تم تحديد القالب '10-marawan' في قائمة القوالب
2025-07-09 16:35:59,141 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10-marawan
2025-07-09 16:35:59,446 - INFO - ✅ تمت المزامنة الشاملة للقالب '10-marawan' بنجاح
2025-07-09 16:36:09,369 - INFO - بدء إغلاق التطبيق
2025-07-09 16:36:09,389 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250709_163609.db
2025-07-09 16:36:09,392 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250709_163609.db
2025-07-09 16:36:09,440 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-09 16:36:09,443 - INFO - تم إغلاق التطبيق بنجاح
