#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار زر حذف الكروت الناجحة للكروت المتعددة من خيار "كرت واحد"
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الزر يظهر عند إنشاء عدة كروت باستخدام خيار "كرت واحد" من البوت
"""

import re

def test_multiple_cards_delete_button_conditions():
    """اختبار شروط إظهار الزر للكروت المتعددة"""
    print("🔍 اختبار شروط إظهار الزر للكروت المتعددة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_details_to_telegram
    func_match = re.search(r'def send_cards_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود الشروط المطلوبة
    required_conditions = [
        'تشخيص شروط زر حذف الكروت الناجحة للكروت المتعددة',
        'show_delete_successful_button.*=.*success_count > 0',
        'getattr.*system_type.*hotspot',
        'hasattr.*single_card_successful_cards',
        'bool.*single_card_successful_cards'
    ]
    
    missing_conditions = []
    for condition in required_conditions:
        if re.search(condition, func_code, re.IGNORECASE):
            print(f"✅ شرط موجود: {condition}")
        else:
            print(f"❌ شرط مفقود: {condition}")
            missing_conditions.append(condition)
    
    return len(missing_conditions) == 0

def test_multiple_cards_delete_button_ui():
    """اختبار واجهة الزر للكروت المتعددة"""
    print("\n🔍 اختبار واجهة الزر للكروت المتعددة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_details_to_telegram
    func_match = re.search(r'def send_cards_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود عناصر الواجهة
    ui_elements = [
        'خيارات إدارة الكروت',
        'حذف الكروت المرسلة بنجاح:',
        'تحديد النص المناسب حسب حالة العملية',
        'if failed_count > 0:.*حالة الفشل الجزئي',
        'else:.*حالة النجاح الكامل',
        'نظراً لوجود.*كرت فاشل.*يمكنك اختيار حذف',
        'يمكنك اختيار حذف.*إذا لم تعد بحاجة إليها',
        'حذف الكروت المرسلة بنجاح من هذه العملية',
        'single_card_delete_successful_',
        'send_telegram_message_with_keyboard'
    ]
    
    missing_ui = []
    for element in ui_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر واجهة موجود: {element}")
        else:
            print(f"❌ عنصر واجهة مفقود: {element}")
            missing_ui.append(element)
    
    return len(missing_ui) == 0

def test_single_vs_multiple_cards_logic():
    """اختبار منطق التمييز بين الكرت الواحد والكروت المتعددة"""
    print("\n🔍 اختبار منطق التمييز بين الكرت الواحد والكروت المتعددة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة process_single_card_creation
    func_match = re.search(r'def process_single_card_creation.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة process_single_card_creation")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من منطق التمييز
    logic_elements = [
        'if card_count == 1:.*send_single_card_details_to_telegram',
        'else:.*send_cards_details_to_telegram',
        'send_single_card_to_mikrotik_silent'
    ]
    
    missing_logic = []
    for element in logic_elements:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ منطق موجود: {element}")
        else:
            print(f"❌ منطق مفقود: {element}")
            missing_logic.append(element)
    
    return len(missing_logic) == 0

def test_data_consistency():
    """اختبار تناسق البيانات بين الدالتين"""
    print("\n🔍 اختبار تناسق البيانات بين الدالتين...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من أن كلا الدالتين تستخدم نفس البيانات
    consistency_checks = [
        # كلا الدالتين تستخدم single_card_successful_cards
        ('send_single_card_details_to_telegram.*single_card_successful_cards', 'دالة الكرت الواحد تستخدم single_card_successful_cards'),
        ('send_cards_details_to_telegram.*single_card_successful_cards', 'دالة الكروت المتعددة تستخدم single_card_successful_cards'),
        
        # كلا الدالتين تستخدم نفس callback_data
        ('send_single_card_details_to_telegram.*single_card_delete_successful_', 'دالة الكرت الواحد تستخدم single_card_delete_successful_'),
        ('send_cards_details_to_telegram.*single_card_delete_successful_', 'دالة الكروت المتعددة تستخدم single_card_delete_successful_'),
        
        # كلا الدالتين تستخدم نفس الشروط
        ('send_single_card_details_to_telegram.*success_count > 0.*system_type.*hotspot', 'دالة الكرت الواحد تستخدم الشروط الصحيحة'),
        ('send_cards_details_to_telegram.*success_count > 0.*system_type.*hotspot', 'دالة الكروت المتعددة تستخدم الشروط الصحيحة')
    ]
    
    all_consistent = True
    for pattern, description in consistency_checks:
        if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
            print(f"✅ {description}")
        else:
            print(f"❌ {description}")
            all_consistent = False
    
    return all_consistent

def simulate_scenarios():
    """محاكاة السيناريوهات المختلفة"""
    print("\n🧪 محاكاة السيناريوهات المختلفة...")
    
    scenarios = [
        {
            "name": "كرت واحد من البوت - نجاح كامل",
            "card_count": 1,
            "success_count": 1,
            "failed_count": 0,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "function": "send_single_card_details_to_telegram",
            "description": "يجب أن يظهر الزر"
        },
        {
            "name": "3 كروت من البوت - نجاح كامل",
            "card_count": 3,
            "success_count": 3,
            "failed_count": 0,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "function": "send_cards_details_to_telegram",
            "description": "يجب أن يظهر الزر الآن"
        },
        {
            "name": "5 كروت من البوت - فشل جزئي",
            "card_count": 5,
            "success_count": 3,
            "failed_count": 2,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "function": "send_cards_details_to_telegram",
            "description": "يجب أن يظهر الزر"
        },
        {
            "name": "10 كروت من البوت - فشل كامل",
            "card_count": 10,
            "success_count": 0,
            "failed_count": 10,
            "system_type": "hotspot",
            "has_data": False,
            "expected": False,
            "function": "send_cards_details_to_telegram",
            "description": "يجب ألا يظهر الزر"
        }
    ]
    
    all_correct = True
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        
        # تطبيق الشروط
        condition1 = scenario['success_count'] > 0
        condition2 = scenario['system_type'] == 'hotspot'
        condition3 = scenario['has_data']
        
        result = condition1 and condition2 and condition3
        
        print(f"   - عدد الكروت: {scenario['card_count']}")
        print(f"   - الكروت الناجحة: {scenario['success_count']}")
        print(f"   - الكروت الفاشلة: {scenario['failed_count']}")
        print(f"   - الدالة المستخدمة: {scenario['function']}")
        print(f"   - النتيجة: {result}")
        print(f"   - المتوقع: {scenario['expected']}")
        
        if result == scenario['expected']:
            print(f"   ✅ صحيح - {scenario['description']}")
        else:
            print(f"   ❌ خطأ - {scenario['description']}")
            all_correct = False
    
    return all_correct

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار زر حذف الكروت الناجحة للكروت المتعددة من خيار 'كرت واحد'")
    print("="*85)
    
    tests = [
        ("شروط إظهار الزر للكروت المتعددة", test_multiple_cards_delete_button_conditions),
        ("واجهة الزر للكروت المتعددة", test_multiple_cards_delete_button_ui),
        ("منطق التمييز بين الكرت الواحد والكروت المتعددة", test_single_vs_multiple_cards_logic),
        ("تناسق البيانات بين الدالتين", test_data_consistency),
        ("محاكاة السيناريوهات", simulate_scenarios)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 70)
    
    print("\n" + "="*85)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إضافة زر حذف الكروت الناجحة للكروت المتعددة بنجاح!")
        print("💡 الزر الآن يظهر عند استخدام خيار 'كرت واحد' من البوت بغض النظر عن عدد الكروت")
        
        print("\n🎯 الحالات المدعومة:")
        print("✅ كرت واحد من البوت → يظهر الزر (send_single_card_details_to_telegram)")
        print("✅ عدة كروت من البوت → يظهر الزر (send_cards_details_to_telegram)")
        print("✅ النجاح الكامل → يظهر الزر مع نص مناسب")
        print("✅ الفشل الجزئي → يظهر الزر مع نص مناسب")
        print("❌ الفشل الكامل → لا يظهر الزر")
        
        print("\n🔧 التحسينات المُطبقة:")
        print("✅ إضافة شروط إظهار الزر إلى دالة send_cards_details_to_telegram")
        print("✅ إضافة النص التوضيحي المناسب لكل حالة")
        print("✅ إضافة الزر مع callback_data صحيح")
        print("✅ استخدام نفس البيانات (single_card_successful_cards)")
        print("✅ استخدام نفس معالجات callback")
        
        print("\n🎮 كيفية الاختبار:")
        print("1. استخدم خيار 'كرت واحد' من البوت")
        print("2. اختر قالب وعدد كروت (1 أو أكثر)")
        print("3. تأكد من حدوث نجاح كامل أو فشل جزئي")
        print("4. ابحث عن الزر في رسالة التفاصيل")
        print("5. جرب التأكيد والإلغاء")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة إضافية.")
    
    return failed == 0

if __name__ == "__main__":
    main()
