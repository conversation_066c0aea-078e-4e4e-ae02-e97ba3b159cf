2025-07-11 00:00:02,932 - INFO - تم بدء تشغيل التطبيق
2025-07-11 00:00:02,948 - INFO - تم إنشاء المجلدات الأساسية
2025-07-11 00:00:02,962 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-11 00:00:02,963 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-11 00:00:03,204 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250711_000003.db
2025-07-11 00:00:03,220 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-11 00:00:03,639 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-11 00:00:03,640 - INFO - ت<PERSON> <PERSON>عداد التطبيق بنجاح
2025-07-11 00:00:05,647 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-11 00:00:06,198 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-11 00:00:06,199 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-11 00:00:09,206 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-11 00:00:09,259 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-11 00:00:09,611 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-11 00:00:09,612 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-11 00:01:11,699 - INFO - معالجة الأمر: /sethost
2025-07-11 00:01:11,700 - INFO - طلب تعديل Host من التلجرام: /sethost
2025-07-11 00:01:18,171 - INFO - معالجة الأمر: /sethost
2025-07-11 00:01:18,172 - INFO - طلب تعديل Host من التلجرام: /sethost
2025-07-11 00:01:30,824 - INFO - معالجة الأمر: /sethost *************
2025-07-11 00:01:30,825 - INFO - طلب تعديل Host من التلجرام: /sethost *************
2025-07-11 00:01:30,827 - ERROR - خطأ في تعديل Host من التلجرام: 'MikroTikCardGenerator' object has no attribute 'api_ip_entry'
2025-07-11 00:01:38,738 - INFO - معالجة callback: select_system_hs
2025-07-11 00:01:39,329 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-11 00:01:41,454 - INFO - معالجة callback: independent_template_hs_10
2025-07-11 00:01:42,132 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-11 00:01:42,133 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-11 00:01:42,436 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-11 00:01:42,647 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-11 00:01:42,755 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-11 00:01:42,756 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-11 00:01:42,757 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-11 00:01:42,759 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-11 00:01:42,767 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-11 00:01:43,222 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-11 00:01:43,389 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-11 00:01:43,390 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-11 00:01:43,650 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-11 00:01:43,651 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-11 00:01:43,679 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-11 00:01:43,682 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-11 00:01:43,686 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-11 00:01:43,687 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-11 00:01:43,717 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-11 00:01:43,958 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-11 00:01:43,961 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-11 00:01:43,976 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-11 00:01:43,978 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-11 00:01:43,983 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-11 00:01:43,986 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-11 00:01:43,993 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-11 00:01:43,994 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-11 00:01:44,298 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-11 00:01:50,094 - INFO - معالجة الأمر: /sethost
2025-07-11 00:01:50,096 - INFO - طلب تعديل Host من التلجرام: /sethost
2025-07-11 00:01:54,127 - INFO - معالجة الأمر: /sethost
2025-07-11 00:01:54,128 - INFO - طلب تعديل Host من التلجرام: /sethost
2025-07-11 00:02:09,124 - INFO - معالجة الأمر: /cards
2025-07-11 00:02:09,125 - INFO - طلب كروت: /cards
2025-07-11 00:02:09,444 - INFO - تم توليد 1 حساب
2025-07-11 00:02:09,445 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 202 (من generate_all)
2025-07-11 00:02:12,460 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 1
2025-07-11 00:02:13,593 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(افتراضي)_ب20-1 كارت-11-07-2025-00-02-12-hotspot.pdf
2025-07-11 00:02:14,215 - INFO - تم إرسال 10 كرت عبر التلجرام
2025-07-11 00:02:14,495 - INFO - محاولة الاتصال بـ ***********:8729 (SSL)
2025-07-11 00:02:14,699 - INFO - نجح الاتصال مع ***********
2025-07-11 00:02:14,701 - INFO - بدء إرسال 1 كرت هوت سبوت مباشرة عبر API
2025-07-11 00:02:14,749 - INFO - تم إرسال المستخدم 02751270764 بنجاح (1/1)
2025-07-11 00:02:14,845 - ERROR - خطأ في عملية الإرسال المباشر: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-11 00:02:14,846 - ERROR - خطأ في إرسال الكروت للسيرفر: فشل في إرسال الكروت مباشرة إلى السيرفر
2025-07-11 00:02:14,940 - INFO - معالجة الأمر: /card
2025-07-11 00:02:14,948 - INFO - طلب إنشاء كروت مع عدد مخصص من التلجرام: /card
2025-07-11 00:02:29,399 - INFO - معالجة الأمر: card 1
2025-07-11 00:02:29,693 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-11 00:02:30,325 - INFO - بدء إغلاق التطبيق
2025-07-11 00:02:30,371 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250711_000230.db
2025-07-11 00:02:30,372 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250711_000230.db
2025-07-11 00:02:30,373 - INFO - تم قطع الاتصال مع MikroTik
2025-07-11 00:02:30,420 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-11 00:02:30,422 - INFO - تم إغلاق التطبيق بنجاح
2025-07-11 23:43:46,021 - INFO - تم بدء تشغيل التطبيق
2025-07-11 23:43:46,042 - INFO - تم إنشاء المجلدات الأساسية
2025-07-11 23:43:46,109 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-11 23:43:46,110 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-11 23:43:46,344 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250711_234346.db
2025-07-11 23:43:46,360 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-11 23:43:46,778 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-11 23:43:46,779 - INFO - تم إعداد التطبيق بنجاح
2025-07-11 23:43:47,370 - INFO - تم اختيار النظام: hotspot
2025-07-11 23:43:47,732 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-11 23:43:47,900 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-11 23:43:47,901 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-11 23:43:48,196 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-11 23:43:48,781 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-11 23:43:49,186 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-11 23:43:49,225 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-11 23:43:52,210 - INFO - بدء إغلاق التطبيق
2025-07-11 23:43:52,247 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250711_234352.db
2025-07-11 23:43:52,248 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250711_234352.db
2025-07-11 23:43:52,313 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-11 23:43:52,314 - INFO - تم إغلاق التطبيق بنجاح
