#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة حذف الكروت المرسلة بنجاح في نظام البرق للهوت سبوت
Test Lightning Delete Successful Cards Feature for HotSpot System

هذا الاختبار يتحقق من:
1. إظهار زر الحذف فقط عند وجود كروت فاشلة وناجحة
2. عمل الميزة فقط مع نظام الهوت سبوت والبرق
3. حفظ قائمة الكروت الناجحة للحذف
4. إرسال رسالة تأكيد قبل الحذف
5. تنفيذ الحذف للكروت المرسلة بنجاح من MikroTik
6. إرسال إحصائيات الحذف النهائية
"""

import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

class TestLightningDeleteSuccessfulFeature(unittest.TestCase):
    """اختبار ميزة حذف الكروت المرسلة بنجاح في البرق"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء mock للتطبيق الرئيسي
        self.mock_app = Mock()
        
        # إعداد logger
        self.mock_app.logger = Mock()
        
        # إعداد نوع النظام (هوت سبوت فقط)
        self.mock_app.system_type = 'hotspot'
        
        # إعداد بيانات التلجرام
        self.mock_app.telegram_bot_token = "test_bot_token"
        self.mock_app.telegram_chat_id = "123456789"
        
        # إعداد إحصائيات البرق مع كروت فاشلة وناجحة
        self.mock_app.last_send_stats = {
            'success': 8,
            'failed': 2,
            'duplicates': 1,
            'total': 10,
            'successful_usernames': [
                'lightning_user_001',
                'lightning_user_002', 
                'lightning_user_003',
                'lightning_user_004',
                'lightning_user_005',
                'lightning_user_006',
                'lightning_user_007',
                'lightning_user_008'
            ]
        }
        
        # إعداد قائمة الكروت الناجحة للحذف
        self.mock_app.lightning_successful_cards = [
            'lightning_user_001',
            'lightning_user_002', 
            'lightning_user_003',
            'lightning_user_004',
            'lightning_user_005',
            'lightning_user_006',
            'lightning_user_007',
            'lightning_user_008'
        ]
        
        # إعداد معلومات الكروت المرسلة بنجاح
        self.mock_app.lightning_successful_cards_info = {
            'timestamp': datetime.now().isoformat(),
            'total_successful': 8,
            'total_failed': 2,
            'total_cards': 10,
            'system_type': 'hotspot',
            'operation_type': 'lightning'
        }
        
        # إضافة الدوال المطلوبة
        self.mock_app.send_telegram_message_direct = self.mock_send_telegram_message_direct
        self.mock_app.connect_api = self.mock_connect_api

    def mock_send_telegram_message_direct(self, bot_token, chat_id, message, max_retries=3):
        """Mock لدالة إرسال رسائل التلجرام"""
        self.mock_app.logger.info(f"✅ تم إرسال رسالة التلجرام بنجاح")
        return True

    def mock_connect_api(self):
        """Mock للاتصال بـ MikroTik"""
        mock_api = Mock()
        mock_resource = Mock()
        
        # محاكاة البحث عن المستخدمين
        mock_resource.get.return_value = [{'id': 'test_id'}]
        mock_resource.remove.return_value = True
        mock_api.get_resource.return_value = mock_resource
        
        return mock_api

    def test_delete_successful_button_conditions(self):
        """اختبار شروط إظهار زر حذف الكروت المرسلة بنجاح"""
        
        # الحالة 1: يجب إظهار الزر (كروت ناجحة وفاشلة)
        failed_count = 2
        success_count = 8
        show_delete_successful_button = (
            failed_count > 0 and
            success_count > 0 and
            hasattr(self.mock_app, 'lightning_successful_cards') and
            bool(self.mock_app.lightning_successful_cards)
        )
        
        self.assertTrue(show_delete_successful_button)
        print("✅ اختبار شروط إظهار زر حذف الكروت المرسلة بنجاح (مع كروت فاشلة وناجحة) نجح")
        
        # الحالة 2: لا يجب إظهار الزر (لا توجد كروت فاشلة)
        failed_count = 0
        success_count = 10
        show_delete_successful_button = (
            failed_count > 0 and
            success_count > 0 and
            hasattr(self.mock_app, 'lightning_successful_cards') and
            bool(self.mock_app.lightning_successful_cards)
        )
        
        self.assertFalse(show_delete_successful_button)
        print("✅ اختبار شروط عدم إظهار زر حذف الكروت المرسلة بنجاح (بدون كروت فاشلة) نجح")

    def test_hotspot_system_only(self):
        """اختبار أن الميزة تعمل فقط مع نظام الهوت سبوت"""
        
        # اختبار مع نظام الهوت سبوت
        self.assertEqual(self.mock_app.system_type, 'hotspot')
        self.assertEqual(self.mock_app.lightning_successful_cards_info['system_type'], 'hotspot')
        
        print("✅ اختبار عمل الميزة مع نظام الهوت سبوت نجح")
        
        # اختبار مع نظام اليوزر منجر (يجب أن يفشل)
        self.mock_app.lightning_successful_cards_info['system_type'] = 'user_manager'
        self.assertNotEqual(self.mock_app.lightning_successful_cards_info['system_type'], 'hotspot')
        
        print("✅ اختبار منع الميزة مع نظام اليوزر منجر نجح")

    def test_lightning_operation_only(self):
        """اختبار أن الميزة تعمل فقط مع البرق"""
        
        # التحقق من أن نوع العملية هو البرق
        self.assertEqual(self.mock_app.lightning_successful_cards_info['operation_type'], 'lightning')
        
        print("✅ اختبار أن الميزة تعمل فقط مع البرق نجح")

    def test_successful_cards_list(self):
        """اختبار قائمة الكروت الناجحة"""
        
        # التحقق من وجود قائمة الكروت الناجحة
        self.assertTrue(hasattr(self.mock_app, 'lightning_successful_cards'))
        self.assertEqual(len(self.mock_app.lightning_successful_cards), 8)
        self.assertIn('lightning_user_001', self.mock_app.lightning_successful_cards)
        
        print("✅ اختبار قائمة الكروت الناجحة نجح")

    def test_successful_cards_info_structure(self):
        """اختبار هيكل معلومات الكروت المرسلة بنجاح"""
        
        # التحقق من وجود جميع المعلومات المطلوبة
        self.assertTrue(hasattr(self.mock_app, 'lightning_successful_cards_info'))
        
        required_keys = ['timestamp', 'total_successful', 'total_failed', 'total_cards', 'system_type', 'operation_type']
        for key in required_keys:
            self.assertIn(key, self.mock_app.lightning_successful_cards_info)
        
        # التحقق من القيم
        self.assertEqual(self.mock_app.lightning_successful_cards_info['total_successful'], 8)
        self.assertEqual(self.mock_app.lightning_successful_cards_info['total_failed'], 2)
        self.assertEqual(self.mock_app.lightning_successful_cards_info['total_cards'], 10)
        self.assertEqual(self.mock_app.lightning_successful_cards_info['system_type'], 'hotspot')
        self.assertEqual(self.mock_app.lightning_successful_cards_info['operation_type'], 'lightning')
        
        print("✅ اختبار هيكل معلومات الكروت المرسلة بنجاح نجح")

    def test_delete_successful_button_text(self):
        """اختبار نص زر حذف الكروت المرسلة بنجاح"""
        
        success_count = 8
        expected_text = f"🗑️ حذف الكروت المرسلة بنجاح من هذه العملية ({success_count})"
        
        # محاكاة إنشاء الزر
        button_text = f"🗑️ حذف الكروت المرسلة بنجاح من هذه العملية ({success_count})"
        
        self.assertEqual(button_text, expected_text)
        print("✅ اختبار نص زر حذف الكروت المرسلة بنجاح نجح")

    def test_callback_data_format(self):
        """اختبار تنسيق callback_data"""
        
        success_count = 8
        expected_callback = f"lightning_delete_successful_{success_count}"
        
        # محاكاة إنشاء callback_data
        callback_data = f"lightning_delete_successful_{success_count}"
        
        self.assertEqual(callback_data, expected_callback)
        print("✅ اختبار تنسيق callback_data نجح")

    def test_confirmation_callback_format(self):
        """اختبار تنسيق callback للتأكيد"""
        
        cards_count = 8
        expected_confirm_callback = f"lightning_delete_successful_confirm_{cards_count}"
        expected_cancel_callback = "lightning_delete_successful_cancel"
        
        # محاكاة إنشاء callback_data للتأكيد
        confirm_callback = f"lightning_delete_successful_confirm_{cards_count}"
        cancel_callback = "lightning_delete_successful_cancel"
        
        self.assertEqual(confirm_callback, expected_confirm_callback)
        self.assertEqual(cancel_callback, expected_cancel_callback)
        print("✅ اختبار تنسيق callback للتأكيد نجح")

    def test_mikrotik_connection(self):
        """اختبار الاتصال بـ MikroTik"""
        
        # اختبار الاتصال
        api = self.mock_app.connect_api()
        self.assertIsNotNone(api)
        
        # اختبار البحث عن مستخدم
        resource = api.get_resource('/ip/hotspot/user')
        users = resource.get(name='test_user')
        self.assertIsNotNone(users)
        
        print("✅ اختبار الاتصال بـ MikroTik نجح")

    def test_statistics_calculation(self):
        """اختبار حساب الإحصائيات"""
        
        # إحصائيات العملية الأصلية
        total_cards = 10
        success_count = 8
        failed_count = 2
        
        # حساب معدل النجاح الأصلي
        original_success_rate = (success_count / total_cards) * 100
        self.assertEqual(original_success_rate, 80.0)
        
        # إحصائيات الحذف (حذف الكروت الناجحة)
        deleted_count = 8  # جميع الكروت الناجحة
        delete_success_rate = (deleted_count / success_count) * 100
        self.assertEqual(delete_success_rate, 100.0)
        
        print("✅ اختبار حساب الإحصائيات نجح")

    def test_data_cleanup(self):
        """اختبار تنظيف البيانات بعد الحذف"""
        
        # التحقق من وجود البيانات قبل التنظيف
        self.assertTrue(hasattr(self.mock_app, 'lightning_successful_cards'))
        self.assertTrue(hasattr(self.mock_app, 'lightning_successful_cards_info'))
        
        # محاكاة تنظيف البيانات
        self.mock_app.lightning_successful_cards = []
        if hasattr(self.mock_app, 'lightning_successful_cards_info'):
            delattr(self.mock_app, 'lightning_successful_cards_info')
        
        # التحقق من التنظيف
        self.assertEqual(len(self.mock_app.lightning_successful_cards), 0)
        self.assertFalse(hasattr(self.mock_app, 'lightning_successful_cards_info'))
        
        print("✅ اختبار تنظيف البيانات بعد الحذف نجح")

    def test_current_operation_only(self):
        """اختبار أن الحذف يؤثر على العملية الحالية فقط"""
        
        # التحقق من أن المعلومات تحتوي على timestamp للعملية الحالية
        self.assertIn('timestamp', self.mock_app.lightning_successful_cards_info)
        
        # التحقق من أن العملية محددة بالبرق
        self.assertEqual(self.mock_app.lightning_successful_cards_info['operation_type'], 'lightning')
        
        # التحقق من أن النظام محدد بالهوت سبوت
        self.assertEqual(self.mock_app.lightning_successful_cards_info['system_type'], 'hotspot')
        
        print("✅ اختبار أن الحذف يؤثر على العملية الحالية فقط نجح")

    def test_failed_cards_not_affected(self):
        """اختبار أن الكروت الفاشلة لا تتأثر"""
        
        # التحقق من أن قائمة الكروت الناجحة لا تحتوي على كروت فاشلة
        successful_cards = self.mock_app.lightning_successful_cards
        
        # جميع الكروت في القائمة يجب أن تكون ناجحة فقط
        self.assertEqual(len(successful_cards), 8)  # عدد الكروت الناجحة
        
        # التحقق من أن عدد الكروت الفاشلة منفصل
        failed_count = self.mock_app.lightning_successful_cards_info['total_failed']
        self.assertEqual(failed_count, 2)
        
        print("✅ اختبار أن الكروت الفاشلة لا تتأثر نجح")

if __name__ == '__main__':
    print("🧪 بدء اختبارات ميزة حذف الكروت المرسلة بنجاح في البرق...")
    print("=" * 80)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 80)
    print("🎉 انتهت جميع اختبارات ميزة حذف الكروت المرسلة بنجاح بنجاح!")
