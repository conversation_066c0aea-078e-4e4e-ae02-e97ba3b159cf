#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح خطأ get_current_time
"""

import re
import os

def test_get_current_time_fix():
    """اختبار إصلاح استدعاءات get_current_time"""
    print("🔍 اختبار إصلاح استدعاءات get_current_time...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من عدم وجود استدعاءات get_current_time
    get_current_time_calls = re.findall(r'self\.get_current_time\(\)', content)
    if get_current_time_calls:
        print(f"❌ لا يزال هناك {len(get_current_time_calls)} استدعاء لـ get_current_time")
        for i, call in enumerate(get_current_time_calls[:3], 1):
            print(f"   {i}. {call}")
        return False
    else:
        print("✅ لا توجد استدعاءات get_current_time")
    
    # التحقق من عدم وجود استدعاءات get_current_date
    get_current_date_calls = re.findall(r'self\.get_current_date\(\)', content)
    if get_current_date_calls:
        print(f"❌ لا يزال هناك {len(get_current_date_calls)} استدعاء لـ get_current_date")
        for i, call in enumerate(get_current_date_calls[:3], 1):
            print(f"   {i}. {call}")
        return False
    else:
        print("✅ لا توجد استدعاءات get_current_date")
    
    return True

def test_datetime_imports():
    """اختبار وجود استيراد datetime في الأماكن المناسبة"""
    print("\n🔍 اختبار وجود استيراد datetime...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_retry_failed_cards
    func_match = re.search(r'def handle_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # التحقق من وجود استيراد datetime
        if 'from datetime import datetime' in func_code:
            print("✅ استيراد datetime موجود في handle_retry_failed_cards")
        else:
            print("❌ استيراد datetime غير موجود في handle_retry_failed_cards")
            return False
        
        # التحقق من استخدام datetime بدلاً من get_current_time
        if 'current_time = datetime.now().strftime(\'%H:%M:%S\')' in func_code:
            print("✅ استخدام datetime مباشرة بدلاً من get_current_time")
        else:
            print("❌ لا يتم استخدام datetime مباشرة")
            return False
    else:
        print("❌ لم يتم العثور على دالة handle_retry_failed_cards")
        return False
    
    return True

def test_delete_successful_cards_fix():
    """اختبار إصلاح دالة حذف الكروت الناجحة"""
    print("\n🔍 اختبار إصلاح دالة حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن النص الذي يحتوي على التاريخ والوقت
    date_time_pattern = r'current_date = datetime\.now\(\)\.strftime\(\'%d/%m/%Y\'\)\s*current_time = datetime\.now\(\)\.strftime\(\'%H:%M:%S\'\)'
    
    if re.search(date_time_pattern, content, re.MULTILINE):
        print("✅ تم إصلاح استخدام التاريخ والوقت في دالة حذف الكروت")
    else:
        print("❌ لم يتم إصلاح استخدام التاريخ والوقت")
        return False
    
    # التحقق من استخدام المتغيرات الجديدة
    if '{current_date}' in content and '{current_time}' in content:
        print("✅ يتم استخدام المتغيرات الجديدة current_date و current_time")
    else:
        print("❌ لا يتم استخدام المتغيرات الجديدة")
        return False
    
    return True

def test_no_undefined_methods():
    """اختبار عدم وجود استدعاءات لدوال غير معرفة"""
    print("\n🔍 اختبار عدم وجود استدعاءات لدوال غير معرفة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # قائمة الدوال التي قد تكون غير معرفة
    undefined_methods = [
        'get_current_time',
        'get_current_date',
        'getCurrentTime',
        'getCurrentDate'
    ]
    
    found_undefined = []
    for method in undefined_methods:
        pattern = f'self\\.{method}\\('
        matches = re.findall(pattern, content)
        if matches:
            found_undefined.extend([(method, len(matches))])
    
    if found_undefined:
        print("❌ تم العثور على استدعاءات لدوال غير معرفة:")
        for method, count in found_undefined:
            print(f"   - {method}: {count} استدعاء")
        return False
    else:
        print("✅ لا توجد استدعاءات لدوال غير معرفة")
    
    return True

def test_error_handling_improvement():
    """اختبار تحسين معالجة الأخطاء"""
    print("\n🔍 اختبار تحسين معالجة الأخطاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود رسالة التشخيص المحسنة
    diagnostic_patterns = [
        'تشخيص إعادة المحاولة',
        'نوع الكرت:',
        'عدد الكروت الفاشلة:',
        'hasattr failed_cards_info:',
        'failed_info_exists:'
    ]
    
    for pattern in diagnostic_patterns:
        if pattern not in content:
            print(f"❌ نمط التشخيص غير موجود: {pattern}")
            return False
        print(f"✅ نمط التشخيص موجود: {pattern}")
    
    return True

def run_get_current_time_fix_test():
    """تشغيل اختبار إصلاح get_current_time"""
    print("🚀 بدء اختبار إصلاح خطأ get_current_time\n")
    
    tests = [
        test_get_current_time_fix,
        test_datetime_imports,
        test_delete_successful_cards_fix,
        test_no_undefined_methods,
        test_error_handling_improvement
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات إصلاح get_current_time نجحت!")
        print("✅ تم إصلاح خطأ get_current_time بنجاح")
        print("\n🎯 ملخص الإصلاح:")
        print("   • تم استبدال self.get_current_time() بـ datetime.now().strftime('%H:%M:%S')")
        print("   • تم استبدال self.get_current_date() بـ datetime.now().strftime('%d/%m/%Y')")
        print("   • تم إضافة استيراد datetime في الأماكن المناسبة")
        print("   • تم تحسين رسائل التشخيص")
        return True
    else:
        print("⚠️ بعض اختبارات الإصلاح فشلت")
        return False

if __name__ == "__main__":
    run_get_current_time_fix_test()
