#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح ميزة حفظ الكروت للكرت الواحد (Single Card) في نظام HotSpot
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من إصلاح مشكلة حفظ الكروت الناجحة والفاشلة وإظهار خيارات الإدارة
"""

import re

def test_failed_cards_saving():
    """اختبار حفظ الكروت الفاشلة للكرت الواحد"""
    print("🔍 اختبار حفظ الكروت الفاشلة للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ الكروت الفاشلة
    failed_saving_patterns = [
        'if not hasattr(self, \'single_card_failed_cards\'):',
        'self.single_card_failed_cards = []',
        'self.single_card_failed_cards.append({',
        '\'username\': cred_username,',
        '\'password\': cred.get("password", ""),',
        '\'profile\': cred.get("profile", ""),',
        '\'error\': str(user_error)',
        'حفظ الكرت الفاشل للكرت الواحد لإعادة المحاولة'
    ]
    
    for pattern in failed_saving_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ الكروت الفاشلة غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ الكروت الفاشلة موجود: {pattern}")
    
    return True

def test_failed_cards_info_saving():
    """اختبار حفظ معلومات الكروت الفاشلة للكرت الواحد"""
    print("\n🔍 اختبار حفظ معلومات الكروت الفاشلة للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ معلومات الكروت الفاشلة
    failed_info_patterns = [
        'حفظ الكروت الفاشلة لخيار "إعادة المحاولة للكروت الفاشلة"',
        'if failed_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\':',
        'حفظ معلومات الكروت الفاشلة للاستخدام في إعادة المحاولة',
        'self.failed_cards_info = {',
        '\'card_type\': \'single\',',
        '\'failed_cards\': self.single_card_failed_cards.copy(),',
        '\'operation_type\': \'single_card\'',
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد'
    ]
    
    for pattern in failed_info_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ معلومات الكروت الفاشلة غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ معلومات الكروت الفاشلة موجود: {pattern}")
    
    return True

def test_retry_button_display():
    """اختبار إظهار زر إعادة المحاولة للكروت الفاشلة"""
    print("\n🔍 اختبار إظهار زر إعادة المحاولة للكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من زر إعادة المحاولة
    retry_button_patterns = [
        'شروط إظهار زر إعادة المحاولة للكروت الفاشلة',
        'show_retry_failed_button = (',
        'failed_count > 0 and',
        'hasattr(self, \'failed_cards_info\')',
        'bool(self.failed_cards_info.get(\'failed_cards\', []))',
        'إعادة المحاولة للكروت الفاشلة:',
        'يمكنك إعادة محاولة إنشاء الـ',
        'retry_failed_cards_single_',
        'تم إضافة زر إعادة المحاولة للكروت الفاشلة للكرت الواحد'
    ]
    
    for pattern in retry_button_patterns:
        if pattern not in func_code:
            print(f"❌ نمط زر إعادة المحاولة غير موجود: {pattern}")
            return False
        print(f"✅ نمط زر إعادة المحاولة موجود: {pattern}")
    
    return True

def test_management_options_display():
    """اختبار إظهار خيارات إدارة الكروت الشاملة"""
    print("\n🔍 اختبار إظهار خيارات إدارة الكروت الشاملة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من خيارات الإدارة الشاملة
    management_patterns = [
        'تشخيص شروط أزرار إدارة الكروت للكرت الواحد',
        'hasattr failed_cards_info:',
        'failed_cards_info count:',
        'نتيجة تقييم شروط الأزرار للكرت الواحد',
        'show_delete_successful_button:',
        'show_retry_failed_button:',
        'خيارات إدارة الكروت:',
        'if show_delete_successful_button or show_retry_failed_button:',
        'keyboard_buttons = []',
        'هذه الخيارات تؤثر فقط على كروت عملية الكرت الواحد الحالية'
    ]
    
    for pattern in management_patterns:
        if pattern not in func_code:
            print(f"❌ نمط خيارات الإدارة غير موجود: {pattern}")
            return False
        print(f"✅ نمط خيارات الإدارة موجود: {pattern}")
    
    return True

def test_callback_handling():
    """اختبار معالجة callback لإعادة المحاولة"""
    print("\n🔍 اختبار معالجة callback لإعادة المحاولة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback
    callback_patterns = [
        'elif callback_data.startswith("retry_failed_cards_"):',
        'self.handle_retry_failed_cards(bot_token, chat_id, callback_data)',
        'def handle_retry_failed_cards(self, bot_token, chat_id, callback_data):',
        'معالجة طلب إعادة المحاولة للكروت الفاشلة'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback موجود: {pattern}")
    
    return True

def test_data_cleanup():
    """اختبار تنظيف البيانات عند عدم الحاجة"""
    print("\n🔍 اختبار تنظيف البيانات عند عدم الحاجة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من تنظيف البيانات
    cleanup_patterns = [
        'مسح أي بيانات سابقة للكروت الفاشلة',
        'if hasattr(self, \'single_card_failed_cards\'):',
        'delattr(self, \'single_card_failed_cards\')',
        'if hasattr(self, \'failed_cards_info\'):',
        'التحقق من أن failed_cards_info خاص بالكرت الواحد قبل الحذف',
        'if getattr(self, \'failed_cards_info\', {}).get(\'operation_type\') == \'single_card\':',
        'delattr(self, \'failed_cards_info\')'
    ]
    
    for pattern in cleanup_patterns:
        if pattern not in func_code:
            print(f"❌ نمط تنظيف البيانات غير موجود: {pattern}")
            return False
        print(f"✅ نمط تنظيف البيانات موجود: {pattern}")
    
    return True

def test_comprehensive_logging():
    """اختبار التسجيل الشامل للتشخيص"""
    print("\n🔍 اختبار التسجيل الشامل للتشخيص...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل التسجيل
    logging_patterns = [
        'تشخيص حفظ الكروت للكرت الواحد',
        'حفظ {success_count} كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد',
        'حفظ {failed_count} كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد',
        'تم حفظ معلومات الكروت الناجحة للكرت الواحد',
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد',
        'لا توجد كروت فاشلة محفوظة للكرت الواحد رغم وجود failed_count',
        'تشخيص شروط أزرار إدارة الكروت للكرت الواحد',
        'نتيجة تقييم شروط الأزرار للكرت الواحد'
    ]
    
    for pattern in logging_patterns:
        if pattern not in content:
            print(f"❌ نمط التسجيل غير موجود: {pattern}")
            return False
        print(f"✅ نمط التسجيل موجود: {pattern}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح ميزة حفظ الكروت للكرت الواحد")
    print("="*70)
    
    tests = [
        ("حفظ الكروت الفاشلة للكرت الواحد", test_failed_cards_saving),
        ("حفظ معلومات الكروت الفاشلة", test_failed_cards_info_saving),
        ("إظهار زر إعادة المحاولة", test_retry_button_display),
        ("خيارات إدارة الكروت الشاملة", test_management_options_display),
        ("معالجة callback لإعادة المحاولة", test_callback_handling),
        ("تنظيف البيانات عند عدم الحاجة", test_data_cleanup),
        ("التسجيل الشامل للتشخيص", test_comprehensive_logging)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح ميزة حفظ الكروت للكرت الواحد بنجاح!")
        print("💡 الميزات المصلحة:")
        print("✅ حفظ الكروت الفاشلة مع تفاصيلها الكاملة")
        print("✅ حفظ معلومات الكروت الفاشلة في failed_cards_info")
        print("✅ إظهار زر إعادة المحاولة للكروت الفاشلة")
        print("✅ إظهار زر حذف الكروت المرسلة بنجاح")
        print("✅ خيارات إدارة شاملة مع أزرار متعددة")
        print("✅ معالجة callback كاملة لجميع الخيارات")
        print("✅ تنظيف البيانات الذكي")
        print("✅ تسجيل مفصل للتشخيص")
        
        print("\n🎯 الآن ستظهر خيارات الإدارة التالية:")
        print("🔄 إعادة المحاولة للكروت الفاشلة (عند وجود كروت فاشلة)")
        print("🗑️ حذف الكروت المرسلة بنجاح (عند وجود كروت فاشلة وناجحة)")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
