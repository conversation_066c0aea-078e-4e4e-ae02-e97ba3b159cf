#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حذف زر حذف الكروت الناجحة للكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن جميع الأجزاء المتعلقة بزر حذف الكروت الناجحة للكرت الواحد قد تم حذفها
"""

import re

def test_button_conditions_removed():
    """اختبار حذف شروط ظهور الزر"""
    print("🔍 اختبار حذف شروط ظهور الزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عدم وجود المتغير والشروط
    removed_patterns = [
        'show_delete_successful_button.*=',
        'شروط إظهار زر حذف الكروت الناجحة للكرت الواحد',
        'تقييم شروط زر حذف الكروت الناجحة للكرت الواحد',
        'single_card_successful_cards.*and',
        'bool.*single_card_successful_cards'
    ]
    
    all_removed = True
    for pattern in removed_patterns:
        if re.search(pattern, func_code, re.IGNORECASE):
            print(f"❌ نمط ما زال موجود (يجب حذفه): {pattern}")
            all_removed = False
        else:
            print(f"✅ نمط تم حذفه: {pattern}")
    
    return all_removed

def test_button_text_removed():
    """اختبار حذف نص الزر"""
    print("\n🔍 اختبار حذف نص الزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن نص الزر للكرت الواحد (مع استثناء البرق)
    single_card_button_patterns = [
        'حذف الكروت.*ناجحة.*كرت.*واحد',
        'single_card_delete_successful_',
        'حذف الكروت المرسلة بنجاح.*single',
        'خيار حذف الكروت المرسلة بنجاح.*كرت.*واحد'
    ]
    
    all_removed = True
    for pattern in single_card_button_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"❌ نمط نص الزر ما زال موجود (يجب حذفه): {pattern}")
            print(f"   المطابقات: {matches}")
            all_removed = False
        else:
            print(f"✅ نمط نص الزر تم حذفه: {pattern}")
    
    # التحقق من أن نص البرق ما زال موجود (يجب عدم حذفه)
    lightning_patterns = [
        'lightning_delete_successful_',
        'حذف الكروت المرسلة بنجاح من هذه العملية.*lightning'
    ]
    
    for pattern in lightning_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ نمط البرق ما زال موجود (صحيح): {pattern}")
        else:
            # هذا مقبول لأن نص البرق قد لا يحتوي على كلمة lightning
            print(f"ℹ️ نمط البرق غير موجود (مقبول): {pattern}")
    
    return all_removed

def test_callback_handlers_removed():
    """اختبار حذف معالجات callback"""
    print("\n🔍 اختبار حذف معالجات callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من عدم وجود معالجات callback للكرت الواحد
    removed_callback_patterns = [
        'elif callback_data.startswith.*single_card_delete_successful',
        'single_card_delete_successful_confirm',
        'single_card_delete_successful_cancel',
        'handle_single_card_delete_successful_request',
        'execute_single_card_delete_successful',
        'cancel_single_card_delete_successful'
    ]
    
    all_removed = True
    for pattern in removed_callback_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"❌ معالج callback ما زال موجود (يجب حذفه): {pattern}")
            all_removed = False
        else:
            print(f"✅ معالج callback تم حذفه: {pattern}")
    
    return all_removed

def test_helper_functions_removed():
    """اختبار حذف الدوال المساعدة"""
    print("\n🔍 اختبار حذف الدوال المساعدة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من عدم وجود الدوال المساعدة
    removed_functions = [
        'def handle_single_card_delete_successful_request',
        'def send_single_card_delete_successful_confirmation',
        'def execute_single_card_delete_successful',
        'def cancel_single_card_delete_successful'
    ]
    
    all_removed = True
    for func in removed_functions:
        if func in content:
            print(f"❌ دالة ما زالت موجودة (يجب حذفها): {func}")
            all_removed = False
        else:
            print(f"✅ دالة تم حذفها: {func}")
    
    return all_removed

def test_data_saving_removed():
    """اختبار حذف كود حفظ البيانات"""
    print("\n🔍 اختبار حذف كود حفظ البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من عدم وجود كود حفظ البيانات (للكرت الواحد فقط)
    removed_data_patterns = [
        'single_card_successful_cards.*=',
        'single_card_successful_cards_info.*=',
        'single_card_successful_usernames',
        'حفظ.*كرت.*ناجح.*حذف.*كروت.*واحد',
        'تم حفظ معلومات الكروت الناجحة للكرت الواحد',
        'لم يتم حفظ الكروت الناجحة للكرت الواحد'
    ]
    
    all_removed = True
    for pattern in removed_data_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"❌ نمط حفظ البيانات ما زال موجود (يجب حذفه): {pattern}")
            print(f"   المطابقات: {matches[:3]}...")  # عرض أول 3 مطابقات فقط
            all_removed = False
        else:
            print(f"✅ نمط حفظ البيانات تم حذفه: {pattern}")
    
    return all_removed

def test_log_messages_removed():
    """اختبار حذف رسائل السجل"""
    print("\n🔍 اختبار حذف رسائل السجل...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من عدم وجود رسائل السجل المتعلقة بالزر (للكرت الواحد فقط)
    removed_log_patterns = [
        'hasattr.*single_card_successful_cards',
        'bool.*single_card_successful_cards',
        'show_delete_successful_button.*كرت.*واحد',
        'تم إضافة زر حذف الكروت.*للكرت الواحد',
        'سيتم إضافة زر حذف الكروت.*كرت واحد'
    ]
    
    all_removed = True
    for pattern in removed_log_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"❌ رسالة سجل ما زالت موجودة (يجب حذفها): {pattern}")
            all_removed = False
        else:
            print(f"✅ رسالة سجل تم حذفها: {pattern}")
    
    return all_removed

def test_lightning_functionality_preserved():
    """اختبار الحفاظ على وظائف البرق"""
    print("\n🔍 اختبار الحفاظ على وظائف البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود وظائف البرق (يجب عدم حذفها)
    preserved_lightning_patterns = [
        'lightning_delete_successful_',
        'lightning_successful_cards',
        'send_lightning_notification_with_delete_successful_button',
        'handle_lightning_delete_successful_request'
    ]
    
    all_preserved = True
    for pattern in preserved_lightning_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ وظيفة البرق محفوظة (صحيح): {pattern}")
        else:
            print(f"⚠️ وظيفة البرق قد تكون مفقودة: {pattern}")
            # لا نعتبر هذا خطأ لأن بعض الوظائف قد لا تكون موجودة
    
    return all_preserved

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار حذف زر حذف الكروت الناجحة للكرت الواحد")
    print("="*70)
    
    tests = [
        ("حذف شروط ظهور الزر", test_button_conditions_removed),
        ("حذف نص الزر", test_button_text_removed),
        ("حذف معالجات callback", test_callback_handlers_removed),
        ("حذف الدوال المساعدة", test_helper_functions_removed),
        ("حذف كود حفظ البيانات", test_data_saving_removed),
        ("حذف رسائل السجل", test_log_messages_removed),
        ("الحفاظ على وظائف البرق", test_lightning_functionality_preserved)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 50)
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم حذف جميع الأجزاء المتعلقة بزر حذف الكروت الناجحة للكرت الواحد بنجاح!")
        print("💡 الميزة تم إزالتها نهائياً مع الحفاظ على باقي الوظائف")
        
        print("\n🗑️ الأجزاء المحذوفة:")
        print("✅ شروط إظهار الزر (show_delete_successful_button)")
        print("✅ النص التوضيحي والزر من keyboard_buttons")
        print("✅ معالجات callback (single_card_delete_successful_)")
        print("✅ الدوال المساعدة (handle, send, execute, cancel)")
        print("✅ رسائل السجل المتعلقة بتشخيص الزر")
        print("✅ البيانات المحفوظة (single_card_successful_cards)")
        
        print("\n🔒 الوظائف المحفوظة:")
        print("✅ ميزة البرق (Lightning) - لم تتأثر")
        print("✅ الكروت العادية - لم تتأثر")
        print("✅ إعادة المحاولة للكروت الفاشلة - لم تتأثر")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - قد تحتاج مراجعة إضافية.")
    
    return failed == 0

if __name__ == "__main__":
    main()
