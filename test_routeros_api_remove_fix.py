#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح دالة الحذف في RouterOS API
Test RouterOS API Remove Function Fix

هذا الاختبار يتحقق من:
1. استخدام الصيغة الصحيحة لدالة remove() في مكتبة RouterOS API
2. استخدام معامل numbers بدلاً من تمرير المعرف مباشرة
3. عدم وجود خطأ "takes 1 positional argument but 2 were given"
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockLogger:
    """Mock للـ logger"""
    def __init__(self):
        self.debug_messages = []
        self.error_messages = []
    
    def debug(self, message):
        self.debug_messages.append(message)
        print(f"[DEBUG] {message}")
    
    def error(self, message):
        self.error_messages.append(message)
        print(f"[ERROR] {message}")

class MockRouterOsResource:
    """Mock لـ RouterOS API Resource يحاكي السلوك الفعلي"""
    
    def __init__(self, simulate_errors=False):
        self.simulate_errors = simulate_errors
        self.removed_users = []
        self.users_db = {
            'user001': {'id': '*1', 'name': 'user001'},
            'user002': {'id': '*2', 'name': 'user002'},
            'user003': {'id': '*3', 'name': 'user003'},
        }
    
    def remove(self, numbers=None, **kwargs):
        """
        محاكاة دالة remove() الفعلية في مكتبة RouterOS API
        الدالة تأخذ معامل واحد فقط (self) + معاملات keyword
        """
        
        # التحقق من وجود معامل numbers
        if numbers is None:
            raise Exception("missing required parameter: numbers")
        
        # محاكاة حذف المستخدم
        if self.simulate_errors and numbers == 'error_user':
            raise Exception("connection timeout")
        
        # محاكاة نجاح الحذف
        if numbers in self.users_db:
            del self.users_db[numbers]
            self.removed_users.append(numbers)
            return True
        else:
            # المستخدم غير موجود - لكن RouterOS لا يرمي خطأ، فقط لا يحذف شيء
            return True

class MockRouterOsAPI:
    """Mock لـ RouterOS API"""
    
    def __init__(self, simulate_errors=False):
        self.simulate_errors = simulate_errors
    
    def get_resource(self, path):
        return MockRouterOsResource(self.simulate_errors)

class TestRouterOsAPIRemoveFix:
    """اختبار إصلاح دالة الحذف في RouterOS API"""
    
    def __init__(self):
        self.logger = MockLogger()
    
    def test_code_structure_fix(self):
        """اختبار أن هيكل الكود تم إصلاحه بشكل صحيح"""
        
        print("=" * 80)
        print("🧪 اختبار إصلاح هيكل الكود")
        print("=" * 80)
        
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                content = file.read()
            
            # البحث عن دالة الحذف
            function_start = content.find('def delete_successful_cards_from_mikrotik(')
            if function_start == -1:
                print("❌ لم يتم العثور على دالة delete_successful_cards_from_mikrotik")
                return False
            
            # استخراج محتوى الدالة (تقريبياً)
            function_end = content.find('\n    def ', function_start + 1)
            if function_end == -1:
                function_end = len(content)
            
            function_content = content[function_start:function_end]
            
            print("🔍 فحص محتوى الدالة:")
            
            # التحقق من استخدام الصيغة الصحيحة
            uses_numbers_param = '.remove(numbers=' in function_content
            print(f"   - استخدام .remove(numbers=username): {'✅ موجود' if uses_numbers_param else '❌ مفقود'}")
            
            # التحقق من عدم استخدام الصيغ الخاطئة
            uses_direct_id = '.remove(user_id)' in function_content
            print(f"   - استخدام .remove(user_id): {'❌ موجود (يجب حذفه)' if uses_direct_id else '✅ غير موجود'}")
            
            uses_name_param = '.remove(name=' in function_content
            print(f"   - استخدام .remove(name=): {'❌ موجود (يجب حذفه)' if uses_name_param else '✅ غير موجود'}")
            
            # التحقق من عدم وجود خطوات البحث غير الضرورية
            has_get_by_name = '.get(name=' in function_content
            print(f"   - استخدام .get(name=): {'❌ موجود (غير ضروري)' if has_get_by_name else '✅ غير موجود'}")
            
            has_user_id_extraction = "user_id = users[0]['id']" in function_content
            print(f"   - استخراج user_id: {'❌ موجود (غير ضروري)' if has_user_id_extraction else '✅ غير موجود'}")
            
            # التحقق من وجود معالجة الأخطاء
            has_error_handling = 'except Exception as user_error:' in function_content
            print(f"   - معالجة الأخطاء: {'✅ موجود' if has_error_handling else '❌ مفقود'}")
            
            # التحقق من تتبع النتائج
            has_deleted_count = 'deleted_count += 1' in function_content
            has_failed_count = 'failed_count += 1' in function_content
            print(f"   - تتبع deleted_count: {'✅ موجود' if has_deleted_count else '❌ مفقود'}")
            print(f"   - تتبع failed_count: {'✅ موجود' if has_failed_count else '❌ مفقود'}")
            
            # النتيجة النهائية
            all_correct = (
                uses_numbers_param and 
                not uses_direct_id and 
                not uses_name_param and 
                not has_get_by_name and
                not has_user_id_extraction and
                has_error_handling and
                has_deleted_count and 
                has_failed_count
            )
            
            print(f"\n📊 النتيجة النهائية:")
            if all_correct:
                print("   ✅ جميع الإصلاحات مطبقة بشكل صحيح!")
            else:
                print("   ❌ بعض الإصلاحات مفقودة أو غير صحيحة!")
            
            return all_correct
            
        except Exception as e:
            print(f"❌ خطأ في فحص الكود: {str(e)}")
            return False
    
    def simulate_correct_api_usage(self):
        """محاكاة الاستخدام الصحيح لـ RouterOS API"""
        
        print("\n" + "=" * 80)
        print("🧪 محاكاة الاستخدام الصحيح لـ RouterOS API")
        print("=" * 80)
        
        # قائمة المستخدمين للاختبار
        test_usernames = [
            'user001',
            'user002', 
            'user003',
            'nonexistent_user',  # مستخدم غير موجود
            'error_user'         # سيسبب خطأ
        ]
        
        print(f"📋 قائمة المستخدمين للاختبار: {test_usernames}")
        
        # محاكاة عملية الحذف بالطريقة الجديدة
        print(f"\n🗑️ بدء محاكاة عملية الحذف:")
        
        api = MockRouterOsAPI(simulate_errors=True)
        deleted_count = 0
        failed_count = 0
        
        for username in test_usernames:
            try:
                # الطريقة الجديدة: حذف مباشر باستخدام numbers
                api.get_resource('/ip/hotspot/user').remove(numbers=username)
                deleted_count += 1
                self.logger.debug(f"✅ تم حذف المستخدم: {username}")
                
            except Exception as user_error:
                failed_count += 1
                self.logger.error(f"❌ فشل في حذف المستخدم {username}: {str(user_error)}")
        
        # النتائج
        total_users = len(test_usernames)
        success_rate = (deleted_count / total_users) * 100
        
        print(f"\n📊 نتائج المحاكاة:")
        print(f"   - إجمالي المستخدمين: {total_users}")
        print(f"   - تم حذفهم بنجاح: {deleted_count}")
        print(f"   - فشل في حذفهم: {failed_count}")
        print(f"   - معدل النجاح: {success_rate:.1f}%")
        
        # التحقق من النتائج المتوقعة
        # متوقع: 3 نجاح (user001-003), 2 فشل (nonexistent_user, error_user)
        expected_deleted = 3
        expected_failed = 2
        
        results_correct = (deleted_count == expected_deleted and failed_count == expected_failed)
        
        print(f"\n✅ التحقق من النتائج:")
        print(f"   - متوقع نجاح: {expected_deleted}, فعلي: {deleted_count}")
        print(f"   - متوقع فشل: {expected_failed}, فعلي: {failed_count}")
        print(f"   - النتائج صحيحة: {'✅ نعم' if results_correct else '❌ لا'}")
        
        return results_correct
    
    def test_api_method_signatures(self):
        """اختبار توقيعات دوال API المختلفة"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار توقيعات دوال API")
        print("=" * 80)
        
        api_methods = [
            {
                'name': 'الطريقة الصحيحة: remove(numbers=username)',
                'method': lambda api, username: api.get_resource('/ip/hotspot/user').remove(numbers=username),
                'should_work': True
            },
            {
                'name': 'الطريقة الخاطئة: remove(user_id) - معامل موضعي',
                'method': lambda api, username: api.get_resource('/ip/hotspot/user').remove('*1'),
                'should_work': False,
                'expected_error': 'takes 1 positional argument but 2 were given'
            },
            {
                'name': 'الطريقة الخاطئة: remove(name=username)',
                'method': lambda api, username: api.get_resource('/ip/hotspot/user').remove(name=username),
                'should_work': False,
                'expected_error': 'unexpected keyword argument'
            }
        ]
        
        print("🔍 اختبار طرق مختلفة:")
        
        all_tests_passed = True
        
        for method_info in api_methods:
            print(f"\n📋 اختبار: {method_info['name']}")
            
            api = MockRouterOsAPI()
            
            try:
                method_info['method'](api, 'test_user')
                result = "نجح"
                
            except Exception as e:
                result = f"فشل: {str(e)}"
            
            if method_info['should_work']:
                test_passed = "فشل:" not in result
                print(f"   - النتيجة: {result}")
                print(f"   - متوقع: نجاح")
            else:
                expected_error = method_info.get('expected_error', '')
                test_passed = expected_error in result or "فشل:" in result
                print(f"   - النتيجة: {result}")
                print(f"   - متوقع: فشل")
            
            print(f"   - الاختبار: {'✅ نجح' if test_passed else '❌ فشل'}")
            
            if not test_passed:
                all_tests_passed = False
        
        return all_tests_passed
    
    def test_error_scenarios(self):
        """اختبار سيناريوهات الأخطاء المختلفة"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار سيناريوهات الأخطاء")
        print("=" * 80)
        
        error_scenarios = [
            {
                'name': 'مستخدم عادي',
                'username': 'normal_user',
                'should_succeed': True
            },
            {
                'name': 'مستخدم يسبب خطأ اتصال',
                'username': 'error_user',
                'should_succeed': False,
                'expected_error': 'connection timeout'
            },
            {
                'name': 'مستخدم غير موجود',
                'username': 'nonexistent_user',
                'should_succeed': True  # RouterOS لا يرمي خطأ للمستخدمين غير الموجودين
            }
        ]
        
        print("🔍 اختبار سيناريوهات مختلفة:")
        
        all_tests_passed = True
        
        for scenario in error_scenarios:
            print(f"\n📋 اختبار: {scenario['name']}")
            
            api = MockRouterOsAPI(simulate_errors=True)
            
            try:
                api.get_resource('/ip/hotspot/user').remove(numbers=scenario['username'])
                result = "نجح"
                
            except Exception as e:
                result = f"فشل: {str(e)}"
            
            if scenario['should_succeed']:
                test_passed = "فشل:" not in result
                print(f"   - النتيجة: {result}")
                print(f"   - متوقع: نجاح")
            else:
                expected_error = scenario.get('expected_error', '')
                test_passed = expected_error in result
                print(f"   - النتيجة: {result}")
                print(f"   - متوقع: فشل مع '{expected_error}'")
            
            print(f"   - الاختبار: {'✅ نجح' if test_passed else '❌ فشل'}")
            
            if not test_passed:
                all_tests_passed = False
        
        return all_tests_passed

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🚀 بدء اختبار إصلاح دالة الحذف في RouterOS API")
    
    tester = TestRouterOsAPIRemoveFix()
    
    # تشغيل جميع الاختبارات
    test1 = tester.test_code_structure_fix()
    test2 = tester.simulate_correct_api_usage()
    test3 = tester.test_api_method_signatures()
    test4 = tester.test_error_scenarios()
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("🎯 ملخص النتائج النهائية")
    print("=" * 80)
    
    all_tests_passed = test1 and test2 and test3 and test4
    
    print(f"📊 نتائج الاختبارات:")
    print(f"   1. إصلاح هيكل الكود: {'✅ نجح' if test1 else '❌ فشل'}")
    print(f"   2. محاكاة الاستخدام الصحيح: {'✅ نجح' if test2 else '❌ فشل'}")
    print(f"   3. اختبار توقيعات الدوال: {'✅ نجح' if test3 else '❌ فشل'}")
    print(f"   4. اختبار سيناريوهات الأخطاء: {'✅ نجح' if test4 else '❌ فشل'}")
    
    if all_tests_passed:
        print(f"\n🎉 جميع الاختبارات نجحت! إصلاح RouterOS API مطبق بشكل صحيح.")
        print(f"💡 الطريقة الصحيحة الآن:")
        print(f"   api.get_resource('/ip/hotspot/user').remove(numbers=username)")
        print(f"\n❌ تجنب هذه الطرق:")
        print(f"   - api.get_resource('/ip/hotspot/user').remove(user_id)  # خطأ معامل موضعي")
        print(f"   - api.get_resource('/ip/hotspot/user').remove(name=username)  # معامل غير مدعوم")
    else:
        print(f"\n❌ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.")
    
    print(f"\n🔍 الفوائد من الإصلاح:")
    print(f"   ✅ لا مزيد من خطأ 'takes 1 positional argument but 2 were given'")
    print(f"   ✅ استخدام الصيغة الصحيحة لمكتبة RouterOS API")
    print(f"   ✅ حذف مباشر وبسيط باستخدام اسم المستخدم")
    print(f"   ✅ عدم الحاجة للبحث عن المعرف أولاً")
    print(f"   ✅ كود أبسط وأسرع")

if __name__ == '__main__':
    main()
