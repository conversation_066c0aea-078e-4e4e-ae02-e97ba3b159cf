#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح تضارب الدوال - التحقق من أن الدالة الصحيحة يتم استدعاؤها
Test Function Conflict Fix - Verify Correct Function is Called

هذا الاختبار يتحقق من:
1. أن دالة send_to_mikrotik_silent() الصحيحة يتم استدعاؤها من البرق الموحد
2. أن دالة send_single_card_to_mikrotik_silent() يتم استدعاؤها من الكرت الواحد
3. أن كود حفظ الكروت الناجحة يعمل بشكل صحيح
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockLogger:
    """Mock للـ logger"""
    def info(self, message):
        print(f"[INFO] {message}")
    
    def error(self, message):
        print(f"[ERROR] {message}")
    
    def warning(self, message):
        print(f"[WARNING] {message}")

class TestFunctionConflict:
    """اختبار تضارب الدوال"""
    
    def __init__(self):
        self.logger = MockLogger()
    
    def test_function_definitions(self):
        """اختبار تعريف الدوال"""
        
        print("=" * 80)
        print("🧪 اختبار تعريف الدوال في الملف")
        print("=" * 80)
        
        # قراءة الملف والبحث عن تعريفات الدوال
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                content = file.read()
            
            # البحث عن تعريفات الدوال
            send_to_mikrotik_silent_count = content.count('def send_to_mikrotik_silent(')
            send_single_card_to_mikrotik_silent_count = content.count('def send_single_card_to_mikrotik_silent(')
            
            print(f"📊 نتائج البحث:")
            print(f"   - def send_to_mikrotik_silent(): {send_to_mikrotik_silent_count} مرة")
            print(f"   - def send_single_card_to_mikrotik_silent(): {send_single_card_to_mikrotik_silent_count} مرة")
            
            # التحقق من النتائج
            if send_to_mikrotik_silent_count == 1:
                print(f"   ✅ دالة send_to_mikrotik_silent() معرفة مرة واحدة فقط - ممتاز!")
            else:
                print(f"   ❌ دالة send_to_mikrotik_silent() معرفة {send_to_mikrotik_silent_count} مرة - مشكلة!")
            
            if send_single_card_to_mikrotik_silent_count == 1:
                print(f"   ✅ دالة send_single_card_to_mikrotik_silent() معرفة مرة واحدة - ممتاز!")
            else:
                print(f"   ❌ دالة send_single_card_to_mikrotik_silent() معرفة {send_single_card_to_mikrotik_silent_count} مرة - مشكلة!")
            
            return send_to_mikrotik_silent_count == 1 and send_single_card_to_mikrotik_silent_count == 1
            
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {str(e)}")
            return False
    
    def test_function_calls(self):
        """اختبار استدعاءات الدوال"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار استدعاءات الدوال")
        print("=" * 80)
        
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # البحث عن استدعاءات الدوال
            send_to_mikrotik_silent_calls = []
            send_single_card_calls = []
            
            for i, line in enumerate(lines, 1):
                if 'send_to_mikrotik_silent()' in line and not line.strip().startswith('#'):
                    send_to_mikrotik_silent_calls.append((i, line.strip()))
                elif 'send_single_card_to_mikrotik_silent()' in line and not line.strip().startswith('#'):
                    send_single_card_calls.append((i, line.strip()))
            
            print(f"📊 استدعاءات send_to_mikrotik_silent():")
            for line_num, line_content in send_to_mikrotik_silent_calls:
                print(f"   السطر {line_num}: {line_content}")
            
            print(f"\n📊 استدعاءات send_single_card_to_mikrotik_silent():")
            for line_num, line_content in send_single_card_calls:
                print(f"   السطر {line_num}: {line_content}")
            
            # التحقق من الاستدعاءات
            expected_lightning_calls = 1  # من البرق الموحد
            expected_single_card_calls = 1  # من الكرت الواحد
            
            lightning_calls_correct = len(send_to_mikrotik_silent_calls) == expected_lightning_calls
            single_card_calls_correct = len(send_single_card_calls) == expected_single_card_calls
            
            print(f"\n🔍 تحليل الاستدعاءات:")
            if lightning_calls_correct:
                print(f"   ✅ استدعاءات send_to_mikrotik_silent() صحيحة ({len(send_to_mikrotik_silent_calls)}/{expected_lightning_calls})")
            else:
                print(f"   ❌ استدعاءات send_to_mikrotik_silent() غير صحيحة ({len(send_to_mikrotik_silent_calls)}/{expected_lightning_calls})")
            
            if single_card_calls_correct:
                print(f"   ✅ استدعاءات send_single_card_to_mikrotik_silent() صحيحة ({len(send_single_card_calls)}/{expected_single_card_calls})")
            else:
                print(f"   ❌ استدعاءات send_single_card_to_mikrotik_silent() غير صحيحة ({len(send_single_card_calls)}/{expected_single_card_calls})")
            
            return lightning_calls_correct and single_card_calls_correct
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الاستدعاءات: {str(e)}")
            return False
    
    def test_successful_cards_saving_code(self):
        """اختبار كود حفظ الكروت الناجحة"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار كود حفظ الكروت الناجحة")
        print("=" * 80)
        
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                content = file.read()
            
            # البحث عن الكود المطلوب
            required_code_snippets = [
                'successful_usernames = []',
                'successful_usernames.append(clean_username)',
                'self.lightning_successful_cards = successful_usernames.copy()',
                'self.lightning_successful_cards_info = {',
                "'successful_usernames': successful_usernames"
            ]
            
            print(f"🔍 البحث عن أجزاء الكود المطلوبة:")
            
            all_found = True
            for snippet in required_code_snippets:
                found = snippet in content
                status = "✅ موجود" if found else "❌ مفقود"
                print(f"   - {snippet}: {status}")
                if not found:
                    all_found = False
            
            print(f"\n📊 النتيجة النهائية:")
            if all_found:
                print(f"   ✅ جميع أجزاء كود حفظ الكروت الناجحة موجودة!")
            else:
                print(f"   ❌ بعض أجزاء كود حفظ الكروت الناجحة مفقودة!")
            
            return all_found
            
        except Exception as e:
            print(f"❌ خطأ في فحص الكود: {str(e)}")
            return False
    
    def test_diagnostic_messages(self):
        """اختبار رسائل التشخيص"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار رسائل التشخيص")
        print("=" * 80)
        
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                content = file.read()
            
            # البحث عن رسائل التشخيص المطلوبة
            diagnostic_messages = [
                '🔍 بدء تنفيذ send_to_mikrotik_silent() - الدالة المخصصة للبرق الموحد',
                '🔍 تشخيص حفظ الكروت الناجحة:',
                '🔍 تشخيص شروط زر الحذف:',
                '🔍 نتيجة تقييم شروط زر الحذف:',
                '💾 حفظ {success_count} كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح'
            ]
            
            print(f"🔍 البحث عن رسائل التشخيص:")
            
            all_found = True
            for message in diagnostic_messages:
                found = message in content
                status = "✅ موجود" if found else "❌ مفقود"
                print(f"   - {message[:50]}...: {status}")
                if not found:
                    all_found = False
            
            print(f"\n📊 النتيجة النهائية:")
            if all_found:
                print(f"   ✅ جميع رسائل التشخيص موجودة!")
            else:
                print(f"   ❌ بعض رسائل التشخيص مفقودة!")
            
            return all_found
            
        except Exception as e:
            print(f"❌ خطأ في فحص رسائل التشخيص: {str(e)}")
            return False

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🚀 بدء اختبار إصلاح تضارب الدوال")
    
    tester = TestFunctionConflict()
    
    # تشغيل الاختبارات
    test1 = tester.test_function_definitions()
    test2 = tester.test_function_calls()
    test3 = tester.test_successful_cards_saving_code()
    test4 = tester.test_diagnostic_messages()
    
    # النتيجة النهائية
    print("\n" + "=" * 80)
    print("🎯 ملخص النتائج")
    print("=" * 80)
    
    all_tests_passed = test1 and test2 and test3 and test4
    
    print(f"📊 نتائج الاختبارات:")
    print(f"   1. تعريف الدوال: {'✅ نجح' if test1 else '❌ فشل'}")
    print(f"   2. استدعاءات الدوال: {'✅ نجح' if test2 else '❌ فشل'}")
    print(f"   3. كود حفظ الكروت الناجحة: {'✅ نجح' if test3 else '❌ فشل'}")
    print(f"   4. رسائل التشخيص: {'✅ نجح' if test4 else '❌ فشل'}")
    
    if all_tests_passed:
        print(f"\n🎉 جميع الاختبارات نجحت! المشكلة تم إصلاحها.")
        print(f"💡 الآن يجب أن يظهر زر حذف الكروت المرسلة بنجاح عند تشغيل البرق مع وجود كروت فاشلة.")
    else:
        print(f"\n❌ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.")
    
    print(f"\n🔍 للتأكد من الإصلاح:")
    print(f"   1. قم بتشغيل عملية البرق مع وجود بعض الكروت الفاشلة")
    print(f"   2. راقب رسائل السجل للتأكد من ظهور رسائل التشخيص")
    print(f"   3. تحقق من ظهور زر حذف الكروت المرسلة بنجاح في رسالة التلجرام")

if __name__ == '__main__':
    main()
