# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 17:46:14
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0145249506
:do {
    /tool user-manager user add customer="admin" username="0145249506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145249506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145249506";
};

# المستخدم 2: 0110161200
:do {
    /tool user-manager user add customer="admin" username="0110161200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110161200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110161200";
};

# المستخدم 3: 0184183593
:do {
    /tool user-manager user add customer="admin" username="0184183593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184183593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184183593";
};

# المستخدم 4: 0182655338
:do {
    /tool user-manager user add customer="admin" username="0182655338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182655338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182655338";
};

# المستخدم 5: 0160418377
:do {
    /tool user-manager user add customer="admin" username="0160418377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160418377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160418377";
};

# المستخدم 6: 0177932675
:do {
    /tool user-manager user add customer="admin" username="0177932675" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177932675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177932675";
};

# المستخدم 7: 0170633989
:do {
    /tool user-manager user add customer="admin" username="0170633989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170633989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170633989";
};

# المستخدم 8: 0160828949
:do {
    /tool user-manager user add customer="admin" username="0160828949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160828949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160828949";
};

# المستخدم 9: 0169573397
:do {
    /tool user-manager user add customer="admin" username="0169573397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169573397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169573397";
};

# المستخدم 10: 0153226184
:do {
    /tool user-manager user add customer="admin" username="0153226184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153226184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153226184";
};

# المستخدم 11: 0177147953
:do {
    /tool user-manager user add customer="admin" username="0177147953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177147953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177147953";
};

# المستخدم 12: 0164524468
:do {
    /tool user-manager user add customer="admin" username="0164524468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164524468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164524468";
};

# المستخدم 13: 0192474250
:do {
    /tool user-manager user add customer="admin" username="0192474250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192474250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192474250";
};

# المستخدم 14: 0154740759
:do {
    /tool user-manager user add customer="admin" username="0154740759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154740759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154740759";
};

# المستخدم 15: 0121483045
:do {
    /tool user-manager user add customer="admin" username="0121483045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121483045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121483045";
};

# المستخدم 16: 0134211825
:do {
    /tool user-manager user add customer="admin" username="0134211825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134211825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134211825";
};

# المستخدم 17: 0167122298
:do {
    /tool user-manager user add customer="admin" username="0167122298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167122298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167122298";
};

# المستخدم 18: 0122895393
:do {
    /tool user-manager user add customer="admin" username="0122895393" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122895393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122895393";
};

# المستخدم 19: 0185509835
:do {
    /tool user-manager user add customer="admin" username="0185509835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185509835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185509835";
};

# المستخدم 20: 0116013704
:do {
    /tool user-manager user add customer="admin" username="0116013704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116013704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116013704";
};

# المستخدم 21: 0129982786
:do {
    /tool user-manager user add customer="admin" username="0129982786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129982786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129982786";
};

# المستخدم 22: 0155892742
:do {
    /tool user-manager user add customer="admin" username="0155892742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155892742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155892742";
};

# المستخدم 23: 0168684933
:do {
    /tool user-manager user add customer="admin" username="0168684933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168684933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168684933";
};

# المستخدم 24: 0144174395
:do {
    /tool user-manager user add customer="admin" username="0144174395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144174395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144174395";
};

# المستخدم 25: 0158780690
:do {
    /tool user-manager user add customer="admin" username="0158780690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158780690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158780690";
};

# المستخدم 26: 0127016869
:do {
    /tool user-manager user add customer="admin" username="0127016869" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127016869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127016869";
};

# المستخدم 27: 0196162529
:do {
    /tool user-manager user add customer="admin" username="0196162529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196162529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196162529";
};

# المستخدم 28: 0104664203
:do {
    /tool user-manager user add customer="admin" username="0104664203" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104664203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104664203";
};

# المستخدم 29: 0171755010
:do {
    /tool user-manager user add customer="admin" username="0171755010" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171755010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171755010";
};

# المستخدم 30: 0190139621
:do {
    /tool user-manager user add customer="admin" username="0190139621" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190139621";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190139621";
};

# المستخدم 31: 0121913568
:do {
    /tool user-manager user add customer="admin" username="0121913568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121913568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121913568";
};

# المستخدم 32: 0177093439
:do {
    /tool user-manager user add customer="admin" username="0177093439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177093439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177093439";
};

# المستخدم 33: 0199222471
:do {
    /tool user-manager user add customer="admin" username="0199222471" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199222471";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199222471";
};

# المستخدم 34: 0148215923
:do {
    /tool user-manager user add customer="admin" username="0148215923" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148215923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148215923";
};

# المستخدم 35: 0177596264
:do {
    /tool user-manager user add customer="admin" username="0177596264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177596264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177596264";
};

# المستخدم 36: 0136476129
:do {
    /tool user-manager user add customer="admin" username="0136476129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136476129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136476129";
};

# المستخدم 37: 0179351050
:do {
    /tool user-manager user add customer="admin" username="0179351050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179351050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179351050";
};

# المستخدم 38: 0143040337
:do {
    /tool user-manager user add customer="admin" username="0143040337" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143040337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143040337";
};

# المستخدم 39: 0191457999
:do {
    /tool user-manager user add customer="admin" username="0191457999" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191457999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191457999";
};

# المستخدم 40: 0185627443
:do {
    /tool user-manager user add customer="admin" username="0185627443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185627443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185627443";
};

# المستخدم 41: 0103293243
:do {
    /tool user-manager user add customer="admin" username="0103293243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103293243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103293243";
};

# المستخدم 42: 0137401814
:do {
    /tool user-manager user add customer="admin" username="0137401814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137401814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137401814";
};

# المستخدم 43: 0182444313
:do {
    /tool user-manager user add customer="admin" username="0182444313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182444313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182444313";
};

# المستخدم 44: 0197697594
:do {
    /tool user-manager user add customer="admin" username="0197697594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197697594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197697594";
};

# المستخدم 45: 0116399948
:do {
    /tool user-manager user add customer="admin" username="0116399948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116399948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116399948";
};

# المستخدم 46: 0192126894
:do {
    /tool user-manager user add customer="admin" username="0192126894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192126894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192126894";
};

# المستخدم 47: 0108986762
:do {
    /tool user-manager user add customer="admin" username="0108986762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108986762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108986762";
};

# المستخدم 48: 0178832708
:do {
    /tool user-manager user add customer="admin" username="0178832708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178832708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178832708";
};

# المستخدم 49: 0118662768
:do {
    /tool user-manager user add customer="admin" username="0118662768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118662768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118662768";
};

# المستخدم 50: 0132897547
:do {
    /tool user-manager user add customer="admin" username="0132897547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132897547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132897547";
};

# المستخدم 51: 0109783191
:do {
    /tool user-manager user add customer="admin" username="0109783191" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109783191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109783191";
};

# المستخدم 52: 0186059901
:do {
    /tool user-manager user add customer="admin" username="0186059901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186059901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186059901";
};

# المستخدم 53: 0175154817
:do {
    /tool user-manager user add customer="admin" username="0175154817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175154817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175154817";
};

# المستخدم 54: 0123000317
:do {
    /tool user-manager user add customer="admin" username="0123000317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123000317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123000317";
};

# المستخدم 55: 0181948226
:do {
    /tool user-manager user add customer="admin" username="0181948226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181948226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181948226";
};

# المستخدم 56: 0123149985
:do {
    /tool user-manager user add customer="admin" username="0123149985" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123149985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123149985";
};

# المستخدم 57: 0165951970
:do {
    /tool user-manager user add customer="admin" username="0165951970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165951970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165951970";
};

# المستخدم 58: 0166993567
:do {
    /tool user-manager user add customer="admin" username="0166993567" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166993567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166993567";
};

# المستخدم 59: 0176424780
:do {
    /tool user-manager user add customer="admin" username="0176424780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176424780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176424780";
};

# المستخدم 60: 0195506011
:do {
    /tool user-manager user add customer="admin" username="0195506011" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195506011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195506011";
};

# المستخدم 61: 0138822327
:do {
    /tool user-manager user add customer="admin" username="0138822327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138822327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138822327";
};

# المستخدم 62: 0196236835
:do {
    /tool user-manager user add customer="admin" username="0196236835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196236835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196236835";
};

# المستخدم 63: 0167482917
:do {
    /tool user-manager user add customer="admin" username="0167482917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167482917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167482917";
};

# المستخدم 64: 0115077800
:do {
    /tool user-manager user add customer="admin" username="0115077800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115077800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115077800";
};

# المستخدم 65: 0127158905
:do {
    /tool user-manager user add customer="admin" username="0127158905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127158905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127158905";
};

# المستخدم 66: 0150381764
:do {
    /tool user-manager user add customer="admin" username="0150381764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150381764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150381764";
};

# المستخدم 67: 0145362955
:do {
    /tool user-manager user add customer="admin" username="0145362955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145362955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145362955";
};

# المستخدم 68: 0196400955
:do {
    /tool user-manager user add customer="admin" username="0196400955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196400955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196400955";
};

# المستخدم 69: 0196589842
:do {
    /tool user-manager user add customer="admin" username="0196589842" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196589842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196589842";
};

# المستخدم 70: 0149661391
:do {
    /tool user-manager user add customer="admin" username="0149661391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149661391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149661391";
};

# المستخدم 71: 0153243968
:do {
    /tool user-manager user add customer="admin" username="0153243968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153243968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153243968";
};

# المستخدم 72: 0194792508
:do {
    /tool user-manager user add customer="admin" username="0194792508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194792508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194792508";
};

# المستخدم 73: 0174773052
:do {
    /tool user-manager user add customer="admin" username="0174773052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174773052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174773052";
};

# المستخدم 74: 0141577845
:do {
    /tool user-manager user add customer="admin" username="0141577845" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141577845";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141577845";
};

# المستخدم 75: 0152949646
:do {
    /tool user-manager user add customer="admin" username="0152949646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152949646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152949646";
};

# المستخدم 76: 0121141882
:do {
    /tool user-manager user add customer="admin" username="0121141882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121141882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121141882";
};

# المستخدم 77: 0196292915
:do {
    /tool user-manager user add customer="admin" username="0196292915" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196292915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196292915";
};

# المستخدم 78: 0110339888
:do {
    /tool user-manager user add customer="admin" username="0110339888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110339888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110339888";
};

# المستخدم 79: 0146782107
:do {
    /tool user-manager user add customer="admin" username="0146782107" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146782107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146782107";
};

# المستخدم 80: 0100904367
:do {
    /tool user-manager user add customer="admin" username="0100904367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100904367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100904367";
};

# المستخدم 81: 0177481566
:do {
    /tool user-manager user add customer="admin" username="0177481566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177481566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177481566";
};

# المستخدم 82: 0135781659
:do {
    /tool user-manager user add customer="admin" username="0135781659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135781659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135781659";
};

# المستخدم 83: 0144190921
:do {
    /tool user-manager user add customer="admin" username="0144190921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144190921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144190921";
};

# المستخدم 84: 0167666999
:do {
    /tool user-manager user add customer="admin" username="0167666999" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167666999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167666999";
};

# المستخدم 85: 0182960522
:do {
    /tool user-manager user add customer="admin" username="0182960522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182960522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182960522";
};

# المستخدم 86: 0154553640
:do {
    /tool user-manager user add customer="admin" username="0154553640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154553640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154553640";
};

# المستخدم 87: 0167284132
:do {
    /tool user-manager user add customer="admin" username="0167284132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167284132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167284132";
};

# المستخدم 88: 0140173907
:do {
    /tool user-manager user add customer="admin" username="0140173907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140173907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140173907";
};

# المستخدم 89: 0182846406
:do {
    /tool user-manager user add customer="admin" username="0182846406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182846406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182846406";
};

# المستخدم 90: 0155438731
:do {
    /tool user-manager user add customer="admin" username="0155438731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155438731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155438731";
};

# المستخدم 91: 0116530169
:do {
    /tool user-manager user add customer="admin" username="0116530169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116530169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116530169";
};

# المستخدم 92: 0130099129
:do {
    /tool user-manager user add customer="admin" username="0130099129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130099129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130099129";
};

# المستخدم 93: 0107589441
:do {
    /tool user-manager user add customer="admin" username="0107589441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107589441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107589441";
};

# المستخدم 94: 0141427100
:do {
    /tool user-manager user add customer="admin" username="0141427100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141427100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141427100";
};

# المستخدم 95: 0105245513
:do {
    /tool user-manager user add customer="admin" username="0105245513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105245513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105245513";
};

# المستخدم 96: 0169943036
:do {
    /tool user-manager user add customer="admin" username="0169943036" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169943036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169943036";
};

# المستخدم 97: 0105006850
:do {
    /tool user-manager user add customer="admin" username="0105006850" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105006850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105006850";
};

# المستخدم 98: 0139938660
:do {
    /tool user-manager user add customer="admin" username="0139938660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139938660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139938660";
};

# المستخدم 99: 0161029799
:do {
    /tool user-manager user add customer="admin" username="0161029799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161029799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161029799";
};

# المستخدم 100: 0155727616
:do {
    /tool user-manager user add customer="admin" username="0155727616" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155727616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155727616";
};

# المستخدم 101: 0131564874
:do {
    /tool user-manager user add customer="admin" username="0131564874" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131564874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131564874";
};

# المستخدم 102: 0167985754
:do {
    /tool user-manager user add customer="admin" username="0167985754" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167985754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167985754";
};

# المستخدم 103: 0182736492
:do {
    /tool user-manager user add customer="admin" username="0182736492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182736492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182736492";
};

# المستخدم 104: 0117129770
:do {
    /tool user-manager user add customer="admin" username="0117129770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117129770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117129770";
};

# المستخدم 105: 0155418006
:do {
    /tool user-manager user add customer="admin" username="0155418006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155418006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155418006";
};

# المستخدم 106: 0156589298
:do {
    /tool user-manager user add customer="admin" username="0156589298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156589298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156589298";
};

# المستخدم 107: 0175035787
:do {
    /tool user-manager user add customer="admin" username="0175035787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175035787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175035787";
};

# المستخدم 108: 0103044862
:do {
    /tool user-manager user add customer="admin" username="0103044862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103044862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103044862";
};

# المستخدم 109: 0107972851
:do {
    /tool user-manager user add customer="admin" username="0107972851" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107972851";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107972851";
};

# المستخدم 110: 0106402083
:do {
    /tool user-manager user add customer="admin" username="0106402083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106402083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106402083";
};

# المستخدم 111: 0123636243
:do {
    /tool user-manager user add customer="admin" username="0123636243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123636243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123636243";
};

# المستخدم 112: 0191988871
:do {
    /tool user-manager user add customer="admin" username="0191988871" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191988871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191988871";
};

# المستخدم 113: 0135482742
:do {
    /tool user-manager user add customer="admin" username="0135482742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135482742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135482742";
};

# المستخدم 114: 0147829729
:do {
    /tool user-manager user add customer="admin" username="0147829729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147829729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147829729";
};

# المستخدم 115: 0113915716
:do {
    /tool user-manager user add customer="admin" username="0113915716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113915716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113915716";
};

# المستخدم 116: 0191263850
:do {
    /tool user-manager user add customer="admin" username="0191263850" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191263850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191263850";
};

# المستخدم 117: 0149618241
:do {
    /tool user-manager user add customer="admin" username="0149618241" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149618241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149618241";
};

# المستخدم 118: 0153777303
:do {
    /tool user-manager user add customer="admin" username="0153777303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153777303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153777303";
};

# المستخدم 119: 0100558455
:do {
    /tool user-manager user add customer="admin" username="0100558455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100558455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100558455";
};

# المستخدم 120: 0152126974
:do {
    /tool user-manager user add customer="admin" username="0152126974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152126974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152126974";
};

# المستخدم 121: 0137662026
:do {
    /tool user-manager user add customer="admin" username="0137662026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137662026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137662026";
};

# المستخدم 122: 0135346676
:do {
    /tool user-manager user add customer="admin" username="0135346676" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135346676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135346676";
};

# المستخدم 123: 0133657431
:do {
    /tool user-manager user add customer="admin" username="0133657431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133657431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133657431";
};

# المستخدم 124: 0113138876
:do {
    /tool user-manager user add customer="admin" username="0113138876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113138876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113138876";
};

# المستخدم 125: 0145426008
:do {
    /tool user-manager user add customer="admin" username="0145426008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145426008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145426008";
};

# المستخدم 126: 0142546268
:do {
    /tool user-manager user add customer="admin" username="0142546268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142546268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142546268";
};

# المستخدم 127: 0144172347
:do {
    /tool user-manager user add customer="admin" username="0144172347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144172347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144172347";
};

# المستخدم 128: 0165944219
:do {
    /tool user-manager user add customer="admin" username="0165944219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165944219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165944219";
};

# المستخدم 129: 0187430061
:do {
    /tool user-manager user add customer="admin" username="0187430061" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187430061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187430061";
};

# المستخدم 130: 0132778622
:do {
    /tool user-manager user add customer="admin" username="0132778622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132778622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132778622";
};

# المستخدم 131: 0119292559
:do {
    /tool user-manager user add customer="admin" username="0119292559" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119292559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119292559";
};

# المستخدم 132: 0169296685
:do {
    /tool user-manager user add customer="admin" username="0169296685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169296685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169296685";
};

# المستخدم 133: 0115569100
:do {
    /tool user-manager user add customer="admin" username="0115569100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115569100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115569100";
};

# المستخدم 134: 0171856298
:do {
    /tool user-manager user add customer="admin" username="0171856298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171856298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171856298";
};

# المستخدم 135: 0196313988
:do {
    /tool user-manager user add customer="admin" username="0196313988" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196313988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196313988";
};

# المستخدم 136: 0197617706
:do {
    /tool user-manager user add customer="admin" username="0197617706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197617706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197617706";
};

# المستخدم 137: 0161387143
:do {
    /tool user-manager user add customer="admin" username="0161387143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161387143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161387143";
};

# المستخدم 138: 0154321474
:do {
    /tool user-manager user add customer="admin" username="0154321474" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154321474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154321474";
};

# المستخدم 139: 0109029076
:do {
    /tool user-manager user add customer="admin" username="0109029076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109029076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109029076";
};

# المستخدم 140: 0192320029
:do {
    /tool user-manager user add customer="admin" username="0192320029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192320029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192320029";
};

# المستخدم 141: 0128640323
:do {
    /tool user-manager user add customer="admin" username="0128640323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128640323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128640323";
};

# المستخدم 142: 0101178060
:do {
    /tool user-manager user add customer="admin" username="0101178060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101178060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101178060";
};

# المستخدم 143: 0150127100
:do {
    /tool user-manager user add customer="admin" username="0150127100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150127100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150127100";
};

# المستخدم 144: 0137232469
:do {
    /tool user-manager user add customer="admin" username="0137232469" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137232469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137232469";
};

# المستخدم 145: 0124601088
:do {
    /tool user-manager user add customer="admin" username="0124601088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124601088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124601088";
};

# المستخدم 146: 0128166986
:do {
    /tool user-manager user add customer="admin" username="0128166986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128166986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128166986";
};

# المستخدم 147: 0121971953
:do {
    /tool user-manager user add customer="admin" username="0121971953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121971953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121971953";
};

# المستخدم 148: 0114138559
:do {
    /tool user-manager user add customer="admin" username="0114138559" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114138559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114138559";
};

# المستخدم 149: 0199354917
:do {
    /tool user-manager user add customer="admin" username="0199354917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199354917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199354917";
};

# المستخدم 150: 0189138241
:do {
    /tool user-manager user add customer="admin" username="0189138241" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189138241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189138241";
};

# المستخدم 151: 0129545489
:do {
    /tool user-manager user add customer="admin" username="0129545489" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129545489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129545489";
};

# المستخدم 152: 0160225688
:do {
    /tool user-manager user add customer="admin" username="0160225688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160225688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160225688";
};

# المستخدم 153: 0199666936
:do {
    /tool user-manager user add customer="admin" username="0199666936" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199666936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199666936";
};

# المستخدم 154: 0185969183
:do {
    /tool user-manager user add customer="admin" username="0185969183" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185969183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185969183";
};

# المستخدم 155: 0135456058
:do {
    /tool user-manager user add customer="admin" username="0135456058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135456058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135456058";
};

# المستخدم 156: 0153610371
:do {
    /tool user-manager user add customer="admin" username="0153610371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153610371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153610371";
};

# المستخدم 157: 0171720716
:do {
    /tool user-manager user add customer="admin" username="0171720716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171720716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171720716";
};

# المستخدم 158: 0107245076
:do {
    /tool user-manager user add customer="admin" username="0107245076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107245076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107245076";
};

# المستخدم 159: 0174298286
:do {
    /tool user-manager user add customer="admin" username="0174298286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174298286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174298286";
};

# المستخدم 160: 0191009060
:do {
    /tool user-manager user add customer="admin" username="0191009060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191009060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191009060";
};

# المستخدم 161: 0150554444
:do {
    /tool user-manager user add customer="admin" username="0150554444" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150554444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150554444";
};

# المستخدم 162: 0142572600
:do {
    /tool user-manager user add customer="admin" username="0142572600" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142572600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142572600";
};

# المستخدم 163: 0198886974
:do {
    /tool user-manager user add customer="admin" username="0198886974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198886974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198886974";
};

# المستخدم 164: 0185249140
:do {
    /tool user-manager user add customer="admin" username="0185249140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185249140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185249140";
};

# المستخدم 165: 0119224303
:do {
    /tool user-manager user add customer="admin" username="0119224303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119224303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119224303";
};

# المستخدم 166: 0163581106
:do {
    /tool user-manager user add customer="admin" username="0163581106" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163581106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163581106";
};

# المستخدم 167: 0147937993
:do {
    /tool user-manager user add customer="admin" username="0147937993" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147937993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147937993";
};

# المستخدم 168: 0135464900
:do {
    /tool user-manager user add customer="admin" username="0135464900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135464900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135464900";
};

# المستخدم 169: 0161328299
:do {
    /tool user-manager user add customer="admin" username="0161328299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161328299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161328299";
};

# المستخدم 170: 0152193220
:do {
    /tool user-manager user add customer="admin" username="0152193220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152193220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152193220";
};

# المستخدم 171: 0123867759
:do {
    /tool user-manager user add customer="admin" username="0123867759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123867759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123867759";
};

# المستخدم 172: 0174924428
:do {
    /tool user-manager user add customer="admin" username="0174924428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174924428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174924428";
};

# المستخدم 173: 0154674560
:do {
    /tool user-manager user add customer="admin" username="0154674560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154674560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154674560";
};

# المستخدم 174: 0162536344
:do {
    /tool user-manager user add customer="admin" username="0162536344" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162536344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162536344";
};

# المستخدم 175: 0198985504
:do {
    /tool user-manager user add customer="admin" username="0198985504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198985504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198985504";
};

# المستخدم 176: 0148146650
:do {
    /tool user-manager user add customer="admin" username="0148146650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148146650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148146650";
};

# المستخدم 177: 0172040054
:do {
    /tool user-manager user add customer="admin" username="0172040054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172040054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172040054";
};

# المستخدم 178: 0178924019
:do {
    /tool user-manager user add customer="admin" username="0178924019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178924019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178924019";
};

# المستخدم 179: 0188393316
:do {
    /tool user-manager user add customer="admin" username="0188393316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188393316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188393316";
};

# المستخدم 180: 0120544917
:do {
    /tool user-manager user add customer="admin" username="0120544917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120544917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120544917";
};

# المستخدم 181: 0167379332
:do {
    /tool user-manager user add customer="admin" username="0167379332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167379332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167379332";
};

# المستخدم 182: 0126491002
:do {
    /tool user-manager user add customer="admin" username="0126491002" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126491002";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126491002";
};

# المستخدم 183: 0174690968
:do {
    /tool user-manager user add customer="admin" username="0174690968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174690968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174690968";
};

# المستخدم 184: 0185683963
:do {
    /tool user-manager user add customer="admin" username="0185683963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185683963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185683963";
};

# المستخدم 185: 0112346209
:do {
    /tool user-manager user add customer="admin" username="0112346209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112346209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112346209";
};

# المستخدم 186: 0134020700
:do {
    /tool user-manager user add customer="admin" username="0134020700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134020700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134020700";
};

# المستخدم 187: 0116998067
:do {
    /tool user-manager user add customer="admin" username="0116998067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116998067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116998067";
};

# المستخدم 188: 0143002873
:do {
    /tool user-manager user add customer="admin" username="0143002873" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143002873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143002873";
};

# المستخدم 189: 0159001019
:do {
    /tool user-manager user add customer="admin" username="0159001019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159001019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159001019";
};

# المستخدم 190: 0172803235
:do {
    /tool user-manager user add customer="admin" username="0172803235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172803235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172803235";
};

# المستخدم 191: 0168107400
:do {
    /tool user-manager user add customer="admin" username="0168107400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168107400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168107400";
};

# المستخدم 192: 0154132542
:do {
    /tool user-manager user add customer="admin" username="0154132542" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154132542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154132542";
};

# المستخدم 193: 0192101269
:do {
    /tool user-manager user add customer="admin" username="0192101269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192101269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192101269";
};

# المستخدم 194: 0179696911
:do {
    /tool user-manager user add customer="admin" username="0179696911" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179696911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179696911";
};

# المستخدم 195: 0198521631
:do {
    /tool user-manager user add customer="admin" username="0198521631" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198521631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198521631";
};

# المستخدم 196: 0113599462
:do {
    /tool user-manager user add customer="admin" username="0113599462" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113599462";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113599462";
};

# المستخدم 197: 0152801084
:do {
    /tool user-manager user add customer="admin" username="0152801084" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152801084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152801084";
};

# المستخدم 198: 0140484760
:do {
    /tool user-manager user add customer="admin" username="0140484760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140484760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140484760";
};

# المستخدم 199: 0113813205
:do {
    /tool user-manager user add customer="admin" username="0113813205" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113813205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113813205";
};

# المستخدم 200: 0119302081
:do {
    /tool user-manager user add customer="admin" username="0119302081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119302081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119302081";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
