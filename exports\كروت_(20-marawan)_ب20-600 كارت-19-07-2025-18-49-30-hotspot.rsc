# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-19 18:49:31
# القالب: 20-marawan
# النظام: hotspot
# عدد الكروت: 600
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 20-marawan";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 600";

:local success 0;
:local errors 0;
:local total 600;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 600 مستخدم Hotspot...";

# المستخدم 1: 2031168652
:do {
    /ip hotspot user add name="2031168652" password="5193" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031168652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031168652";
};

# المستخدم 2: 2008362997
:do {
    /ip hotspot user add name="2008362997" password="9016" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008362997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008362997";
};

# المستخدم 3: 2005373402
:do {
    /ip hotspot user add name="2005373402" password="4309" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005373402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005373402";
};

# المستخدم 4: 2069606792
:do {
    /ip hotspot user add name="2069606792" password="8328" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069606792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069606792";
};

# المستخدم 5: 2091333568
:do {
    /ip hotspot user add name="2091333568" password="9775" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091333568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091333568";
};

# المستخدم 6: 2017893267
:do {
    /ip hotspot user add name="2017893267" password="9819" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017893267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017893267";
};

# المستخدم 7: 2093517664
:do {
    /ip hotspot user add name="2093517664" password="5611" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093517664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093517664";
};

# المستخدم 8: 2000833753
:do {
    /ip hotspot user add name="2000833753" password="5296" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000833753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000833753";
};

# المستخدم 9: 2088966707
:do {
    /ip hotspot user add name="2088966707" password="4408" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088966707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088966707";
};

# المستخدم 10: 2015471051
:do {
    /ip hotspot user add name="2015471051" password="2837" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015471051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015471051";
};

# المستخدم 11: 2056223164
:do {
    /ip hotspot user add name="2056223164" password="8225" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056223164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056223164";
};

# المستخدم 12: 2066701179
:do {
    /ip hotspot user add name="2066701179" password="4385" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066701179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066701179";
};

# المستخدم 13: 2063310897
:do {
    /ip hotspot user add name="2063310897" password="6062" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063310897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063310897";
};

# المستخدم 14: 2048362194
:do {
    /ip hotspot user add name="2048362194" password="1748" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048362194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048362194";
};

# المستخدم 15: 2087220458
:do {
    /ip hotspot user add name="2087220458" password="9207" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087220458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087220458";
};

# المستخدم 16: 2005743332
:do {
    /ip hotspot user add name="2005743332" password="9397" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005743332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005743332";
};

# المستخدم 17: 2076369807
:do {
    /ip hotspot user add name="2076369807" password="0028" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076369807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076369807";
};

# المستخدم 18: 2000581047
:do {
    /ip hotspot user add name="2000581047" password="5357" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000581047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000581047";
};

# المستخدم 19: 2041766330
:do {
    /ip hotspot user add name="2041766330" password="1143" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041766330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041766330";
};

# المستخدم 20: 2081481388
:do {
    /ip hotspot user add name="2081481388" password="5875" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081481388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081481388";
};

# المستخدم 21: 2081311141
:do {
    /ip hotspot user add name="2081311141" password="9286" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081311141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081311141";
};

# المستخدم 22: 2060242761
:do {
    /ip hotspot user add name="2060242761" password="7414" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060242761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060242761";
};

# المستخدم 23: 2034931249
:do {
    /ip hotspot user add name="2034931249" password="1245" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034931249";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034931249";
};

# المستخدم 24: 2005789116
:do {
    /ip hotspot user add name="2005789116" password="9251" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005789116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005789116";
};

# المستخدم 25: 2069524443
:do {
    /ip hotspot user add name="2069524443" password="0131" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069524443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069524443";
};

# المستخدم 26: 2056740244
:do {
    /ip hotspot user add name="2056740244" password="6067" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056740244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056740244";
};

# المستخدم 27: 2008000141
:do {
    /ip hotspot user add name="2008000141" password="7241" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008000141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008000141";
};

# المستخدم 28: 2060908224
:do {
    /ip hotspot user add name="2060908224" password="5466" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060908224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060908224";
};

# المستخدم 29: 2073291203
:do {
    /ip hotspot user add name="2073291203" password="9750" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073291203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073291203";
};

# المستخدم 30: 2003318533
:do {
    /ip hotspot user add name="2003318533" password="2773" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003318533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003318533";
};

# المستخدم 31: 2085450651
:do {
    /ip hotspot user add name="2085450651" password="3429" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085450651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085450651";
};

# المستخدم 32: 2029842012
:do {
    /ip hotspot user add name="2029842012" password="3523" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029842012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029842012";
};

# المستخدم 33: 2002227129
:do {
    /ip hotspot user add name="2002227129" password="0458" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002227129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002227129";
};

# المستخدم 34: 2054387647
:do {
    /ip hotspot user add name="2054387647" password="8571" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054387647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054387647";
};

# المستخدم 35: 2011178302
:do {
    /ip hotspot user add name="2011178302" password="1033" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011178302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011178302";
};

# المستخدم 36: 2056776373
:do {
    /ip hotspot user add name="2056776373" password="6302" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056776373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056776373";
};

# المستخدم 37: 2050952863
:do {
    /ip hotspot user add name="2050952863" password="7488" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050952863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050952863";
};

# المستخدم 38: 2049771825
:do {
    /ip hotspot user add name="2049771825" password="4454" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049771825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049771825";
};

# المستخدم 39: 2038650818
:do {
    /ip hotspot user add name="2038650818" password="7412" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038650818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038650818";
};

# المستخدم 40: 2084341089
:do {
    /ip hotspot user add name="2084341089" password="3030" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084341089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084341089";
};

# المستخدم 41: 2064605991
:do {
    /ip hotspot user add name="2064605991" password="8442" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064605991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064605991";
};

# المستخدم 42: 2033987883
:do {
    /ip hotspot user add name="2033987883" password="9199" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033987883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033987883";
};

# المستخدم 43: 2056903510
:do {
    /ip hotspot user add name="2056903510" password="2092" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056903510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056903510";
};

# المستخدم 44: 2035557454
:do {
    /ip hotspot user add name="2035557454" password="7516" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035557454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035557454";
};

# المستخدم 45: 2070421461
:do {
    /ip hotspot user add name="2070421461" password="5168" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070421461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070421461";
};

# المستخدم 46: 2081544484
:do {
    /ip hotspot user add name="2081544484" password="7308" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081544484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081544484";
};

# المستخدم 47: 2045884066
:do {
    /ip hotspot user add name="2045884066" password="4137" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045884066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045884066";
};

# المستخدم 48: 2027378427
:do {
    /ip hotspot user add name="2027378427" password="4153" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027378427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027378427";
};

# المستخدم 49: 2085857847
:do {
    /ip hotspot user add name="2085857847" password="3041" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085857847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085857847";
};

# المستخدم 50: 2032956668
:do {
    /ip hotspot user add name="2032956668" password="1122" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032956668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032956668";
};

# المستخدم 51: 2030785613
:do {
    /ip hotspot user add name="2030785613" password="2987" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030785613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030785613";
};

# المستخدم 52: 2037776899
:do {
    /ip hotspot user add name="2037776899" password="5746" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037776899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037776899";
};

# المستخدم 53: 2021663086
:do {
    /ip hotspot user add name="2021663086" password="9518" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021663086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021663086";
};

# المستخدم 54: 2070665689
:do {
    /ip hotspot user add name="2070665689" password="8727" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070665689";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070665689";
};

# المستخدم 55: 2066930034
:do {
    /ip hotspot user add name="2066930034" password="6532" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066930034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066930034";
};

# المستخدم 56: 2040949079
:do {
    /ip hotspot user add name="2040949079" password="7320" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040949079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040949079";
};

# المستخدم 57: 2060377779
:do {
    /ip hotspot user add name="2060377779" password="1407" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060377779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060377779";
};

# المستخدم 58: 2064605038
:do {
    /ip hotspot user add name="2064605038" password="6802" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064605038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064605038";
};

# المستخدم 59: 2019549363
:do {
    /ip hotspot user add name="2019549363" password="7245" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019549363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019549363";
};

# المستخدم 60: 2017491437
:do {
    /ip hotspot user add name="2017491437" password="5668" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017491437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017491437";
};

# المستخدم 61: 2048361302
:do {
    /ip hotspot user add name="2048361302" password="2161" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048361302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048361302";
};

# المستخدم 62: 2021191285
:do {
    /ip hotspot user add name="2021191285" password="0040" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021191285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021191285";
};

# المستخدم 63: 2084354276
:do {
    /ip hotspot user add name="2084354276" password="1613" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084354276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084354276";
};

# المستخدم 64: 2063952048
:do {
    /ip hotspot user add name="2063952048" password="1188" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063952048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063952048";
};

# المستخدم 65: 2079619330
:do {
    /ip hotspot user add name="2079619330" password="2628" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079619330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079619330";
};

# المستخدم 66: 2076468382
:do {
    /ip hotspot user add name="2076468382" password="9209" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076468382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076468382";
};

# المستخدم 67: 2016954737
:do {
    /ip hotspot user add name="2016954737" password="4334" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016954737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016954737";
};

# المستخدم 68: 2088218677
:do {
    /ip hotspot user add name="2088218677" password="4786" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088218677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088218677";
};

# المستخدم 69: 2071208530
:do {
    /ip hotspot user add name="2071208530" password="3445" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071208530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071208530";
};

# المستخدم 70: 2095432910
:do {
    /ip hotspot user add name="2095432910" password="6745" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095432910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095432910";
};

# المستخدم 71: 2055117927
:do {
    /ip hotspot user add name="2055117927" password="8533" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055117927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055117927";
};

# المستخدم 72: 2029871009
:do {
    /ip hotspot user add name="2029871009" password="3098" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029871009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029871009";
};

# المستخدم 73: 2012106108
:do {
    /ip hotspot user add name="2012106108" password="9255" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012106108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012106108";
};

# المستخدم 74: 2064707610
:do {
    /ip hotspot user add name="2064707610" password="1613" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064707610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064707610";
};

# المستخدم 75: 2089249697
:do {
    /ip hotspot user add name="2089249697" password="5958" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089249697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089249697";
};

# المستخدم 76: 2087859823
:do {
    /ip hotspot user add name="2087859823" password="0197" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087859823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087859823";
};

# المستخدم 77: 2046744671
:do {
    /ip hotspot user add name="2046744671" password="0397" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046744671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046744671";
};

# المستخدم 78: 2086786188
:do {
    /ip hotspot user add name="2086786188" password="0487" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086786188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086786188";
};

# المستخدم 79: 2013142729
:do {
    /ip hotspot user add name="2013142729" password="8846" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013142729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013142729";
};

# المستخدم 80: 2052340079
:do {
    /ip hotspot user add name="2052340079" password="1267" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052340079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052340079";
};

# المستخدم 81: 2063159205
:do {
    /ip hotspot user add name="2063159205" password="2731" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063159205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063159205";
};

# المستخدم 82: 2061276619
:do {
    /ip hotspot user add name="2061276619" password="9347" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061276619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061276619";
};

# المستخدم 83: 2072738894
:do {
    /ip hotspot user add name="2072738894" password="9221" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072738894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072738894";
};

# المستخدم 84: 2054120044
:do {
    /ip hotspot user add name="2054120044" password="8903" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054120044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054120044";
};

# المستخدم 85: 2038890277
:do {
    /ip hotspot user add name="2038890277" password="7932" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038890277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038890277";
};

# المستخدم 86: 2008396929
:do {
    /ip hotspot user add name="2008396929" password="8216" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008396929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008396929";
};

# المستخدم 87: 2011347282
:do {
    /ip hotspot user add name="2011347282" password="8917" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011347282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011347282";
};

# المستخدم 88: 2012312967
:do {
    /ip hotspot user add name="2012312967" password="7022" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012312967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012312967";
};

# المستخدم 89: 2010439391
:do {
    /ip hotspot user add name="2010439391" password="8015" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010439391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010439391";
};

# المستخدم 90: 2038541846
:do {
    /ip hotspot user add name="2038541846" password="4609" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038541846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038541846";
};

# المستخدم 91: 2052435531
:do {
    /ip hotspot user add name="2052435531" password="1029" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052435531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052435531";
};

# المستخدم 92: 2038541007
:do {
    /ip hotspot user add name="2038541007" password="5379" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038541007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038541007";
};

# المستخدم 93: 2051893574
:do {
    /ip hotspot user add name="2051893574" password="8116" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051893574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051893574";
};

# المستخدم 94: 2020652323
:do {
    /ip hotspot user add name="2020652323" password="5348" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020652323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020652323";
};

# المستخدم 95: 2033065435
:do {
    /ip hotspot user add name="2033065435" password="3840" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033065435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033065435";
};

# المستخدم 96: 2010092413
:do {
    /ip hotspot user add name="2010092413" password="7228" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010092413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010092413";
};

# المستخدم 97: 2031184569
:do {
    /ip hotspot user add name="2031184569" password="3598" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031184569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031184569";
};

# المستخدم 98: 2043869871
:do {
    /ip hotspot user add name="2043869871" password="4459" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043869871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043869871";
};

# المستخدم 99: 2019729587
:do {
    /ip hotspot user add name="2019729587" password="7041" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019729587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019729587";
};

# المستخدم 100: 2094965051
:do {
    /ip hotspot user add name="2094965051" password="1463" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094965051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094965051";
};

# المستخدم 101: 2045649645
:do {
    /ip hotspot user add name="2045649645" password="3891" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045649645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045649645";
};

# المستخدم 102: 2055131313
:do {
    /ip hotspot user add name="2055131313" password="9599" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055131313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055131313";
};

# المستخدم 103: 2079483424
:do {
    /ip hotspot user add name="2079483424" password="9875" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079483424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079483424";
};

# المستخدم 104: 2065258794
:do {
    /ip hotspot user add name="2065258794" password="3049" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065258794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065258794";
};

# المستخدم 105: 2005435683
:do {
    /ip hotspot user add name="2005435683" password="5171" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005435683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005435683";
};

# المستخدم 106: 2003781833
:do {
    /ip hotspot user add name="2003781833" password="6434" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003781833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003781833";
};

# المستخدم 107: 2095786454
:do {
    /ip hotspot user add name="2095786454" password="8245" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095786454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095786454";
};

# المستخدم 108: 2044687840
:do {
    /ip hotspot user add name="2044687840" password="3407" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044687840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044687840";
};

# المستخدم 109: 2027896933
:do {
    /ip hotspot user add name="2027896933" password="2675" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027896933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027896933";
};

# المستخدم 110: 2029788639
:do {
    /ip hotspot user add name="2029788639" password="6579" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029788639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029788639";
};

# المستخدم 111: 2065797408
:do {
    /ip hotspot user add name="2065797408" password="5118" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065797408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065797408";
};

# المستخدم 112: 2073164304
:do {
    /ip hotspot user add name="2073164304" password="8649" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073164304";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073164304";
};

# المستخدم 113: 2057929827
:do {
    /ip hotspot user add name="2057929827" password="0830" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057929827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057929827";
};

# المستخدم 114: 2096052318
:do {
    /ip hotspot user add name="2096052318" password="3201" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096052318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096052318";
};

# المستخدم 115: 2084443828
:do {
    /ip hotspot user add name="2084443828" password="0361" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084443828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084443828";
};

# المستخدم 116: 2090063914
:do {
    /ip hotspot user add name="2090063914" password="3836" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090063914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090063914";
};

# المستخدم 117: 2065073277
:do {
    /ip hotspot user add name="2065073277" password="4017" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065073277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065073277";
};

# المستخدم 118: 2028797267
:do {
    /ip hotspot user add name="2028797267" password="4761" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028797267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028797267";
};

# المستخدم 119: 2096539240
:do {
    /ip hotspot user add name="2096539240" password="8258" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096539240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096539240";
};

# المستخدم 120: 2011256312
:do {
    /ip hotspot user add name="2011256312" password="8118" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011256312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011256312";
};

# المستخدم 121: 2045335086
:do {
    /ip hotspot user add name="2045335086" password="5026" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045335086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045335086";
};

# المستخدم 122: 2096445742
:do {
    /ip hotspot user add name="2096445742" password="8489" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096445742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096445742";
};

# المستخدم 123: 2010680563
:do {
    /ip hotspot user add name="2010680563" password="4484" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010680563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010680563";
};

# المستخدم 124: 2078948996
:do {
    /ip hotspot user add name="2078948996" password="6198" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078948996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078948996";
};

# المستخدم 125: 2001129657
:do {
    /ip hotspot user add name="2001129657" password="0819" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001129657";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001129657";
};

# المستخدم 126: 2096301944
:do {
    /ip hotspot user add name="2096301944" password="3792" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096301944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096301944";
};

# المستخدم 127: 2039504661
:do {
    /ip hotspot user add name="2039504661" password="3000" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039504661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039504661";
};

# المستخدم 128: 2061603187
:do {
    /ip hotspot user add name="2061603187" password="7989" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061603187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061603187";
};

# المستخدم 129: 2019161935
:do {
    /ip hotspot user add name="2019161935" password="0047" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019161935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019161935";
};

# المستخدم 130: 2007969379
:do {
    /ip hotspot user add name="2007969379" password="3792" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007969379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007969379";
};

# المستخدم 131: 2033401273
:do {
    /ip hotspot user add name="2033401273" password="2426" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033401273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033401273";
};

# المستخدم 132: 2050212380
:do {
    /ip hotspot user add name="2050212380" password="6199" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050212380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050212380";
};

# المستخدم 133: 2089752844
:do {
    /ip hotspot user add name="2089752844" password="7666" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089752844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089752844";
};

# المستخدم 134: 2051939836
:do {
    /ip hotspot user add name="2051939836" password="4206" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051939836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051939836";
};

# المستخدم 135: 2017113863
:do {
    /ip hotspot user add name="2017113863" password="2660" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017113863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017113863";
};

# المستخدم 136: 2010188814
:do {
    /ip hotspot user add name="2010188814" password="3260" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010188814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010188814";
};

# المستخدم 137: 2056505923
:do {
    /ip hotspot user add name="2056505923" password="7836" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056505923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056505923";
};

# المستخدم 138: 2083726118
:do {
    /ip hotspot user add name="2083726118" password="1721" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083726118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083726118";
};

# المستخدم 139: 2036928753
:do {
    /ip hotspot user add name="2036928753" password="7163" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036928753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036928753";
};

# المستخدم 140: 2022954647
:do {
    /ip hotspot user add name="2022954647" password="6023" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022954647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022954647";
};

# المستخدم 141: 2003974727
:do {
    /ip hotspot user add name="2003974727" password="3152" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003974727";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003974727";
};

# المستخدم 142: 2024411753
:do {
    /ip hotspot user add name="2024411753" password="5738" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024411753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024411753";
};

# المستخدم 143: 2086485663
:do {
    /ip hotspot user add name="2086485663" password="4452" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086485663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086485663";
};

# المستخدم 144: 2037696479
:do {
    /ip hotspot user add name="2037696479" password="5070" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037696479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037696479";
};

# المستخدم 145: 2040646293
:do {
    /ip hotspot user add name="2040646293" password="3326" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040646293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040646293";
};

# المستخدم 146: 2050954727
:do {
    /ip hotspot user add name="2050954727" password="2662" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050954727";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050954727";
};

# المستخدم 147: 2041798840
:do {
    /ip hotspot user add name="2041798840" password="3811" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041798840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041798840";
};

# المستخدم 148: 2088529339
:do {
    /ip hotspot user add name="2088529339" password="9023" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088529339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088529339";
};

# المستخدم 149: 2053461448
:do {
    /ip hotspot user add name="2053461448" password="8791" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053461448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053461448";
};

# المستخدم 150: 2091434214
:do {
    /ip hotspot user add name="2091434214" password="2159" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091434214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091434214";
};

# المستخدم 151: 2083353187
:do {
    /ip hotspot user add name="2083353187" password="2039" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083353187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083353187";
};

# المستخدم 152: 2075342522
:do {
    /ip hotspot user add name="2075342522" password="9232" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075342522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075342522";
};

# المستخدم 153: 2068722534
:do {
    /ip hotspot user add name="2068722534" password="7611" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068722534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068722534";
};

# المستخدم 154: 2027845810
:do {
    /ip hotspot user add name="2027845810" password="7916" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027845810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027845810";
};

# المستخدم 155: 2001181994
:do {
    /ip hotspot user add name="2001181994" password="4746" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001181994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001181994";
};

# المستخدم 156: 2027622419
:do {
    /ip hotspot user add name="2027622419" password="7371" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027622419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027622419";
};

# المستخدم 157: 2020164412
:do {
    /ip hotspot user add name="2020164412" password="2631" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020164412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020164412";
};

# المستخدم 158: 2025736746
:do {
    /ip hotspot user add name="2025736746" password="1779" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025736746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025736746";
};

# المستخدم 159: 2011222722
:do {
    /ip hotspot user add name="2011222722" password="1665" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011222722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011222722";
};

# المستخدم 160: 2027177047
:do {
    /ip hotspot user add name="2027177047" password="8036" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027177047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027177047";
};

# المستخدم 161: 2066966841
:do {
    /ip hotspot user add name="2066966841" password="0167" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066966841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066966841";
};

# المستخدم 162: 2077583538
:do {
    /ip hotspot user add name="2077583538" password="6619" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077583538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077583538";
};

# المستخدم 163: 2081207101
:do {
    /ip hotspot user add name="2081207101" password="5437" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081207101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081207101";
};

# المستخدم 164: 2025273562
:do {
    /ip hotspot user add name="2025273562" password="7973" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025273562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025273562";
};

# المستخدم 165: 2018838754
:do {
    /ip hotspot user add name="2018838754" password="7409" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018838754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018838754";
};

# المستخدم 166: 2021503708
:do {
    /ip hotspot user add name="2021503708" password="4198" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021503708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021503708";
};

# المستخدم 167: 2085431001
:do {
    /ip hotspot user add name="2085431001" password="5642" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085431001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085431001";
};

# المستخدم 168: 2076955443
:do {
    /ip hotspot user add name="2076955443" password="6476" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076955443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076955443";
};

# المستخدم 169: 2043383004
:do {
    /ip hotspot user add name="2043383004" password="3536" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043383004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043383004";
};

# المستخدم 170: 2054446251
:do {
    /ip hotspot user add name="2054446251" password="9688" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054446251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054446251";
};

# المستخدم 171: 2082880071
:do {
    /ip hotspot user add name="2082880071" password="9488" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082880071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082880071";
};

# المستخدم 172: 2080219113
:do {
    /ip hotspot user add name="2080219113" password="7670" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080219113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080219113";
};

# المستخدم 173: 2057598874
:do {
    /ip hotspot user add name="2057598874" password="5375" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057598874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057598874";
};

# المستخدم 174: 2046319382
:do {
    /ip hotspot user add name="2046319382" password="7243" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046319382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046319382";
};

# المستخدم 175: 2057437089
:do {
    /ip hotspot user add name="2057437089" password="9147" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057437089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057437089";
};

# المستخدم 176: 2084558375
:do {
    /ip hotspot user add name="2084558375" password="6589" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084558375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084558375";
};

# المستخدم 177: 2057218755
:do {
    /ip hotspot user add name="2057218755" password="5067" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057218755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057218755";
};

# المستخدم 178: 2007448063
:do {
    /ip hotspot user add name="2007448063" password="2898" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007448063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007448063";
};

# المستخدم 179: 2064880225
:do {
    /ip hotspot user add name="2064880225" password="1931" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064880225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064880225";
};

# المستخدم 180: 2000126536
:do {
    /ip hotspot user add name="2000126536" password="5157" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000126536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000126536";
};

# المستخدم 181: 2055585840
:do {
    /ip hotspot user add name="2055585840" password="2314" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055585840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055585840";
};

# المستخدم 182: 2049690998
:do {
    /ip hotspot user add name="2049690998" password="4761" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049690998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049690998";
};

# المستخدم 183: 2098769708
:do {
    /ip hotspot user add name="2098769708" password="4737" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098769708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098769708";
};

# المستخدم 184: 2076082533
:do {
    /ip hotspot user add name="2076082533" password="7067" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076082533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076082533";
};

# المستخدم 185: 2072388603
:do {
    /ip hotspot user add name="2072388603" password="7101" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072388603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072388603";
};

# المستخدم 186: 2056902179
:do {
    /ip hotspot user add name="2056902179" password="4972" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056902179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056902179";
};

# المستخدم 187: 2096454156
:do {
    /ip hotspot user add name="2096454156" password="9760" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096454156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096454156";
};

# المستخدم 188: 2058917707
:do {
    /ip hotspot user add name="2058917707" password="7142" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058917707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058917707";
};

# المستخدم 189: 2023246407
:do {
    /ip hotspot user add name="2023246407" password="9803" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023246407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023246407";
};

# المستخدم 190: 2058727200
:do {
    /ip hotspot user add name="2058727200" password="2574" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058727200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058727200";
};

# المستخدم 191: 2063837858
:do {
    /ip hotspot user add name="2063837858" password="5424" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063837858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063837858";
};

# المستخدم 192: 2041185692
:do {
    /ip hotspot user add name="2041185692" password="6549" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041185692";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041185692";
};

# المستخدم 193: 2092492661
:do {
    /ip hotspot user add name="2092492661" password="6481" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092492661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092492661";
};

# المستخدم 194: 2003602396
:do {
    /ip hotspot user add name="2003602396" password="6244" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003602396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003602396";
};

# المستخدم 195: 2066180398
:do {
    /ip hotspot user add name="2066180398" password="5992" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066180398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066180398";
};

# المستخدم 196: 2085381532
:do {
    /ip hotspot user add name="2085381532" password="4150" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085381532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085381532";
};

# المستخدم 197: 2021804775
:do {
    /ip hotspot user add name="2021804775" password="4615" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021804775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021804775";
};

# المستخدم 198: 2046852435
:do {
    /ip hotspot user add name="2046852435" password="1983" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046852435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046852435";
};

# المستخدم 199: 2038497521
:do {
    /ip hotspot user add name="2038497521" password="4599" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038497521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038497521";
};

# المستخدم 200: 2079560196
:do {
    /ip hotspot user add name="2079560196" password="5348" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079560196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079560196";
};

# المستخدم 201: 2026348138
:do {
    /ip hotspot user add name="2026348138" password="8998" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026348138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026348138";
};

# المستخدم 202: 2056948856
:do {
    /ip hotspot user add name="2056948856" password="4733" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056948856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056948856";
};

# المستخدم 203: 2083177423
:do {
    /ip hotspot user add name="2083177423" password="5325" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083177423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083177423";
};

# المستخدم 204: 2056367521
:do {
    /ip hotspot user add name="2056367521" password="0960" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056367521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056367521";
};

# المستخدم 205: 2035026649
:do {
    /ip hotspot user add name="2035026649" password="4603" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035026649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035026649";
};

# المستخدم 206: 2067867630
:do {
    /ip hotspot user add name="2067867630" password="3403" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067867630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067867630";
};

# المستخدم 207: 2033759145
:do {
    /ip hotspot user add name="2033759145" password="4097" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033759145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033759145";
};

# المستخدم 208: 2037722312
:do {
    /ip hotspot user add name="2037722312" password="4733" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037722312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037722312";
};

# المستخدم 209: 2052282656
:do {
    /ip hotspot user add name="2052282656" password="1967" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052282656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052282656";
};

# المستخدم 210: 2073342034
:do {
    /ip hotspot user add name="2073342034" password="5072" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073342034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073342034";
};

# المستخدم 211: 2034166907
:do {
    /ip hotspot user add name="2034166907" password="5640" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034166907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034166907";
};

# المستخدم 212: 2024186735
:do {
    /ip hotspot user add name="2024186735" password="7799" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024186735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024186735";
};

# المستخدم 213: 2082036852
:do {
    /ip hotspot user add name="2082036852" password="2682" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082036852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082036852";
};

# المستخدم 214: 2006589150
:do {
    /ip hotspot user add name="2006589150" password="5949" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006589150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006589150";
};

# المستخدم 215: 2091428215
:do {
    /ip hotspot user add name="2091428215" password="8171" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091428215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091428215";
};

# المستخدم 216: 2078019988
:do {
    /ip hotspot user add name="2078019988" password="1407" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078019988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078019988";
};

# المستخدم 217: 2083622360
:do {
    /ip hotspot user add name="2083622360" password="8761" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083622360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083622360";
};

# المستخدم 218: 2003697356
:do {
    /ip hotspot user add name="2003697356" password="8296" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003697356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003697356";
};

# المستخدم 219: 2022037090
:do {
    /ip hotspot user add name="2022037090" password="1169" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022037090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022037090";
};

# المستخدم 220: 2054207219
:do {
    /ip hotspot user add name="2054207219" password="2910" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054207219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054207219";
};

# المستخدم 221: 2074673136
:do {
    /ip hotspot user add name="2074673136" password="6956" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074673136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074673136";
};

# المستخدم 222: 2090871607
:do {
    /ip hotspot user add name="2090871607" password="2462" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090871607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090871607";
};

# المستخدم 223: 2029032773
:do {
    /ip hotspot user add name="2029032773" password="1849" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029032773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029032773";
};

# المستخدم 224: 2000649743
:do {
    /ip hotspot user add name="2000649743" password="2293" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000649743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000649743";
};

# المستخدم 225: 2005654580
:do {
    /ip hotspot user add name="2005654580" password="3715" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005654580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005654580";
};

# المستخدم 226: 2059022370
:do {
    /ip hotspot user add name="2059022370" password="5319" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059022370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059022370";
};

# المستخدم 227: 2061684761
:do {
    /ip hotspot user add name="2061684761" password="6875" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061684761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061684761";
};

# المستخدم 228: 2007814448
:do {
    /ip hotspot user add name="2007814448" password="3577" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007814448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007814448";
};

# المستخدم 229: 2012592819
:do {
    /ip hotspot user add name="2012592819" password="3705" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012592819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012592819";
};

# المستخدم 230: 2021564742
:do {
    /ip hotspot user add name="2021564742" password="5251" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021564742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021564742";
};

# المستخدم 231: 2091809856
:do {
    /ip hotspot user add name="2091809856" password="8848" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091809856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091809856";
};

# المستخدم 232: 2050221390
:do {
    /ip hotspot user add name="2050221390" password="8635" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050221390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050221390";
};

# المستخدم 233: 2066607851
:do {
    /ip hotspot user add name="2066607851" password="5665" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066607851";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066607851";
};

# المستخدم 234: 2024340782
:do {
    /ip hotspot user add name="2024340782" password="6763" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024340782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024340782";
};

# المستخدم 235: 2089793295
:do {
    /ip hotspot user add name="2089793295" password="9917" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089793295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089793295";
};

# المستخدم 236: 2021960762
:do {
    /ip hotspot user add name="2021960762" password="5735" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021960762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021960762";
};

# المستخدم 237: 2013543959
:do {
    /ip hotspot user add name="2013543959" password="5589" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013543959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013543959";
};

# المستخدم 238: 2039871216
:do {
    /ip hotspot user add name="2039871216" password="8854" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039871216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039871216";
};

# المستخدم 239: 2097722400
:do {
    /ip hotspot user add name="2097722400" password="3000" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097722400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097722400";
};

# المستخدم 240: 2082286062
:do {
    /ip hotspot user add name="2082286062" password="3488" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082286062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082286062";
};

# المستخدم 241: 2030869505
:do {
    /ip hotspot user add name="2030869505" password="7749" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030869505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030869505";
};

# المستخدم 242: 2069311012
:do {
    /ip hotspot user add name="2069311012" password="1126" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069311012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069311012";
};

# المستخدم 243: 2060102812
:do {
    /ip hotspot user add name="2060102812" password="5106" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060102812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060102812";
};

# المستخدم 244: 2040253333
:do {
    /ip hotspot user add name="2040253333" password="6720" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040253333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040253333";
};

# المستخدم 245: 2095313905
:do {
    /ip hotspot user add name="2095313905" password="7535" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095313905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095313905";
};

# المستخدم 246: 2033923216
:do {
    /ip hotspot user add name="2033923216" password="7054" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033923216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033923216";
};

# المستخدم 247: 2084133576
:do {
    /ip hotspot user add name="2084133576" password="9956" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084133576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084133576";
};

# المستخدم 248: 2006621520
:do {
    /ip hotspot user add name="2006621520" password="9515" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006621520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006621520";
};

# المستخدم 249: 2029622555
:do {
    /ip hotspot user add name="2029622555" password="5969" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029622555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029622555";
};

# المستخدم 250: 2020152099
:do {
    /ip hotspot user add name="2020152099" password="8650" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020152099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020152099";
};

# المستخدم 251: 2020951489
:do {
    /ip hotspot user add name="2020951489" password="6607" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020951489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020951489";
};

# المستخدم 252: 2007441600
:do {
    /ip hotspot user add name="2007441600" password="1852" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007441600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007441600";
};

# المستخدم 253: 2040337087
:do {
    /ip hotspot user add name="2040337087" password="5229" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040337087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040337087";
};

# المستخدم 254: 2040688804
:do {
    /ip hotspot user add name="2040688804" password="8030" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040688804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040688804";
};

# المستخدم 255: 2070082761
:do {
    /ip hotspot user add name="2070082761" password="5420" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070082761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070082761";
};

# المستخدم 256: 2013063093
:do {
    /ip hotspot user add name="2013063093" password="0299" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013063093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013063093";
};

# المستخدم 257: 2067696969
:do {
    /ip hotspot user add name="2067696969" password="0701" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067696969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067696969";
};

# المستخدم 258: 2081609208
:do {
    /ip hotspot user add name="2081609208" password="7546" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081609208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081609208";
};

# المستخدم 259: 2019895854
:do {
    /ip hotspot user add name="2019895854" password="3072" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019895854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019895854";
};

# المستخدم 260: 2084824992
:do {
    /ip hotspot user add name="2084824992" password="8056" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084824992";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084824992";
};

# المستخدم 261: 2022522684
:do {
    /ip hotspot user add name="2022522684" password="0828" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022522684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022522684";
};

# المستخدم 262: 2060322771
:do {
    /ip hotspot user add name="2060322771" password="6590" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060322771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060322771";
};

# المستخدم 263: 2075167709
:do {
    /ip hotspot user add name="2075167709" password="0613" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075167709";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075167709";
};

# المستخدم 264: 2014695890
:do {
    /ip hotspot user add name="2014695890" password="1536" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014695890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014695890";
};

# المستخدم 265: 2012221870
:do {
    /ip hotspot user add name="2012221870" password="9178" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012221870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012221870";
};

# المستخدم 266: 2064918627
:do {
    /ip hotspot user add name="2064918627" password="8255" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064918627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064918627";
};

# المستخدم 267: 2042814463
:do {
    /ip hotspot user add name="2042814463" password="4451" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042814463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042814463";
};

# المستخدم 268: 2058481855
:do {
    /ip hotspot user add name="2058481855" password="0454" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058481855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058481855";
};

# المستخدم 269: 2038923658
:do {
    /ip hotspot user add name="2038923658" password="2574" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038923658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038923658";
};

# المستخدم 270: 2065993200
:do {
    /ip hotspot user add name="2065993200" password="5274" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065993200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065993200";
};

# المستخدم 271: 2059527720
:do {
    /ip hotspot user add name="2059527720" password="5622" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059527720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059527720";
};

# المستخدم 272: 2026633467
:do {
    /ip hotspot user add name="2026633467" password="1539" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026633467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026633467";
};

# المستخدم 273: 2014944216
:do {
    /ip hotspot user add name="2014944216" password="1125" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014944216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014944216";
};

# المستخدم 274: 2020936648
:do {
    /ip hotspot user add name="2020936648" password="3394" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020936648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020936648";
};

# المستخدم 275: 2032487127
:do {
    /ip hotspot user add name="2032487127" password="4458" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032487127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032487127";
};

# المستخدم 276: 2025912951
:do {
    /ip hotspot user add name="2025912951" password="2354" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025912951";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025912951";
};

# المستخدم 277: 2010382514
:do {
    /ip hotspot user add name="2010382514" password="6377" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010382514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010382514";
};

# المستخدم 278: 2083232164
:do {
    /ip hotspot user add name="2083232164" password="4951" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083232164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083232164";
};

# المستخدم 279: 2081912561
:do {
    /ip hotspot user add name="2081912561" password="2208" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081912561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081912561";
};

# المستخدم 280: 2086064380
:do {
    /ip hotspot user add name="2086064380" password="8628" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086064380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086064380";
};

# المستخدم 281: 2077292909
:do {
    /ip hotspot user add name="2077292909" password="6464" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077292909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077292909";
};

# المستخدم 282: 2004713143
:do {
    /ip hotspot user add name="2004713143" password="6643" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004713143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004713143";
};

# المستخدم 283: 2030797596
:do {
    /ip hotspot user add name="2030797596" password="7987" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030797596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030797596";
};

# المستخدم 284: 2036361391
:do {
    /ip hotspot user add name="2036361391" password="7433" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036361391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036361391";
};

# المستخدم 285: 2074344457
:do {
    /ip hotspot user add name="2074344457" password="2256" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074344457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074344457";
};

# المستخدم 286: 2096275576
:do {
    /ip hotspot user add name="2096275576" password="8900" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096275576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096275576";
};

# المستخدم 287: 2035035681
:do {
    /ip hotspot user add name="2035035681" password="0062" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035035681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035035681";
};

# المستخدم 288: 2010494959
:do {
    /ip hotspot user add name="2010494959" password="5173" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010494959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010494959";
};

# المستخدم 289: 2037176748
:do {
    /ip hotspot user add name="2037176748" password="6798" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037176748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037176748";
};

# المستخدم 290: 2017882730
:do {
    /ip hotspot user add name="2017882730" password="9120" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017882730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017882730";
};

# المستخدم 291: 2048525108
:do {
    /ip hotspot user add name="2048525108" password="7989" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048525108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048525108";
};

# المستخدم 292: 2017340750
:do {
    /ip hotspot user add name="2017340750" password="7101" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017340750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017340750";
};

# المستخدم 293: 2017845296
:do {
    /ip hotspot user add name="2017845296" password="7077" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017845296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017845296";
};

# المستخدم 294: 2031311533
:do {
    /ip hotspot user add name="2031311533" password="1031" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031311533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031311533";
};

# المستخدم 295: 2045745713
:do {
    /ip hotspot user add name="2045745713" password="8190" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045745713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045745713";
};

# المستخدم 296: 2031389829
:do {
    /ip hotspot user add name="2031389829" password="2081" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031389829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031389829";
};

# المستخدم 297: 2057674740
:do {
    /ip hotspot user add name="2057674740" password="2293" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057674740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057674740";
};

# المستخدم 298: 2044417920
:do {
    /ip hotspot user add name="2044417920" password="3473" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044417920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044417920";
};

# المستخدم 299: 2003448688
:do {
    /ip hotspot user add name="2003448688" password="1285" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003448688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003448688";
};

# المستخدم 300: 2041645090
:do {
    /ip hotspot user add name="2041645090" password="8155" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041645090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041645090";
};

# المستخدم 301: 2093233020
:do {
    /ip hotspot user add name="2093233020" password="8728" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093233020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093233020";
};

# المستخدم 302: 2058332933
:do {
    /ip hotspot user add name="2058332933" password="8501" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058332933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058332933";
};

# المستخدم 303: 2019400475
:do {
    /ip hotspot user add name="2019400475" password="7195" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019400475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019400475";
};

# المستخدم 304: 2048704862
:do {
    /ip hotspot user add name="2048704862" password="0128" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048704862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048704862";
};

# المستخدم 305: 2075230124
:do {
    /ip hotspot user add name="2075230124" password="1779" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075230124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075230124";
};

# المستخدم 306: 2064594934
:do {
    /ip hotspot user add name="2064594934" password="0426" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064594934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064594934";
};

# المستخدم 307: 2077630701
:do {
    /ip hotspot user add name="2077630701" password="8622" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077630701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077630701";
};

# المستخدم 308: 2082239590
:do {
    /ip hotspot user add name="2082239590" password="1512" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082239590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082239590";
};

# المستخدم 309: 2030355255
:do {
    /ip hotspot user add name="2030355255" password="1991" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030355255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030355255";
};

# المستخدم 310: 2065683999
:do {
    /ip hotspot user add name="2065683999" password="8031" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065683999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065683999";
};

# المستخدم 311: 2023896626
:do {
    /ip hotspot user add name="2023896626" password="3308" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023896626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023896626";
};

# المستخدم 312: 2080840178
:do {
    /ip hotspot user add name="2080840178" password="7762" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080840178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080840178";
};

# المستخدم 313: 2084208661
:do {
    /ip hotspot user add name="2084208661" password="2752" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084208661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084208661";
};

# المستخدم 314: 2020441227
:do {
    /ip hotspot user add name="2020441227" password="4519" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020441227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020441227";
};

# المستخدم 315: 2040793248
:do {
    /ip hotspot user add name="2040793248" password="1089" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040793248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040793248";
};

# المستخدم 316: 2077916866
:do {
    /ip hotspot user add name="2077916866" password="3029" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077916866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077916866";
};

# المستخدم 317: 2037624293
:do {
    /ip hotspot user add name="2037624293" password="4049" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037624293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037624293";
};

# المستخدم 318: 2042983209
:do {
    /ip hotspot user add name="2042983209" password="2785" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042983209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042983209";
};

# المستخدم 319: 2032525739
:do {
    /ip hotspot user add name="2032525739" password="8089" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032525739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032525739";
};

# المستخدم 320: 2027290541
:do {
    /ip hotspot user add name="2027290541" password="7808" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027290541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027290541";
};

# المستخدم 321: 2028963489
:do {
    /ip hotspot user add name="2028963489" password="0619" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028963489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028963489";
};

# المستخدم 322: 2091870334
:do {
    /ip hotspot user add name="2091870334" password="9262" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091870334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091870334";
};

# المستخدم 323: 2018163584
:do {
    /ip hotspot user add name="2018163584" password="5104" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018163584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018163584";
};

# المستخدم 324: 2099406592
:do {
    /ip hotspot user add name="2099406592" password="5214" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099406592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099406592";
};

# المستخدم 325: 2085082020
:do {
    /ip hotspot user add name="2085082020" password="5446" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085082020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085082020";
};

# المستخدم 326: 2064689773
:do {
    /ip hotspot user add name="2064689773" password="3370" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064689773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064689773";
};

# المستخدم 327: 2071038197
:do {
    /ip hotspot user add name="2071038197" password="3383" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071038197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071038197";
};

# المستخدم 328: 2017973335
:do {
    /ip hotspot user add name="2017973335" password="9554" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017973335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017973335";
};

# المستخدم 329: 2032705694
:do {
    /ip hotspot user add name="2032705694" password="2504" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032705694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032705694";
};

# المستخدم 330: 2041768119
:do {
    /ip hotspot user add name="2041768119" password="1501" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041768119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041768119";
};

# المستخدم 331: 2059843134
:do {
    /ip hotspot user add name="2059843134" password="7767" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059843134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059843134";
};

# المستخدم 332: 2099367157
:do {
    /ip hotspot user add name="2099367157" password="2094" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099367157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099367157";
};

# المستخدم 333: 2066664878
:do {
    /ip hotspot user add name="2066664878" password="9707" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066664878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066664878";
};

# المستخدم 334: 2042686196
:do {
    /ip hotspot user add name="2042686196" password="0420" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042686196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042686196";
};

# المستخدم 335: 2036213472
:do {
    /ip hotspot user add name="2036213472" password="6163" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036213472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036213472";
};

# المستخدم 336: 2005195823
:do {
    /ip hotspot user add name="2005195823" password="2294" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005195823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005195823";
};

# المستخدم 337: 2025937910
:do {
    /ip hotspot user add name="2025937910" password="9432" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025937910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025937910";
};

# المستخدم 338: 2045632770
:do {
    /ip hotspot user add name="2045632770" password="2292" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045632770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045632770";
};

# المستخدم 339: 2065554779
:do {
    /ip hotspot user add name="2065554779" password="1159" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065554779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065554779";
};

# المستخدم 340: 2049075759
:do {
    /ip hotspot user add name="2049075759" password="6396" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049075759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049075759";
};

# المستخدم 341: 2008501494
:do {
    /ip hotspot user add name="2008501494" password="7623" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008501494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008501494";
};

# المستخدم 342: 2002678829
:do {
    /ip hotspot user add name="2002678829" password="6895" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002678829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002678829";
};

# المستخدم 343: 2075078133
:do {
    /ip hotspot user add name="2075078133" password="2533" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075078133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075078133";
};

# المستخدم 344: 2052219165
:do {
    /ip hotspot user add name="2052219165" password="1950" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052219165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052219165";
};

# المستخدم 345: 2074129837
:do {
    /ip hotspot user add name="2074129837" password="1814" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074129837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074129837";
};

# المستخدم 346: 2086339739
:do {
    /ip hotspot user add name="2086339739" password="2012" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086339739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086339739";
};

# المستخدم 347: 2026938179
:do {
    /ip hotspot user add name="2026938179" password="8314" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026938179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026938179";
};

# المستخدم 348: 2082012933
:do {
    /ip hotspot user add name="2082012933" password="4132" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082012933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082012933";
};

# المستخدم 349: 2003681476
:do {
    /ip hotspot user add name="2003681476" password="2368" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003681476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003681476";
};

# المستخدم 350: 2054926899
:do {
    /ip hotspot user add name="2054926899" password="0011" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054926899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054926899";
};

# المستخدم 351: 2085063517
:do {
    /ip hotspot user add name="2085063517" password="5421" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085063517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085063517";
};

# المستخدم 352: 2080970199
:do {
    /ip hotspot user add name="2080970199" password="9466" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080970199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080970199";
};

# المستخدم 353: 2094407894
:do {
    /ip hotspot user add name="2094407894" password="9293" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094407894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094407894";
};

# المستخدم 354: 2063141844
:do {
    /ip hotspot user add name="2063141844" password="5106" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063141844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063141844";
};

# المستخدم 355: 2013893651
:do {
    /ip hotspot user add name="2013893651" password="2835" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013893651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013893651";
};

# المستخدم 356: 2068072725
:do {
    /ip hotspot user add name="2068072725" password="1995" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068072725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068072725";
};

# المستخدم 357: 2088746864
:do {
    /ip hotspot user add name="2088746864" password="5779" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088746864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088746864";
};

# المستخدم 358: 2056866885
:do {
    /ip hotspot user add name="2056866885" password="0376" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056866885";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056866885";
};

# المستخدم 359: 2009281240
:do {
    /ip hotspot user add name="2009281240" password="4482" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009281240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009281240";
};

# المستخدم 360: 2002304746
:do {
    /ip hotspot user add name="2002304746" password="3826" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002304746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002304746";
};

# المستخدم 361: 2021709539
:do {
    /ip hotspot user add name="2021709539" password="3411" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021709539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021709539";
};

# المستخدم 362: 2052260745
:do {
    /ip hotspot user add name="2052260745" password="6041" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052260745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052260745";
};

# المستخدم 363: 2045343176
:do {
    /ip hotspot user add name="2045343176" password="3150" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045343176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045343176";
};

# المستخدم 364: 2018209930
:do {
    /ip hotspot user add name="2018209930" password="3642" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018209930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018209930";
};

# المستخدم 365: 2045914860
:do {
    /ip hotspot user add name="2045914860" password="4580" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045914860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045914860";
};

# المستخدم 366: 2067124489
:do {
    /ip hotspot user add name="2067124489" password="0002" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067124489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067124489";
};

# المستخدم 367: 2000850831
:do {
    /ip hotspot user add name="2000850831" password="0058" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000850831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000850831";
};

# المستخدم 368: 2005308051
:do {
    /ip hotspot user add name="2005308051" password="8928" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005308051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005308051";
};

# المستخدم 369: 2086929255
:do {
    /ip hotspot user add name="2086929255" password="5910" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086929255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086929255";
};

# المستخدم 370: 2006343475
:do {
    /ip hotspot user add name="2006343475" password="7442" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006343475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006343475";
};

# المستخدم 371: 2017157538
:do {
    /ip hotspot user add name="2017157538" password="5607" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017157538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017157538";
};

# المستخدم 372: 2067103855
:do {
    /ip hotspot user add name="2067103855" password="6096" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067103855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067103855";
};

# المستخدم 373: 2072216071
:do {
    /ip hotspot user add name="2072216071" password="3212" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072216071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072216071";
};

# المستخدم 374: 2091702942
:do {
    /ip hotspot user add name="2091702942" password="7853" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091702942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091702942";
};

# المستخدم 375: 2066967647
:do {
    /ip hotspot user add name="2066967647" password="1888" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066967647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066967647";
};

# المستخدم 376: 2052466383
:do {
    /ip hotspot user add name="2052466383" password="3728" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052466383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052466383";
};

# المستخدم 377: 2008711288
:do {
    /ip hotspot user add name="2008711288" password="5531" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008711288";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008711288";
};

# المستخدم 378: 2076088414
:do {
    /ip hotspot user add name="2076088414" password="9144" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076088414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076088414";
};

# المستخدم 379: 2007690561
:do {
    /ip hotspot user add name="2007690561" password="1388" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007690561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007690561";
};

# المستخدم 380: 2040104338
:do {
    /ip hotspot user add name="2040104338" password="9012" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040104338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040104338";
};

# المستخدم 381: 2031721254
:do {
    /ip hotspot user add name="2031721254" password="1503" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031721254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031721254";
};

# المستخدم 382: 2084909067
:do {
    /ip hotspot user add name="2084909067" password="7506" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084909067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084909067";
};

# المستخدم 383: 2011359745
:do {
    /ip hotspot user add name="2011359745" password="4931" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011359745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011359745";
};

# المستخدم 384: 2047754650
:do {
    /ip hotspot user add name="2047754650" password="7429" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047754650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047754650";
};

# المستخدم 385: 2072137609
:do {
    /ip hotspot user add name="2072137609" password="0484" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072137609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072137609";
};

# المستخدم 386: 2015208841
:do {
    /ip hotspot user add name="2015208841" password="3313" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015208841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015208841";
};

# المستخدم 387: 2057159556
:do {
    /ip hotspot user add name="2057159556" password="8703" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057159556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057159556";
};

# المستخدم 388: 2098414485
:do {
    /ip hotspot user add name="2098414485" password="9089" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098414485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098414485";
};

# المستخدم 389: 2004795376
:do {
    /ip hotspot user add name="2004795376" password="7414" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004795376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004795376";
};

# المستخدم 390: 2023646370
:do {
    /ip hotspot user add name="2023646370" password="9828" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023646370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023646370";
};

# المستخدم 391: 2028224277
:do {
    /ip hotspot user add name="2028224277" password="7676" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028224277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028224277";
};

# المستخدم 392: 2013980124
:do {
    /ip hotspot user add name="2013980124" password="4712" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013980124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013980124";
};

# المستخدم 393: 2060633836
:do {
    /ip hotspot user add name="2060633836" password="1920" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060633836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060633836";
};

# المستخدم 394: 2088942961
:do {
    /ip hotspot user add name="2088942961" password="0071" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088942961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088942961";
};

# المستخدم 395: 2057919749
:do {
    /ip hotspot user add name="2057919749" password="1723" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057919749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057919749";
};

# المستخدم 396: 2067265594
:do {
    /ip hotspot user add name="2067265594" password="7646" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067265594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067265594";
};

# المستخدم 397: 2031173736
:do {
    /ip hotspot user add name="2031173736" password="6674" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031173736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031173736";
};

# المستخدم 398: 2024595786
:do {
    /ip hotspot user add name="2024595786" password="0781" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024595786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024595786";
};

# المستخدم 399: 2017097516
:do {
    /ip hotspot user add name="2017097516" password="6097" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017097516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017097516";
};

# المستخدم 400: 2064363402
:do {
    /ip hotspot user add name="2064363402" password="3748" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064363402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064363402";
};

# المستخدم 401: 2088505803
:do {
    /ip hotspot user add name="2088505803" password="8705" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088505803";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088505803";
};

# المستخدم 402: 2079968474
:do {
    /ip hotspot user add name="2079968474" password="1511" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079968474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079968474";
};

# المستخدم 403: 2006049874
:do {
    /ip hotspot user add name="2006049874" password="3923" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006049874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006049874";
};

# المستخدم 404: 2000953648
:do {
    /ip hotspot user add name="2000953648" password="8122" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000953648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000953648";
};

# المستخدم 405: 2081246753
:do {
    /ip hotspot user add name="2081246753" password="1262" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081246753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081246753";
};

# المستخدم 406: 2066386083
:do {
    /ip hotspot user add name="2066386083" password="2550" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066386083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066386083";
};

# المستخدم 407: 2088582021
:do {
    /ip hotspot user add name="2088582021" password="2085" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088582021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088582021";
};

# المستخدم 408: 2048236407
:do {
    /ip hotspot user add name="2048236407" password="6999" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048236407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048236407";
};

# المستخدم 409: 2036592691
:do {
    /ip hotspot user add name="2036592691" password="9491" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036592691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036592691";
};

# المستخدم 410: 2003555272
:do {
    /ip hotspot user add name="2003555272" password="3976" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003555272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003555272";
};

# المستخدم 411: 2040830642
:do {
    /ip hotspot user add name="2040830642" password="8323" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040830642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040830642";
};

# المستخدم 412: 2087213938
:do {
    /ip hotspot user add name="2087213938" password="7265" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087213938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087213938";
};

# المستخدم 413: 2061339448
:do {
    /ip hotspot user add name="2061339448" password="5754" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061339448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061339448";
};

# المستخدم 414: 2071723707
:do {
    /ip hotspot user add name="2071723707" password="7209" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071723707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071723707";
};

# المستخدم 415: 2071815023
:do {
    /ip hotspot user add name="2071815023" password="1043" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071815023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071815023";
};

# المستخدم 416: 2012621832
:do {
    /ip hotspot user add name="2012621832" password="5956" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012621832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012621832";
};

# المستخدم 417: 2035172311
:do {
    /ip hotspot user add name="2035172311" password="5122" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035172311";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035172311";
};

# المستخدم 418: 2034475165
:do {
    /ip hotspot user add name="2034475165" password="3790" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034475165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034475165";
};

# المستخدم 419: 2007451608
:do {
    /ip hotspot user add name="2007451608" password="6152" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007451608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007451608";
};

# المستخدم 420: 2003511193
:do {
    /ip hotspot user add name="2003511193" password="8570" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003511193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003511193";
};

# المستخدم 421: 2024616550
:do {
    /ip hotspot user add name="2024616550" password="5480" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024616550";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024616550";
};

# المستخدم 422: 2018191044
:do {
    /ip hotspot user add name="2018191044" password="8650" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018191044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018191044";
};

# المستخدم 423: 2024341595
:do {
    /ip hotspot user add name="2024341595" password="4425" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024341595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024341595";
};

# المستخدم 424: 2054120401
:do {
    /ip hotspot user add name="2054120401" password="0392" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054120401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054120401";
};

# المستخدم 425: 2061476891
:do {
    /ip hotspot user add name="2061476891" password="7343" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061476891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061476891";
};

# المستخدم 426: 2092072158
:do {
    /ip hotspot user add name="2092072158" password="6633" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092072158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092072158";
};

# المستخدم 427: 2090110394
:do {
    /ip hotspot user add name="2090110394" password="3677" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090110394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090110394";
};

# المستخدم 428: 2018081239
:do {
    /ip hotspot user add name="2018081239" password="5308" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018081239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018081239";
};

# المستخدم 429: 2017132873
:do {
    /ip hotspot user add name="2017132873" password="4419" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017132873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017132873";
};

# المستخدم 430: 2048688166
:do {
    /ip hotspot user add name="2048688166" password="4676" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048688166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048688166";
};

# المستخدم 431: 2013587179
:do {
    /ip hotspot user add name="2013587179" password="3172" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013587179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013587179";
};

# المستخدم 432: 2017243640
:do {
    /ip hotspot user add name="2017243640" password="7769" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017243640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017243640";
};

# المستخدم 433: 2026296936
:do {
    /ip hotspot user add name="2026296936" password="0996" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026296936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026296936";
};

# المستخدم 434: 2040452539
:do {
    /ip hotspot user add name="2040452539" password="0613" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040452539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040452539";
};

# المستخدم 435: 2004189627
:do {
    /ip hotspot user add name="2004189627" password="2532" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004189627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004189627";
};

# المستخدم 436: 2079805397
:do {
    /ip hotspot user add name="2079805397" password="0495" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079805397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079805397";
};

# المستخدم 437: 2001414349
:do {
    /ip hotspot user add name="2001414349" password="3678" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001414349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001414349";
};

# المستخدم 438: 2089839943
:do {
    /ip hotspot user add name="2089839943" password="8395" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089839943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089839943";
};

# المستخدم 439: 2064144824
:do {
    /ip hotspot user add name="2064144824" password="9536" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064144824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064144824";
};

# المستخدم 440: 2070675168
:do {
    /ip hotspot user add name="2070675168" password="5321" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070675168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070675168";
};

# المستخدم 441: 2084055430
:do {
    /ip hotspot user add name="2084055430" password="3596" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084055430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084055430";
};

# المستخدم 442: 2013109140
:do {
    /ip hotspot user add name="2013109140" password="7983" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013109140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013109140";
};

# المستخدم 443: 2009368597
:do {
    /ip hotspot user add name="2009368597" password="8000" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009368597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009368597";
};

# المستخدم 444: 2098432037
:do {
    /ip hotspot user add name="2098432037" password="6003" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098432037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098432037";
};

# المستخدم 445: 2001543409
:do {
    /ip hotspot user add name="2001543409" password="3953" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001543409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001543409";
};

# المستخدم 446: 2045909683
:do {
    /ip hotspot user add name="2045909683" password="8096" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045909683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045909683";
};

# المستخدم 447: 2072451012
:do {
    /ip hotspot user add name="2072451012" password="9945" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072451012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072451012";
};

# المستخدم 448: 2088750179
:do {
    /ip hotspot user add name="2088750179" password="8145" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088750179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088750179";
};

# المستخدم 449: 2076010725
:do {
    /ip hotspot user add name="2076010725" password="4815" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076010725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076010725";
};

# المستخدم 450: 2014111869
:do {
    /ip hotspot user add name="2014111869" password="8995" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014111869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014111869";
};

# المستخدم 451: 2004309633
:do {
    /ip hotspot user add name="2004309633" password="8518" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004309633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004309633";
};

# المستخدم 452: 2052517098
:do {
    /ip hotspot user add name="2052517098" password="4147" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052517098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052517098";
};

# المستخدم 453: 2052553764
:do {
    /ip hotspot user add name="2052553764" password="5608" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052553764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052553764";
};

# المستخدم 454: 2068542233
:do {
    /ip hotspot user add name="2068542233" password="4306" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068542233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068542233";
};

# المستخدم 455: 2055426601
:do {
    /ip hotspot user add name="2055426601" password="2467" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055426601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055426601";
};

# المستخدم 456: 2045272600
:do {
    /ip hotspot user add name="2045272600" password="8006" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045272600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045272600";
};

# المستخدم 457: 2089764650
:do {
    /ip hotspot user add name="2089764650" password="0099" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089764650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089764650";
};

# المستخدم 458: 2072236544
:do {
    /ip hotspot user add name="2072236544" password="6570" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072236544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072236544";
};

# المستخدم 459: 2020705321
:do {
    /ip hotspot user add name="2020705321" password="9099" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020705321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020705321";
};

# المستخدم 460: 2066239938
:do {
    /ip hotspot user add name="2066239938" password="7552" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066239938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066239938";
};

# المستخدم 461: 2051782440
:do {
    /ip hotspot user add name="2051782440" password="1595" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051782440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051782440";
};

# المستخدم 462: 2000184929
:do {
    /ip hotspot user add name="2000184929" password="5999" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000184929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000184929";
};

# المستخدم 463: 2059426130
:do {
    /ip hotspot user add name="2059426130" password="6533" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059426130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059426130";
};

# المستخدم 464: 2082471217
:do {
    /ip hotspot user add name="2082471217" password="1015" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082471217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082471217";
};

# المستخدم 465: 2035200890
:do {
    /ip hotspot user add name="2035200890" password="9756" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035200890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035200890";
};

# المستخدم 466: 2037676734
:do {
    /ip hotspot user add name="2037676734" password="1144" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037676734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037676734";
};

# المستخدم 467: 2026194891
:do {
    /ip hotspot user add name="2026194891" password="4914" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026194891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026194891";
};

# المستخدم 468: 2035676710
:do {
    /ip hotspot user add name="2035676710" password="8899" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035676710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035676710";
};

# المستخدم 469: 2028419339
:do {
    /ip hotspot user add name="2028419339" password="2614" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028419339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028419339";
};

# المستخدم 470: 2060098793
:do {
    /ip hotspot user add name="2060098793" password="2660" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060098793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060098793";
};

# المستخدم 471: 2031662063
:do {
    /ip hotspot user add name="2031662063" password="5436" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031662063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031662063";
};

# المستخدم 472: 2092607646
:do {
    /ip hotspot user add name="2092607646" password="4977" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092607646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092607646";
};

# المستخدم 473: 2033308812
:do {
    /ip hotspot user add name="2033308812" password="2989" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033308812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033308812";
};

# المستخدم 474: 2083027915
:do {
    /ip hotspot user add name="2083027915" password="6927" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083027915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083027915";
};

# المستخدم 475: 2063880452
:do {
    /ip hotspot user add name="2063880452" password="2160" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063880452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063880452";
};

# المستخدم 476: 2041652766
:do {
    /ip hotspot user add name="2041652766" password="0552" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041652766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041652766";
};

# المستخدم 477: 2013104280
:do {
    /ip hotspot user add name="2013104280" password="6423" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013104280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013104280";
};

# المستخدم 478: 2059317964
:do {
    /ip hotspot user add name="2059317964" password="5986" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059317964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059317964";
};

# المستخدم 479: 2072140210
:do {
    /ip hotspot user add name="2072140210" password="5533" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072140210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072140210";
};

# المستخدم 480: 2092524766
:do {
    /ip hotspot user add name="2092524766" password="7773" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092524766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092524766";
};

# المستخدم 481: 2061761113
:do {
    /ip hotspot user add name="2061761113" password="6047" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061761113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061761113";
};

# المستخدم 482: 2049986435
:do {
    /ip hotspot user add name="2049986435" password="5315" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049986435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049986435";
};

# المستخدم 483: 2051227976
:do {
    /ip hotspot user add name="2051227976" password="6383" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051227976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051227976";
};

# المستخدم 484: 2098193236
:do {
    /ip hotspot user add name="2098193236" password="4587" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098193236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098193236";
};

# المستخدم 485: 2066120701
:do {
    /ip hotspot user add name="2066120701" password="9238" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066120701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066120701";
};

# المستخدم 486: 2038622130
:do {
    /ip hotspot user add name="2038622130" password="8040" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038622130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038622130";
};

# المستخدم 487: 2094471746
:do {
    /ip hotspot user add name="2094471746" password="9366" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094471746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094471746";
};

# المستخدم 488: 2023190038
:do {
    /ip hotspot user add name="2023190038" password="1173" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023190038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023190038";
};

# المستخدم 489: 2058048970
:do {
    /ip hotspot user add name="2058048970" password="7708" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058048970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058048970";
};

# المستخدم 490: 2075183336
:do {
    /ip hotspot user add name="2075183336" password="4039" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075183336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075183336";
};

# المستخدم 491: 2075703837
:do {
    /ip hotspot user add name="2075703837" password="1431" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075703837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075703837";
};

# المستخدم 492: 2046186453
:do {
    /ip hotspot user add name="2046186453" password="9066" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046186453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046186453";
};

# المستخدم 493: 2020802949
:do {
    /ip hotspot user add name="2020802949" password="2950" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020802949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020802949";
};

# المستخدم 494: 2033787909
:do {
    /ip hotspot user add name="2033787909" password="2184" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033787909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033787909";
};

# المستخدم 495: 2064754946
:do {
    /ip hotspot user add name="2064754946" password="8405" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064754946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064754946";
};

# المستخدم 496: 2044617145
:do {
    /ip hotspot user add name="2044617145" password="5627" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044617145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044617145";
};

# المستخدم 497: 2053842916
:do {
    /ip hotspot user add name="2053842916" password="5792" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053842916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053842916";
};

# المستخدم 498: 2046091734
:do {
    /ip hotspot user add name="2046091734" password="8633" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046091734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046091734";
};

# المستخدم 499: 2095372498
:do {
    /ip hotspot user add name="2095372498" password="8743" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095372498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095372498";
};

# المستخدم 500: 2000662883
:do {
    /ip hotspot user add name="2000662883" password="4036" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000662883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000662883";
};

# المستخدم 501: 2007790065
:do {
    /ip hotspot user add name="2007790065" password="0254" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007790065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007790065";
};

# المستخدم 502: 2007503011
:do {
    /ip hotspot user add name="2007503011" password="3555" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007503011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007503011";
};

# المستخدم 503: 2095088514
:do {
    /ip hotspot user add name="2095088514" password="2972" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095088514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095088514";
};

# المستخدم 504: 2024795993
:do {
    /ip hotspot user add name="2024795993" password="4971" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024795993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024795993";
};

# المستخدم 505: 2057181046
:do {
    /ip hotspot user add name="2057181046" password="9496" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057181046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057181046";
};

# المستخدم 506: 2074804103
:do {
    /ip hotspot user add name="2074804103" password="3243" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074804103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074804103";
};

# المستخدم 507: 2023345768
:do {
    /ip hotspot user add name="2023345768" password="0356" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023345768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023345768";
};

# المستخدم 508: 2003359204
:do {
    /ip hotspot user add name="2003359204" password="4540" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003359204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003359204";
};

# المستخدم 509: 2022756483
:do {
    /ip hotspot user add name="2022756483" password="4195" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022756483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022756483";
};

# المستخدم 510: 2099074035
:do {
    /ip hotspot user add name="2099074035" password="6208" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099074035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099074035";
};

# المستخدم 511: 2047212768
:do {
    /ip hotspot user add name="2047212768" password="6203" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047212768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047212768";
};

# المستخدم 512: 2007083703
:do {
    /ip hotspot user add name="2007083703" password="3105" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007083703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007083703";
};

# المستخدم 513: 2026740071
:do {
    /ip hotspot user add name="2026740071" password="9800" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026740071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026740071";
};

# المستخدم 514: 2079597280
:do {
    /ip hotspot user add name="2079597280" password="8968" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079597280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079597280";
};

# المستخدم 515: 2068765195
:do {
    /ip hotspot user add name="2068765195" password="1170" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068765195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068765195";
};

# المستخدم 516: 2016708757
:do {
    /ip hotspot user add name="2016708757" password="6733" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016708757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016708757";
};

# المستخدم 517: 2018200391
:do {
    /ip hotspot user add name="2018200391" password="7670" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018200391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018200391";
};

# المستخدم 518: 2043806761
:do {
    /ip hotspot user add name="2043806761" password="4287" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043806761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043806761";
};

# المستخدم 519: 2042475054
:do {
    /ip hotspot user add name="2042475054" password="5104" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042475054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042475054";
};

# المستخدم 520: 2049761272
:do {
    /ip hotspot user add name="2049761272" password="1143" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049761272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049761272";
};

# المستخدم 521: 2075555535
:do {
    /ip hotspot user add name="2075555535" password="1340" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075555535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075555535";
};

# المستخدم 522: 2013807250
:do {
    /ip hotspot user add name="2013807250" password="3121" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013807250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013807250";
};

# المستخدم 523: 2093057485
:do {
    /ip hotspot user add name="2093057485" password="9693" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093057485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093057485";
};

# المستخدم 524: 2079676175
:do {
    /ip hotspot user add name="2079676175" password="0325" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079676175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079676175";
};

# المستخدم 525: 2054274838
:do {
    /ip hotspot user add name="2054274838" password="8670" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054274838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054274838";
};

# المستخدم 526: 2054487460
:do {
    /ip hotspot user add name="2054487460" password="4837" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054487460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054487460";
};

# المستخدم 527: 2035496140
:do {
    /ip hotspot user add name="2035496140" password="8399" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035496140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035496140";
};

# المستخدم 528: 2075512763
:do {
    /ip hotspot user add name="2075512763" password="4815" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075512763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075512763";
};

# المستخدم 529: 2071093012
:do {
    /ip hotspot user add name="2071093012" password="4218" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071093012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071093012";
};

# المستخدم 530: 2044801311
:do {
    /ip hotspot user add name="2044801311" password="2238" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044801311";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044801311";
};

# المستخدم 531: 2036096470
:do {
    /ip hotspot user add name="2036096470" password="3004" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036096470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036096470";
};

# المستخدم 532: 2052026147
:do {
    /ip hotspot user add name="2052026147" password="5333" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052026147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052026147";
};

# المستخدم 533: 2096377476
:do {
    /ip hotspot user add name="2096377476" password="6808" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096377476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096377476";
};

# المستخدم 534: 2094408477
:do {
    /ip hotspot user add name="2094408477" password="7053" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094408477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094408477";
};

# المستخدم 535: 2082369395
:do {
    /ip hotspot user add name="2082369395" password="3410" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082369395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082369395";
};

# المستخدم 536: 2099740581
:do {
    /ip hotspot user add name="2099740581" password="2667" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099740581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099740581";
};

# المستخدم 537: 2004621374
:do {
    /ip hotspot user add name="2004621374" password="9180" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004621374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004621374";
};

# المستخدم 538: 2086024805
:do {
    /ip hotspot user add name="2086024805" password="3748" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086024805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086024805";
};

# المستخدم 539: 2000448008
:do {
    /ip hotspot user add name="2000448008" password="5959" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000448008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000448008";
};

# المستخدم 540: 2088471744
:do {
    /ip hotspot user add name="2088471744" password="3435" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088471744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088471744";
};

# المستخدم 541: 2059803656
:do {
    /ip hotspot user add name="2059803656" password="4126" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059803656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059803656";
};

# المستخدم 542: 2010760228
:do {
    /ip hotspot user add name="2010760228" password="4761" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010760228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010760228";
};

# المستخدم 543: 2037375895
:do {
    /ip hotspot user add name="2037375895" password="3403" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037375895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037375895";
};

# المستخدم 544: 2037418774
:do {
    /ip hotspot user add name="2037418774" password="2407" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037418774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037418774";
};

# المستخدم 545: 2034885938
:do {
    /ip hotspot user add name="2034885938" password="9514" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034885938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034885938";
};

# المستخدم 546: 2000913660
:do {
    /ip hotspot user add name="2000913660" password="1476" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000913660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000913660";
};

# المستخدم 547: 2025067870
:do {
    /ip hotspot user add name="2025067870" password="9771" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025067870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025067870";
};

# المستخدم 548: 2046962928
:do {
    /ip hotspot user add name="2046962928" password="4406" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046962928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046962928";
};

# المستخدم 549: 2066441805
:do {
    /ip hotspot user add name="2066441805" password="7415" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066441805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066441805";
};

# المستخدم 550: 2056743600
:do {
    /ip hotspot user add name="2056743600" password="3859" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056743600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056743600";
};

# المستخدم 551: 2046893436
:do {
    /ip hotspot user add name="2046893436" password="1514" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046893436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046893436";
};

# المستخدم 552: 2040505915
:do {
    /ip hotspot user add name="2040505915" password="2117" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040505915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040505915";
};

# المستخدم 553: 2079489495
:do {
    /ip hotspot user add name="2079489495" password="9408" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079489495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079489495";
};

# المستخدم 554: 2052131085
:do {
    /ip hotspot user add name="2052131085" password="3740" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052131085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052131085";
};

# المستخدم 555: 2071177188
:do {
    /ip hotspot user add name="2071177188" password="8113" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071177188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071177188";
};

# المستخدم 556: 2055674771
:do {
    /ip hotspot user add name="2055674771" password="4210" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055674771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055674771";
};

# المستخدم 557: 2072495004
:do {
    /ip hotspot user add name="2072495004" password="3078" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072495004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072495004";
};

# المستخدم 558: 2027530219
:do {
    /ip hotspot user add name="2027530219" password="8202" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027530219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027530219";
};

# المستخدم 559: 2025801329
:do {
    /ip hotspot user add name="2025801329" password="6253" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025801329";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025801329";
};

# المستخدم 560: 2001873916
:do {
    /ip hotspot user add name="2001873916" password="5830" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001873916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001873916";
};

# المستخدم 561: 2025912929
:do {
    /ip hotspot user add name="2025912929" password="0627" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025912929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025912929";
};

# المستخدم 562: 2074191694
:do {
    /ip hotspot user add name="2074191694" password="5888" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074191694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074191694";
};

# المستخدم 563: 2055203624
:do {
    /ip hotspot user add name="2055203624" password="0959" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055203624";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055203624";
};

# المستخدم 564: 2049298071
:do {
    /ip hotspot user add name="2049298071" password="1715" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049298071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049298071";
};

# المستخدم 565: 2049682633
:do {
    /ip hotspot user add name="2049682633" password="3553" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049682633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049682633";
};

# المستخدم 566: 2035963730
:do {
    /ip hotspot user add name="2035963730" password="4542" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035963730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035963730";
};

# المستخدم 567: 2011114221
:do {
    /ip hotspot user add name="2011114221" password="2240" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011114221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011114221";
};

# المستخدم 568: 2066553533
:do {
    /ip hotspot user add name="2066553533" password="9276" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066553533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066553533";
};

# المستخدم 569: 2047653013
:do {
    /ip hotspot user add name="2047653013" password="5152" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047653013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047653013";
};

# المستخدم 570: 2058899256
:do {
    /ip hotspot user add name="2058899256" password="6315" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058899256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058899256";
};

# المستخدم 571: 2081943549
:do {
    /ip hotspot user add name="2081943549" password="3443" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081943549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081943549";
};

# المستخدم 572: 2082566033
:do {
    /ip hotspot user add name="2082566033" password="7243" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082566033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082566033";
};

# المستخدم 573: 2038679206
:do {
    /ip hotspot user add name="2038679206" password="3992" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038679206";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038679206";
};

# المستخدم 574: 2085720038
:do {
    /ip hotspot user add name="2085720038" password="1903" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085720038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085720038";
};

# المستخدم 575: 2043751264
:do {
    /ip hotspot user add name="2043751264" password="7485" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043751264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043751264";
};

# المستخدم 576: 2094638448
:do {
    /ip hotspot user add name="2094638448" password="5899" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094638448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094638448";
};

# المستخدم 577: 2003608039
:do {
    /ip hotspot user add name="2003608039" password="7999" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003608039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003608039";
};

# المستخدم 578: 2018928316
:do {
    /ip hotspot user add name="2018928316" password="5815" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018928316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018928316";
};

# المستخدم 579: 2073474757
:do {
    /ip hotspot user add name="2073474757" password="6966" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073474757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073474757";
};

# المستخدم 580: 2040530060
:do {
    /ip hotspot user add name="2040530060" password="8475" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040530060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040530060";
};

# المستخدم 581: 2063305618
:do {
    /ip hotspot user add name="2063305618" password="1281" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063305618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063305618";
};

# المستخدم 582: 2064476556
:do {
    /ip hotspot user add name="2064476556" password="7774" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064476556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064476556";
};

# المستخدم 583: 2065125456
:do {
    /ip hotspot user add name="2065125456" password="9130" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065125456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065125456";
};

# المستخدم 584: 2028842637
:do {
    /ip hotspot user add name="2028842637" password="9768" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028842637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028842637";
};

# المستخدم 585: 2013747789
:do {
    /ip hotspot user add name="2013747789" password="5680" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013747789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013747789";
};

# المستخدم 586: 2069816151
:do {
    /ip hotspot user add name="2069816151" password="2717" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069816151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069816151";
};

# المستخدم 587: 2072955590
:do {
    /ip hotspot user add name="2072955590" password="5980" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072955590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072955590";
};

# المستخدم 588: 2085689001
:do {
    /ip hotspot user add name="2085689001" password="0273" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085689001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085689001";
};

# المستخدم 589: 2023934694
:do {
    /ip hotspot user add name="2023934694" password="1829" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023934694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023934694";
};

# المستخدم 590: 2099189852
:do {
    /ip hotspot user add name="2099189852" password="2761" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099189852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099189852";
};

# المستخدم 591: 2069137330
:do {
    /ip hotspot user add name="2069137330" password="0278" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069137330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069137330";
};

# المستخدم 592: 2016540714
:do {
    /ip hotspot user add name="2016540714" password="8161" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016540714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016540714";
};

# المستخدم 593: 2071147407
:do {
    /ip hotspot user add name="2071147407" password="0471" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071147407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071147407";
};

# المستخدم 594: 2038787362
:do {
    /ip hotspot user add name="2038787362" password="7093" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038787362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038787362";
};

# المستخدم 595: 2028753811
:do {
    /ip hotspot user add name="2028753811" password="3007" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028753811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028753811";
};

# المستخدم 596: 2039480843
:do {
    /ip hotspot user add name="2039480843" password="1044" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039480843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039480843";
};

# المستخدم 597: 2046788964
:do {
    /ip hotspot user add name="2046788964" password="1901" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046788964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046788964";
};

# المستخدم 598: 2015510779
:do {
    /ip hotspot user add name="2015510779" password="5733" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015510779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015510779";
};

# المستخدم 599: 2018998360
:do {
    /ip hotspot user add name="2018998360" password="5093" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018998360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018998360";
};

# المستخدم 600: 2012290762
:do {
    /ip hotspot user add name="2012290762" password="1407" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012290762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012290762";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 20-marawan";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
