#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار طريقة الحذف المباشر للكروت المرسلة بنجاح
Test Direct Delete Method for Successfully Sent Cards

هذا الاختبار يتحقق من:
1. أن دالة delete_successful_cards_from_mikrotik() تستخدم الحذف المباشر
2. أن الدالة لا تحتوي على خطوات البحث عن المعرف
3. أن آلية تتبع النتائج ومعالجة الأخطاء محفوظة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockLogger:
    """Mock للـ logger"""
    def info(self, message):
        print(f"[INFO] {message}")
    
    def debug(self, message):
        print(f"[DEBUG] {message}")
    
    def error(self, message):
        print(f"[ERROR] {message}")
    
    def warning(self, message):
        print(f"[WARNING] {message}")

class MockAPI:
    """Mock لـ MikroTik API"""
    
    def __init__(self, simulate_errors=False):
        self.simulate_errors = simulate_errors
        self.deleted_users = []
        self.error_users = []
    
    def get_resource(self, path):
        return MockResource(self.simulate_errors, self.deleted_users, self.error_users)

class MockResource:
    """Mock لـ MikroTik Resource"""
    
    def __init__(self, simulate_errors, deleted_users, error_users):
        self.simulate_errors = simulate_errors
        self.deleted_users = deleted_users
        self.error_users = error_users
    
    def remove(self, name=None):
        """محاكاة أمر الحذف المباشر"""
        if self.simulate_errors and name in ['error_user_1', 'error_user_2']:
            self.error_users.append(name)
            raise Exception(f"Connection error for user {name}")
        else:
            self.deleted_users.append(name)
            return True

class TestDirectDeleteMethod:
    """اختبار طريقة الحذف المباشر"""
    
    def __init__(self):
        self.logger = MockLogger()
    
    def test_code_structure(self):
        """اختبار هيكل الكود للتأكد من استخدام الحذف المباشر"""
        
        print("=" * 80)
        print("🧪 اختبار هيكل كود الحذف المباشر")
        print("=" * 80)
        
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                content = file.read()
            
            # البحث عن دالة delete_successful_cards_from_mikrotik
            function_start = content.find('def delete_successful_cards_from_mikrotik(')
            if function_start == -1:
                print("❌ لم يتم العثور على دالة delete_successful_cards_from_mikrotik")
                return False
            
            # استخراج محتوى الدالة (تقريبياً)
            function_end = content.find('\n    def ', function_start + 1)
            if function_end == -1:
                function_end = len(content)
            
            function_content = content[function_start:function_end]
            
            print("🔍 فحص محتوى الدالة:")
            
            # التحقق من وجود الحذف المباشر
            direct_remove = '.remove(name=' in function_content
            print(f"   - استخدام .remove(name=username): {'✅ موجود' if direct_remove else '❌ مفقود'}")
            
            # التحقق من عدم وجود البحث عن المستخدم
            get_by_name = '.get(name=' in function_content
            print(f"   - استخدام .get(name=username): {'❌ موجود (يجب حذفه)' if get_by_name else '✅ غير موجود'}")
            
            # التحقق من عدم وجود استخدام user_id
            user_id_usage = "user_id = users[0]['id']" in function_content
            print(f"   - استخدام user_id: {'❌ موجود (يجب حذفه)' if user_id_usage else '✅ غير موجود'}")
            
            # التحقق من وجود تتبع النتائج
            deleted_count = 'deleted_count += 1' in function_content
            failed_count = 'failed_count += 1' in function_content
            print(f"   - تتبع deleted_count: {'✅ موجود' if deleted_count else '❌ مفقود'}")
            print(f"   - تتبع failed_count: {'✅ موجود' if failed_count else '❌ مفقود'}")
            
            # التحقق من معالجة الأخطاء
            exception_handling = 'except Exception as user_error:' in function_content
            print(f"   - معالجة الأخطاء: {'✅ موجود' if exception_handling else '❌ مفقود'}")
            
            # النتيجة النهائية
            all_correct = (
                direct_remove and 
                not get_by_name and 
                not user_id_usage and 
                deleted_count and 
                failed_count and 
                exception_handling
            )
            
            print(f"\n📊 النتيجة النهائية:")
            if all_correct:
                print("   ✅ جميع التعديلات المطلوبة مطبقة بشكل صحيح!")
            else:
                print("   ❌ بعض التعديلات المطلوبة غير مطبقة!")
            
            return all_correct
            
        except Exception as e:
            print(f"❌ خطأ في فحص الكود: {str(e)}")
            return False
    
    def simulate_delete_operation(self):
        """محاكاة عملية الحذف المباشر"""
        
        print("\n" + "=" * 80)
        print("🧪 محاكاة عملية الحذف المباشر")
        print("=" * 80)
        
        # قائمة المستخدمين للاختبار
        test_usernames = [
            'lightning_user_001',
            'lightning_user_002',
            'lightning_user_003',
            'error_user_1',  # سيفشل
            'lightning_user_004',
            'error_user_2',  # سيفشل
            'lightning_user_005'
        ]
        
        print(f"📋 قائمة المستخدمين للاختبار: {len(test_usernames)} مستخدم")
        for i, username in enumerate(test_usernames, 1):
            status = "❌ سيفشل" if 'error_user' in username else "✅ سينجح"
            print(f"   {i}. {username}: {status}")
        
        # محاكاة عملية الحذف
        print(f"\n🗑️ بدء محاكاة عملية الحذف:")
        
        api = MockAPI(simulate_errors=True)
        deleted_count = 0
        failed_count = 0
        
        for username in test_usernames:
            try:
                # الحذف المباشر (كما في الكود الجديد)
                api.get_resource('/ip/hotspot/user').remove(name=username)
                deleted_count += 1
                self.logger.debug(f"✅ تم حذف المستخدم: {username}")
                
            except Exception as user_error:
                failed_count += 1
                self.logger.error(f"❌ فشل في حذف المستخدم {username}: {str(user_error)}")
        
        # النتائج
        success_rate = (deleted_count / len(test_usernames)) * 100
        
        print(f"\n📊 نتائج المحاكاة:")
        print(f"   - إجمالي المستخدمين: {len(test_usernames)}")
        print(f"   - تم حذفهم بنجاح: {deleted_count}")
        print(f"   - فشل في حذفهم: {failed_count}")
        print(f"   - معدل النجاح: {success_rate:.1f}%")
        
        print(f"\n🔍 تفاصيل العملية:")
        print(f"   - المستخدمون المحذوفون: {api.deleted_users}")
        print(f"   - المستخدمون الفاشلون: {api.error_users}")
        
        # التحقق من النتائج المتوقعة
        expected_deleted = 5  # 7 مستخدمين - 2 خطأ
        expected_failed = 2   # 2 مستخدم خطأ
        
        results_correct = (deleted_count == expected_deleted and failed_count == expected_failed)
        
        print(f"\n✅ التحقق من النتائج:")
        print(f"   - النتائج متوقعة: {'✅ نعم' if results_correct else '❌ لا'}")
        
        return results_correct
    
    def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار معالجة الأخطاء")
        print("=" * 80)
        
        # اختبار حالات مختلفة من الأخطاء
        test_scenarios = [
            {
                'name': 'مستخدم عادي',
                'username': 'normal_user',
                'should_fail': False
            },
            {
                'name': 'مستخدم بخطأ اتصال',
                'username': 'error_user_1',
                'should_fail': True
            },
            {
                'name': 'مستخدم بخطأ في الاسم',
                'username': 'error_user_2',
                'should_fail': True
            }
        ]
        
        print("🔍 اختبار سيناريوهات مختلفة:")
        
        all_tests_passed = True
        
        for scenario in test_scenarios:
            print(f"\n📋 اختبار: {scenario['name']}")
            
            api = MockAPI(simulate_errors=True)
            deleted_count = 0
            failed_count = 0
            
            try:
                api.get_resource('/ip/hotspot/user').remove(name=scenario['username'])
                deleted_count += 1
                result = "نجح"
            except Exception as e:
                failed_count += 1
                result = f"فشل: {str(e)}"
            
            expected_result = "فشل" if scenario['should_fail'] else "نجح"
            test_passed = (
                (scenario['should_fail'] and failed_count > 0) or
                (not scenario['should_fail'] and deleted_count > 0)
            )
            
            print(f"   - النتيجة: {result}")
            print(f"   - متوقع: {expected_result}")
            print(f"   - الاختبار: {'✅ نجح' if test_passed else '❌ فشل'}")
            
            if not test_passed:
                all_tests_passed = False
        
        return all_tests_passed

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🚀 بدء اختبار طريقة الحذف المباشر للكروت المرسلة بنجاح")
    
    tester = TestDirectDeleteMethod()
    
    # تشغيل الاختبارات
    test1 = tester.test_code_structure()
    test2 = tester.simulate_delete_operation()
    test3 = tester.test_error_handling()
    
    # النتيجة النهائية
    print("\n" + "=" * 80)
    print("🎯 ملخص النتائج")
    print("=" * 80)
    
    all_tests_passed = test1 and test2 and test3
    
    print(f"📊 نتائج الاختبارات:")
    print(f"   1. هيكل الكود: {'✅ نجح' if test1 else '❌ فشل'}")
    print(f"   2. محاكاة العملية: {'✅ نجح' if test2 else '❌ فشل'}")
    print(f"   3. معالجة الأخطاء: {'✅ نجح' if test3 else '❌ فشل'}")
    
    if all_tests_passed:
        print(f"\n🎉 جميع الاختبارات نجحت! التعديل تم بشكل صحيح.")
        print(f"💡 الآن تستخدم دالة الحذف الطريقة المباشرة:")
        print(f"   api.get_resource('/ip/hotspot/user').remove(name=username)")
    else:
        print(f"\n❌ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.")
    
    print(f"\n🔍 الفوائد من التعديل:")
    print(f"   ✅ تبسيط عملية الحذف (خطوة واحدة بدلاً من خطوتين)")
    print(f"   ✅ تقليل استهلاك الموارد (عدم الحاجة للبحث أولاً)")
    print(f"   ✅ تقليل وقت التنفيذ")
    print(f"   ✅ تقليل احتمالية الأخطاء")
    print(f"   ✅ كود أبسط وأسهل في الصيانة")

if __name__ == '__main__':
    main()
