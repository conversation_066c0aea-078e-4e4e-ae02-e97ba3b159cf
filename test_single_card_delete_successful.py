#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة حذف الكروت المرسلة بنجاح للكرت الواحد
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن ميزة حذف الكروت المرسلة بنجاح تعمل بشكل صحيح للكرت الواحد
"""

import re

def test_single_card_successful_cards_saving():
    """اختبار حفظ الكروت الناجحة للكرت الواحد"""
    print("🔍 اختبار حفظ الكروت الناجحة للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ الكروت الناجحة
    saving_patterns = [
        'if not hasattr(self, \'single_card_successful_usernames\'):',
        'self.single_card_successful_usernames = []',
        'self.single_card_successful_usernames.append(cred_username)',
        'self.single_card_successful_cards = self.single_card_successful_usernames.copy()',
        'self.single_card_successful_cards_info = {',
        '\'operation_type\': \'single_card\'',
        'failed_count > 0 and success_count > 0'
    ]
    
    for pattern in saving_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ الكروت الناجحة غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ الكروت الناجحة موجود: {pattern}")
    
    return True

def test_single_card_delete_button():
    """اختبار زر حذف الكروت المرسلة بنجاح للكرت الواحد"""
    print("\n🔍 اختبار زر حذف الكروت المرسلة بنجاح للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من زر الحذف
    button_patterns = [
        'show_delete_successful_button = (',
        'failed_count > 0 and',
        'success_count > 0 and',
        'hasattr(self, \'single_card_successful_cards\')',
        'bool(self.single_card_successful_cards)',
        'خيار حذف الكروت المرسلة بنجاح:',
        'single_card_delete_successful_',
        'حذف الكروت المرسلة بنجاح من هذه العملية'
    ]
    
    for pattern in button_patterns:
        if pattern not in func_code:
            print(f"❌ نمط زر الحذف غير موجود: {pattern}")
            return False
        print(f"✅ نمط زر الحذف موجود: {pattern}")
    
    return True

def test_single_card_delete_functions():
    """اختبار دوال حذف الكروت المرسلة بنجاح للكرت الواحد"""
    print("\n🔍 اختبار دوال حذف الكروت المرسلة بنجاح للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدوال المطلوبة
    required_functions = [
        'def handle_single_card_delete_successful_request(',
        'def execute_single_card_delete_successful(',
        'def cancel_single_card_delete_successful('
    ]
    
    for func in required_functions:
        if func not in content:
            print(f"❌ الدالة غير موجودة: {func}")
            return False
        print(f"✅ الدالة موجودة: {func}")
    
    return True

def test_single_card_callback_handling():
    """اختبار معالجة callback للكرت الواحد"""
    print("\n🔍 اختبار معالجة callback للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback
    callback_patterns = [
        'elif callback_data.startswith("single_card_delete_successful_"):',
        'if callback_data.startswith("single_card_delete_successful_confirm_"):',
        'elif callback_data == "single_card_delete_successful_cancel":',
        'self.execute_single_card_delete_successful(bot_token, chat_id, cards_count)',
        'self.cancel_single_card_delete_successful(bot_token, chat_id)',
        'self.handle_single_card_delete_successful_request(bot_token, chat_id, success_count)'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback موجود: {pattern}")
    
    return True

def test_single_card_confirmation_message():
    """اختبار رسالة التأكيد للكرت الواحد"""
    print("\n🔍 اختبار رسالة التأكيد للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_single_card_delete_successful_request
    func_match = re.search(r'def handle_single_card_delete_successful_request.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_single_card_delete_successful_request")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من رسالة التأكيد
    confirmation_elements = [
        'تأكيد حذف الكروت المرسلة بنجاح',
        'العملية المطلوبة:',
        'تفاصيل الحذف:',
        'النظام:</b> 🌐 HotSpot',
        'نوع العملية:</b> 🎴 الكرت الواحد',
        'تحذير مهم:',
        'سيتم حذف الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية فقط',
        'هل أنت متأكد من المتابعة؟',
        'single_card_delete_successful_confirm_',
        'single_card_delete_successful_cancel'
    ]
    
    for element in confirmation_elements:
        if element not in func_code:
            print(f"❌ عنصر رسالة التأكيد غير موجود: {element}")
            return False
        print(f"✅ عنصر رسالة التأكيد موجود: {element}")
    
    return True

def test_single_card_execution_function():
    """اختبار دالة تنفيذ الحذف للكرت الواحد"""
    print("\n🔍 اختبار دالة تنفيذ الحذف للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة execute_single_card_delete_successful
    func_match = re.search(r'def execute_single_card_delete_successful.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_single_card_delete_successful")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر التنفيذ
    execution_elements = [
        'بدء عملية حذف الكروت',
        'جاري حذف',
        'الاتصال بالخادم...',
        'api = self.connect_api()',
        'فشل في الاتصال',
        'self.delete_successful_cards_from_mikrotik',
        'تم حذف الكروت المرسلة بنجاح!',
        'إحصائيات الحذف:',
        'نوع العملية:</b> 🎴 حذف الكروت المرسلة بنجاح من عملية الكرت الواحد',
        'self.single_card_successful_cards = []',
        'delattr(self, \'single_card_successful_cards_info\')',
        'delattr(self, \'single_card_successful_usernames\')'
    ]
    
    for element in execution_elements:
        if element not in func_code:
            print(f"❌ عنصر التنفيذ غير موجود: {element}")
            return False
        print(f"✅ عنصر التنفيذ موجود: {element}")
    
    return True

def test_single_card_cancel_function():
    """اختبار دالة إلغاء الحذف للكرت الواحد"""
    print("\n🔍 اختبار دالة إلغاء الحذف للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة cancel_single_card_delete_successful
    func_match = re.search(r'def cancel_single_card_delete_successful.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة cancel_single_card_delete_successful")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر الإلغاء
    cancel_elements = [
        'تم إلغاء حذف الكروت المرسلة بنجاح',
        'الحالة:</b> لم يتم حذف أي كروت',
        'جميع الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية ما زالت موجودة',
        'يمكنك طلب حذف الكروت المرسلة بنجاح مرة أخرى'
    ]
    
    for element in cancel_elements:
        if element not in func_code:
            print(f"❌ عنصر الإلغاء غير موجود: {element}")
            return False
        print(f"✅ عنصر الإلغاء موجود: {element}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار ميزة حذف الكروت المرسلة بنجاح للكرت الواحد")
    print("="*70)
    
    tests = [
        ("حفظ الكروت الناجحة للكرت الواحد", test_single_card_successful_cards_saving),
        ("زر حذف الكروت المرسلة بنجاح", test_single_card_delete_button),
        ("دوال حذف الكروت المرسلة بنجاح", test_single_card_delete_functions),
        ("معالجة callback للكرت الواحد", test_single_card_callback_handling),
        ("رسالة التأكيد للكرت الواحد", test_single_card_confirmation_message),
        ("دالة تنفيذ الحذف للكرت الواحد", test_single_card_execution_function),
        ("دالة إلغاء الحذف للكرت الواحد", test_single_card_cancel_function)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تطبيق ميزة حذف الكروت المرسلة بنجاح للكرت الواحد بنجاح!")
        print("💡 الميزة جاهزة للاستخدام مع نفس آلية البرق")
        
        print("\n🎯 الميزات المطبقة:")
        print("✅ حفظ الكروت الناجحة للكرت الواحد عند وجود كروت فاشلة")
        print("✅ زر حذف الكروت المرسلة بنجاح في التقرير النهائي")
        print("✅ رسالة تأكيد مفصلة قبل الحذف")
        print("✅ تنفيذ الحذف من خادم MikroTik")
        print("✅ إمكانية إلغاء الحذف")
        print("✅ معالجة callback شاملة")
        print("✅ تنظيف البيانات بعد الحذف")
        print("✅ رسائل إحصائيات مفصلة")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
