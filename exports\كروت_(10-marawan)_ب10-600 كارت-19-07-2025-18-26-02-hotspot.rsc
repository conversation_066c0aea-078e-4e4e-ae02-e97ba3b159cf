# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-19 18:26:03
# القالب: 10-marawan
# النظام: hotspot
# عدد الكروت: 600
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10-marawan";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 600";

:local success 0;
:local errors 0;
:local total 600;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 600 مستخدم Hotspot...";

# المستخدم 1: 1024811140
:do {
    /ip hotspot user add name="1024811140" password="1896" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024811140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024811140";
};

# المستخدم 2: 1030374631
:do {
    /ip hotspot user add name="1030374631" password="8057" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030374631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030374631";
};

# المستخدم 3: 1088053921
:do {
    /ip hotspot user add name="1088053921" password="2369" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088053921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088053921";
};

# المستخدم 4: 1062308149
:do {
    /ip hotspot user add name="1062308149" password="4067" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1062308149";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1062308149";
};

# المستخدم 5: 1080921980
:do {
    /ip hotspot user add name="1080921980" password="8302" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080921980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080921980";
};

# المستخدم 6: 1044412347
:do {
    /ip hotspot user add name="1044412347" password="2944" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1044412347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1044412347";
};

# المستخدم 7: 1093879183
:do {
    /ip hotspot user add name="1093879183" password="0601" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093879183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093879183";
};

# المستخدم 8: 1086602672
:do {
    /ip hotspot user add name="1086602672" password="9194" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1086602672";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1086602672";
};

# المستخدم 9: 1077925834
:do {
    /ip hotspot user add name="1077925834" password="8931" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077925834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077925834";
};

# المستخدم 10: 1077367475
:do {
    /ip hotspot user add name="1077367475" password="7267" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077367475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077367475";
};

# المستخدم 11: 1022251848
:do {
    /ip hotspot user add name="1022251848" password="4441" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1022251848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1022251848";
};

# المستخدم 12: 1036622638
:do {
    /ip hotspot user add name="1036622638" password="0782" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036622638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036622638";
};

# المستخدم 13: 1076951608
:do {
    /ip hotspot user add name="1076951608" password="9237" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1076951608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1076951608";
};

# المستخدم 14: 1040910015
:do {
    /ip hotspot user add name="1040910015" password="0307" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040910015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040910015";
};

# المستخدم 15: 1049921852
:do {
    /ip hotspot user add name="1049921852" password="3788" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1049921852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1049921852";
};

# المستخدم 16: 1000627755
:do {
    /ip hotspot user add name="1000627755" password="4651" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1000627755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1000627755";
};

# المستخدم 17: 1015303099
:do {
    /ip hotspot user add name="1015303099" password="7542" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015303099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015303099";
};

# المستخدم 18: 1041666349
:do {
    /ip hotspot user add name="1041666349" password="3674" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041666349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041666349";
};

# المستخدم 19: 1066308820
:do {
    /ip hotspot user add name="1066308820" password="3889" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066308820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066308820";
};

# المستخدم 20: 1056565028
:do {
    /ip hotspot user add name="1056565028" password="5697" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056565028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056565028";
};

# المستخدم 21: 1026162051
:do {
    /ip hotspot user add name="1026162051" password="2611" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1026162051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1026162051";
};

# المستخدم 22: 1061031562
:do {
    /ip hotspot user add name="1061031562" password="1454" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1061031562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1061031562";
};

# المستخدم 23: 1001556314
:do {
    /ip hotspot user add name="1001556314" password="9037" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1001556314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1001556314";
};

# المستخدم 24: 1012881217
:do {
    /ip hotspot user add name="1012881217" password="1138" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012881217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012881217";
};

# المستخدم 25: 1043382419
:do {
    /ip hotspot user add name="1043382419" password="2426" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1043382419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1043382419";
};

# المستخدم 26: 1065674809
:do {
    /ip hotspot user add name="1065674809" password="4411" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065674809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065674809";
};

# المستخدم 27: 1023221883
:do {
    /ip hotspot user add name="1023221883" password="6216" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023221883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023221883";
};

# المستخدم 28: 1079493711
:do {
    /ip hotspot user add name="1079493711" password="0699" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079493711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079493711";
};

# المستخدم 29: 1082776937
:do {
    /ip hotspot user add name="1082776937" password="3804" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1082776937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1082776937";
};

# المستخدم 30: 1027386597
:do {
    /ip hotspot user add name="1027386597" password="3764" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027386597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027386597";
};

# المستخدم 31: 1036368042
:do {
    /ip hotspot user add name="1036368042" password="4060" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036368042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036368042";
};

# المستخدم 32: 1084843321
:do {
    /ip hotspot user add name="1084843321" password="4586" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084843321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084843321";
};

# المستخدم 33: 1085917487
:do {
    /ip hotspot user add name="1085917487" password="9062" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085917487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085917487";
};

# المستخدم 34: 1003800898
:do {
    /ip hotspot user add name="1003800898" password="8560" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003800898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003800898";
};

# المستخدم 35: 1013272179
:do {
    /ip hotspot user add name="1013272179" password="1960" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013272179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013272179";
};

# المستخدم 36: 1033358409
:do {
    /ip hotspot user add name="1033358409" password="3110" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1033358409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1033358409";
};

# المستخدم 37: 1016053557
:do {
    /ip hotspot user add name="1016053557" password="7648" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016053557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016053557";
};

# المستخدم 38: 1019322222
:do {
    /ip hotspot user add name="1019322222" password="4074" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1019322222";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1019322222";
};

# المستخدم 39: 1063040009
:do {
    /ip hotspot user add name="1063040009" password="8832" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1063040009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1063040009";
};

# المستخدم 40: 1034907031
:do {
    /ip hotspot user add name="1034907031" password="1348" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034907031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034907031";
};

# المستخدم 41: 1080605488
:do {
    /ip hotspot user add name="1080605488" password="9203" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080605488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080605488";
};

# المستخدم 42: 1041868256
:do {
    /ip hotspot user add name="1041868256" password="2630" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041868256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041868256";
};

# المستخدم 43: 1005338663
:do {
    /ip hotspot user add name="1005338663" password="2679" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005338663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005338663";
};

# المستخدم 44: 1009294084
:do {
    /ip hotspot user add name="1009294084" password="1283" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009294084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009294084";
};

# المستخدم 45: 1041025771
:do {
    /ip hotspot user add name="1041025771" password="6877" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041025771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041025771";
};

# المستخدم 46: 1028484038
:do {
    /ip hotspot user add name="1028484038" password="0693" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1028484038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1028484038";
};

# المستخدم 47: 1051834000
:do {
    /ip hotspot user add name="1051834000" password="0816" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051834000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051834000";
};

# المستخدم 48: 1003636543
:do {
    /ip hotspot user add name="1003636543" password="3759" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003636543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003636543";
};

# المستخدم 49: 1066204549
:do {
    /ip hotspot user add name="1066204549" password="7964" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066204549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066204549";
};

# المستخدم 50: 1085061467
:do {
    /ip hotspot user add name="1085061467" password="1697" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085061467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085061467";
};

# المستخدم 51: 1040745546
:do {
    /ip hotspot user add name="1040745546" password="0715" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040745546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040745546";
};

# المستخدم 52: 1010155875
:do {
    /ip hotspot user add name="1010155875" password="3371" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1010155875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1010155875";
};

# المستخدم 53: 1045203748
:do {
    /ip hotspot user add name="1045203748" password="6325" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045203748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045203748";
};

# المستخدم 54: 1062434975
:do {
    /ip hotspot user add name="1062434975" password="2176" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1062434975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1062434975";
};

# المستخدم 55: 1083914801
:do {
    /ip hotspot user add name="1083914801" password="4892" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1083914801";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1083914801";
};

# المستخدم 56: 1076689660
:do {
    /ip hotspot user add name="1076689660" password="7469" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1076689660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1076689660";
};

# المستخدم 57: 1004844321
:do {
    /ip hotspot user add name="1004844321" password="2032" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004844321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004844321";
};

# المستخدم 58: 1012085778
:do {
    /ip hotspot user add name="1012085778" password="5341" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012085778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012085778";
};

# المستخدم 59: 1041980356
:do {
    /ip hotspot user add name="1041980356" password="7632" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041980356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041980356";
};

# المستخدم 60: 1065822089
:do {
    /ip hotspot user add name="1065822089" password="1133" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065822089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065822089";
};

# المستخدم 61: 1027493000
:do {
    /ip hotspot user add name="1027493000" password="1380" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027493000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027493000";
};

# المستخدم 62: 1017489015
:do {
    /ip hotspot user add name="1017489015" password="2115" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1017489015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1017489015";
};

# المستخدم 63: 1064841734
:do {
    /ip hotspot user add name="1064841734" password="6707" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1064841734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1064841734";
};

# المستخدم 64: 1004902924
:do {
    /ip hotspot user add name="1004902924" password="6856" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004902924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004902924";
};

# المستخدم 65: 1033971444
:do {
    /ip hotspot user add name="1033971444" password="4286" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1033971444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1033971444";
};

# المستخدم 66: 1022929491
:do {
    /ip hotspot user add name="1022929491" password="9327" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1022929491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1022929491";
};

# المستخدم 67: 1049644896
:do {
    /ip hotspot user add name="1049644896" password="3541" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1049644896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1049644896";
};

# المستخدم 68: 1020825567
:do {
    /ip hotspot user add name="1020825567" password="9380" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020825567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020825567";
};

# المستخدم 69: 1080037425
:do {
    /ip hotspot user add name="1080037425" password="8211" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080037425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080037425";
};

# المستخدم 70: 1048918980
:do {
    /ip hotspot user add name="1048918980" password="1292" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1048918980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1048918980";
};

# المستخدم 71: 1010264989
:do {
    /ip hotspot user add name="1010264989" password="0211" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1010264989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1010264989";
};

# المستخدم 72: 1034163609
:do {
    /ip hotspot user add name="1034163609" password="5689" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034163609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034163609";
};

# المستخدم 73: 1066985556
:do {
    /ip hotspot user add name="1066985556" password="4660" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066985556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066985556";
};

# المستخدم 74: 1081032636
:do {
    /ip hotspot user add name="1081032636" password="2607" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081032636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081032636";
};

# المستخدم 75: 1090447484
:do {
    /ip hotspot user add name="1090447484" password="1420" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090447484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090447484";
};

# المستخدم 76: 1035019706
:do {
    /ip hotspot user add name="1035019706" password="6890" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1035019706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1035019706";
};

# المستخدم 77: 1031783170
:do {
    /ip hotspot user add name="1031783170" password="5131" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1031783170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1031783170";
};

# المستخدم 78: 1069504530
:do {
    /ip hotspot user add name="1069504530" password="1254" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1069504530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1069504530";
};

# المستخدم 79: 1030799125
:do {
    /ip hotspot user add name="1030799125" password="9905" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030799125";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030799125";
};

# المستخدم 80: 1041947364
:do {
    /ip hotspot user add name="1041947364" password="6487" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041947364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041947364";
};

# المستخدم 81: 1024501801
:do {
    /ip hotspot user add name="1024501801" password="3683" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024501801";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024501801";
};

# المستخدم 82: 1042720240
:do {
    /ip hotspot user add name="1042720240" password="8661" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042720240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042720240";
};

# المستخدم 83: 1088812353
:do {
    /ip hotspot user add name="1088812353" password="8354" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088812353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088812353";
};

# المستخدم 84: 1076691309
:do {
    /ip hotspot user add name="1076691309" password="6253" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1076691309";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1076691309";
};

# المستخدم 85: 1092894471
:do {
    /ip hotspot user add name="1092894471" password="9273" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092894471";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092894471";
};

# المستخدم 86: 1071087234
:do {
    /ip hotspot user add name="1071087234" password="4437" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1071087234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1071087234";
};

# المستخدم 87: 1024723003
:do {
    /ip hotspot user add name="1024723003" password="3267" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024723003";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024723003";
};

# المستخدم 88: 1034166510
:do {
    /ip hotspot user add name="1034166510" password="7522" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034166510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034166510";
};

# المستخدم 89: 1019316991
:do {
    /ip hotspot user add name="1019316991" password="1840" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1019316991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1019316991";
};

# المستخدم 90: 1092889273
:do {
    /ip hotspot user add name="1092889273" password="6326" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092889273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092889273";
};

# المستخدم 91: 1057153812
:do {
    /ip hotspot user add name="1057153812" password="7953" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057153812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057153812";
};

# المستخدم 92: 1058764961
:do {
    /ip hotspot user add name="1058764961" password="2020" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058764961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058764961";
};

# المستخدم 93: 1099786052
:do {
    /ip hotspot user add name="1099786052" password="4765" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1099786052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1099786052";
};

# المستخدم 94: 1063542208
:do {
    /ip hotspot user add name="1063542208" password="2302" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1063542208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1063542208";
};

# المستخدم 95: 1020780687
:do {
    /ip hotspot user add name="1020780687" password="7790" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020780687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020780687";
};

# المستخدم 96: 1017950859
:do {
    /ip hotspot user add name="1017950859" password="7621" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1017950859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1017950859";
};

# المستخدم 97: 1003987228
:do {
    /ip hotspot user add name="1003987228" password="9914" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003987228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003987228";
};

# المستخدم 98: 1046676137
:do {
    /ip hotspot user add name="1046676137" password="1141" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1046676137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1046676137";
};

# المستخدم 99: 1042186339
:do {
    /ip hotspot user add name="1042186339" password="3533" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042186339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042186339";
};

# المستخدم 100: 1025195248
:do {
    /ip hotspot user add name="1025195248" password="2646" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1025195248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1025195248";
};

# المستخدم 101: 1008829109
:do {
    /ip hotspot user add name="1008829109" password="2686" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008829109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008829109";
};

# المستخدم 102: 1003095886
:do {
    /ip hotspot user add name="1003095886" password="1588" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003095886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003095886";
};

# المستخدم 103: 1078096175
:do {
    /ip hotspot user add name="1078096175" password="6995" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1078096175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1078096175";
};

# المستخدم 104: 1073924224
:do {
    /ip hotspot user add name="1073924224" password="3123" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1073924224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1073924224";
};

# المستخدم 105: 1039192466
:do {
    /ip hotspot user add name="1039192466" password="3775" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039192466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039192466";
};

# المستخدم 106: 1023712656
:do {
    /ip hotspot user add name="1023712656" password="8829" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023712656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023712656";
};

# المستخدم 107: 1083908774
:do {
    /ip hotspot user add name="1083908774" password="4879" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1083908774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1083908774";
};

# المستخدم 108: 1023187136
:do {
    /ip hotspot user add name="1023187136" password="4208" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023187136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023187136";
};

# المستخدم 109: 1013334195
:do {
    /ip hotspot user add name="1013334195" password="9561" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013334195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013334195";
};

# المستخدم 110: 1081553830
:do {
    /ip hotspot user add name="1081553830" password="8656" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081553830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081553830";
};

# المستخدم 111: 1023780939
:do {
    /ip hotspot user add name="1023780939" password="3749" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023780939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023780939";
};

# المستخدم 112: 1034656477
:do {
    /ip hotspot user add name="1034656477" password="6325" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034656477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034656477";
};

# المستخدم 113: 1091806774
:do {
    /ip hotspot user add name="1091806774" password="2692" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1091806774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1091806774";
};

# المستخدم 114: 1041886795
:do {
    /ip hotspot user add name="1041886795" password="2531" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041886795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041886795";
};

# المستخدم 115: 1088630112
:do {
    /ip hotspot user add name="1088630112" password="7365" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088630112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088630112";
};

# المستخدم 116: 1089377419
:do {
    /ip hotspot user add name="1089377419" password="3729" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1089377419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1089377419";
};

# المستخدم 117: 1035846311
:do {
    /ip hotspot user add name="1035846311" password="7135" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1035846311";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1035846311";
};

# المستخدم 118: 1097547413
:do {
    /ip hotspot user add name="1097547413" password="5223" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1097547413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1097547413";
};

# المستخدم 119: 1023295865
:do {
    /ip hotspot user add name="1023295865" password="8323" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023295865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023295865";
};

# المستخدم 120: 1008438834
:do {
    /ip hotspot user add name="1008438834" password="0102" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008438834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008438834";
};

# المستخدم 121: 1089799020
:do {
    /ip hotspot user add name="1089799020" password="3706" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1089799020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1089799020";
};

# المستخدم 122: 1043846785
:do {
    /ip hotspot user add name="1043846785" password="7572" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1043846785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1043846785";
};

# المستخدم 123: 1098554037
:do {
    /ip hotspot user add name="1098554037" password="3664" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098554037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098554037";
};

# المستخدم 124: 1014921177
:do {
    /ip hotspot user add name="1014921177" password="1868" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1014921177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1014921177";
};

# المستخدم 125: 1038445282
:do {
    /ip hotspot user add name="1038445282" password="4047" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038445282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038445282";
};

# المستخدم 126: 1077204047
:do {
    /ip hotspot user add name="1077204047" password="9004" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077204047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077204047";
};

# المستخدم 127: 1030526191
:do {
    /ip hotspot user add name="1030526191" password="6727" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030526191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030526191";
};

# المستخدم 128: 1051514068
:do {
    /ip hotspot user add name="1051514068" password="7364" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051514068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051514068";
};

# المستخدم 129: 1083618257
:do {
    /ip hotspot user add name="1083618257" password="3582" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1083618257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1083618257";
};

# المستخدم 130: 1013685991
:do {
    /ip hotspot user add name="1013685991" password="2116" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013685991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013685991";
};

# المستخدم 131: 1038348376
:do {
    /ip hotspot user add name="1038348376" password="6948" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038348376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038348376";
};

# المستخدم 132: 1005627004
:do {
    /ip hotspot user add name="1005627004" password="7697" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005627004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005627004";
};

# المستخدم 133: 1074093609
:do {
    /ip hotspot user add name="1074093609" password="3304" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074093609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074093609";
};

# المستخدم 134: 1007747877
:do {
    /ip hotspot user add name="1007747877" password="0862" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1007747877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1007747877";
};

# المستخدم 135: 1028306400
:do {
    /ip hotspot user add name="1028306400" password="4510" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1028306400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1028306400";
};

# المستخدم 136: 1002007721
:do {
    /ip hotspot user add name="1002007721" password="2048" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002007721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002007721";
};

# المستخدم 137: 1090874166
:do {
    /ip hotspot user add name="1090874166" password="9919" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090874166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090874166";
};

# المستخدم 138: 1007192241
:do {
    /ip hotspot user add name="1007192241" password="2973" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1007192241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1007192241";
};

# المستخدم 139: 1057247195
:do {
    /ip hotspot user add name="1057247195" password="8511" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057247195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057247195";
};

# المستخدم 140: 1016764055
:do {
    /ip hotspot user add name="1016764055" password="8950" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016764055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016764055";
};

# المستخدم 141: 1051544016
:do {
    /ip hotspot user add name="1051544016" password="1525" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051544016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051544016";
};

# المستخدم 142: 1033247549
:do {
    /ip hotspot user add name="1033247549" password="6733" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1033247549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1033247549";
};

# المستخدم 143: 1039008170
:do {
    /ip hotspot user add name="1039008170" password="5780" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039008170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039008170";
};

# المستخدم 144: 1053106166
:do {
    /ip hotspot user add name="1053106166" password="5698" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053106166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053106166";
};

# المستخدم 145: 1008769235
:do {
    /ip hotspot user add name="1008769235" password="8039" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008769235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008769235";
};

# المستخدم 146: 1083631593
:do {
    /ip hotspot user add name="1083631593" password="4259" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1083631593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1083631593";
};

# المستخدم 147: 1087271433
:do {
    /ip hotspot user add name="1087271433" password="8590" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1087271433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1087271433";
};

# المستخدم 148: 1016777881
:do {
    /ip hotspot user add name="1016777881" password="0567" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016777881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016777881";
};

# المستخدم 149: 1053443786
:do {
    /ip hotspot user add name="1053443786" password="7372" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053443786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053443786";
};

# المستخدم 150: 1026171773
:do {
    /ip hotspot user add name="1026171773" password="2972" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1026171773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1026171773";
};

# المستخدم 151: 1079762453
:do {
    /ip hotspot user add name="1079762453" password="8497" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079762453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079762453";
};

# المستخدم 152: 1096735190
:do {
    /ip hotspot user add name="1096735190" password="8333" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096735190";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096735190";
};

# المستخدم 153: 1099059893
:do {
    /ip hotspot user add name="1099059893" password="1698" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1099059893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1099059893";
};

# المستخدم 154: 1015620600
:do {
    /ip hotspot user add name="1015620600" password="8306" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015620600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015620600";
};

# المستخدم 155: 1015255297
:do {
    /ip hotspot user add name="1015255297" password="0131" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015255297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015255297";
};

# المستخدم 156: 1028904148
:do {
    /ip hotspot user add name="1028904148" password="0781" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1028904148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1028904148";
};

# المستخدم 157: 1053748207
:do {
    /ip hotspot user add name="1053748207" password="9329" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053748207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053748207";
};

# المستخدم 158: 1045139167
:do {
    /ip hotspot user add name="1045139167" password="6147" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045139167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045139167";
};

# المستخدم 159: 1056109565
:do {
    /ip hotspot user add name="1056109565" password="5684" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056109565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056109565";
};

# المستخدم 160: 1088691340
:do {
    /ip hotspot user add name="1088691340" password="5788" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088691340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088691340";
};

# المستخدم 161: 1072916947
:do {
    /ip hotspot user add name="1072916947" password="9547" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072916947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072916947";
};

# المستخدم 162: 1054696865
:do {
    /ip hotspot user add name="1054696865" password="7323" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1054696865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1054696865";
};

# المستخدم 163: 1081695139
:do {
    /ip hotspot user add name="1081695139" password="6829" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081695139";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081695139";
};

# المستخدم 164: 1076940651
:do {
    /ip hotspot user add name="1076940651" password="1907" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1076940651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1076940651";
};

# المستخدم 165: 1018160598
:do {
    /ip hotspot user add name="1018160598" password="7191" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1018160598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1018160598";
};

# المستخدم 166: 1098154130
:do {
    /ip hotspot user add name="1098154130" password="0475" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098154130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098154130";
};

# المستخدم 167: 1026450318
:do {
    /ip hotspot user add name="1026450318" password="9754" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1026450318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1026450318";
};

# المستخدم 168: 1039309658
:do {
    /ip hotspot user add name="1039309658" password="2720" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039309658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039309658";
};

# المستخدم 169: 1043795950
:do {
    /ip hotspot user add name="1043795950" password="1470" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1043795950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1043795950";
};

# المستخدم 170: 1056419899
:do {
    /ip hotspot user add name="1056419899" password="5378" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056419899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056419899";
};

# المستخدم 171: 1018728762
:do {
    /ip hotspot user add name="1018728762" password="5961" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1018728762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1018728762";
};

# المستخدم 172: 1090145361
:do {
    /ip hotspot user add name="1090145361" password="2122" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090145361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090145361";
};

# المستخدم 173: 1031679876
:do {
    /ip hotspot user add name="1031679876" password="3012" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1031679876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1031679876";
};

# المستخدم 174: 1000361490
:do {
    /ip hotspot user add name="1000361490" password="0881" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1000361490";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1000361490";
};

# المستخدم 175: 1098145260
:do {
    /ip hotspot user add name="1098145260" password="3188" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098145260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098145260";
};

# المستخدم 176: 1095441167
:do {
    /ip hotspot user add name="1095441167" password="3518" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1095441167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1095441167";
};

# المستخدم 177: 1079616109
:do {
    /ip hotspot user add name="1079616109" password="8917" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079616109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079616109";
};

# المستخدم 178: 1065141543
:do {
    /ip hotspot user add name="1065141543" password="8388" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065141543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065141543";
};

# المستخدم 179: 1090441746
:do {
    /ip hotspot user add name="1090441746" password="2406" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090441746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090441746";
};

# المستخدم 180: 1036129708
:do {
    /ip hotspot user add name="1036129708" password="1677" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036129708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036129708";
};

# المستخدم 181: 1092605713
:do {
    /ip hotspot user add name="1092605713" password="6507" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092605713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092605713";
};

# المستخدم 182: 1056439827
:do {
    /ip hotspot user add name="1056439827" password="8051" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056439827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056439827";
};

# المستخدم 183: 1050745877
:do {
    /ip hotspot user add name="1050745877" password="2439" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1050745877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1050745877";
};

# المستخدم 184: 1028860058
:do {
    /ip hotspot user add name="1028860058" password="2197" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1028860058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1028860058";
};

# المستخدم 185: 1041582621
:do {
    /ip hotspot user add name="1041582621" password="0975" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041582621";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041582621";
};

# المستخدم 186: 1030881513
:do {
    /ip hotspot user add name="1030881513" password="2157" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030881513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030881513";
};

# المستخدم 187: 1051300326
:do {
    /ip hotspot user add name="1051300326" password="8352" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051300326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051300326";
};

# المستخدم 188: 1050328342
:do {
    /ip hotspot user add name="1050328342" password="5815" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1050328342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1050328342";
};

# المستخدم 189: 1086473166
:do {
    /ip hotspot user add name="1086473166" password="3075" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1086473166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1086473166";
};

# المستخدم 190: 1071469958
:do {
    /ip hotspot user add name="1071469958" password="4259" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1071469958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1071469958";
};

# المستخدم 191: 1066932591
:do {
    /ip hotspot user add name="1066932591" password="6211" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066932591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066932591";
};

# المستخدم 192: 1058853941
:do {
    /ip hotspot user add name="1058853941" password="1569" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058853941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058853941";
};

# المستخدم 193: 1005034500
:do {
    /ip hotspot user add name="1005034500" password="7284" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005034500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005034500";
};

# المستخدم 194: 1079137577
:do {
    /ip hotspot user add name="1079137577" password="5461" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079137577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079137577";
};

# المستخدم 195: 1008695269
:do {
    /ip hotspot user add name="1008695269" password="3773" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008695269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008695269";
};

# المستخدم 196: 1043407348
:do {
    /ip hotspot user add name="1043407348" password="1560" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1043407348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1043407348";
};

# المستخدم 197: 1072944866
:do {
    /ip hotspot user add name="1072944866" password="6327" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072944866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072944866";
};

# المستخدم 198: 1075157926
:do {
    /ip hotspot user add name="1075157926" password="2653" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075157926";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075157926";
};

# المستخدم 199: 1014180777
:do {
    /ip hotspot user add name="1014180777" password="1992" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1014180777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1014180777";
};

# المستخدم 200: 1053427587
:do {
    /ip hotspot user add name="1053427587" password="3653" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053427587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053427587";
};

# المستخدم 201: 1030234477
:do {
    /ip hotspot user add name="1030234477" password="1863" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030234477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030234477";
};

# المستخدم 202: 1031126390
:do {
    /ip hotspot user add name="1031126390" password="2570" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1031126390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1031126390";
};

# المستخدم 203: 1008529846
:do {
    /ip hotspot user add name="1008529846" password="6179" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008529846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008529846";
};

# المستخدم 204: 1057554980
:do {
    /ip hotspot user add name="1057554980" password="2617" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057554980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057554980";
};

# المستخدم 205: 1013669070
:do {
    /ip hotspot user add name="1013669070" password="2149" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013669070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013669070";
};

# المستخدم 206: 1016446420
:do {
    /ip hotspot user add name="1016446420" password="4629" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016446420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016446420";
};

# المستخدم 207: 1084917709
:do {
    /ip hotspot user add name="1084917709" password="2584" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084917709";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084917709";
};

# المستخدم 208: 1004685974
:do {
    /ip hotspot user add name="1004685974" password="9015" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004685974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004685974";
};

# المستخدم 209: 1000140595
:do {
    /ip hotspot user add name="1000140595" password="5397" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1000140595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1000140595";
};

# المستخدم 210: 1089445995
:do {
    /ip hotspot user add name="1089445995" password="8277" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1089445995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1089445995";
};

# المستخدم 211: 1082114483
:do {
    /ip hotspot user add name="1082114483" password="0514" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1082114483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1082114483";
};

# المستخدم 212: 1014841059
:do {
    /ip hotspot user add name="1014841059" password="0296" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1014841059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1014841059";
};

# المستخدم 213: 1086989291
:do {
    /ip hotspot user add name="1086989291" password="9194" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1086989291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1086989291";
};

# المستخدم 214: 1036170458
:do {
    /ip hotspot user add name="1036170458" password="1766" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036170458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036170458";
};

# المستخدم 215: 1066195689
:do {
    /ip hotspot user add name="1066195689" password="2573" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066195689";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066195689";
};

# المستخدم 216: 1063508815
:do {
    /ip hotspot user add name="1063508815" password="3747" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1063508815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1063508815";
};

# المستخدم 217: 1024287941
:do {
    /ip hotspot user add name="1024287941" password="0943" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024287941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024287941";
};

# المستخدم 218: 1009418220
:do {
    /ip hotspot user add name="1009418220" password="0102" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009418220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009418220";
};

# المستخدم 219: 1069829694
:do {
    /ip hotspot user add name="1069829694" password="8637" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1069829694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1069829694";
};

# المستخدم 220: 1010827674
:do {
    /ip hotspot user add name="1010827674" password="4063" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1010827674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1010827674";
};

# المستخدم 221: 1084495969
:do {
    /ip hotspot user add name="1084495969" password="9656" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084495969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084495969";
};

# المستخدم 222: 1082989180
:do {
    /ip hotspot user add name="1082989180" password="6824" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1082989180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1082989180";
};

# المستخدم 223: 1037655185
:do {
    /ip hotspot user add name="1037655185" password="7030" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1037655185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1037655185";
};

# المستخدم 224: 1089112662
:do {
    /ip hotspot user add name="1089112662" password="9885" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1089112662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1089112662";
};

# المستخدم 225: 1080037005
:do {
    /ip hotspot user add name="1080037005" password="0433" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080037005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080037005";
};

# المستخدم 226: 1065811631
:do {
    /ip hotspot user add name="1065811631" password="6897" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065811631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065811631";
};

# المستخدم 227: 1096659777
:do {
    /ip hotspot user add name="1096659777" password="0843" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096659777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096659777";
};

# المستخدم 228: 1063789525
:do {
    /ip hotspot user add name="1063789525" password="8206" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1063789525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1063789525";
};

# المستخدم 229: 1015501550
:do {
    /ip hotspot user add name="1015501550" password="4218" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015501550";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015501550";
};

# المستخدم 230: 1052981281
:do {
    /ip hotspot user add name="1052981281" password="1551" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1052981281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1052981281";
};

# المستخدم 231: 1015135508
:do {
    /ip hotspot user add name="1015135508" password="0996" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015135508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015135508";
};

# المستخدم 232: 1094340486
:do {
    /ip hotspot user add name="1094340486" password="0663" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1094340486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1094340486";
};

# المستخدم 233: 1042756810
:do {
    /ip hotspot user add name="1042756810" password="7173" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042756810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042756810";
};

# المستخدم 234: 1071703527
:do {
    /ip hotspot user add name="1071703527" password="4030" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1071703527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1071703527";
};

# المستخدم 235: 1020366107
:do {
    /ip hotspot user add name="1020366107" password="8225" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020366107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020366107";
};

# المستخدم 236: 1075089508
:do {
    /ip hotspot user add name="1075089508" password="6463" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075089508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075089508";
};

# المستخدم 237: 1082299045
:do {
    /ip hotspot user add name="1082299045" password="3384" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1082299045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1082299045";
};

# المستخدم 238: 1096349182
:do {
    /ip hotspot user add name="1096349182" password="9141" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096349182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096349182";
};

# المستخدم 239: 1070566420
:do {
    /ip hotspot user add name="1070566420" password="7624" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070566420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070566420";
};

# المستخدم 240: 1035895397
:do {
    /ip hotspot user add name="1035895397" password="1866" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1035895397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1035895397";
};

# المستخدم 241: 1025065581
:do {
    /ip hotspot user add name="1025065581" password="6111" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1025065581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1025065581";
};

# المستخدم 242: 1090751083
:do {
    /ip hotspot user add name="1090751083" password="8597" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090751083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090751083";
};

# المستخدم 243: 1017064806
:do {
    /ip hotspot user add name="1017064806" password="3941" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1017064806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1017064806";
};

# المستخدم 244: 1009502380
:do {
    /ip hotspot user add name="1009502380" password="5051" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009502380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009502380";
};

# المستخدم 245: 1020953441
:do {
    /ip hotspot user add name="1020953441" password="6110" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020953441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020953441";
};

# المستخدم 246: 1002440822
:do {
    /ip hotspot user add name="1002440822" password="4676" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002440822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002440822";
};

# المستخدم 247: 1031587105
:do {
    /ip hotspot user add name="1031587105" password="2031" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1031587105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1031587105";
};

# المستخدم 248: 1004083217
:do {
    /ip hotspot user add name="1004083217" password="8642" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004083217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004083217";
};

# المستخدم 249: 1051515289
:do {
    /ip hotspot user add name="1051515289" password="7857" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051515289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051515289";
};

# المستخدم 250: 1016792100
:do {
    /ip hotspot user add name="1016792100" password="8419" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016792100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016792100";
};

# المستخدم 251: 1011672556
:do {
    /ip hotspot user add name="1011672556" password="9778" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1011672556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1011672556";
};

# المستخدم 252: 1012111266
:do {
    /ip hotspot user add name="1012111266" password="5372" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012111266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012111266";
};

# المستخدم 253: 1016752340
:do {
    /ip hotspot user add name="1016752340" password="6684" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016752340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016752340";
};

# المستخدم 254: 1064869909
:do {
    /ip hotspot user add name="1064869909" password="8699" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1064869909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1064869909";
};

# المستخدم 255: 1077220434
:do {
    /ip hotspot user add name="1077220434" password="0978" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077220434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077220434";
};

# المستخدم 256: 1034755357
:do {
    /ip hotspot user add name="1034755357" password="9971" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034755357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034755357";
};

# المستخدم 257: 1041193554
:do {
    /ip hotspot user add name="1041193554" password="3141" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041193554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041193554";
};

# المستخدم 258: 1003491096
:do {
    /ip hotspot user add name="1003491096" password="4191" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003491096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003491096";
};

# المستخدم 259: 1047513400
:do {
    /ip hotspot user add name="1047513400" password="3504" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1047513400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1047513400";
};

# المستخدم 260: 1045211873
:do {
    /ip hotspot user add name="1045211873" password="4535" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045211873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045211873";
};

# المستخدم 261: 1009906997
:do {
    /ip hotspot user add name="1009906997" password="1344" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009906997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009906997";
};

# المستخدم 262: 1095628991
:do {
    /ip hotspot user add name="1095628991" password="4821" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1095628991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1095628991";
};

# المستخدم 263: 1039902796
:do {
    /ip hotspot user add name="1039902796" password="7354" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039902796";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039902796";
};

# المستخدم 264: 1092443925
:do {
    /ip hotspot user add name="1092443925" password="6997" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092443925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092443925";
};

# المستخدم 265: 1088386646
:do {
    /ip hotspot user add name="1088386646" password="7368" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088386646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088386646";
};

# المستخدم 266: 1065458145
:do {
    /ip hotspot user add name="1065458145" password="1943" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065458145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065458145";
};

# المستخدم 267: 1058718332
:do {
    /ip hotspot user add name="1058718332" password="6799" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058718332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058718332";
};

# المستخدم 268: 1005524579
:do {
    /ip hotspot user add name="1005524579" password="4095" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005524579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005524579";
};

# المستخدم 269: 1027503846
:do {
    /ip hotspot user add name="1027503846" password="8783" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027503846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027503846";
};

# المستخدم 270: 1080253432
:do {
    /ip hotspot user add name="1080253432" password="8531" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080253432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080253432";
};

# المستخدم 271: 1079533632
:do {
    /ip hotspot user add name="1079533632" password="4024" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079533632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079533632";
};

# المستخدم 272: 1015117780
:do {
    /ip hotspot user add name="1015117780" password="9134" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015117780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015117780";
};

# المستخدم 273: 1057778954
:do {
    /ip hotspot user add name="1057778954" password="2803" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057778954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057778954";
};

# المستخدم 274: 1059690066
:do {
    /ip hotspot user add name="1059690066" password="9756" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1059690066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1059690066";
};

# المستخدم 275: 1093188563
:do {
    /ip hotspot user add name="1093188563" password="4707" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093188563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093188563";
};

# المستخدم 276: 1005889927
:do {
    /ip hotspot user add name="1005889927" password="8687" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005889927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005889927";
};

# المستخدم 277: 1079868253
:do {
    /ip hotspot user add name="1079868253" password="1911" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079868253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079868253";
};

# المستخدم 278: 1010403583
:do {
    /ip hotspot user add name="1010403583" password="9384" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1010403583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1010403583";
};

# المستخدم 279: 1080700589
:do {
    /ip hotspot user add name="1080700589" password="2523" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080700589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080700589";
};

# المستخدم 280: 1092786452
:do {
    /ip hotspot user add name="1092786452" password="2717" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092786452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092786452";
};

# المستخدم 281: 1066859765
:do {
    /ip hotspot user add name="1066859765" password="2817" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066859765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066859765";
};

# المستخدم 282: 1032196718
:do {
    /ip hotspot user add name="1032196718" password="3976" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1032196718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1032196718";
};

# المستخدم 283: 1027899071
:do {
    /ip hotspot user add name="1027899071" password="5461" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027899071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027899071";
};

# المستخدم 284: 1075797498
:do {
    /ip hotspot user add name="1075797498" password="2394" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075797498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075797498";
};

# المستخدم 285: 1038485095
:do {
    /ip hotspot user add name="1038485095" password="4535" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038485095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038485095";
};

# المستخدم 286: 1075469577
:do {
    /ip hotspot user add name="1075469577" password="5859" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075469577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075469577";
};

# المستخدم 287: 1010782501
:do {
    /ip hotspot user add name="1010782501" password="2135" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1010782501";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1010782501";
};

# المستخدم 288: 1099219380
:do {
    /ip hotspot user add name="1099219380" password="5929" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1099219380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1099219380";
};

# المستخدم 289: 1029684291
:do {
    /ip hotspot user add name="1029684291" password="3702" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1029684291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1029684291";
};

# المستخدم 290: 1050439983
:do {
    /ip hotspot user add name="1050439983" password="6031" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1050439983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1050439983";
};

# المستخدم 291: 1088081412
:do {
    /ip hotspot user add name="1088081412" password="4943" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088081412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088081412";
};

# المستخدم 292: 1027625215
:do {
    /ip hotspot user add name="1027625215" password="4560" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027625215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027625215";
};

# المستخدم 293: 1097104793
:do {
    /ip hotspot user add name="1097104793" password="7779" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1097104793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1097104793";
};

# المستخدم 294: 1075729238
:do {
    /ip hotspot user add name="1075729238" password="1103" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075729238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075729238";
};

# المستخدم 295: 1077515310
:do {
    /ip hotspot user add name="1077515310" password="7458" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077515310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077515310";
};

# المستخدم 296: 1054374140
:do {
    /ip hotspot user add name="1054374140" password="6135" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1054374140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1054374140";
};

# المستخدم 297: 1046018253
:do {
    /ip hotspot user add name="1046018253" password="4570" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1046018253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1046018253";
};

# المستخدم 298: 1012108840
:do {
    /ip hotspot user add name="1012108840" password="6398" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012108840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012108840";
};

# المستخدم 299: 1033005158
:do {
    /ip hotspot user add name="1033005158" password="7795" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1033005158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1033005158";
};

# المستخدم 300: 1070517224
:do {
    /ip hotspot user add name="1070517224" password="0375" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070517224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070517224";
};

# المستخدم 301: 1038801965
:do {
    /ip hotspot user add name="1038801965" password="6880" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038801965";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038801965";
};

# المستخدم 302: 1012061269
:do {
    /ip hotspot user add name="1012061269" password="8061" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012061269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012061269";
};

# المستخدم 303: 1084031645
:do {
    /ip hotspot user add name="1084031645" password="9617" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084031645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084031645";
};

# المستخدم 304: 1006598777
:do {
    /ip hotspot user add name="1006598777" password="9257" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1006598777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1006598777";
};

# المستخدم 305: 1084964340
:do {
    /ip hotspot user add name="1084964340" password="3610" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084964340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084964340";
};

# المستخدم 306: 1011488209
:do {
    /ip hotspot user add name="1011488209" password="6801" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1011488209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1011488209";
};

# المستخدم 307: 1018884842
:do {
    /ip hotspot user add name="1018884842" password="9626" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1018884842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1018884842";
};

# المستخدم 308: 1097136478
:do {
    /ip hotspot user add name="1097136478" password="7613" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1097136478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1097136478";
};

# المستخدم 309: 1074692672
:do {
    /ip hotspot user add name="1074692672" password="2838" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074692672";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074692672";
};

# المستخدم 310: 1081871596
:do {
    /ip hotspot user add name="1081871596" password="7114" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081871596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081871596";
};

# المستخدم 311: 1085524044
:do {
    /ip hotspot user add name="1085524044" password="6926" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085524044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085524044";
};

# المستخدم 312: 1081184153
:do {
    /ip hotspot user add name="1081184153" password="4524" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081184153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081184153";
};

# المستخدم 313: 1059228462
:do {
    /ip hotspot user add name="1059228462" password="8911" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1059228462";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1059228462";
};

# المستخدم 314: 1024871325
:do {
    /ip hotspot user add name="1024871325" password="5007" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024871325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024871325";
};

# المستخدم 315: 1006920981
:do {
    /ip hotspot user add name="1006920981" password="0493" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1006920981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1006920981";
};

# المستخدم 316: 1037859628
:do {
    /ip hotspot user add name="1037859628" password="2353" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1037859628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1037859628";
};

# المستخدم 317: 1014603010
:do {
    /ip hotspot user add name="1014603010" password="4506" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1014603010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1014603010";
};

# المستخدم 318: 1019573860
:do {
    /ip hotspot user add name="1019573860" password="0223" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1019573860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1019573860";
};

# المستخدم 319: 1079855984
:do {
    /ip hotspot user add name="1079855984" password="6844" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079855984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079855984";
};

# المستخدم 320: 1040304297
:do {
    /ip hotspot user add name="1040304297" password="9114" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040304297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040304297";
};

# المستخدم 321: 1083838229
:do {
    /ip hotspot user add name="1083838229" password="0904" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1083838229";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1083838229";
};

# المستخدم 322: 1036730976
:do {
    /ip hotspot user add name="1036730976" password="5311" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036730976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036730976";
};

# المستخدم 323: 1031349525
:do {
    /ip hotspot user add name="1031349525" password="7634" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1031349525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1031349525";
};

# المستخدم 324: 1058221710
:do {
    /ip hotspot user add name="1058221710" password="0699" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058221710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058221710";
};

# المستخدم 325: 1035927411
:do {
    /ip hotspot user add name="1035927411" password="3589" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1035927411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1035927411";
};

# المستخدم 326: 1010919330
:do {
    /ip hotspot user add name="1010919330" password="6047" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1010919330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1010919330";
};

# المستخدم 327: 1090373124
:do {
    /ip hotspot user add name="1090373124" password="4765" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090373124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090373124";
};

# المستخدم 328: 1024169787
:do {
    /ip hotspot user add name="1024169787" password="2178" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024169787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024169787";
};

# المستخدم 329: 1093458980
:do {
    /ip hotspot user add name="1093458980" password="2493" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093458980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093458980";
};

# المستخدم 330: 1001460592
:do {
    /ip hotspot user add name="1001460592" password="7557" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1001460592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1001460592";
};

# المستخدم 331: 1074878850
:do {
    /ip hotspot user add name="1074878850" password="1772" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074878850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074878850";
};

# المستخدم 332: 1078016231
:do {
    /ip hotspot user add name="1078016231" password="3160" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1078016231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1078016231";
};

# المستخدم 333: 1087972711
:do {
    /ip hotspot user add name="1087972711" password="6047" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1087972711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1087972711";
};

# المستخدم 334: 1038657969
:do {
    /ip hotspot user add name="1038657969" password="7333" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038657969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038657969";
};

# المستخدم 335: 1065943408
:do {
    /ip hotspot user add name="1065943408" password="0455" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065943408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065943408";
};

# المستخدم 336: 1096886179
:do {
    /ip hotspot user add name="1096886179" password="5386" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096886179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096886179";
};

# المستخدم 337: 1050061310
:do {
    /ip hotspot user add name="1050061310" password="6113" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1050061310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1050061310";
};

# المستخدم 338: 1063055929
:do {
    /ip hotspot user add name="1063055929" password="7367" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1063055929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1063055929";
};

# المستخدم 339: 1024876080
:do {
    /ip hotspot user add name="1024876080" password="4441" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024876080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024876080";
};

# المستخدم 340: 1052576291
:do {
    /ip hotspot user add name="1052576291" password="7331" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1052576291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1052576291";
};

# المستخدم 341: 1004289324
:do {
    /ip hotspot user add name="1004289324" password="3337" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004289324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004289324";
};

# المستخدم 342: 1082524196
:do {
    /ip hotspot user add name="1082524196" password="0635" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1082524196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1082524196";
};

# المستخدم 343: 1070883758
:do {
    /ip hotspot user add name="1070883758" password="8745" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070883758";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070883758";
};

# المستخدم 344: 1077946048
:do {
    /ip hotspot user add name="1077946048" password="9535" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077946048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077946048";
};

# المستخدم 345: 1045188876
:do {
    /ip hotspot user add name="1045188876" password="8371" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045188876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045188876";
};

# المستخدم 346: 1070858534
:do {
    /ip hotspot user add name="1070858534" password="9427" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070858534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070858534";
};

# المستخدم 347: 1090313463
:do {
    /ip hotspot user add name="1090313463" password="5353" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090313463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090313463";
};

# المستخدم 348: 1013438674
:do {
    /ip hotspot user add name="1013438674" password="3047" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013438674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013438674";
};

# المستخدم 349: 1016482938
:do {
    /ip hotspot user add name="1016482938" password="6922" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016482938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016482938";
};

# المستخدم 350: 1075649169
:do {
    /ip hotspot user add name="1075649169" password="2529" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075649169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075649169";
};

# المستخدم 351: 1014181143
:do {
    /ip hotspot user add name="1014181143" password="0569" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1014181143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1014181143";
};

# المستخدم 352: 1047370675
:do {
    /ip hotspot user add name="1047370675" password="8717" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1047370675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1047370675";
};

# المستخدم 353: 1076174827
:do {
    /ip hotspot user add name="1076174827" password="3254" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1076174827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1076174827";
};

# المستخدم 354: 1079175251
:do {
    /ip hotspot user add name="1079175251" password="2742" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079175251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079175251";
};

# المستخدم 355: 1024801976
:do {
    /ip hotspot user add name="1024801976" password="8737" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024801976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024801976";
};

# المستخدم 356: 1056014240
:do {
    /ip hotspot user add name="1056014240" password="9837" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056014240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056014240";
};

# المستخدم 357: 1015906237
:do {
    /ip hotspot user add name="1015906237" password="8369" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015906237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015906237";
};

# المستخدم 358: 1033877594
:do {
    /ip hotspot user add name="1033877594" password="3555" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1033877594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1033877594";
};

# المستخدم 359: 1051183734
:do {
    /ip hotspot user add name="1051183734" password="0570" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051183734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051183734";
};

# المستخدم 360: 1098267194
:do {
    /ip hotspot user add name="1098267194" password="4212" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098267194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098267194";
};

# المستخدم 361: 1083619036
:do {
    /ip hotspot user add name="1083619036" password="4739" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1083619036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1083619036";
};

# المستخدم 362: 1000049217
:do {
    /ip hotspot user add name="1000049217" password="0648" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1000049217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1000049217";
};

# المستخدم 363: 1066323224
:do {
    /ip hotspot user add name="1066323224" password="9868" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066323224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066323224";
};

# المستخدم 364: 1053154772
:do {
    /ip hotspot user add name="1053154772" password="7794" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053154772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053154772";
};

# المستخدم 365: 1091971809
:do {
    /ip hotspot user add name="1091971809" password="9096" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1091971809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1091971809";
};

# المستخدم 366: 1040437516
:do {
    /ip hotspot user add name="1040437516" password="6306" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040437516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040437516";
};

# المستخدم 367: 1015046899
:do {
    /ip hotspot user add name="1015046899" password="9787" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1015046899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1015046899";
};

# المستخدم 368: 1056983900
:do {
    /ip hotspot user add name="1056983900" password="1366" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056983900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056983900";
};

# المستخدم 369: 1064796385
:do {
    /ip hotspot user add name="1064796385" password="8656" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1064796385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1064796385";
};

# المستخدم 370: 1051362456
:do {
    /ip hotspot user add name="1051362456" password="1849" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051362456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051362456";
};

# المستخدم 371: 1075002534
:do {
    /ip hotspot user add name="1075002534" password="1499" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075002534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075002534";
};

# المستخدم 372: 1056184650
:do {
    /ip hotspot user add name="1056184650" password="2759" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056184650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056184650";
};

# المستخدم 373: 1046873154
:do {
    /ip hotspot user add name="1046873154" password="7256" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1046873154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1046873154";
};

# المستخدم 374: 1084764554
:do {
    /ip hotspot user add name="1084764554" password="7579" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084764554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084764554";
};

# المستخدم 375: 1004895979
:do {
    /ip hotspot user add name="1004895979" password="6257" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004895979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004895979";
};

# المستخدم 376: 1025392961
:do {
    /ip hotspot user add name="1025392961" password="7426" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1025392961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1025392961";
};

# المستخدم 377: 1062600797
:do {
    /ip hotspot user add name="1062600797" password="8686" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1062600797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1062600797";
};

# المستخدم 378: 1085322604
:do {
    /ip hotspot user add name="1085322604" password="4454" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085322604";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085322604";
};

# المستخدم 379: 1064790116
:do {
    /ip hotspot user add name="1064790116" password="2880" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1064790116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1064790116";
};

# المستخدم 380: 1070419691
:do {
    /ip hotspot user add name="1070419691" password="8034" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070419691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070419691";
};

# المستخدم 381: 1002209920
:do {
    /ip hotspot user add name="1002209920" password="3901" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002209920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002209920";
};

# المستخدم 382: 1012791183
:do {
    /ip hotspot user add name="1012791183" password="7269" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012791183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012791183";
};

# المستخدم 383: 1041889179
:do {
    /ip hotspot user add name="1041889179" password="9310" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041889179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041889179";
};

# المستخدم 384: 1071878079
:do {
    /ip hotspot user add name="1071878079" password="0357" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1071878079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1071878079";
};

# المستخدم 385: 1053317962
:do {
    /ip hotspot user add name="1053317962" password="6970" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053317962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053317962";
};

# المستخدم 386: 1043168441
:do {
    /ip hotspot user add name="1043168441" password="8830" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1043168441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1043168441";
};

# المستخدم 387: 1012013954
:do {
    /ip hotspot user add name="1012013954" password="5370" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012013954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012013954";
};

# المستخدم 388: 1035627084
:do {
    /ip hotspot user add name="1035627084" password="0866" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1035627084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1035627084";
};

# المستخدم 389: 1042817971
:do {
    /ip hotspot user add name="1042817971" password="6859" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042817971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042817971";
};

# المستخدم 390: 1098181438
:do {
    /ip hotspot user add name="1098181438" password="5770" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098181438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098181438";
};

# المستخدم 391: 1058496822
:do {
    /ip hotspot user add name="1058496822" password="4662" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058496822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058496822";
};

# المستخدم 392: 1030401995
:do {
    /ip hotspot user add name="1030401995" password="2708" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030401995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030401995";
};

# المستخدم 393: 1076288924
:do {
    /ip hotspot user add name="1076288924" password="3262" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1076288924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1076288924";
};

# المستخدم 394: 1098219648
:do {
    /ip hotspot user add name="1098219648" password="6737" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098219648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098219648";
};

# المستخدم 395: 1038938177
:do {
    /ip hotspot user add name="1038938177" password="1073" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038938177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038938177";
};

# المستخدم 396: 1079019344
:do {
    /ip hotspot user add name="1079019344" password="4647" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1079019344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1079019344";
};

# المستخدم 397: 1042531193
:do {
    /ip hotspot user add name="1042531193" password="1077" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042531193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042531193";
};

# المستخدم 398: 1058774205
:do {
    /ip hotspot user add name="1058774205" password="1527" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058774205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058774205";
};

# المستخدم 399: 1000633737
:do {
    /ip hotspot user add name="1000633737" password="7566" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1000633737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1000633737";
};

# المستخدم 400: 1064581561
:do {
    /ip hotspot user add name="1064581561" password="1333" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1064581561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1064581561";
};

# المستخدم 401: 1085506242
:do {
    /ip hotspot user add name="1085506242" password="9369" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085506242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085506242";
};

# المستخدم 402: 1090212669
:do {
    /ip hotspot user add name="1090212669" password="9452" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090212669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090212669";
};

# المستخدم 403: 1009097448
:do {
    /ip hotspot user add name="1009097448" password="9841" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009097448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009097448";
};

# المستخدم 404: 1098978383
:do {
    /ip hotspot user add name="1098978383" password="9681" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1098978383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1098978383";
};

# المستخدم 405: 1066231766
:do {
    /ip hotspot user add name="1066231766" password="5743" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066231766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066231766";
};

# المستخدم 406: 1053935131
:do {
    /ip hotspot user add name="1053935131" password="8865" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053935131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053935131";
};

# المستخدم 407: 1003571707
:do {
    /ip hotspot user add name="1003571707" password="5158" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003571707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003571707";
};

# المستخدم 408: 1037336123
:do {
    /ip hotspot user add name="1037336123" password="5305" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1037336123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1037336123";
};

# المستخدم 409: 1048342248
:do {
    /ip hotspot user add name="1048342248" password="2511" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1048342248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1048342248";
};

# المستخدم 410: 1093120830
:do {
    /ip hotspot user add name="1093120830" password="3037" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093120830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093120830";
};

# المستخدم 411: 1091997752
:do {
    /ip hotspot user add name="1091997752" password="5630" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1091997752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1091997752";
};

# المستخدم 412: 1008620429
:do {
    /ip hotspot user add name="1008620429" password="9365" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008620429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008620429";
};

# المستخدم 413: 1087760381
:do {
    /ip hotspot user add name="1087760381" password="8246" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1087760381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1087760381";
};

# المستخدم 414: 1077583174
:do {
    /ip hotspot user add name="1077583174" password="8849" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077583174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077583174";
};

# المستخدم 415: 1061803795
:do {
    /ip hotspot user add name="1061803795" password="0737" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1061803795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1061803795";
};

# المستخدم 416: 1040325957
:do {
    /ip hotspot user add name="1040325957" password="2537" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040325957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040325957";
};

# المستخدم 417: 1040478255
:do {
    /ip hotspot user add name="1040478255" password="2079" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040478255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040478255";
};

# المستخدم 418: 1045483057
:do {
    /ip hotspot user add name="1045483057" password="0711" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045483057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045483057";
};

# المستخدم 419: 1091041961
:do {
    /ip hotspot user add name="1091041961" password="8586" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1091041961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1091041961";
};

# المستخدم 420: 1065252750
:do {
    /ip hotspot user add name="1065252750" password="1953" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065252750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065252750";
};

# المستخدم 421: 1072604806
:do {
    /ip hotspot user add name="1072604806" password="9369" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072604806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072604806";
};

# المستخدم 422: 1087414837
:do {
    /ip hotspot user add name="1087414837" password="8561" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1087414837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1087414837";
};

# المستخدم 423: 1025686904
:do {
    /ip hotspot user add name="1025686904" password="4506" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1025686904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1025686904";
};

# المستخدم 424: 1077381765
:do {
    /ip hotspot user add name="1077381765" password="1305" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077381765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077381765";
};

# المستخدم 425: 1003013982
:do {
    /ip hotspot user add name="1003013982" password="8562" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003013982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003013982";
};

# المستخدم 426: 1023170843
:do {
    /ip hotspot user add name="1023170843" password="1451" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023170843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023170843";
};

# المستخدم 427: 1036551310
:do {
    /ip hotspot user add name="1036551310" password="6674" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036551310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036551310";
};

# المستخدم 428: 1000842817
:do {
    /ip hotspot user add name="1000842817" password="3315" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1000842817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1000842817";
};

# المستخدم 429: 1093030869
:do {
    /ip hotspot user add name="1093030869" password="7277" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093030869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093030869";
};

# المستخدم 430: 1020427047
:do {
    /ip hotspot user add name="1020427047" password="2977" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020427047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020427047";
};

# المستخدم 431: 1078423775
:do {
    /ip hotspot user add name="1078423775" password="4689" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1078423775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1078423775";
};

# المستخدم 432: 1042491264
:do {
    /ip hotspot user add name="1042491264" password="0446" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042491264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042491264";
};

# المستخدم 433: 1071947673
:do {
    /ip hotspot user add name="1071947673" password="4961" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1071947673";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1071947673";
};

# المستخدم 434: 1035961359
:do {
    /ip hotspot user add name="1035961359" password="7768" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1035961359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1035961359";
};

# المستخدم 435: 1072425113
:do {
    /ip hotspot user add name="1072425113" password="8878" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072425113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072425113";
};

# المستخدم 436: 1085903099
:do {
    /ip hotspot user add name="1085903099" password="2315" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085903099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085903099";
};

# المستخدم 437: 1045142905
:do {
    /ip hotspot user add name="1045142905" password="9715" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045142905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045142905";
};

# المستخدم 438: 1070334254
:do {
    /ip hotspot user add name="1070334254" password="4924" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070334254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070334254";
};

# المستخدم 439: 1038534363
:do {
    /ip hotspot user add name="1038534363" password="5472" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038534363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038534363";
};

# المستخدم 440: 1022512183
:do {
    /ip hotspot user add name="1022512183" password="9499" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1022512183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1022512183";
};

# المستخدم 441: 1090664375
:do {
    /ip hotspot user add name="1090664375" password="7274" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090664375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090664375";
};

# المستخدم 442: 1074196632
:do {
    /ip hotspot user add name="1074196632" password="4295" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074196632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074196632";
};

# المستخدم 443: 1021454434
:do {
    /ip hotspot user add name="1021454434" password="5210" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1021454434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1021454434";
};

# المستخدم 444: 1041023546
:do {
    /ip hotspot user add name="1041023546" password="7166" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041023546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041023546";
};

# المستخدم 445: 1008650159
:do {
    /ip hotspot user add name="1008650159" password="2245" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008650159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008650159";
};

# المستخدم 446: 1054742581
:do {
    /ip hotspot user add name="1054742581" password="1343" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1054742581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1054742581";
};

# المستخدم 447: 1013168061
:do {
    /ip hotspot user add name="1013168061" password="4760" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013168061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013168061";
};

# المستخدم 448: 1062607414
:do {
    /ip hotspot user add name="1062607414" password="0235" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1062607414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1062607414";
};

# المستخدم 449: 1016513409
:do {
    /ip hotspot user add name="1016513409" password="6322" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016513409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016513409";
};

# المستخدم 450: 1034982480
:do {
    /ip hotspot user add name="1034982480" password="9383" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034982480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034982480";
};

# المستخدم 451: 1077029930
:do {
    /ip hotspot user add name="1077029930" password="5307" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077029930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077029930";
};

# المستخدم 452: 1045165523
:do {
    /ip hotspot user add name="1045165523" password="4590" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045165523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045165523";
};

# المستخدم 453: 1057300974
:do {
    /ip hotspot user add name="1057300974" password="6105" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057300974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057300974";
};

# المستخدم 454: 1036464039
:do {
    /ip hotspot user add name="1036464039" password="0559" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036464039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036464039";
};

# المستخدم 455: 1042432336
:do {
    /ip hotspot user add name="1042432336" password="4101" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042432336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042432336";
};

# المستخدم 456: 1070886855
:do {
    /ip hotspot user add name="1070886855" password="5666" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070886855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070886855";
};

# المستخدم 457: 1072656176
:do {
    /ip hotspot user add name="1072656176" password="2383" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072656176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072656176";
};

# المستخدم 458: 1096609914
:do {
    /ip hotspot user add name="1096609914" password="0773" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096609914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096609914";
};

# المستخدم 459: 1021674275
:do {
    /ip hotspot user add name="1021674275" password="6723" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1021674275";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1021674275";
};

# المستخدم 460: 1071826538
:do {
    /ip hotspot user add name="1071826538" password="6942" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1071826538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1071826538";
};

# المستخدم 461: 1092403266
:do {
    /ip hotspot user add name="1092403266" password="0350" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092403266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092403266";
};

# المستخدم 462: 1014757514
:do {
    /ip hotspot user add name="1014757514" password="5252" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1014757514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1014757514";
};

# المستخدم 463: 1090540111
:do {
    /ip hotspot user add name="1090540111" password="2955" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090540111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090540111";
};

# المستخدم 464: 1012148035
:do {
    /ip hotspot user add name="1012148035" password="0766" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1012148035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1012148035";
};

# المستخدم 465: 1009063840
:do {
    /ip hotspot user add name="1009063840" password="5651" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009063840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009063840";
};

# المستخدم 466: 1038156739
:do {
    /ip hotspot user add name="1038156739" password="4483" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1038156739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1038156739";
};

# المستخدم 467: 1088172666
:do {
    /ip hotspot user add name="1088172666" password="9166" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088172666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088172666";
};

# المستخدم 468: 1074602433
:do {
    /ip hotspot user add name="1074602433" password="1862" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074602433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074602433";
};

# المستخدم 469: 1005926134
:do {
    /ip hotspot user add name="1005926134" password="7251" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005926134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005926134";
};

# المستخدم 470: 1047153763
:do {
    /ip hotspot user add name="1047153763" password="3268" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1047153763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1047153763";
};

# المستخدم 471: 1080629487
:do {
    /ip hotspot user add name="1080629487" password="3262" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080629487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080629487";
};

# المستخدم 472: 1077828329
:do {
    /ip hotspot user add name="1077828329" password="5335" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077828329";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077828329";
};

# المستخدم 473: 1077525297
:do {
    /ip hotspot user add name="1077525297" password="6378" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077525297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077525297";
};

# المستخدم 474: 1055039991
:do {
    /ip hotspot user add name="1055039991" password="7531" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1055039991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1055039991";
};

# المستخدم 475: 1005598652
:do {
    /ip hotspot user add name="1005598652" password="4844" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1005598652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1005598652";
};

# المستخدم 476: 1006802938
:do {
    /ip hotspot user add name="1006802938" password="0861" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1006802938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1006802938";
};

# المستخدم 477: 1032716000
:do {
    /ip hotspot user add name="1032716000" password="1643" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1032716000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1032716000";
};

# المستخدم 478: 1081303848
:do {
    /ip hotspot user add name="1081303848" password="5291" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081303848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081303848";
};

# المستخدم 479: 1046188588
:do {
    /ip hotspot user add name="1046188588" password="2734" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1046188588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1046188588";
};

# المستخدم 480: 1054466508
:do {
    /ip hotspot user add name="1054466508" password="0437" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1054466508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1054466508";
};

# المستخدم 481: 1061129159
:do {
    /ip hotspot user add name="1061129159" password="4622" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1061129159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1061129159";
};

# المستخدم 482: 1094701563
:do {
    /ip hotspot user add name="1094701563" password="0785" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1094701563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1094701563";
};

# المستخدم 483: 1069024305
:do {
    /ip hotspot user add name="1069024305" password="0302" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1069024305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1069024305";
};

# المستخدم 484: 1013224771
:do {
    /ip hotspot user add name="1013224771" password="9280" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013224771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013224771";
};

# المستخدم 485: 1021474609
:do {
    /ip hotspot user add name="1021474609" password="9915" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1021474609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1021474609";
};

# المستخدم 486: 1045449260
:do {
    /ip hotspot user add name="1045449260" password="7676" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045449260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045449260";
};

# المستخدم 487: 1045978111
:do {
    /ip hotspot user add name="1045978111" password="7796" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045978111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045978111";
};

# المستخدم 488: 1053335019
:do {
    /ip hotspot user add name="1053335019" password="7928" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1053335019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1053335019";
};

# المستخدم 489: 1023344225
:do {
    /ip hotspot user add name="1023344225" password="9565" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023344225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023344225";
};

# المستخدم 490: 1042170081
:do {
    /ip hotspot user add name="1042170081" password="9501" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042170081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042170081";
};

# المستخدم 491: 1075552738
:do {
    /ip hotspot user add name="1075552738" password="8995" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075552738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075552738";
};

# المستخدم 492: 1020439477
:do {
    /ip hotspot user add name="1020439477" password="3182" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020439477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020439477";
};

# المستخدم 493: 1016280134
:do {
    /ip hotspot user add name="1016280134" password="2929" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016280134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016280134";
};

# المستخدم 494: 1090381587
:do {
    /ip hotspot user add name="1090381587" password="1163" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090381587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090381587";
};

# المستخدم 495: 1057877454
:do {
    /ip hotspot user add name="1057877454" password="4943" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057877454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057877454";
};

# المستخدم 496: 1034487458
:do {
    /ip hotspot user add name="1034487458" password="7600" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034487458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034487458";
};

# المستخدم 497: 1077710157
:do {
    /ip hotspot user add name="1077710157" password="2175" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077710157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077710157";
};

# المستخدم 498: 1077855481
:do {
    /ip hotspot user add name="1077855481" password="9031" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077855481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077855481";
};

# المستخدم 499: 1063984323
:do {
    /ip hotspot user add name="1063984323" password="1007" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1063984323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1063984323";
};

# المستخدم 500: 1061984922
:do {
    /ip hotspot user add name="1061984922" password="0036" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1061984922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1061984922";
};

# المستخدم 501: 1027097989
:do {
    /ip hotspot user add name="1027097989" password="2388" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027097989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027097989";
};

# المستخدم 502: 1075556190
:do {
    /ip hotspot user add name="1075556190" password="1490" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075556190";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075556190";
};

# المستخدم 503: 1041299677
:do {
    /ip hotspot user add name="1041299677" password="0700" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1041299677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1041299677";
};

# المستخدم 504: 1096322362
:do {
    /ip hotspot user add name="1096322362" password="4709" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096322362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096322362";
};

# المستخدم 505: 1052776637
:do {
    /ip hotspot user add name="1052776637" password="0202" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1052776637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1052776637";
};

# المستخدم 506: 1078878650
:do {
    /ip hotspot user add name="1078878650" password="0032" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1078878650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1078878650";
};

# المستخدم 507: 1013909567
:do {
    /ip hotspot user add name="1013909567" password="3593" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013909567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013909567";
};

# المستخدم 508: 1055240800
:do {
    /ip hotspot user add name="1055240800" password="3458" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1055240800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1055240800";
};

# المستخدم 509: 1082716882
:do {
    /ip hotspot user add name="1082716882" password="0847" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1082716882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1082716882";
};

# المستخدم 510: 1004385054
:do {
    /ip hotspot user add name="1004385054" password="7055" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1004385054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1004385054";
};

# المستخدم 511: 1003875059
:do {
    /ip hotspot user add name="1003875059" password="9576" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1003875059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1003875059";
};

# المستخدم 512: 1013612441
:do {
    /ip hotspot user add name="1013612441" password="1542" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013612441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013612441";
};

# المستخدم 513: 1002843720
:do {
    /ip hotspot user add name="1002843720" password="2117" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002843720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002843720";
};

# المستخدم 514: 1013174888
:do {
    /ip hotspot user add name="1013174888" password="5463" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013174888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013174888";
};

# المستخدم 515: 1048739516
:do {
    /ip hotspot user add name="1048739516" password="0277" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1048739516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1048739516";
};

# المستخدم 516: 1090958945
:do {
    /ip hotspot user add name="1090958945" password="8323" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090958945";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090958945";
};

# المستخدم 517: 1077929609
:do {
    /ip hotspot user add name="1077929609" password="5802" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077929609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077929609";
};

# المستخدم 518: 1059540804
:do {
    /ip hotspot user add name="1059540804" password="5256" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1059540804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1059540804";
};

# المستخدم 519: 1007004296
:do {
    /ip hotspot user add name="1007004296" password="3762" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1007004296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1007004296";
};

# المستخدم 520: 1051547819
:do {
    /ip hotspot user add name="1051547819" password="4903" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051547819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051547819";
};

# المستخدم 521: 1046092414
:do {
    /ip hotspot user add name="1046092414" password="9000" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1046092414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1046092414";
};

# المستخدم 522: 1065067380
:do {
    /ip hotspot user add name="1065067380" password="8455" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1065067380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1065067380";
};

# المستخدم 523: 1044143174
:do {
    /ip hotspot user add name="1044143174" password="7976" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1044143174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1044143174";
};

# المستخدم 524: 1020668967
:do {
    /ip hotspot user add name="1020668967" password="4485" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020668967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020668967";
};

# المستخدم 525: 1094192930
:do {
    /ip hotspot user add name="1094192930" password="4416" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1094192930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1094192930";
};

# المستخدم 526: 1085389986
:do {
    /ip hotspot user add name="1085389986" password="3760" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085389986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085389986";
};

# المستخدم 527: 1062738364
:do {
    /ip hotspot user add name="1062738364" password="8603" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1062738364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1062738364";
};

# المستخدم 528: 1093609141
:do {
    /ip hotspot user add name="1093609141" password="5270" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093609141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093609141";
};

# المستخدم 529: 1075001445
:do {
    /ip hotspot user add name="1075001445" password="6152" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1075001445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1075001445";
};

# المستخدم 530: 1036660137
:do {
    /ip hotspot user add name="1036660137" password="9829" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036660137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036660137";
};

# المستخدم 531: 1096037103
:do {
    /ip hotspot user add name="1096037103" password="8854" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096037103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096037103";
};

# المستخدم 532: 1039332475
:do {
    /ip hotspot user add name="1039332475" password="3383" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039332475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039332475";
};

# المستخدم 533: 1030882771
:do {
    /ip hotspot user add name="1030882771" password="6258" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1030882771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1030882771";
};

# المستخدم 534: 1067335151
:do {
    /ip hotspot user add name="1067335151" password="2294" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1067335151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1067335151";
};

# المستخدم 535: 1057204765
:do {
    /ip hotspot user add name="1057204765" password="3870" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057204765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057204765";
};

# المستخدم 536: 1034228038
:do {
    /ip hotspot user add name="1034228038" password="8522" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034228038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034228038";
};

# المستخدم 537: 1045457507
:do {
    /ip hotspot user add name="1045457507" password="1590" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045457507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045457507";
};

# المستخدم 538: 1008217840
:do {
    /ip hotspot user add name="1008217840" password="5024" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008217840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008217840";
};

# المستخدم 539: 1070117666
:do {
    /ip hotspot user add name="1070117666" password="2273" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070117666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070117666";
};

# المستخدم 540: 1002730104
:do {
    /ip hotspot user add name="1002730104" password="6478" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002730104";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002730104";
};

# المستخدم 541: 1036738603
:do {
    /ip hotspot user add name="1036738603" password="5797" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036738603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036738603";
};

# المستخدم 542: 1089871299
:do {
    /ip hotspot user add name="1089871299" password="3631" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1089871299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1089871299";
};

# المستخدم 543: 1021712558
:do {
    /ip hotspot user add name="1021712558" password="6927" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1021712558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1021712558";
};

# المستخدم 544: 1009006507
:do {
    /ip hotspot user add name="1009006507" password="4878" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1009006507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1009006507";
};

# المستخدم 545: 1072628043
:do {
    /ip hotspot user add name="1072628043" password="4436" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072628043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072628043";
};

# المستخدم 546: 1036864912
:do {
    /ip hotspot user add name="1036864912" password="4159" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1036864912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1036864912";
};

# المستخدم 547: 1024879821
:do {
    /ip hotspot user add name="1024879821" password="5382" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1024879821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1024879821";
};

# المستخدم 548: 1085049748
:do {
    /ip hotspot user add name="1085049748" password="5927" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1085049748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1085049748";
};

# المستخدم 549: 1020616034
:do {
    /ip hotspot user add name="1020616034" password="3746" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020616034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020616034";
};

# المستخدم 550: 1016379754
:do {
    /ip hotspot user add name="1016379754" password="4713" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1016379754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1016379754";
};

# المستخدم 551: 1070145110
:do {
    /ip hotspot user add name="1070145110" password="0744" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070145110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070145110";
};

# المستخدم 552: 1023488477
:do {
    /ip hotspot user add name="1023488477" password="0526" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1023488477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1023488477";
};

# المستخدم 553: 1080729973
:do {
    /ip hotspot user add name="1080729973" password="3118" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080729973";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080729973";
};

# المستخدم 554: 1042408483
:do {
    /ip hotspot user add name="1042408483" password="7067" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042408483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042408483";
};

# المستخدم 555: 1051567140
:do {
    /ip hotspot user add name="1051567140" password="4479" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051567140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051567140";
};

# المستخدم 556: 1092882080
:do {
    /ip hotspot user add name="1092882080" password="0666" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1092882080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1092882080";
};

# المستخدم 557: 1072076921
:do {
    /ip hotspot user add name="1072076921" password="8786" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1072076921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1072076921";
};

# المستخدم 558: 1073501754
:do {
    /ip hotspot user add name="1073501754" password="0799" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1073501754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1073501754";
};

# المستخدم 559: 1048346981
:do {
    /ip hotspot user add name="1048346981" password="7260" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1048346981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1048346981";
};

# المستخدم 560: 1042861969
:do {
    /ip hotspot user add name="1042861969" password="3660" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042861969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042861969";
};

# المستخدم 561: 1093281883
:do {
    /ip hotspot user add name="1093281883" password="3759" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1093281883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1093281883";
};

# المستخدم 562: 1084127041
:do {
    /ip hotspot user add name="1084127041" password="4523" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1084127041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1084127041";
};

# المستخدم 563: 1008517431
:do {
    /ip hotspot user add name="1008517431" password="9488" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1008517431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1008517431";
};

# المستخدم 564: 1057828982
:do {
    /ip hotspot user add name="1057828982" password="4784" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1057828982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1057828982";
};

# المستخدم 565: 1017387694
:do {
    /ip hotspot user add name="1017387694" password="5335" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1017387694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1017387694";
};

# المستخدم 566: 1027138086
:do {
    /ip hotspot user add name="1027138086" password="6198" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027138086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027138086";
};

# المستخدم 567: 1088740756
:do {
    /ip hotspot user add name="1088740756" password="2944" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1088740756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1088740756";
};

# المستخدم 568: 1097011719
:do {
    /ip hotspot user add name="1097011719" password="8457" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1097011719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1097011719";
};

# المستخدم 569: 1087840358
:do {
    /ip hotspot user add name="1087840358" password="9709" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1087840358";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1087840358";
};

# المستخدم 570: 1050751936
:do {
    /ip hotspot user add name="1050751936" password="5593" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1050751936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1050751936";
};

# المستخدم 571: 1090313745
:do {
    /ip hotspot user add name="1090313745" password="8264" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090313745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090313745";
};

# المستخدم 572: 1039152706
:do {
    /ip hotspot user add name="1039152706" password="1171" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039152706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039152706";
};

# المستخدم 573: 1078836185
:do {
    /ip hotspot user add name="1078836185" password="4830" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1078836185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1078836185";
};

# المستخدم 574: 1066601998
:do {
    /ip hotspot user add name="1066601998" password="2553" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1066601998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1066601998";
};

# المستخدم 575: 1013793294
:do {
    /ip hotspot user add name="1013793294" password="2758" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1013793294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1013793294";
};

# المستخدم 576: 1002333029
:do {
    /ip hotspot user add name="1002333029" password="7712" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002333029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002333029";
};

# المستخدم 577: 1045489613
:do {
    /ip hotspot user add name="1045489613" password="0754" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1045489613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1045489613";
};

# المستخدم 578: 1074218065
:do {
    /ip hotspot user add name="1074218065" password="5753" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074218065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074218065";
};

# المستخدم 579: 1002904753
:do {
    /ip hotspot user add name="1002904753" password="3776" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1002904753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1002904753";
};

# المستخدم 580: 1020038536
:do {
    /ip hotspot user add name="1020038536" password="1534" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020038536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020038536";
};

# المستخدم 581: 1096895706
:do {
    /ip hotspot user add name="1096895706" password="2937" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1096895706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1096895706";
};

# المستخدم 582: 1020629919
:do {
    /ip hotspot user add name="1020629919" password="6358" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1020629919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1020629919";
};

# المستخدم 583: 1042846008
:do {
    /ip hotspot user add name="1042846008" password="7938" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1042846008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1042846008";
};

# المستخدم 584: 1051531708
:do {
    /ip hotspot user add name="1051531708" password="9259" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1051531708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1051531708";
};

# المستخدم 585: 1077387506
:do {
    /ip hotspot user add name="1077387506" password="9343" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1077387506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1077387506";
};

# المستخدم 586: 1090625872
:do {
    /ip hotspot user add name="1090625872" password="1502" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1090625872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1090625872";
};

# المستخدم 587: 1032074204
:do {
    /ip hotspot user add name="1032074204" password="8436" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1032074204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1032074204";
};

# المستخدم 588: 1074461976
:do {
    /ip hotspot user add name="1074461976" password="3520" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1074461976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1074461976";
};

# المستخدم 589: 1080707005
:do {
    /ip hotspot user add name="1080707005" password="8540" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080707005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080707005";
};

# المستخدم 590: 1007064046
:do {
    /ip hotspot user add name="1007064046" password="8320" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1007064046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1007064046";
};

# المستخدم 591: 1081629560
:do {
    /ip hotspot user add name="1081629560" password="9932" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1081629560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1081629560";
};

# المستخدم 592: 1032011679
:do {
    /ip hotspot user add name="1032011679" password="9208" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1032011679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1032011679";
};

# المستخدم 593: 1048086975
:do {
    /ip hotspot user add name="1048086975" password="8539" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1048086975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1048086975";
};

# المستخدم 594: 1056743113
:do {
    /ip hotspot user add name="1056743113" password="2047" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1056743113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1056743113";
};

# المستخدم 595: 1058945270
:do {
    /ip hotspot user add name="1058945270" password="7976" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1058945270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1058945270";
};

# المستخدم 596: 1034239291
:do {
    /ip hotspot user add name="1034239291" password="0495" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034239291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034239291";
};

# المستخدم 597: 1007935803
:do {
    /ip hotspot user add name="1007935803" password="5047" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1007935803";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1007935803";
};

# المستخدم 598: 1070434019
:do {
    /ip hotspot user add name="1070434019" password="1541" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1070434019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1070434019";
};

# المستخدم 599: 1026112041
:do {
    /ip hotspot user add name="1026112041" password="7969" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1026112041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1026112041";
};

# المستخدم 600: 1091585394
:do {
    /ip hotspot user add name="1091585394" password="3628" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1091585394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1091585394";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10-marawan";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
