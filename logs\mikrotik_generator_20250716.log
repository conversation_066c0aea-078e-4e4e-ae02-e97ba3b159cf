2025-07-16 00:07:16,571 - INFO - تم بدء تشغيل التطبيق
2025-07-16 00:07:16,822 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 00:07:16,852 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 00:07:16,852 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:07:16,954 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 00:07:18,016 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 00:07:18,016 - INFO - تم <PERSON>عداد التطبيق بنجاح
2025-07-16 00:07:20,016 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 00:07:20,321 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 00:07:20,322 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 00:07:23,327 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:07:23,363 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:07:23,774 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:07:23,775 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:08:22,528 - INFO - معالجة callback: select_system_um
2025-07-16 00:08:24,538 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:08:26,097 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:08:27,468 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 00:08:27,469 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 00:08:28,148 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:08:28,240 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:28,341 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 00:08:28,341 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:08:28,341 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:08:28,343 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:08:28,344 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:28,914 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:08:29,134 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:08:29,135 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:29,856 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:08:29,859 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:08:29,926 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:08:29,955 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 00:08:29,965 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:08:29,966 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:08:29,966 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:08:30,328 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:30,331 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:08:30,332 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:08:30,332 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:08:30,332 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:08:30,427 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:08:30,427 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:08:30,432 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:08:30,450 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:08:30,453 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:08:30,454 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:08:30,760 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:08:30,928 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:30,929 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:08:31,054 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:31,057 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:08:43,675 - INFO - معالجة الأمر: /start
2025-07-16 00:08:44,002 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:08:53,293 - INFO - معالجة callback: single_card
2025-07-16 00:08:53,563 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:08:53,564 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:08:53,828 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:09:14,464 - INFO - معالجة callback: card_count_1
2025-07-16 00:09:14,684 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:09:14,926 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:09:18,947 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:09:19,184 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:09:19,442 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:09:19,444 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,445 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,549 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:09:19,550 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:09:19,550 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:09:19,550 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:09:19,551 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,551 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,858 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:09:19,858 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,859 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,961 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:09:19,966 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:09:19,968 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:09:19,969 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:09:20,140 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:09:20,141 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:09:20,147 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:09:20,245 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:09:20,246 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:09:20,544 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:09:20,544 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:09:20,547 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:09:20,551 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:09:20,551 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:09:20,552 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:09:20,555 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:09:20,567 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:09:20,570 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:09:20,571 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:09:20,572 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:09:20,572 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:09:20,572 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:09:20,622 - INFO - تم توليد 1 حساب
2025-07-16 00:09:20,623 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:09:20,629 - ERROR - خطأ في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:09:20,629 - INFO - محاولة إعادة التوليد...
2025-07-16 00:09:20,635 - INFO - تم توليد 1 حساب
2025-07-16 00:09:20,635 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 7 (من generate_all)
2025-07-16 00:09:20,640 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: فشل في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:09,133 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:10:09,377 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:10:09,684 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:10:09,686 - INFO - النظام hotspot مفعل بالفعل
2025-07-16 00:10:09,686 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-16 00:10:09,688 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:10:09,728 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:10:09,729 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:10:09,729 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:10:09,752 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:10:09,753 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:10:09,782 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:10:09,782 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:10:09,785 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:10:09,789 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:10:09,789 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:10:09,790 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:10:09,793 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:10:09,806 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:10:09,813 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:10:09,814 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:10:09,815 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:10:09,815 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:10:09,815 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:10:09,822 - INFO - تم توليد 1 حساب
2025-07-16 00:10:09,824 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 8 (من generate_all)
2025-07-16 00:10:09,830 - ERROR - خطأ في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:09,830 - INFO - محاولة إعادة التوليد...
2025-07-16 00:10:09,841 - INFO - تم توليد 1 حساب
2025-07-16 00:10:09,843 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 9 (من generate_all)
2025-07-16 00:10:09,849 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: فشل في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:14,860 - INFO - بدء إغلاق التطبيق
2025-07-16 00:10:14,861 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:28:29,212 - INFO - تم بدء تشغيل التطبيق
2025-07-16 00:28:29,402 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 00:28:29,484 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 00:28:29,484 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:30,318 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 00:28:31,972 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 00:28:31,973 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 00:28:34,013 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 00:28:34,593 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 00:28:34,594 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 00:28:35,031 - INFO - معالجة الأمر: /start
2025-07-16 00:28:35,554 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:28:37,598 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:28:37,599 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:28:38,054 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:28:38,054 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:28:44,946 - INFO - معالجة callback: select_system_um
2025-07-16 00:28:45,489 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:28:46,554 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:28:47,064 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 00:28:47,064 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 00:28:47,325 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:28:47,399 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:47,500 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 00:28:47,501 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:28:47,501 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:28:47,503 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:28:47,504 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:48,029 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:28:48,193 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:28:48,194 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:48,979 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:28:48,979 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:28:49,043 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:28:49,062 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 00:28:49,063 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:28:49,063 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:28:49,063 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:28:49,386 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:28:49,392 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:28:49,392 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:28:49,392 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:28:49,393 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:28:49,503 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:28:49,503 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:28:49,506 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:28:49,517 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:28:49,520 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:28:49,522 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:28:49,803 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:28:50,006 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:28:50,011 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:28:50,119 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:28:50,120 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:28:55,594 - INFO - معالجة الأمر: /start
2025-07-16 00:28:55,883 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:28:59,765 - INFO - معالجة callback: single_card
2025-07-16 00:29:00,023 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:29:00,026 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:29:00,369 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:29:08,398 - INFO - معالجة callback: card_count_1
2025-07-16 00:29:08,677 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:29:08,996 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:29:10,509 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:29:10,854 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:29:11,266 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:29:11,267 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:29:11,268 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:11,372 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:29:11,372 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:29:11,373 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:29:11,373 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:29:11,373 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:29:11,374 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:11,679 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:29:11,682 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:29:11,682 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:11,785 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:29:11,789 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:29:11,791 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:29:11,791 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:29:11,956 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:29:12,011 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:12,014 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:12,037 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:12,038 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:12,344 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:29:12,564 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:12,568 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:12,573 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:12,614 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:29:12,624 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:29:12,628 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:29:12,640 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:29:12,696 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:12,696 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:29:12,698 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:29:12,698 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:29:12,698 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:29:12,717 - INFO - تم توليد 1 حساب
2025-07-16 00:29:12,723 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:29:12,729 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:29:12,734 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 00:29:12,734 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:29:12,798 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:29:12,919 - INFO - ✅ تم إرسال المستخدم: 02572797237
2025-07-16 00:29:12,919 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 00:29:12,919 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 00:29:13,172 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 00:29:27,731 - INFO - معالجة الأمر: /start
2025-07-16 00:29:28,001 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:29:33,568 - INFO - معالجة callback: select_system_um
2025-07-16 00:29:34,057 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:29:36,408 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:29:36,914 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-16 00:29:36,914 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-16 00:29:37,375 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:29:37,376 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:29:37,376 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:37,482 - WARNING - تحذير: فشل في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:29:37,482 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:29:37,483 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:29:37,484 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:29:37,485 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:37,752 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:29:37,776 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:29:37,777 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:37,961 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:29:37,963 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:29:37,982 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:29:37,989 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-16 00:29:37,990 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:29:37,990 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:37,991 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:38,198 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:38,199 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:29:38,200 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:29:38,200 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:38,200 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:38,298 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:29:38,298 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:38,310 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:38,321 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:38,324 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:38,326 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:29:38,595 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:29:38,804 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:38,807 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:38,927 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:38,928 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:41,570 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-16 00:29:43,329 - INFO - معالجة callback: independent_count_um_10_lightning_5
2025-07-16 00:29:43,839 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:43,840 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:43,854 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:43,855 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:29:43,855 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:29:43,856 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:43,856 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:43,949 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:29:43,950 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:43,953 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:43,969 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:44,308 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:44,309 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:44,324 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,325 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:29:44,325 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:29:44,326 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:44,326 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:44,421 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:29:44,421 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:44,423 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:44,446 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:44,448 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-16 00:29:44,450 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,453 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:44,465 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-16 00:29:44,466 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:29:44,466 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:29:44,466 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-16 00:29:44,467 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 5 حساب
2025-07-16 00:29:44,467 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-16 00:29:44,467 - INFO - ⚡ البرق للتلجرام: تم توليد 5 حساب
2025-07-16 00:29:44,467 - INFO - ⚡ البرق للتلجرام: تنفيذ مجدول لـ 5 كارت (حد التقسيم: 100)
2025-07-16 00:29:44,509 - INFO - ⚡ البرق للتلجرام: تخطي حفظ الملفات المحلية - استخدام الجدولة المؤقتة
2025-07-16 00:29:44,510 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:29:44,588 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:29:44,845 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,846 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:44,870 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,873 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:44,937 - INFO - ⚡ تم إضافة السكريبت: telegram_lightning_user_manager_20250716_002944
2025-07-16 00:29:44,978 - INFO - ⚡ وقت MikroTik الحالي: 21:29:44
2025-07-16 00:29:45,101 - INFO - ⚡ سيتم تنفيذ السكريبت في: 21:29:47 (بعد 3 ثواني من وقت MikroTik)
2025-07-16 00:29:45,102 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:45,103 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:45,160 - INFO - ⚡ تم إنشاء الجدولة المؤقتة: telegram_schedule_20250716_002944 للتنفيذ في 21:29:47
2025-07-16 00:29:45,162 - INFO - ⚡ تم إعداد البرق المجدول للتلجرام بنجاح - السكريبت: telegram_lightning_user_manager_20250716_002944, الجدولة: telegram_schedule_20250716_002944
2025-07-16 00:29:45,163 - INFO - ⚡ سيتم تنفيذ السكريبت في 21:29:47 مع تنظيف تلقائي بعد الانتهاء
2025-07-16 00:29:45,164 - INFO - ⚡ البرق للتلجرام مكتمل: تم جدولة إنشاء وإرسال 5 كارت بنجاح (مع تنظيف تلقائي)
2025-07-16 00:29:47,782 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-16 00:29:47,926 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-16-07-2025-00-29-47-um.pdf
2025-07-16 00:29:47,945 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-16-07-2025-00-29-47-um.rsc
2025-07-16 00:29:50,225 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-16-07-2025-00-29-47-um.pdf
2025-07-16 00:29:50,471 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-16 00:29:58,508 - INFO - معالجة الأمر: /start
2025-07-16 00:29:58,762 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:30:00,396 - INFO - معالجة callback: select_system_hs
2025-07-16 00:30:01,017 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-16 00:30:02,312 - INFO - معالجة callback: independent_template_hs_10
2025-07-16 00:30:02,789 - INFO - 🔄 تبديل النظام تلقائياً من user_manager إلى hotspot
2025-07-16 00:30:02,789 - INFO - 🔄 طلب تبديل النظام من التلجرام: user_manager → hotspot
2025-07-16 00:30:03,100 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:30:03,100 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:30:03,101 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:30:03,208 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:30:03,209 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:30:03,209 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:30:03,213 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:30:03,214 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:30:03,497 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:30:03,497 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:30:03,498 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:30:03,600 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:30:03,604 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:30:03,606 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:30:03,737 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:30:03,740 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:30:03,752 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:30:03,753 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:30:03,764 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:30:03,809 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:30:03,820 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:30:04,118 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:30:04,121 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:30:04,124 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:04,130 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:30:04,133 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:04,134 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:30:04,468 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:30:11,470 - INFO - معالجة الأمر: /start
2025-07-16 00:30:11,782 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:30:13,275 - INFO - معالجة callback: single_card
2025-07-16 00:30:13,660 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:30:13,661 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:30:14,017 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:30:15,846 - INFO - معالجة callback: card_count_1
2025-07-16 00:30:16,070 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:30:16,335 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:30:17,424 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:30:17,666 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:30:17,951 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:30:17,952 - INFO - النظام hotspot مفعل بالفعل
2025-07-16 00:30:17,953 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-16 00:30:17,953 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:30:18,010 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:30:18,022 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:30:18,031 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:30:18,049 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:30:18,050 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:30:18,077 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:30:18,078 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:30:18,081 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:18,085 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:30:18,088 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:30:18,090 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:30:18,094 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:30:18,108 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:30:18,111 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:18,111 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:30:18,112 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:30:18,113 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:30:18,113 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:30:18,122 - INFO - تم توليد 1 حساب
2025-07-16 00:30:18,127 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:30:18,134 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:30:18,135 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 00:30:18,135 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:30:18,234 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:30:18,322 - INFO - ✅ تم إرسال المستخدم: 02916176295
2025-07-16 00:30:18,322 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 00:30:18,322 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 00:30:18,673 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 00:31:10,443 - INFO - معالجة الأمر: /start
2025-07-16 00:31:10,697 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:31:14,484 - INFO - معالجة callback: select_system_um
2025-07-16 00:31:14,951 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:31:16,002 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:31:16,511 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-16 00:31:16,511 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-16 00:31:16,780 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:31:16,781 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:31:16,781 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:31:16,885 - WARNING - تحذير: فشل في حفظ الإعدادات: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:31:16,886 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:31:16,886 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:31:16,888 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:31:16,888 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:31:17,190 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:31:17,217 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:31:17,217 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:31:17,413 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:31:17,414 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:31:17,580 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:31:17,582 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-16 00:31:17,584 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:31:17,585 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:31:17,588 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:31:17,649 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:17,649 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:31:17,650 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:31:17,650 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:31:17,650 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:31:17,772 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:31:17,773 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:31:17,775 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:17,788 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:31:17,791 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:17,792 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:31:18,109 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:31:18,278 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:18,279 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:18,388 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:18,389 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:26,392 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-16 00:31:36,356 - INFO - معالجة callback: independent_count_um_10_lightning_10
2025-07-16 00:31:41,212 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:31:41,212 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:31:41,226 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:41,227 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:31:41,227 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:31:41,227 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:31:41,228 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:31:41,343 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:31:41,343 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:31:41,346 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:41,359 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:31:41,849 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:41,850 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:42,069 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:42,070 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:42,902 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:31:42,902 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:31:42,916 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:42,917 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:31:42,917 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:31:42,917 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:31:42,918 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:31:43,033 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:31:43,033 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:31:43,036 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:43,049 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:31:43,051 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-16 00:31:43,051 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-16 00:31:43,052 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:31:43,052 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:31:43,052 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 10
2025-07-16 00:31:43,052 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 10 حساب
2025-07-16 00:31:43,052 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 10
2025-07-16 00:31:43,053 - INFO - ⚡ البرق للتلجرام: تم توليد 10 حساب
2025-07-16 00:31:43,053 - INFO - ⚡ البرق للتلجرام: تنفيذ مجدول لـ 10 كارت (حد التقسيم: 100)
2025-07-16 00:31:43,053 - INFO - ⚡ البرق للتلجرام: تخطي حفظ الملفات المحلية - استخدام الجدولة المؤقتة
2025-07-16 00:31:43,053 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:31:43,120 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:31:43,174 - INFO - ⚡ تم إضافة السكريبت: telegram_lightning_user_manager_20250716_003143
2025-07-16 00:31:43,210 - INFO - ⚡ وقت MikroTik الحالي: 21:31:42
2025-07-16 00:31:43,211 - INFO - ⚡ سيتم تنفيذ السكريبت في: 21:31:45 (بعد 3 ثواني من وقت MikroTik)
2025-07-16 00:31:43,262 - INFO - ⚡ تم إنشاء الجدولة المؤقتة: telegram_schedule_20250716_003143 للتنفيذ في 21:31:45
2025-07-16 00:31:43,265 - INFO - ⚡ تم إعداد البرق المجدول للتلجرام بنجاح - السكريبت: telegram_lightning_user_manager_20250716_003143, الجدولة: telegram_schedule_20250716_003143
2025-07-16 00:31:43,265 - INFO - ⚡ سيتم تنفيذ السكريبت في 21:31:45 مع تنظيف تلقائي بعد الانتهاء
2025-07-16 00:31:43,265 - INFO - ⚡ البرق للتلجرام مكتمل: تم جدولة إنشاء وإرسال 10 كارت بنجاح (مع تنظيف تلقائي)
2025-07-16 00:31:43,410 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:43,411 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:43,541 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:43,542 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:46,087 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10
2025-07-16 00:31:46,149 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-10 كارت-16-07-2025-00-31-46-um.pdf
2025-07-16 00:31:46,164 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-10 كارت-16-07-2025-00-31-46-um.rsc
2025-07-16 00:31:46,888 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-10 كارت-16-07-2025-00-31-46-um.pdf
2025-07-16 00:31:47,414 - INFO - تم إرسال 10 كرت عبر التلجرام باستخدام قالب 10
2025-07-16 00:31:54,873 - INFO - معالجة الأمر: /start
2025-07-16 00:31:55,186 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:31:57,022 - INFO - معالجة callback: single_card
2025-07-16 00:31:57,262 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:31:57,263 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:31:57,525 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:31:58,739 - INFO - معالجة callback: card_count_1
2025-07-16 00:31:58,955 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:31:59,219 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:32:00,546 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:32:00,794 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:32:01,050 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:32:01,051 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:32:01,052 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:32:01,053 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:32:01,157 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:32:01,158 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:32:01,158 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:32:01,158 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:32:01,158 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:32:01,159 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:32:01,467 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:32:01,467 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:32:01,468 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:32:01,570 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas5.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:32:01,577 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:32:01,577 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:32:01,579 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:32:01,771 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:32:01,773 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:32:01,790 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:32:01,849 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:32:01,850 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:32:02,133 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:32:02,133 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:32:02,136 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:32:02,141 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:32:02,141 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:32:02,142 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:32:02,145 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:32:02,157 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:32:02,160 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:32:02,160 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:32:02,164 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:32:02,164 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:32:02,165 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:32:02,172 - INFO - تم توليد 1 حساب
2025-07-16 00:32:02,173 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 11 (من generate_all)
2025-07-16 00:32:02,178 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas5.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:32:02,178 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 00:32:02,178 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:32:02,241 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:32:02,324 - INFO - ✅ تم إرسال المستخدم: 02531445019
2025-07-16 00:32:02,324 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 00:32:02,324 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 00:32:02,574 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 00:32:12,830 - INFO - بدء إغلاق التطبيق
2025-07-16 00:32:12,830 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas5.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:57:09,580 - INFO - تم بدء تشغيل التطبيق
2025-07-16 00:57:09,633 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 00:57:09,635 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 00:57:09,635 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:57:09,738 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 00:57:10,475 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 00:57:10,475 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 00:57:12,475 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 00:57:12,753 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 00:57:12,778 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 00:57:15,796 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:57:15,827 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:57:16,136 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:57:16,136 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:57:22,816 - INFO - معالجة callback: select_system_um
2025-07-16 00:57:23,309 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:57:24,727 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:57:25,325 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 00:57:25,325 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 00:57:25,673 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:57:25,763 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:57:25,863 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 00:57:25,864 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:57:25,864 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:57:25,865 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:57:25,869 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:57:26,491 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:57:26,696 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:57:26,700 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:57:27,400 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:57:27,402 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:57:27,482 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:57:27,568 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 00:57:27,572 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:57:27,573 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:57:27,574 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:57:27,735 - INFO - ✅ تم تطبيق حد تقسيم البرق الافتراضي: 100
2025-07-16 00:57:27,810 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:57:27,823 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:57:27,825 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:57:27,825 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:57:27,826 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:57:27,941 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 70 إعدادات
2025-07-16 00:57:27,941 - INFO - ✅ تم تطبيق 70 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:57:27,944 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:57:27,956 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:57:27,960 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:57:27,967 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:57:28,246 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:57:28,449 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:57:28,452 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:57:28,569 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:57:28,570 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:57:50,451 - INFO - تم حفظ القالب: 20
2025-07-16 00:57:50,955 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:57:50,956 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:57:51,211 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:57:51,212 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:58:08,406 - INFO - تم حفظ القالب: 10
2025-07-16 00:58:08,908 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:58:08,909 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:58:09,154 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:58:09,154 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:58:39,861 - INFO - بدء إغلاق التطبيق
2025-07-16 00:58:39,963 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-16 00:58:39,964 - INFO - تم إغلاق التطبيق بنجاح
2025-07-16 02:07:33,581 - INFO - تم بدء تشغيل التطبيق
2025-07-16 02:07:33,631 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 02:07:33,633 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 02:07:33,633 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 02:07:33,736 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 02:07:34,104 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 02:07:34,105 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 02:07:36,110 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 02:07:36,464 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 02:07:36,465 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 02:07:39,463 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 02:07:39,465 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 02:07:39,793 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 02:07:39,793 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 02:07:48,910 - INFO - معالجة callback: select_system_um
2025-07-16 02:07:49,359 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 02:07:50,332 - INFO - معالجة callback: independent_template_um_10
2025-07-16 02:07:50,827 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 02:07:50,827 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 02:07:51,084 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 02:07:51,096 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 02:07:51,197 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 02:07:51,197 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 02:07:51,197 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 02:07:51,198 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 02:07:51,200 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 02:07:51,655 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 02:07:51,822 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 02:07:51,823 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 02:07:51,936 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 02:07:51,937 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 02:07:52,023 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 02:07:52,030 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 02:07:52,031 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 02:07:52,031 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 02:07:52,032 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 02:07:52,219 - INFO - ✅ تم تطبيق حد تقسيم البرق: 100
2025-07-16 02:07:52,275 - INFO - 🖼️ محاولة تحميل صورة الخلفية: 100100_1.jpg
2025-07-16 02:07:52,309 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 02:07:52,310 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 02:07:52,310 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 02:07:52,310 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 02:07:52,558 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 70 إعدادات
2025-07-16 02:07:52,558 - INFO - ✅ تم تطبيق 70 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 02:07:52,562 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 02:07:52,576 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 02:07:52,579 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 02:07:52,580 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 02:07:52,974 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 02:07:53,067 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 02:07:53,069 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 02:07:53,179 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 02:07:53,180 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 02:08:21,429 - INFO - تم حفظ القالب: 20
2025-07-16 02:08:21,941 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 02:08:21,943 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 02:08:22,287 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 02:08:22,287 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 02:08:35,806 - INFO - تم حفظ القالب: 10
2025-07-16 02:08:36,316 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 02:08:36,317 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 02:08:36,588 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 02:08:36,588 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 02:08:51,222 - INFO - بدء إغلاق التطبيق
2025-07-16 02:08:51,228 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-16 02:08:51,228 - INFO - تم إغلاق التطبيق بنجاح
2025-07-16 03:46:05,152 - INFO - تم بدء تشغيل التطبيق
2025-07-16 03:46:05,258 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 03:46:05,516 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 03:46:05,516 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:46:06,656 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 03:46:18,960 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 03:46:18,960 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 03:46:21,001 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 03:46:21,781 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 03:46:21,781 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 03:46:24,786 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 03:46:24,894 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 03:46:25,151 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 03:46:25,151 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 03:46:27,443 - INFO - معالجة callback: select_system_um
2025-07-16 03:46:28,104 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 03:46:29,522 - INFO - معالجة callback: independent_template_um_10
2025-07-16 03:46:30,178 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 03:46:30,178 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 03:46:30,508 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 03:46:31,705 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:46:31,805 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 03:46:31,806 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 03:46:31,806 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 03:46:31,808 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 03:46:31,810 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:46:32,526 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 03:46:32,731 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 03:46:32,731 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:46:33,001 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 03:46:33,002 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 03:46:33,192 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 03:46:33,227 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 03:46:33,230 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 03:46:33,236 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 03:46:33,236 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 03:46:33,397 - INFO - 🖼️ محاولة تحميل صورة الخلفية: 100100_1.jpg
2025-07-16 03:46:33,814 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 03:46:33,815 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 03:46:33,815 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 03:46:33,815 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 03:46:34,246 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 03:46:34,247 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 03:46:34,250 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 03:46:34,264 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 03:46:34,265 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 03:46:34,267 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 03:46:34,562 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 03:46:34,755 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 03:46:34,756 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 03:46:34,866 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 03:46:34,867 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 03:46:46,536 - INFO - معالجة الأمر: /start
2025-07-16 03:46:47,647 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 03:46:49,647 - INFO - معالجة callback: single_card
2025-07-16 03:46:50,246 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 03:46:50,247 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 03:46:51,780 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 03:46:56,872 - INFO - معالجة callback: single_card
2025-07-16 03:46:57,612 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 03:46:57,612 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 03:46:58,227 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 03:46:58,904 - INFO - معالجة callback: card_count_1
2025-07-16 03:46:59,695 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 03:47:00,597 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 03:47:03,205 - INFO - معالجة callback: cards_template_1_10
2025-07-16 03:47:04,121 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 03:47:05,108 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 03:47:05,109 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 03:47:05,111 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:47:05,214 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 03:47:05,215 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 03:47:05,215 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 03:47:05,215 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 03:47:05,216 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 03:47:05,216 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:47:05,559 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 03:47:05,559 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 03:47:05,559 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:47:05,663 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 03:47:05,670 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 03:47:05,670 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 03:47:05,673 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 03:47:05,847 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 03:47:05,849 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 03:47:05,851 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 03:47:05,921 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 03:47:05,923 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 03:47:06,248 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 03:47:06,248 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 03:47:06,251 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 03:47:06,255 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 03:47:06,256 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 03:47:06,256 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 03:47:06,259 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 03:47:06,271 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 03:47:06,276 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 03:47:06,276 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 03:47:06,278 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 03:47:06,279 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 03:47:06,279 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 03:47:06,311 - INFO - تم توليد 1 حساب
2025-07-16 03:47:06,345 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 11 (من generate_all)
2025-07-16 03:47:06,351 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 03:47:06,351 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 03:47:06,351 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 03:47:06,447 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 03:47:06,542 - INFO - ✅ تم إرسال المستخدم: 02877705701
2025-07-16 03:47:06,542 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 03:47:06,543 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 03:47:07,573 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 03:47:40,818 - INFO - تم بدء تشغيل التطبيق
2025-07-16 03:47:40,831 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 03:47:40,843 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 03:47:40,844 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:47:40,947 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 03:47:41,286 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 03:47:41,286 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 03:47:43,293 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 03:47:43,709 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 03:47:43,711 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 03:47:43,713 - INFO - تم اختيار النظام: user_manager
2025-07-16 03:47:44,000 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 03:47:44,154 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 03:47:44,155 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 03:47:44,364 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام user_manager
2025-07-16 03:47:46,715 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 03:47:46,716 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 03:47:46,977 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 03:47:46,977 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 05:17:40,705 - INFO - تم بدء تشغيل التطبيق
2025-07-16 05:17:41,103 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 05:17:41,181 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 05:17:41,182 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:17:41,420 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 05:17:42,569 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 05:17:42,569 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 05:17:44,590 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 05:17:44,961 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 05:17:44,978 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 05:17:47,994 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 05:17:47,997 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 05:17:48,235 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 05:17:48,235 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 05:18:20,956 - INFO - تم اختيار النظام: user_manager
2025-07-16 05:18:22,203 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 05:18:22,520 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 05:18:22,534 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:18:24,901 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام user_manager
2025-07-16 05:18:40,785 - INFO - تم حفظ القالب: 20
2025-07-16 05:18:41,293 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 05:18:41,294 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 05:18:41,551 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 05:18:41,553 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 05:19:12,082 - INFO - بدء إغلاق التطبيق
2025-07-16 05:19:12,136 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-16 05:19:12,137 - INFO - تم إغلاق التطبيق بنجاح
2025-07-16 05:24:26,731 - INFO - تم بدء تشغيل التطبيق
2025-07-16 05:24:26,759 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 05:24:26,761 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 05:24:26,761 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:26,863 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 05:24:27,221 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 05:24:27,221 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 05:24:29,227 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 05:24:29,521 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 05:24:29,521 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 05:24:32,526 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 05:24:32,529 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 05:24:32,849 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 05:24:32,936 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 05:24:34,320 - INFO - معالجة callback: select_system_um
2025-07-16 05:24:35,020 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 05:24:35,020 - INFO - معالجة callback: select_system_um
2025-07-16 05:24:35,502 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 05:24:36,832 - INFO - معالجة callback: independent_template_um_10
2025-07-16 05:24:37,344 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 05:24:37,344 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 05:24:37,587 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 05:24:37,642 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:37,744 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 05:24:37,744 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 05:24:37,745 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 05:24:37,745 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 05:24:37,752 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:38,200 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 05:24:38,370 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 05:24:38,371 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:38,608 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 05:24:38,608 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 05:24:38,617 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 05:24:38,619 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 05:24:38,620 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 05:24:38,620 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 05:24:38,721 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 05:24:38,994 - INFO - 🖼️ محاولة تحميل صورة الخلفية: 100100_1.jpg
2025-07-16 05:24:38,994 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 05:24:38,995 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 05:24:38,995 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 05:24:38,995 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 05:24:39,126 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 05:24:39,128 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 05:24:39,132 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 05:24:39,147 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 05:24:39,150 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 05:24:39,151 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 05:24:39,403 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 05:24:39,628 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 05:24:39,629 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 05:24:39,759 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 05:24:39,761 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 05:24:43,607 - INFO - معالجة الأمر: /start
2025-07-16 05:24:43,900 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 05:24:45,803 - INFO - معالجة callback: single_card
2025-07-16 05:24:46,012 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 05:24:46,013 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 05:24:46,250 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 05:24:47,346 - INFO - معالجة callback: card_count_1
2025-07-16 05:24:47,556 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 05:24:47,790 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 05:24:49,165 - INFO - معالجة callback: cards_template_1_10
2025-07-16 05:24:49,376 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 05:24:49,635 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 05:24:49,637 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 05:24:49,638 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:49,742 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 05:24:49,742 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 05:24:49,745 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 05:24:49,745 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 05:24:49,746 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 05:24:49,746 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:50,090 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 05:24:50,091 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 05:24:50,091 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 05:24:50,194 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 05:24:50,199 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 05:24:50,200 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 05:24:50,201 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 05:24:50,413 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 05:24:50,414 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 05:24:50,415 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 05:24:50,492 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 05:24:50,492 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 05:24:50,831 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 05:24:50,831 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 05:24:50,834 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 05:24:50,838 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 05:24:50,839 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 05:24:50,839 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 05:24:50,842 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 05:24:50,854 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 05:24:50,857 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 05:24:50,858 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 05:24:50,859 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 05:24:50,859 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 05:24:50,859 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 05:24:50,917 - INFO - تم توليد 1 حساب
2025-07-16 05:24:50,918 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 11 (من generate_all)
2025-07-16 05:24:50,924 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 05:24:50,924 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 05:24:50,925 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 05:24:51,016 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 05:24:51,119 - INFO - ✅ تم إرسال المستخدم: 02988465665
2025-07-16 05:24:51,119 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 05:24:51,120 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 05:24:51,360 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 05:24:56,005 - INFO - معالجة الأمر: /sethost
2025-07-16 05:24:56,005 - INFO - طلب تعديل Host من التلجرام: /sethost
2025-07-16 05:25:14,744 - INFO - معالجة الأمر: /start
2025-07-16 05:25:14,976 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 13:33:16,991 - INFO - تم بدء تشغيل التطبيق
2025-07-16 13:33:17,378 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 13:33:18,823 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 13:33:18,910 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:33:19,424 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 13:33:29,041 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 13:33:29,042 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 13:33:31,176 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 13:33:34,601 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 13:33:34,630 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 13:33:37,640 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 13:33:37,815 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 13:33:38,089 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 13:33:38,091 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 13:33:50,012 - INFO - معالجة callback: select_system_um
2025-07-16 13:33:50,523 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 13:33:52,637 - INFO - معالجة callback: independent_template_um_10
2025-07-16 13:33:53,284 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 13:33:53,285 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 13:33:53,625 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 13:33:54,034 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:33:54,135 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 13:33:54,135 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 13:33:54,136 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 13:33:54,137 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 13:33:54,139 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:33:54,701 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 13:33:54,916 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 13:33:54,917 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:33:55,835 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 13:33:55,837 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 13:33:55,915 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 13:33:55,925 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 13:33:55,961 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 13:33:55,973 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 13:33:55,973 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 13:33:57,050 - INFO - 🖼️ محاولة تحميل صورة الخلفية: 100100_1.jpg
2025-07-16 13:33:57,050 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 13:33:57,051 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 13:33:57,053 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 13:33:57,054 - INFO - ✅ تم تحديث حد تقسيم البرق: 200 → 100
2025-07-16 13:33:57,056 - INFO - 🔄 تم جدولة تحديث معلومات الخادم في البوت
2025-07-16 13:33:57,057 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 1 إعدادات
2025-07-16 13:33:57,222 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 70 إعدادات
2025-07-16 13:33:57,222 - INFO - ✅ تم تطبيق 70 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 13:33:57,226 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 13:33:57,241 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 13:33:57,253 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 13:33:57,255 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 13:33:57,520 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 13:33:57,725 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 13:33:57,726 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 13:33:57,861 - INFO - 🔄 إجبار تحديث صورة الخلفية: 100100_1.jpg
2025-07-16 13:33:57,862 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 13:34:36,231 - INFO - معالجة الأمر: /start
2025-07-16 13:34:36,507 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 13:34:37,849 - INFO - معالجة callback: single_card
2025-07-16 13:34:38,110 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 13:34:38,111 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 13:34:38,408 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 13:34:41,980 - INFO - معالجة callback: card_count_1
2025-07-16 13:34:42,278 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 13:34:42,517 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 13:34:44,037 - INFO - معالجة callback: cards_template_1_10
2025-07-16 13:34:44,257 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 13:34:44,520 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 13:34:44,522 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 13:34:44,523 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:34:44,629 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 13:34:44,630 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 13:34:44,630 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 13:34:44,630 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 13:34:44,631 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 13:34:44,632 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:34:44,976 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 13:34:44,977 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 13:34:44,979 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 13:34:45,082 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 13:34:45,087 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 13:34:45,088 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 13:34:45,099 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 13:34:45,332 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 13:34:45,334 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 13:34:45,336 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 13:34:45,376 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 13:34:45,377 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 13:34:45,587 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 13:34:45,589 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 13:34:45,641 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 13:34:45,647 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 13:34:45,647 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 13:34:45,651 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 13:34:45,655 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 13:34:45,671 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 13:34:45,674 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 13:34:45,676 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 13:34:45,677 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 13:34:45,677 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 13:34:45,678 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 13:34:45,736 - INFO - تم توليد 1 حساب
2025-07-16 13:34:45,736 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 11 (من generate_all)
2025-07-16 13:34:45,745 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 13:34:45,756 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 13:34:45,758 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 13:34:45,853 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 13:34:45,975 - INFO - ✅ تم إرسال المستخدم: 02982966646
2025-07-16 13:34:45,976 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 13:34:45,977 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 13:34:46,308 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 13:34:59,528 - INFO - بدء إغلاق التطبيق
2025-07-16 13:34:59,529 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
