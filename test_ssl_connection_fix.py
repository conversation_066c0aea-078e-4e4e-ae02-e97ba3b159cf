#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح اتصال SSL مع MikroTik API
Test SSL Connection Fix for MikroTik API
"""

import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

class TestSSLConnectionFix(unittest.TestCase):
    """اختبارات إصلاح اتصال SSL"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء mock للبرنامج الرئيسي
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.api_ip_entry = Mock()
        self.mock_app.api_username_entry = Mock()
        self.mock_app.api_password_entry = Mock()
        self.mock_app.api_port_entry = Mock()
        self.mock_app.use_ssl_var = Mock()
        
        # محاكاة القيم الافتراضية
        self.mock_app.api_ip_entry.get.return_value = "***********"
        self.mock_app.api_username_entry.get.return_value = "admin"
        self.mock_app.api_password_entry.get.return_value = "password"
        self.mock_app.api_port_entry.get.return_value = "8729"
        self.mock_app.use_ssl_var.get.return_value = True
        
    def test_ssl_connection_parameters(self):
        """اختبار معاملات اتصال SSL"""
        print("🧪 اختبار معاملات اتصال SSL...")
        
        # محاكاة دالة إنشاء اتصال SSL
        def mock_create_ssl_connection(host, username, password, port, use_ssl):
            ssl_configs = {
                "basic_ssl": {
                    "use_ssl": True,
                    "ssl_verify": False,
                    "ssl_verify_hostname": False,
                    "plaintext_login": True
                },
                "secure_ssl": {
                    "use_ssl": True,
                    "ssl_verify": True,
                    "ssl_verify_hostname": True,
                    "plaintext_login": False
                },
                "no_ssl": {
                    "use_ssl": False,
                    "plaintext_login": True
                }
            }
            
            if use_ssl:
                if port == 8729:
                    return {"success": True, "config": ssl_configs["basic_ssl"], "port": port}
                else:
                    return {"success": False, "error": "منفذ SSL غير صحيح"}
            else:
                if port == 8728:
                    return {"success": True, "config": ssl_configs["no_ssl"], "port": port}
                else:
                    return {"success": False, "error": "منفذ عادي غير صحيح"}
        
        # اختبار اتصال SSL صحيح
        result_ssl = mock_create_ssl_connection("***********", "admin", "password", 8729, True)
        self.assertTrue(result_ssl["success"], "يجب أن ينجح اتصال SSL مع المنفذ 8729")
        self.assertTrue(result_ssl["config"]["use_ssl"], "يجب أن يكون SSL مفعل")
        self.assertFalse(result_ssl["config"]["ssl_verify"], "يجب أن يكون ssl_verify معطل للبساطة")
        
        # اختبار اتصال عادي صحيح
        result_normal = mock_create_ssl_connection("***********", "admin", "password", 8728, False)
        self.assertTrue(result_normal["success"], "يجب أن ينجح الاتصال العادي مع المنفذ 8728")
        self.assertFalse(result_normal["config"]["use_ssl"], "يجب أن يكون SSL معطل")
        
        # اختبار منفذ خاطئ مع SSL
        result_wrong_port = mock_create_ssl_connection("***********", "admin", "password", 8728, True)
        self.assertFalse(result_wrong_port["success"], "يجب أن يفشل SSL مع المنفذ 8728")
        
        print("✅ نجح اختبار معاملات اتصال SSL")
    
    def test_ssl_error_handling(self):
        """اختبار معالجة أخطاء SSL"""
        print("🧪 اختبار معالجة أخطاء SSL...")
        
        # محاكاة أنواع أخطاء SSL المختلفة
        def mock_handle_ssl_error(error_type, error_message, use_ssl):
            if "login" in error_message.lower() or "authentication" in error_message.lower():
                if use_ssl:
                    return {
                        "error_type": "authentication_ssl",
                        "suggestions": [
                            "تأكد من تفعيل API-SSL في MikroTik",
                            "المنفذ الافتراضي لـ SSL هو 8729",
                            "جرب إلغاء تفعيل SSL مؤقتاً للاختبار"
                        ]
                    }
                else:
                    return {
                        "error_type": "authentication_normal",
                        "suggestions": [
                            "تأكد من صحة اسم المستخدم وكلمة المرور",
                            "تأكد من صلاحيات المستخدم"
                        ]
                    }
            elif "ssl" in error_message.lower() or "certificate" in error_message.lower():
                return {
                    "error_type": "ssl_certificate",
                    "suggestions": [
                        "تأكد من تفعيل API-SSL في MikroTik",
                        "تأكد من استخدام المنفذ الصحيح (عادة 8729)",
                        "تأكد من إعدادات الشهادة في MikroTik"
                    ]
                }
            elif "connection" in error_message.lower():
                return {
                    "error_type": "connection",
                    "suggestions": [
                        "تأكد من أن الجهاز متصل بالشبكة",
                        "تأكد من عدم وجود جدار حماية يحجب الاتصال"
                    ]
                }
            else:
                return {
                    "error_type": "unknown",
                    "suggestions": ["خطأ غير معروف"]
                }
        
        # اختبار خطأ مصادقة مع SSL
        auth_ssl_error = mock_handle_ssl_error("authentication", "invalid user name or password", True)
        self.assertEqual(auth_ssl_error["error_type"], "authentication_ssl")
        self.assertIn("API-SSL", " ".join(auth_ssl_error["suggestions"]))
        
        # اختبار خطأ شهادة SSL
        cert_error = mock_handle_ssl_error("ssl", "certificate verification failed", True)
        self.assertEqual(cert_error["error_type"], "ssl_certificate")
        self.assertIn("8729", " ".join(cert_error["suggestions"]))
        
        # اختبار خطأ اتصال عام
        conn_error = mock_handle_ssl_error("connection", "connection refused", False)
        self.assertEqual(conn_error["error_type"], "connection")
        self.assertIn("شبكة", " ".join(conn_error["suggestions"]))
        
        print("✅ نجح اختبار معالجة أخطاء SSL")
    
    def test_ssl_configuration_testing(self):
        """اختبار تجريب إعدادات SSL مختلفة"""
        print("🧪 اختبار تجريب إعدادات SSL مختلفة...")
        
        # محاكاة دالة اختبار إعدادات SSL متعددة
        def mock_test_multiple_ssl_configs(host, username, password, port):
            configs = [
                {
                    "name": "SSL مع plaintext_login=True",
                    "use_ssl": True,
                    "ssl_verify": False,
                    "ssl_verify_hostname": False,
                    "plaintext_login": True,
                    "success_rate": 0.8  # محاكاة معدل نجاح
                },
                {
                    "name": "SSL مع plaintext_login=False",
                    "use_ssl": True,
                    "ssl_verify": False,
                    "ssl_verify_hostname": False,
                    "plaintext_login": False,
                    "success_rate": 0.6
                },
                {
                    "name": "SSL مع ssl_verify=True",
                    "use_ssl": True,
                    "ssl_verify": True,
                    "ssl_verify_hostname": False,
                    "plaintext_login": True,
                    "success_rate": 0.3
                }
            ]
            
            # محاكاة اختبار كل إعداد
            results = []
            for config in configs:
                # محاكاة نجاح/فشل بناءً على معدل النجاح
                import random
                success = random.random() < config["success_rate"]
                
                result = {
                    "config": config,
                    "success": success,
                    "router_name": "MikroTik-Test" if success else None,
                    "error": None if success else "Connection failed"
                }
                results.append(result)
                
                # إذا نجح أول إعداد، نعيده
                if success:
                    return {
                        "overall_success": True,
                        "successful_config": config,
                        "all_results": results
                    }
            
            return {
                "overall_success": False,
                "successful_config": None,
                "all_results": results
            }
        
        # تشغيل الاختبار عدة مرات للتأكد من الاستقرار
        success_count = 0
        total_tests = 10
        
        for i in range(total_tests):
            result = mock_test_multiple_ssl_configs("***********", "admin", "password", 8729)
            if result["overall_success"]:
                success_count += 1
                # التحقق من أن الإعداد الناجح صحيح
                successful_config = result["successful_config"]
                self.assertTrue(successful_config["use_ssl"], "الإعداد الناجح يجب أن يكون SSL")
                self.assertFalse(successful_config["ssl_verify"], "ssl_verify يجب أن يكون معطل للبساطة")
        
        # يجب أن ينجح على الأقل 50% من المحاولات
        success_rate = success_count / total_tests
        self.assertGreaterEqual(success_rate, 0.5, f"معدل نجاح منخفض: {success_rate}")
        
        print(f"✅ نجح اختبار إعدادات SSL - معدل النجاح: {success_rate:.1%}")
    
    def test_ssl_port_auto_adjustment(self):
        """اختبار التعديل التلقائي للمنفذ مع SSL"""
        print("🧪 اختبار التعديل التلقائي للمنفذ مع SSL...")
        
        # محاكاة دالة تبديل SSL مع تعديل المنفذ
        def mock_ssl_toggle_with_port_adjustment(current_ssl, current_port):
            new_ssl = not current_ssl
            
            if new_ssl:
                # تفعيل SSL
                if current_port in ["8728", ""]:
                    new_port = "8729"
                else:
                    new_port = current_port
            else:
                # إلغاء SSL
                if current_port in ["8729", ""]:
                    new_port = "8728"
                else:
                    new_port = current_port
            
            return {
                "old_ssl": current_ssl,
                "new_ssl": new_ssl,
                "old_port": current_port,
                "new_port": new_port,
                "port_changed": current_port != new_port
            }
        
        # اختبار تفعيل SSL من المنفذ العادي
        result1 = mock_ssl_toggle_with_port_adjustment(False, "8728")
        self.assertTrue(result1["new_ssl"], "يجب تفعيل SSL")
        self.assertEqual(result1["new_port"], "8729", "يجب تغيير المنفذ إلى 8729")
        self.assertTrue(result1["port_changed"], "يجب أن يتغير المنفذ")
        
        # اختبار إلغاء SSL من منفذ SSL
        result2 = mock_ssl_toggle_with_port_adjustment(True, "8729")
        self.assertFalse(result2["new_ssl"], "يجب إلغاء SSL")
        self.assertEqual(result2["new_port"], "8728", "يجب تغيير المنفذ إلى 8728")
        self.assertTrue(result2["port_changed"], "يجب أن يتغير المنفذ")
        
        # اختبار مع منفذ مخصص
        result3 = mock_ssl_toggle_with_port_adjustment(False, "9999")
        self.assertTrue(result3["new_ssl"], "يجب تفعيل SSL")
        self.assertEqual(result3["new_port"], "9999", "يجب الاحتفاظ بالمنفذ المخصص")
        self.assertFalse(result3["port_changed"], "يجب عدم تغيير المنفذ المخصص")
        
        print("✅ نجح اختبار التعديل التلقائي للمنفذ مع SSL")
    
    def test_ssl_troubleshooting_tips(self):
        """اختبار نصائح حل مشاكل SSL"""
        print("🧪 اختبار نصائح حل مشاكل SSL...")
        
        # محاكاة دالة الحصول على نصائح حل المشاكل
        def mock_get_ssl_troubleshooting_tips(host, port, use_ssl):
            tips = []
            is_ssl_port = port == 8729 or use_ssl
            
            if is_ssl_port:
                tips.extend([
                    f"المنفذ {port} (SSL) غير متاح على {host}",
                    "تأكد من تفعيل خدمة API-SSL في MikroTik",
                    "تأكد من إعداد شهادة SSL",
                    "تأكد من عدم حجب المنفذ بواسطة Firewall"
                ])
                
                tips.extend([
                    "🔒 نصائح SSL إضافية:",
                    "تأكد من وجود شهادة SSL صالحة في MikroTik",
                    "جرب استخدام المنفذ 8729 (الافتراضي لـ SSL)",
                    "جرب إلغاء تفعيل SSL مؤقتاً للاختبار"
                ])
            else:
                tips.extend([
                    f"المنفذ {port} غير متاح على {host}",
                    "تأكد من تفعيل خدمة API في MikroTik",
                    "تأكد من عدم حجب المنفذ بواسطة Firewall"
                ])
            
            tips.extend([
                "تأكد من صحة عنوان IP",
                "تأكد من أن الجهاز متصل بالشبكة",
                "جرب استخدام Winbox للتأكد من الاتصال"
            ])
            
            return tips
        
        # اختبار نصائح SSL
        ssl_tips = mock_get_ssl_troubleshooting_tips("***********", 8729, True)
        self.assertIn("API-SSL", " ".join(ssl_tips), "يجب أن تتضمن نصائح API-SSL")
        self.assertIn("شهادة SSL", " ".join(ssl_tips), "يجب أن تتضمن نصائح الشهادة")
        self.assertIn("8729", " ".join(ssl_tips), "يجب أن تذكر المنفذ 8729")
        
        # اختبار نصائح الاتصال العادي
        normal_tips = mock_get_ssl_troubleshooting_tips("***********", 8728, False)
        self.assertNotIn("SSL", " ".join(normal_tips), "يجب ألا تتضمن نصائح SSL للاتصال العادي")
        self.assertIn("API", " ".join(normal_tips), "يجب أن تتضمن نصائح API العادي")
        
        print("✅ نجح اختبار نصائح حل مشاكل SSL")

def run_ssl_connection_tests():
    """تشغيل جميع اختبارات إصلاح اتصال SSL"""
    print("🔒 بدء اختبارات إصلاح اتصال SSL")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("=" * 60)
    print("✅ انتهت جميع الاختبارات")
    
    print("\n📋 ملخص الوظائف المختبرة:")
    print("• ✅ معاملات اتصال SSL")
    print("• ✅ معالجة أخطاء SSL")
    print("• ✅ تجريب إعدادات SSL مختلفة")
    print("• ✅ التعديل التلقائي للمنفذ مع SSL")
    print("• ✅ نصائح حل مشاكل SSL")

if __name__ == "__main__":
    run_ssl_connection_tests()
