# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 13:53:42
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0158884805
:do {
    /tool user-manager user add customer="adm8n" username="0158884805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158884805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158884805";
};

# المستخدم 2: 0171950578
:do {
    /tool user-manager user add customer="adm8n" username="0171950578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171950578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171950578";
};

# المستخدم 3: 0114003590
:do {
    /tool user-manager user add customer="adm8n" username="0114003590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114003590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114003590";
};

# المستخدم 4: 0149350608
:do {
    /tool user-manager user add customer="adm8n" username="0149350608" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149350608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149350608";
};

# المستخدم 5: 0171066265
:do {
    /tool user-manager user add customer="adm8n" username="0171066265" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171066265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171066265";
};

# المستخدم 6: 0129007280
:do {
    /tool user-manager user add customer="adm8n" username="0129007280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129007280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129007280";
};

# المستخدم 7: 0123266363
:do {
    /tool user-manager user add customer="adm8n" username="0123266363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123266363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123266363";
};

# المستخدم 8: 0155811591
:do {
    /tool user-manager user add customer="adm8n" username="0155811591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155811591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155811591";
};

# المستخدم 9: 0135083449
:do {
    /tool user-manager user add customer="adm8n" username="0135083449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135083449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135083449";
};

# المستخدم 10: 0194463785
:do {
    /tool user-manager user add customer="adm8n" username="0194463785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194463785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194463785";
};

# المستخدم 11: 0152170915
:do {
    /tool user-manager user add customer="adm8n" username="0152170915" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152170915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152170915";
};

# المستخدم 12: 0194862223
:do {
    /tool user-manager user add customer="adm8n" username="0194862223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194862223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194862223";
};

# المستخدم 13: 0187039397
:do {
    /tool user-manager user add customer="adm8n" username="0187039397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187039397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187039397";
};

# المستخدم 14: 0121096905
:do {
    /tool user-manager user add customer="adm8n" username="0121096905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121096905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121096905";
};

# المستخدم 15: 0140732010
:do {
    /tool user-manager user add customer="adm8n" username="0140732010" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140732010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140732010";
};

# المستخدم 16: 0191897703
:do {
    /tool user-manager user add customer="adm8n" username="0191897703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191897703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191897703";
};

# المستخدم 17: 0132016630
:do {
    /tool user-manager user add customer="adm8n" username="0132016630" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132016630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132016630";
};

# المستخدم 18: 0138521828
:do {
    /tool user-manager user add customer="adm8n" username="0138521828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138521828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138521828";
};

# المستخدم 19: 0135776396
:do {
    /tool user-manager user add customer="adm8n" username="0135776396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135776396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135776396";
};

# المستخدم 20: 0104228870
:do {
    /tool user-manager user add customer="adm8n" username="0104228870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104228870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104228870";
};

# المستخدم 21: 0195685414
:do {
    /tool user-manager user add customer="adm8n" username="0195685414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195685414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195685414";
};

# المستخدم 22: 0185681977
:do {
    /tool user-manager user add customer="adm8n" username="0185681977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185681977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185681977";
};

# المستخدم 23: 0133158608
:do {
    /tool user-manager user add customer="adm8n" username="0133158608" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133158608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133158608";
};

# المستخدم 24: 0174596383
:do {
    /tool user-manager user add customer="adm8n" username="0174596383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174596383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174596383";
};

# المستخدم 25: 0100668273
:do {
    /tool user-manager user add customer="adm8n" username="0100668273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100668273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100668273";
};

# المستخدم 26: 0157481383
:do {
    /tool user-manager user add customer="adm8n" username="0157481383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157481383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157481383";
};

# المستخدم 27: 0138080638
:do {
    /tool user-manager user add customer="adm8n" username="0138080638" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138080638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138080638";
};

# المستخدم 28: 0131329691
:do {
    /tool user-manager user add customer="adm8n" username="0131329691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131329691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131329691";
};

# المستخدم 29: 0138951216
:do {
    /tool user-manager user add customer="adm8n" username="0138951216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138951216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138951216";
};

# المستخدم 30: 0147376901
:do {
    /tool user-manager user add customer="adm8n" username="0147376901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147376901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147376901";
};

# المستخدم 31: 0169054496
:do {
    /tool user-manager user add customer="adm8n" username="0169054496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169054496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169054496";
};

# المستخدم 32: 0168739507
:do {
    /tool user-manager user add customer="adm8n" username="0168739507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168739507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168739507";
};

# المستخدم 33: 0104225967
:do {
    /tool user-manager user add customer="adm8n" username="0104225967" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104225967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104225967";
};

# المستخدم 34: 0198484744
:do {
    /tool user-manager user add customer="adm8n" username="0198484744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198484744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198484744";
};

# المستخدم 35: 0160329430
:do {
    /tool user-manager user add customer="adm8n" username="0160329430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160329430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160329430";
};

# المستخدم 36: 0118125602
:do {
    /tool user-manager user add customer="adm8n" username="0118125602" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118125602";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118125602";
};

# المستخدم 37: 0123941496
:do {
    /tool user-manager user add customer="adm8n" username="0123941496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123941496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123941496";
};

# المستخدم 38: 0169271334
:do {
    /tool user-manager user add customer="adm8n" username="0169271334" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169271334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169271334";
};

# المستخدم 39: 0100690800
:do {
    /tool user-manager user add customer="adm8n" username="0100690800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100690800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100690800";
};

# المستخدم 40: 0195115633
:do {
    /tool user-manager user add customer="adm8n" username="0195115633" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195115633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195115633";
};

# المستخدم 41: 0193904370
:do {
    /tool user-manager user add customer="adm8n" username="0193904370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193904370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193904370";
};

# المستخدم 42: 0142648804
:do {
    /tool user-manager user add customer="adm8n" username="0142648804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142648804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142648804";
};

# المستخدم 43: 0106546068
:do {
    /tool user-manager user add customer="adm8n" username="0106546068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106546068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106546068";
};

# المستخدم 44: 0110294212
:do {
    /tool user-manager user add customer="adm8n" username="0110294212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110294212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110294212";
};

# المستخدم 45: 0162542056
:do {
    /tool user-manager user add customer="adm8n" username="0162542056" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162542056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162542056";
};

# المستخدم 46: 0121419321
:do {
    /tool user-manager user add customer="adm8n" username="0121419321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121419321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121419321";
};

# المستخدم 47: 0146020054
:do {
    /tool user-manager user add customer="adm8n" username="0146020054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146020054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146020054";
};

# المستخدم 48: 0195117225
:do {
    /tool user-manager user add customer="adm8n" username="0195117225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195117225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195117225";
};

# المستخدم 49: 0154016892
:do {
    /tool user-manager user add customer="adm8n" username="0154016892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154016892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154016892";
};

# المستخدم 50: 0128764062
:do {
    /tool user-manager user add customer="adm8n" username="0128764062" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128764062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128764062";
};

# المستخدم 51: 0137386385
:do {
    /tool user-manager user add customer="adm8n" username="0137386385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137386385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137386385";
};

# المستخدم 52: 0143832764
:do {
    /tool user-manager user add customer="adm8n" username="0143832764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143832764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143832764";
};

# المستخدم 53: 0139805034
:do {
    /tool user-manager user add customer="adm8n" username="0139805034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139805034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139805034";
};

# المستخدم 54: 0171620337
:do {
    /tool user-manager user add customer="adm8n" username="0171620337" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171620337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171620337";
};

# المستخدم 55: 0132757089
:do {
    /tool user-manager user add customer="adm8n" username="0132757089" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132757089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132757089";
};

# المستخدم 56: 0102341626
:do {
    /tool user-manager user add customer="adm8n" username="0102341626" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102341626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102341626";
};

# المستخدم 57: 0186723158
:do {
    /tool user-manager user add customer="adm8n" username="0186723158" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186723158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186723158";
};

# المستخدم 58: 0163426722
:do {
    /tool user-manager user add customer="adm8n" username="0163426722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163426722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163426722";
};

# المستخدم 59: 0190867726
:do {
    /tool user-manager user add customer="adm8n" username="0190867726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190867726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190867726";
};

# المستخدم 60: 0175708831
:do {
    /tool user-manager user add customer="adm8n" username="0175708831" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175708831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175708831";
};

# المستخدم 61: 0151390041
:do {
    /tool user-manager user add customer="adm8n" username="0151390041" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151390041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151390041";
};

# المستخدم 62: 0157753470
:do {
    /tool user-manager user add customer="adm8n" username="0157753470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157753470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157753470";
};

# المستخدم 63: 0151945338
:do {
    /tool user-manager user add customer="adm8n" username="0151945338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151945338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151945338";
};

# المستخدم 64: 0133439935
:do {
    /tool user-manager user add customer="adm8n" username="0133439935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133439935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133439935";
};

# المستخدم 65: 0130615318
:do {
    /tool user-manager user add customer="adm8n" username="0130615318" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130615318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130615318";
};

# المستخدم 66: 0140274857
:do {
    /tool user-manager user add customer="adm8n" username="0140274857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140274857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140274857";
};

# المستخدم 67: 0176506098
:do {
    /tool user-manager user add customer="adm8n" username="0176506098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176506098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176506098";
};

# المستخدم 68: 0104233538
:do {
    /tool user-manager user add customer="adm8n" username="0104233538" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104233538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104233538";
};

# المستخدم 69: 0111587136
:do {
    /tool user-manager user add customer="adm8n" username="0111587136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111587136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111587136";
};

# المستخدم 70: 0108419806
:do {
    /tool user-manager user add customer="adm8n" username="0108419806" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108419806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108419806";
};

# المستخدم 71: 0118949711
:do {
    /tool user-manager user add customer="adm8n" username="0118949711" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118949711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118949711";
};

# المستخدم 72: 0193000005
:do {
    /tool user-manager user add customer="adm8n" username="0193000005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193000005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193000005";
};

# المستخدم 73: 0176169509
:do {
    /tool user-manager user add customer="adm8n" username="0176169509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176169509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176169509";
};

# المستخدم 74: 0103200690
:do {
    /tool user-manager user add customer="adm8n" username="0103200690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103200690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103200690";
};

# المستخدم 75: 0174419450
:do {
    /tool user-manager user add customer="adm8n" username="0174419450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174419450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174419450";
};

# المستخدم 76: 0106397866
:do {
    /tool user-manager user add customer="adm8n" username="0106397866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106397866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106397866";
};

# المستخدم 77: 0172273962
:do {
    /tool user-manager user add customer="adm8n" username="0172273962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172273962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172273962";
};

# المستخدم 78: 0185354757
:do {
    /tool user-manager user add customer="adm8n" username="0185354757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185354757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185354757";
};

# المستخدم 79: 0117348711
:do {
    /tool user-manager user add customer="adm8n" username="0117348711" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117348711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117348711";
};

# المستخدم 80: 0104575910
:do {
    /tool user-manager user add customer="adm8n" username="0104575910" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104575910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104575910";
};

# المستخدم 81: 0131298015
:do {
    /tool user-manager user add customer="adm8n" username="0131298015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131298015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131298015";
};

# المستخدم 82: 0164699302
:do {
    /tool user-manager user add customer="adm8n" username="0164699302" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164699302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164699302";
};

# المستخدم 83: 0133838584
:do {
    /tool user-manager user add customer="adm8n" username="0133838584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133838584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133838584";
};

# المستخدم 84: 0138121265
:do {
    /tool user-manager user add customer="adm8n" username="0138121265" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138121265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138121265";
};

# المستخدم 85: 0137840845
:do {
    /tool user-manager user add customer="adm8n" username="0137840845" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137840845";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137840845";
};

# المستخدم 86: 0143055432
:do {
    /tool user-manager user add customer="adm8n" username="0143055432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143055432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143055432";
};

# المستخدم 87: 0123735377
:do {
    /tool user-manager user add customer="adm8n" username="0123735377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123735377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123735377";
};

# المستخدم 88: 0123390169
:do {
    /tool user-manager user add customer="adm8n" username="0123390169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123390169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123390169";
};

# المستخدم 89: 0143342985
:do {
    /tool user-manager user add customer="adm8n" username="0143342985" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143342985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143342985";
};

# المستخدم 90: 0118440671
:do {
    /tool user-manager user add customer="adm8n" username="0118440671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118440671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118440671";
};

# المستخدم 91: 0186467327
:do {
    /tool user-manager user add customer="adm8n" username="0186467327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186467327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186467327";
};

# المستخدم 92: 0171535688
:do {
    /tool user-manager user add customer="adm8n" username="0171535688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171535688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171535688";
};

# المستخدم 93: 0176606398
:do {
    /tool user-manager user add customer="adm8n" username="0176606398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176606398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176606398";
};

# المستخدم 94: 0176832349
:do {
    /tool user-manager user add customer="adm8n" username="0176832349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176832349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176832349";
};

# المستخدم 95: 0121676416
:do {
    /tool user-manager user add customer="adm8n" username="0121676416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121676416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121676416";
};

# المستخدم 96: 0191964667
:do {
    /tool user-manager user add customer="adm8n" username="0191964667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191964667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191964667";
};

# المستخدم 97: 0144667231
:do {
    /tool user-manager user add customer="adm8n" username="0144667231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144667231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144667231";
};

# المستخدم 98: 0197166148
:do {
    /tool user-manager user add customer="adm8n" username="0197166148" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197166148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197166148";
};

# المستخدم 99: 0184438827
:do {
    /tool user-manager user add customer="adm8n" username="0184438827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184438827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184438827";
};

# المستخدم 100: 0102372611
:do {
    /tool user-manager user add customer="adm8n" username="0102372611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102372611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102372611";
};

# المستخدم 101: 0180920567
:do {
    /tool user-manager user add customer="adm8n" username="0180920567" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180920567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180920567";
};

# المستخدم 102: 0176727732
:do {
    /tool user-manager user add customer="adm8n" username="0176727732" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176727732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176727732";
};

# المستخدم 103: 0195696238
:do {
    /tool user-manager user add customer="adm8n" username="0195696238" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195696238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195696238";
};

# المستخدم 104: 0171450578
:do {
    /tool user-manager user add customer="adm8n" username="0171450578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171450578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171450578";
};

# المستخدم 105: 0168972873
:do {
    /tool user-manager user add customer="adm8n" username="0168972873" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168972873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168972873";
};

# المستخدم 106: 0159773503
:do {
    /tool user-manager user add customer="adm8n" username="0159773503" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159773503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159773503";
};

# المستخدم 107: 0118508735
:do {
    /tool user-manager user add customer="adm8n" username="0118508735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118508735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118508735";
};

# المستخدم 108: 0195790726
:do {
    /tool user-manager user add customer="adm8n" username="0195790726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195790726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195790726";
};

# المستخدم 109: 0174150010
:do {
    /tool user-manager user add customer="adm8n" username="0174150010" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174150010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174150010";
};

# المستخدم 110: 0142782745
:do {
    /tool user-manager user add customer="adm8n" username="0142782745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142782745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142782745";
};

# المستخدم 111: 0195089137
:do {
    /tool user-manager user add customer="adm8n" username="0195089137" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195089137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195089137";
};

# المستخدم 112: 0176388704
:do {
    /tool user-manager user add customer="adm8n" username="0176388704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176388704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176388704";
};

# المستخدم 113: 0168220179
:do {
    /tool user-manager user add customer="adm8n" username="0168220179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168220179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168220179";
};

# المستخدم 114: 0193500457
:do {
    /tool user-manager user add customer="adm8n" username="0193500457" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193500457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193500457";
};

# المستخدم 115: 0129913485
:do {
    /tool user-manager user add customer="adm8n" username="0129913485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129913485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129913485";
};

# المستخدم 116: 0172080148
:do {
    /tool user-manager user add customer="adm8n" username="0172080148" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172080148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172080148";
};

# المستخدم 117: 0124800483
:do {
    /tool user-manager user add customer="adm8n" username="0124800483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124800483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124800483";
};

# المستخدم 118: 0136997233
:do {
    /tool user-manager user add customer="adm8n" username="0136997233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136997233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136997233";
};

# المستخدم 119: 0124994592
:do {
    /tool user-manager user add customer="adm8n" username="0124994592" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124994592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124994592";
};

# المستخدم 120: 0140298798
:do {
    /tool user-manager user add customer="adm8n" username="0140298798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140298798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140298798";
};

# المستخدم 121: 0193747636
:do {
    /tool user-manager user add customer="adm8n" username="0193747636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193747636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193747636";
};

# المستخدم 122: 0182154199
:do {
    /tool user-manager user add customer="adm8n" username="0182154199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182154199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182154199";
};

# المستخدم 123: 0177303178
:do {
    /tool user-manager user add customer="adm8n" username="0177303178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177303178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177303178";
};

# المستخدم 124: 0192434294
:do {
    /tool user-manager user add customer="adm8n" username="0192434294" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192434294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192434294";
};

# المستخدم 125: 0100417365
:do {
    /tool user-manager user add customer="adm8n" username="0100417365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100417365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100417365";
};

# المستخدم 126: 0159427128
:do {
    /tool user-manager user add customer="adm8n" username="0159427128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159427128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159427128";
};

# المستخدم 127: 0102036654
:do {
    /tool user-manager user add customer="adm8n" username="0102036654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102036654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102036654";
};

# المستخدم 128: 0129886148
:do {
    /tool user-manager user add customer="adm8n" username="0129886148" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129886148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129886148";
};

# المستخدم 129: 0180997922
:do {
    /tool user-manager user add customer="adm8n" username="0180997922" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180997922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180997922";
};

# المستخدم 130: 0157510161
:do {
    /tool user-manager user add customer="adm8n" username="0157510161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157510161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157510161";
};

# المستخدم 131: 0185776275
:do {
    /tool user-manager user add customer="adm8n" username="0185776275" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185776275";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185776275";
};

# المستخدم 132: 0115803118
:do {
    /tool user-manager user add customer="adm8n" username="0115803118" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115803118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115803118";
};

# المستخدم 133: 0168196167
:do {
    /tool user-manager user add customer="adm8n" username="0168196167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168196167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168196167";
};

# المستخدم 134: 0162562681
:do {
    /tool user-manager user add customer="adm8n" username="0162562681" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162562681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162562681";
};

# المستخدم 135: 0139634938
:do {
    /tool user-manager user add customer="adm8n" username="0139634938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139634938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139634938";
};

# المستخدم 136: 0117025308
:do {
    /tool user-manager user add customer="adm8n" username="0117025308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117025308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117025308";
};

# المستخدم 137: 0150430837
:do {
    /tool user-manager user add customer="adm8n" username="0150430837" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150430837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150430837";
};

# المستخدم 138: 0139906481
:do {
    /tool user-manager user add customer="adm8n" username="0139906481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139906481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139906481";
};

# المستخدم 139: 0147788914
:do {
    /tool user-manager user add customer="adm8n" username="0147788914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147788914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147788914";
};

# المستخدم 140: 0173381397
:do {
    /tool user-manager user add customer="adm8n" username="0173381397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173381397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173381397";
};

# المستخدم 141: 0133056007
:do {
    /tool user-manager user add customer="adm8n" username="0133056007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133056007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133056007";
};

# المستخدم 142: 0104560388
:do {
    /tool user-manager user add customer="adm8n" username="0104560388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104560388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104560388";
};

# المستخدم 143: 0129341464
:do {
    /tool user-manager user add customer="adm8n" username="0129341464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129341464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129341464";
};

# المستخدم 144: 0193109839
:do {
    /tool user-manager user add customer="adm8n" username="0193109839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193109839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193109839";
};

# المستخدم 145: 0164869531
:do {
    /tool user-manager user add customer="adm8n" username="0164869531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164869531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164869531";
};

# المستخدم 146: 0153700020
:do {
    /tool user-manager user add customer="adm8n" username="0153700020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153700020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153700020";
};

# المستخدم 147: 0150388614
:do {
    /tool user-manager user add customer="adm8n" username="0150388614" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150388614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150388614";
};

# المستخدم 148: 0179014720
:do {
    /tool user-manager user add customer="adm8n" username="0179014720" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179014720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179014720";
};

# المستخدم 149: 0162744316
:do {
    /tool user-manager user add customer="adm8n" username="0162744316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162744316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162744316";
};

# المستخدم 150: 0102312643
:do {
    /tool user-manager user add customer="adm8n" username="0102312643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102312643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102312643";
};

# المستخدم 151: 0137903159
:do {
    /tool user-manager user add customer="adm8n" username="0137903159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137903159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137903159";
};

# المستخدم 152: 0180130498
:do {
    /tool user-manager user add customer="adm8n" username="0180130498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180130498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180130498";
};

# المستخدم 153: 0176638762
:do {
    /tool user-manager user add customer="adm8n" username="0176638762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176638762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176638762";
};

# المستخدم 154: 0118375024
:do {
    /tool user-manager user add customer="adm8n" username="0118375024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118375024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118375024";
};

# المستخدم 155: 0184977215
:do {
    /tool user-manager user add customer="adm8n" username="0184977215" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184977215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184977215";
};

# المستخدم 156: 0177071424
:do {
    /tool user-manager user add customer="adm8n" username="0177071424" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177071424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177071424";
};

# المستخدم 157: 0103276127
:do {
    /tool user-manager user add customer="adm8n" username="0103276127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103276127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103276127";
};

# المستخدم 158: 0113255187
:do {
    /tool user-manager user add customer="adm8n" username="0113255187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113255187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113255187";
};

# المستخدم 159: 0104908088
:do {
    /tool user-manager user add customer="adm8n" username="0104908088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104908088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104908088";
};

# المستخدم 160: 0121423905
:do {
    /tool user-manager user add customer="adm8n" username="0121423905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121423905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121423905";
};

# المستخدم 161: 0115737930
:do {
    /tool user-manager user add customer="adm8n" username="0115737930" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115737930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115737930";
};

# المستخدم 162: 0106671095
:do {
    /tool user-manager user add customer="adm8n" username="0106671095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106671095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106671095";
};

# المستخدم 163: 0118883928
:do {
    /tool user-manager user add customer="adm8n" username="0118883928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118883928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118883928";
};

# المستخدم 164: 0132764553
:do {
    /tool user-manager user add customer="adm8n" username="0132764553" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132764553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132764553";
};

# المستخدم 165: 0198441529
:do {
    /tool user-manager user add customer="adm8n" username="0198441529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198441529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198441529";
};

# المستخدم 166: 0155180619
:do {
    /tool user-manager user add customer="adm8n" username="0155180619" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155180619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155180619";
};

# المستخدم 167: 0102566251
:do {
    /tool user-manager user add customer="adm8n" username="0102566251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102566251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102566251";
};

# المستخدم 168: 0101955236
:do {
    /tool user-manager user add customer="adm8n" username="0101955236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101955236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101955236";
};

# المستخدم 169: 0177294744
:do {
    /tool user-manager user add customer="adm8n" username="0177294744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177294744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177294744";
};

# المستخدم 170: 0167560508
:do {
    /tool user-manager user add customer="adm8n" username="0167560508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167560508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167560508";
};

# المستخدم 171: 0110144753
:do {
    /tool user-manager user add customer="adm8n" username="0110144753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110144753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110144753";
};

# المستخدم 172: 0136461563
:do {
    /tool user-manager user add customer="adm8n" username="0136461563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136461563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136461563";
};

# المستخدم 173: 0177753007
:do {
    /tool user-manager user add customer="adm8n" username="0177753007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177753007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177753007";
};

# المستخدم 174: 0130262870
:do {
    /tool user-manager user add customer="adm8n" username="0130262870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130262870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130262870";
};

# المستخدم 175: 0160129793
:do {
    /tool user-manager user add customer="adm8n" username="0160129793" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160129793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160129793";
};

# المستخدم 176: 0102739366
:do {
    /tool user-manager user add customer="adm8n" username="0102739366" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102739366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102739366";
};

# المستخدم 177: 0158922970
:do {
    /tool user-manager user add customer="adm8n" username="0158922970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158922970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158922970";
};

# المستخدم 178: 0154352947
:do {
    /tool user-manager user add customer="adm8n" username="0154352947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154352947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154352947";
};

# المستخدم 179: 0122739969
:do {
    /tool user-manager user add customer="adm8n" username="0122739969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122739969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122739969";
};

# المستخدم 180: 0156337247
:do {
    /tool user-manager user add customer="adm8n" username="0156337247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156337247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156337247";
};

# المستخدم 181: 0151986012
:do {
    /tool user-manager user add customer="adm8n" username="0151986012" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151986012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151986012";
};

# المستخدم 182: 0182069824
:do {
    /tool user-manager user add customer="adm8n" username="0182069824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182069824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182069824";
};

# المستخدم 183: 0161927702
:do {
    /tool user-manager user add customer="adm8n" username="0161927702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161927702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161927702";
};

# المستخدم 184: 0120308370
:do {
    /tool user-manager user add customer="adm8n" username="0120308370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120308370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120308370";
};

# المستخدم 185: 0111599037
:do {
    /tool user-manager user add customer="adm8n" username="0111599037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111599037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111599037";
};

# المستخدم 186: 0137706396
:do {
    /tool user-manager user add customer="adm8n" username="0137706396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137706396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137706396";
};

# المستخدم 187: 0149618744
:do {
    /tool user-manager user add customer="adm8n" username="0149618744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149618744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149618744";
};

# المستخدم 188: 0158207761
:do {
    /tool user-manager user add customer="adm8n" username="0158207761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158207761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158207761";
};

# المستخدم 189: 0109021217
:do {
    /tool user-manager user add customer="adm8n" username="0109021217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109021217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109021217";
};

# المستخدم 190: 0139731982
:do {
    /tool user-manager user add customer="adm8n" username="0139731982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139731982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139731982";
};

# المستخدم 191: 0191037324
:do {
    /tool user-manager user add customer="adm8n" username="0191037324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191037324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191037324";
};

# المستخدم 192: 0194199729
:do {
    /tool user-manager user add customer="adm8n" username="0194199729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194199729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194199729";
};

# المستخدم 193: 0134374591
:do {
    /tool user-manager user add customer="adm8n" username="0134374591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134374591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134374591";
};

# المستخدم 194: 0139787694
:do {
    /tool user-manager user add customer="adm8n" username="0139787694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139787694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139787694";
};

# المستخدم 195: 0115487839
:do {
    /tool user-manager user add customer="adm8n" username="0115487839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115487839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115487839";
};

# المستخدم 196: 0183291316
:do {
    /tool user-manager user add customer="adm8n" username="0183291316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183291316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183291316";
};

# المستخدم 197: 0105619558
:do {
    /tool user-manager user add customer="adm8n" username="0105619558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105619558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105619558";
};

# المستخدم 198: 0106567198
:do {
    /tool user-manager user add customer="adm8n" username="0106567198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106567198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106567198";
};

# المستخدم 199: 0122144196
:do {
    /tool user-manager user add customer="adm8n" username="0122144196" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122144196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122144196";
};

# المستخدم 200: 0178728171
:do {
    /tool user-manager user add customer="adm8n" username="0178728171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178728171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178728171";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
