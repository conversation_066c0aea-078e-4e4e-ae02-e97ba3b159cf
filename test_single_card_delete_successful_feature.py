#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة حذف الكروت الناجحة المرسلة للميكروتيك للكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الميزة تعمل بنفس طريقة البرق مع الشروط الصحيحة
"""

import re

def test_data_saving():
    """اختبار حفظ البيانات"""
    print("🔍 اختبار حفظ البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود كود حفظ البيانات
    required_data_saving = [
        'حفظ الكروت الناجحة لخيار.*حذف الكروت المرسلة بنجاح.*للكرت الواحد',
        'single_card_successful_cards.*=.*successful_usernames',
        'single_card_successful_cards_info.*=',
        'operation_type.*single_card',
        'تم حفظ معلومات الكروت الناجحة للكرت الواحد'
    ]
    
    missing_elements = []
    for element in required_data_saving:
        if re.search(element, func_code, re.IGNORECASE):
            print(f"✅ عنصر حفظ البيانات موجود: {element}")
        else:
            print(f"❌ عنصر حفظ البيانات مفقود: {element}")
            missing_elements.append(element)
    
    return len(missing_elements) == 0

def test_button_conditions():
    """اختبار شروط ظهور الزر"""
    print("\n🔍 اختبار شروط ظهور الزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود الشروط الصحيحة
    required_conditions = [
        'شروط إظهار زر حذف الكروت الناجحة للكرت الواحد',
        'failed_count > 0 and',
        'success_count > 0 and',
        "getattr.*system_type.*hotspot",
        'hasattr.*single_card_successful_cards',
        'bool.*single_card_successful_cards',
        'show_delete_successful_button.*='
    ]
    
    missing_conditions = []
    for condition in required_conditions:
        if re.search(condition, func_code, re.IGNORECASE):
            print(f"✅ شرط موجود: {condition}")
        else:
            print(f"❌ شرط مفقود: {condition}")
            missing_conditions.append(condition)
    
    return len(missing_conditions) == 0

def test_button_text_and_ui():
    """اختبار نص الزر والواجهة"""
    print("\n🔍 اختبار نص الزر والواجهة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن النص التوضيحي والزر
    ui_elements = [
        'حذف الكروت المرسلة بنجاح:',
        'نظراً لوجود.*كرت فاشل.*يمكنك اختيار حذف',
        'هذا الخيار يحذف فقط الكروت المرسلة بنجاح من عملية الكرت الواحد الحالية',
        'حذف الكروت المرسلة بنجاح من هذه العملية',
        'single_card_delete_successful_',
        'show_retry_failed_button or show_delete_successful_button'
    ]
    
    missing_ui = []
    for element in ui_elements:
        if re.search(element, content, re.IGNORECASE):
            print(f"✅ عنصر واجهة موجود: {element}")
        else:
            print(f"❌ عنصر واجهة مفقود: {element}")
            missing_ui.append(element)
    
    return len(missing_ui) == 0

def test_callback_handlers():
    """اختبار معالجات callback"""
    print("\n🔍 اختبار معالجات callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود معالجات callback
    callback_handlers = [
        'elif callback_data.startswith.*single_card_delete_successful_',
        'single_card_delete_successful_confirm_',
        'single_card_delete_successful_cancel',
        'معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد',
        'تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد',
        'إلغاء عملية حذف الكروت المرسلة بنجاح للكرت الواحد'
    ]
    
    missing_handlers = []
    for handler in callback_handlers:
        if re.search(handler, content, re.IGNORECASE):
            print(f"✅ معالج callback موجود: {handler}")
        else:
            print(f"❌ معالج callback مفقود: {handler}")
            missing_handlers.append(handler)
    
    return len(missing_handlers) == 0

def test_helper_functions():
    """اختبار الدوال المساعدة"""
    print("\n🔍 اختبار الدوال المساعدة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدوال المطلوبة
    required_functions = [
        'def handle_single_card_delete_successful_request',
        'def send_single_card_delete_successful_confirmation',
        'def execute_single_card_delete_successful',
        'def cancel_single_card_delete_successful'
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in content:
            print(f"✅ دالة موجودة: {func}")
        else:
            print(f"❌ دالة مفقودة: {func}")
            missing_functions.append(func)
    
    return len(missing_functions) == 0

def test_lightning_compatibility():
    """اختبار التوافق مع ميزة البرق"""
    print("\n🔍 اختبار التوافق مع ميزة البرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من أن ميزة البرق لم تتأثر
    lightning_elements = [
        'lightning_delete_successful_',
        'lightning_successful_cards',
        'handle_lightning_delete_successful_request',
        'send_lightning_notification_with_delete_successful_button'
    ]
    
    all_preserved = True
    for element in lightning_elements:
        if re.search(element, content, re.IGNORECASE):
            print(f"✅ عنصر البرق محفوظ: {element}")
        else:
            print(f"⚠️ عنصر البرق قد يكون مفقود: {element}")
            # لا نعتبر هذا خطأ لأن بعض العناصر قد لا تكون موجودة
    
    return all_preserved

def simulate_scenarios():
    """محاكاة السيناريوهات المختلفة"""
    print("\n🧪 محاكاة السيناريوهات المختلفة...")
    
    scenarios = [
        {
            "name": "فشل جزئي في HotSpot - يجب أن يظهر الزر",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "reason": "جميع الشروط مستوفاة"
        },
        {
            "name": "نجاح كامل في HotSpot - يجب ألا يظهر الزر",
            "failed_count": 0,
            "success_count": 3,
            "system_type": "hotspot",
            "has_data": True,
            "expected": False,
            "reason": "لا توجد كروت فاشلة"
        },
        {
            "name": "فشل كامل في HotSpot - يجب ألا يظهر الزر",
            "failed_count": 3,
            "success_count": 0,
            "system_type": "hotspot",
            "has_data": False,
            "expected": False,
            "reason": "لا توجد كروت ناجحة"
        },
        {
            "name": "فشل جزئي في User Manager - يجب ألا يظهر الزر",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "usermanager",
            "has_data": True,
            "expected": False,
            "reason": "ليس نظام HotSpot"
        },
        {
            "name": "فشل جزئي في HotSpot بدون بيانات - يجب ألا يظهر الزر",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "hotspot",
            "has_data": False,
            "expected": False,
            "reason": "لا توجد بيانات محفوظة"
        }
    ]
    
    all_correct = True
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        
        # تطبيق الشروط
        condition1 = scenario['failed_count'] > 0
        condition2 = scenario['success_count'] > 0
        condition3 = scenario['system_type'] == 'hotspot'
        condition4 = scenario['has_data']
        
        result = condition1 and condition2 and condition3 and condition4
        
        print(f"   - failed_count > 0: {condition1}")
        print(f"   - success_count > 0: {condition2}")
        print(f"   - system_type == 'hotspot': {condition3}")
        print(f"   - has_data: {condition4}")
        print(f"   - النتيجة: {result}")
        print(f"   - المتوقع: {scenario['expected']}")
        
        if result == scenario['expected']:
            print(f"   ✅ صحيح - {scenario['reason']}")
        else:
            print(f"   ❌ خطأ - متوقع {scenario['expected']} لكن حصل على {result}")
            all_correct = False
    
    return all_correct

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار ميزة حذف الكروت الناجحة للكرت الواحد")
    print("="*75)
    
    tests = [
        ("حفظ البيانات", test_data_saving),
        ("شروط ظهور الزر", test_button_conditions),
        ("نص الزر والواجهة", test_button_text_and_ui),
        ("معالجات callback", test_callback_handlers),
        ("الدوال المساعدة", test_helper_functions),
        ("التوافق مع البرق", test_lightning_compatibility),
        ("محاكاة السيناريوهات", simulate_scenarios)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 55)
    
    print("\n" + "="*75)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إضافة ميزة حذف الكروت الناجحة للكرت الواحد بنجاح!")
        print("💡 الميزة تعمل بنفس طريقة البرق مع الشروط الصحيحة")
        
        print("\n🎯 الميزات المُضافة:")
        print("✅ حفظ البيانات عند الفشل الجزئي في HotSpot")
        print("✅ شروط ظهور الزر الصحيحة")
        print("✅ النص التوضيحي والزر")
        print("✅ معالجات callback كاملة")
        print("✅ الدوال المساعدة (handle, send, execute, cancel)")
        print("✅ التوافق مع ميزة البرق")
        
        print("\n🔍 كيفية الاختبار:")
        print("1. أنشئ عدة كروت في HotSpot باستخدام الكرت الواحد")
        print("2. تأكد من حدوث فشل جزئي (بعض الكروت تنجح وبعضها يفشل)")
        print("3. ابحث عن الزر: '🗑️ حذف الكروت المرسلة بنجاح من هذه العملية'")
        print("4. اضغط على الزر وجرب التأكيد والإلغاء")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    main()
