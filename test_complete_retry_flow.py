#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لتدفق إعادة المحاولة للكروت الفاشلة من البداية إلى النهاية
"""

import re
import os

def test_complete_retry_workflow():
    """اختبار تدفق إعادة المحاولة الكامل"""
    print("🔍 اختبار تدفق إعادة المحاولة الكامل...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n📋 مراحل تدفق إعادة المحاولة:")
    
    # 1. حفظ الكروت الفاشلة أثناء الإرسال
    print("\n1️⃣ مرحلة حفظ الكروت الفاشلة أثناء الإرسال:")
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # التحقق من حفظ الكروت الفاشلة
        if 'self.single_card_failed_cards.append({' in func_code:
            print("✅ يتم حفظ الكروت الفاشلة في single_card_failed_cards")
        else:
            print("❌ لا يتم حفظ الكروت الفاشلة في single_card_failed_cards")
        
        # التحقق من الإصلاح الجديد
        if 'استخدام قائمة failed_cards المحلية كبديل' in func_code:
            print("✅ الإصلاح الجديد موجود (استخدام failed_cards كبديل)")
        else:
            print("❌ الإصلاح الجديد غير موجود")
    
    # 2. حفظ failed_cards_info
    print("\n2️⃣ مرحلة حفظ failed_cards_info:")
    
    if func_match:
        if 'self.failed_cards_info = {' in func_code:
            print("✅ يتم حفظ failed_cards_info")
        else:
            print("❌ لا يتم حفظ failed_cards_info")
        
        # التحقق من الشرط المحسن
        if 'if failed_cards:  # استخدام قائمة failed_cards المحلية' in func_code:
            print("✅ الشرط المحسن موجود (استخدام failed_cards كبديل)")
        else:
            print("❌ الشرط المحسن غير موجود")
    
    # 3. إظهار زر إعادة المحاولة
    print("\n3️⃣ مرحلة إظهار زر إعادة المحاولة:")
    
    details_func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if details_func_match:
        details_func_code = details_func_match.group(0)
        
        if 'show_retry_failed_button = (' in details_func_code:
            print("✅ شروط إظهار زر إعادة المحاولة موجودة")
        else:
            print("❌ شروط إظهار زر إعادة المحاولة غير موجودة")
        
        if 'retry_failed_cards_single_' in details_func_code:
            print("✅ زر إعادة المحاولة يتم إنشاؤه")
        else:
            print("❌ زر إعادة المحاولة لا يتم إنشاؤه")
    
    # 4. معالجة callback
    print("\n4️⃣ مرحلة معالجة callback:")
    
    if 'elif callback_data.startswith("retry_failed_cards_"):' in content:
        print("✅ معالج callback لإعادة المحاولة موجود")
    else:
        print("❌ معالج callback لإعادة المحاولة غير موجود")
    
    # 5. دالة handle_retry_failed_cards
    print("\n5️⃣ مرحلة معالجة طلب إعادة المحاولة:")
    
    handle_func_match = re.search(r'def handle_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if handle_func_match:
        handle_func_code = handle_func_match.group(0)
        
        if 'if not has_failed_info or not failed_info_exists:' in handle_func_code:
            print("✅ التحقق من وجود معلومات الكروت الفاشلة موجود")
        else:
            print("❌ التحقق من وجود معلومات الكروت الفاشلة غير موجود")
        
        if 'لا توجد معلومات محفوظة عن الكروت الفاشلة' in handle_func_code:
            print("✅ رسالة الخطأ موجودة")
        else:
            print("❌ رسالة الخطأ غير موجودة")
    
    # 6. دالة execute_retry_failed_cards
    print("\n6️⃣ مرحلة تنفيذ إعادة المحاولة:")
    
    execute_func_match = re.search(r'def execute_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if execute_func_match:
        execute_func_code = execute_func_match.group(0)
        
        if 'if card_type == \'single\':' in execute_func_code:
            print("✅ معالجة نوع الكرت الواحد موجودة")
        else:
            print("❌ معالجة نوع الكرت الواحد غير موجودة")
        
        if 'success = self.retry_single_card_failed_cards(failed_cards)' in execute_func_code:
            print("✅ استدعاء دالة إعادة المحاولة موجود")
        else:
            print("❌ استدعاء دالة إعادة المحاولة غير موجود")
    
    # 7. دالة retry_single_card_failed_cards
    print("\n7️⃣ مرحلة تنفيذ إعادة المحاولة للكرت الواحد:")
    
    retry_func_match = re.search(r'def retry_single_card_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if retry_func_match:
        retry_func_code = retry_func_match.group(0)
        
        if 'retry_credentials = []' in retry_func_code:
            print("✅ إنشاء قائمة كروت إعادة المحاولة موجود")
        else:
            print("❌ إنشاء قائمة كروت إعادة المحاولة غير موجود")
        
        if 'success = self.send_single_card_to_mikrotik_silent()' in retry_func_code:
            print("✅ إعادة إرسال الكروت موجودة")
        else:
            print("❌ إعادة إرسال الكروت غير موجودة")
    
    return True

def test_error_scenarios():
    """اختبار سيناريوهات الأخطاء"""
    print("\n🔍 اختبار سيناريوهات الأخطاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n📋 سيناريوهات الأخطاء المدعومة:")
    
    # 1. عدم وجود معلومات الكروت الفاشلة
    if 'لا توجد معلومات محفوظة عن الكروت الفاشلة' in content:
        print("✅ سيناريو: عدم وجود معلومات الكروت الفاشلة")
    else:
        print("❌ سيناريو: عدم وجود معلومات الكروت الفاشلة")
    
    # 2. قائمة كروت فاشلة فارغة
    if 'لم يتم العثور على كروت فاشلة لإعادة المحاولة' in content:
        print("✅ سيناريو: قائمة كروت فاشلة فارغة")
    else:
        print("❌ سيناريو: قائمة كروت فاشلة فارغة")
    
    # 3. فشل في إعادة المحاولة
    if 'فشلت إعادة المحاولة للكرت الواحد' in content:
        print("✅ سيناريو: فشل في إعادة المحاولة")
    else:
        print("❌ سيناريو: فشل في إعادة المحاولة")
    
    # 4. نجاح إعادة المحاولة
    if 'نجحت إعادة المحاولة للكرت الواحد' in content:
        print("✅ سيناريو: نجاح إعادة المحاولة")
    else:
        print("❌ سيناريو: نجاح إعادة المحاولة")
    
    return True

def test_data_integrity():
    """اختبار سلامة البيانات"""
    print("\n🔍 اختبار سلامة البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n📋 فحص سلامة البيانات:")
    
    # 1. حفظ البيانات الأصلية قبل إعادة المحاولة
    if 'original_credentials = self.generated_credentials.copy()' in content:
        print("✅ حفظ البيانات الأصلية قبل إعادة المحاولة")
    else:
        print("❌ حفظ البيانات الأصلية قبل إعادة المحاولة")
    
    # 2. استعادة البيانات الأصلية بعد إعادة المحاولة
    if 'self.generated_credentials = original_credentials' in content:
        print("✅ استعادة البيانات الأصلية بعد إعادة المحاولة")
    else:
        print("❌ استعادة البيانات الأصلية بعد إعادة المحاولة")
    
    # 3. تحويل البيانات بشكل صحيح
    if 'converted_failed_cards = []' in content:
        print("✅ تحويل البيانات بشكل صحيح")
    else:
        print("❌ تحويل البيانات بشكل صحيح")
    
    # 4. حفظ معلومات الخطأ
    if '\'error\': failed_card.get(\'error\', \'خطأ غير محدد\')' in content:
        print("✅ حفظ معلومات الخطأ")
    else:
        print("❌ حفظ معلومات الخطأ")
    
    return True

def run_complete_test():
    """تشغيل الاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل لتدفق إعادة المحاولة\n")
    
    tests = [
        test_complete_retry_workflow,
        test_error_scenarios,
        test_data_integrity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات الشاملة نجحت!")
        print("✅ تدفق إعادة المحاولة يعمل بشكل صحيح")
        print("\n🎯 ملخص الإصلاح:")
        print("   • تم إصلاح مشكلة حفظ معلومات الكروت الفاشلة")
        print("   • تم إضافة آلية احتياط لاستخدام failed_cards المحلية")
        print("   • تم تحسين معالجة الأخطاء والرسائل التشخيصية")
        print("   • تم ضمان سلامة البيانات أثناء إعادة المحاولة")
        return True
    else:
        print("⚠️ بعض الاختبارات الشاملة فشلت")
        return False

if __name__ == "__main__":
    run_complete_test()
