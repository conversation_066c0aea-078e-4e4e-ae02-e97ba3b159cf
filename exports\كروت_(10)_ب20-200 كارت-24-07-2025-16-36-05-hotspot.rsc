# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 16:36:08
# القالب: 10
# النظام: hotspot
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 200 مستخدم Hotspot...";

# المستخدم 1: 0194273914
:do {
    /ip hotspot user add name="0194273914" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194273914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194273914";
};

# المستخدم 2: 0187486155
:do {
    /ip hotspot user add name="0187486155" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187486155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187486155";
};

# المستخدم 3: 0101856348
:do {
    /ip hotspot user add name="0101856348" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101856348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101856348";
};

# المستخدم 4: 0152046432
:do {
    /ip hotspot user add name="0152046432" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152046432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152046432";
};

# المستخدم 5: 0165510455
:do {
    /ip hotspot user add name="0165510455" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165510455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165510455";
};

# المستخدم 6: 0186865019
:do {
    /ip hotspot user add name="0186865019" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186865019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186865019";
};

# المستخدم 7: 0157034671
:do {
    /ip hotspot user add name="0157034671" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157034671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157034671";
};

# المستخدم 8: 0134418755
:do {
    /ip hotspot user add name="0134418755" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134418755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134418755";
};

# المستخدم 9: 0195176827
:do {
    /ip hotspot user add name="0195176827" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195176827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195176827";
};

# المستخدم 10: 0189071867
:do {
    /ip hotspot user add name="0189071867" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189071867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189071867";
};

# المستخدم 11: 0120531593
:do {
    /ip hotspot user add name="0120531593" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120531593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120531593";
};

# المستخدم 12: 0137829572
:do {
    /ip hotspot user add name="0137829572" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137829572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137829572";
};

# المستخدم 13: 0193215805
:do {
    /ip hotspot user add name="0193215805" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193215805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193215805";
};

# المستخدم 14: 0106477904
:do {
    /ip hotspot user add name="0106477904" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106477904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106477904";
};

# المستخدم 15: 0152770218
:do {
    /ip hotspot user add name="0152770218" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152770218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152770218";
};

# المستخدم 16: 0158311011
:do {
    /ip hotspot user add name="0158311011" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158311011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158311011";
};

# المستخدم 17: 0117117782
:do {
    /ip hotspot user add name="0117117782" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117117782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117117782";
};

# المستخدم 18: 0118625991
:do {
    /ip hotspot user add name="0118625991" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118625991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118625991";
};

# المستخدم 19: 0166870803
:do {
    /ip hotspot user add name="0166870803" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166870803";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166870803";
};

# المستخدم 20: 0192747397
:do {
    /ip hotspot user add name="0192747397" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192747397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192747397";
};

# المستخدم 21: 0199649195
:do {
    /ip hotspot user add name="0199649195" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199649195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199649195";
};

# المستخدم 22: 0172948606
:do {
    /ip hotspot user add name="0172948606" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172948606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172948606";
};

# المستخدم 23: 0174255209
:do {
    /ip hotspot user add name="0174255209" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174255209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174255209";
};

# المستخدم 24: 0188417389
:do {
    /ip hotspot user add name="0188417389" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188417389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188417389";
};

# المستخدم 25: 0135806343
:do {
    /ip hotspot user add name="0135806343" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135806343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135806343";
};

# المستخدم 26: 0100060016
:do {
    /ip hotspot user add name="0100060016" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100060016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100060016";
};

# المستخدم 27: 0121877586
:do {
    /ip hotspot user add name="0121877586" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121877586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121877586";
};

# المستخدم 28: 0140738728
:do {
    /ip hotspot user add name="0140738728" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140738728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140738728";
};

# المستخدم 29: 0165945205
:do {
    /ip hotspot user add name="0165945205" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165945205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165945205";
};

# المستخدم 30: 0138015516
:do {
    /ip hotspot user add name="0138015516" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138015516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138015516";
};

# المستخدم 31: 0139854579
:do {
    /ip hotspot user add name="0139854579" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139854579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139854579";
};

# المستخدم 32: 0165554527
:do {
    /ip hotspot user add name="0165554527" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165554527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165554527";
};

# المستخدم 33: 0118037473
:do {
    /ip hotspot user add name="0118037473" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118037473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118037473";
};

# المستخدم 34: 0180157321
:do {
    /ip hotspot user add name="0180157321" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180157321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180157321";
};

# المستخدم 35: 0105072153
:do {
    /ip hotspot user add name="0105072153" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105072153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105072153";
};

# المستخدم 36: 0142381899
:do {
    /ip hotspot user add name="0142381899" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142381899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142381899";
};

# المستخدم 37: 0195928914
:do {
    /ip hotspot user add name="0195928914" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195928914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195928914";
};

# المستخدم 38: 0108682457
:do {
    /ip hotspot user add name="0108682457" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108682457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108682457";
};

# المستخدم 39: 0158943169
:do {
    /ip hotspot user add name="0158943169" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158943169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158943169";
};

# المستخدم 40: 0194016539
:do {
    /ip hotspot user add name="0194016539" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194016539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194016539";
};

# المستخدم 41: 0192749834
:do {
    /ip hotspot user add name="0192749834" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192749834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192749834";
};

# المستخدم 42: 0107802090
:do {
    /ip hotspot user add name="0107802090" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107802090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107802090";
};

# المستخدم 43: 0107975375
:do {
    /ip hotspot user add name="0107975375" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107975375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107975375";
};

# المستخدم 44: 0198709207
:do {
    /ip hotspot user add name="0198709207" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198709207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198709207";
};

# المستخدم 45: 0131561732
:do {
    /ip hotspot user add name="0131561732" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131561732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131561732";
};

# المستخدم 46: 0151857800
:do {
    /ip hotspot user add name="0151857800" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151857800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151857800";
};

# المستخدم 47: 0108647357
:do {
    /ip hotspot user add name="0108647357" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108647357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108647357";
};

# المستخدم 48: 0175946240
:do {
    /ip hotspot user add name="0175946240" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175946240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175946240";
};

# المستخدم 49: 0106655304
:do {
    /ip hotspot user add name="0106655304" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106655304";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106655304";
};

# المستخدم 50: 0134460443
:do {
    /ip hotspot user add name="0134460443" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134460443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134460443";
};

# المستخدم 51: 0115216741
:do {
    /ip hotspot user add name="0115216741" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115216741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115216741";
};

# المستخدم 52: 0123775743
:do {
    /ip hotspot user add name="0123775743" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123775743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123775743";
};

# المستخدم 53: 0166800583
:do {
    /ip hotspot user add name="0166800583" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166800583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166800583";
};

# المستخدم 54: 0183138337
:do {
    /ip hotspot user add name="0183138337" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183138337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183138337";
};

# المستخدم 55: 0173820054
:do {
    /ip hotspot user add name="0173820054" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173820054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173820054";
};

# المستخدم 56: 0182822701
:do {
    /ip hotspot user add name="0182822701" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182822701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182822701";
};

# المستخدم 57: 0131772597
:do {
    /ip hotspot user add name="0131772597" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131772597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131772597";
};

# المستخدم 58: 0149543598
:do {
    /ip hotspot user add name="0149543598" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149543598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149543598";
};

# المستخدم 59: 0127266068
:do {
    /ip hotspot user add name="0127266068" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127266068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127266068";
};

# المستخدم 60: 0144854695
:do {
    /ip hotspot user add name="0144854695" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144854695";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144854695";
};

# المستخدم 61: 0122990723
:do {
    /ip hotspot user add name="0122990723" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122990723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122990723";
};

# المستخدم 62: 0154111208
:do {
    /ip hotspot user add name="0154111208" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154111208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154111208";
};

# المستخدم 63: 0135216931
:do {
    /ip hotspot user add name="0135216931" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135216931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135216931";
};

# المستخدم 64: 0186144136
:do {
    /ip hotspot user add name="0186144136" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186144136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186144136";
};

# المستخدم 65: 0158804964
:do {
    /ip hotspot user add name="0158804964" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158804964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158804964";
};

# المستخدم 66: 0148500215
:do {
    /ip hotspot user add name="0148500215" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148500215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148500215";
};

# المستخدم 67: 0101413361
:do {
    /ip hotspot user add name="0101413361" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101413361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101413361";
};

# المستخدم 68: 0137443123
:do {
    /ip hotspot user add name="0137443123" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137443123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137443123";
};

# المستخدم 69: 0125060762
:do {
    /ip hotspot user add name="0125060762" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125060762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125060762";
};

# المستخدم 70: 0160312071
:do {
    /ip hotspot user add name="0160312071" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160312071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160312071";
};

# المستخدم 71: 0194019105
:do {
    /ip hotspot user add name="0194019105" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194019105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194019105";
};

# المستخدم 72: 0141251853
:do {
    /ip hotspot user add name="0141251853" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141251853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141251853";
};

# المستخدم 73: 0171618319
:do {
    /ip hotspot user add name="0171618319" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171618319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171618319";
};

# المستخدم 74: 0102358124
:do {
    /ip hotspot user add name="0102358124" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102358124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102358124";
};

# المستخدم 75: 0190121538
:do {
    /ip hotspot user add name="0190121538" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190121538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190121538";
};

# المستخدم 76: 0139449495
:do {
    /ip hotspot user add name="0139449495" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139449495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139449495";
};

# المستخدم 77: 0169821874
:do {
    /ip hotspot user add name="0169821874" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169821874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169821874";
};

# المستخدم 78: 0123342476
:do {
    /ip hotspot user add name="0123342476" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123342476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123342476";
};

# المستخدم 79: 0151817661
:do {
    /ip hotspot user add name="0151817661" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151817661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151817661";
};

# المستخدم 80: 0161612204
:do {
    /ip hotspot user add name="0161612204" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161612204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161612204";
};

# المستخدم 81: 0130365246
:do {
    /ip hotspot user add name="0130365246" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130365246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130365246";
};

# المستخدم 82: 0162376377
:do {
    /ip hotspot user add name="0162376377" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162376377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162376377";
};

# المستخدم 83: 0154515222
:do {
    /ip hotspot user add name="0154515222" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154515222";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154515222";
};

# المستخدم 84: 0114552054
:do {
    /ip hotspot user add name="0114552054" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114552054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114552054";
};

# المستخدم 85: 0123170330
:do {
    /ip hotspot user add name="0123170330" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123170330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123170330";
};

# المستخدم 86: 0199206862
:do {
    /ip hotspot user add name="0199206862" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199206862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199206862";
};

# المستخدم 87: 0156966023
:do {
    /ip hotspot user add name="0156966023" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156966023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156966023";
};

# المستخدم 88: 0175774775
:do {
    /ip hotspot user add name="0175774775" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175774775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175774775";
};

# المستخدم 89: 0140554271
:do {
    /ip hotspot user add name="0140554271" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140554271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140554271";
};

# المستخدم 90: 0119099250
:do {
    /ip hotspot user add name="0119099250" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119099250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119099250";
};

# المستخدم 91: 0133846949
:do {
    /ip hotspot user add name="0133846949" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133846949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133846949";
};

# المستخدم 92: 0136803816
:do {
    /ip hotspot user add name="0136803816" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136803816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136803816";
};

# المستخدم 93: 0131695209
:do {
    /ip hotspot user add name="0131695209" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131695209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131695209";
};

# المستخدم 94: 0172061079
:do {
    /ip hotspot user add name="0172061079" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172061079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172061079";
};

# المستخدم 95: 0121252508
:do {
    /ip hotspot user add name="0121252508" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121252508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121252508";
};

# المستخدم 96: 0193221381
:do {
    /ip hotspot user add name="0193221381" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193221381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193221381";
};

# المستخدم 97: 0102732347
:do {
    /ip hotspot user add name="0102732347" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102732347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102732347";
};

# المستخدم 98: 0176062562
:do {
    /ip hotspot user add name="0176062562" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176062562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176062562";
};

# المستخدم 99: 0157163340
:do {
    /ip hotspot user add name="0157163340" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157163340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157163340";
};

# المستخدم 100: 0124350869
:do {
    /ip hotspot user add name="0124350869" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124350869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124350869";
};

# المستخدم 101: 0134619773
:do {
    /ip hotspot user add name="0134619773" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134619773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134619773";
};

# المستخدم 102: 0135287667
:do {
    /ip hotspot user add name="0135287667" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135287667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135287667";
};

# المستخدم 103: 0115250805
:do {
    /ip hotspot user add name="0115250805" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115250805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115250805";
};

# المستخدم 104: 0120442349
:do {
    /ip hotspot user add name="0120442349" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120442349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120442349";
};

# المستخدم 105: 0193826936
:do {
    /ip hotspot user add name="0193826936" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193826936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193826936";
};

# المستخدم 106: 0118667879
:do {
    /ip hotspot user add name="0118667879" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118667879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118667879";
};

# المستخدم 107: 0188583088
:do {
    /ip hotspot user add name="0188583088" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188583088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188583088";
};

# المستخدم 108: 0176124573
:do {
    /ip hotspot user add name="0176124573" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176124573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176124573";
};

# المستخدم 109: 0132492802
:do {
    /ip hotspot user add name="0132492802" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132492802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132492802";
};

# المستخدم 110: 0141653907
:do {
    /ip hotspot user add name="0141653907" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141653907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141653907";
};

# المستخدم 111: 0166400497
:do {
    /ip hotspot user add name="0166400497" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166400497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166400497";
};

# المستخدم 112: 0194984409
:do {
    /ip hotspot user add name="0194984409" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194984409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194984409";
};

# المستخدم 113: 0152185370
:do {
    /ip hotspot user add name="0152185370" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152185370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152185370";
};

# المستخدم 114: 0164676033
:do {
    /ip hotspot user add name="0164676033" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164676033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164676033";
};

# المستخدم 115: 0150379575
:do {
    /ip hotspot user add name="0150379575" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150379575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150379575";
};

# المستخدم 116: 0122734723
:do {
    /ip hotspot user add name="0122734723" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122734723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122734723";
};

# المستخدم 117: 0188141596
:do {
    /ip hotspot user add name="0188141596" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188141596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188141596";
};

# المستخدم 118: 0126811398
:do {
    /ip hotspot user add name="0126811398" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126811398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126811398";
};

# المستخدم 119: 0139751561
:do {
    /ip hotspot user add name="0139751561" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139751561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139751561";
};

# المستخدم 120: 0154883593
:do {
    /ip hotspot user add name="0154883593" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154883593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154883593";
};

# المستخدم 121: 0129501656
:do {
    /ip hotspot user add name="0129501656" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129501656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129501656";
};

# المستخدم 122: 0127688780
:do {
    /ip hotspot user add name="0127688780" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127688780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127688780";
};

# المستخدم 123: 0196672846
:do {
    /ip hotspot user add name="0196672846" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196672846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196672846";
};

# المستخدم 124: 0170301864
:do {
    /ip hotspot user add name="0170301864" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170301864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170301864";
};

# المستخدم 125: 0113981424
:do {
    /ip hotspot user add name="0113981424" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113981424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113981424";
};

# المستخدم 126: 0124185795
:do {
    /ip hotspot user add name="0124185795" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124185795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124185795";
};

# المستخدم 127: 0152214948
:do {
    /ip hotspot user add name="0152214948" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152214948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152214948";
};

# المستخدم 128: 0191453165
:do {
    /ip hotspot user add name="0191453165" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191453165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191453165";
};

# المستخدم 129: 0193369005
:do {
    /ip hotspot user add name="0193369005" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193369005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193369005";
};

# المستخدم 130: 0124871500
:do {
    /ip hotspot user add name="0124871500" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124871500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124871500";
};

# المستخدم 131: 0194111854
:do {
    /ip hotspot user add name="0194111854" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194111854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194111854";
};

# المستخدم 132: 0172462455
:do {
    /ip hotspot user add name="0172462455" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172462455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172462455";
};

# المستخدم 133: 0114706745
:do {
    /ip hotspot user add name="0114706745" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114706745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114706745";
};

# المستخدم 134: 0184820817
:do {
    /ip hotspot user add name="0184820817" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184820817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184820817";
};

# المستخدم 135: 0178155988
:do {
    /ip hotspot user add name="0178155988" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178155988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178155988";
};

# المستخدم 136: 0110487619
:do {
    /ip hotspot user add name="0110487619" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110487619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110487619";
};

# المستخدم 137: 0114821052
:do {
    /ip hotspot user add name="0114821052" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114821052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114821052";
};

# المستخدم 138: 0173744994
:do {
    /ip hotspot user add name="0173744994" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173744994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173744994";
};

# المستخدم 139: 0147597673
:do {
    /ip hotspot user add name="0147597673" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147597673";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147597673";
};

# المستخدم 140: 0198996923
:do {
    /ip hotspot user add name="0198996923" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198996923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198996923";
};

# المستخدم 141: 0117396614
:do {
    /ip hotspot user add name="0117396614" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117396614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117396614";
};

# المستخدم 142: 0111054827
:do {
    /ip hotspot user add name="0111054827" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111054827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111054827";
};

# المستخدم 143: 0185065668
:do {
    /ip hotspot user add name="0185065668" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185065668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185065668";
};

# المستخدم 144: 0136380725
:do {
    /ip hotspot user add name="0136380725" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136380725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136380725";
};

# المستخدم 145: 0100550886
:do {
    /ip hotspot user add name="0100550886" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100550886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100550886";
};

# المستخدم 146: 0150868216
:do {
    /ip hotspot user add name="0150868216" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150868216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150868216";
};

# المستخدم 147: 0124297000
:do {
    /ip hotspot user add name="0124297000" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124297000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124297000";
};

# المستخدم 148: 0169972370
:do {
    /ip hotspot user add name="0169972370" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169972370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169972370";
};

# المستخدم 149: 0111255100
:do {
    /ip hotspot user add name="0111255100" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111255100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111255100";
};

# المستخدم 150: 0180421404
:do {
    /ip hotspot user add name="0180421404" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180421404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180421404";
};

# المستخدم 151: 0130033112
:do {
    /ip hotspot user add name="0130033112" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130033112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130033112";
};

# المستخدم 152: 0190727570
:do {
    /ip hotspot user add name="0190727570" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190727570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190727570";
};

# المستخدم 153: 0182960110
:do {
    /ip hotspot user add name="0182960110" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182960110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182960110";
};

# المستخدم 154: 0184080997
:do {
    /ip hotspot user add name="0184080997" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184080997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184080997";
};

# المستخدم 155: 0183597019
:do {
    /ip hotspot user add name="0183597019" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183597019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183597019";
};

# المستخدم 156: 0174612123
:do {
    /ip hotspot user add name="0174612123" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174612123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174612123";
};

# المستخدم 157: 0109879488
:do {
    /ip hotspot user add name="0109879488" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109879488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109879488";
};

# المستخدم 158: 0108883769
:do {
    /ip hotspot user add name="0108883769" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108883769";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108883769";
};

# المستخدم 159: 0112464596
:do {
    /ip hotspot user add name="0112464596" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112464596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112464596";
};

# المستخدم 160: 0148232711
:do {
    /ip hotspot user add name="0148232711" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148232711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148232711";
};

# المستخدم 161: 0124839015
:do {
    /ip hotspot user add name="0124839015" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124839015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124839015";
};

# المستخدم 162: 0197920386
:do {
    /ip hotspot user add name="0197920386" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197920386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197920386";
};

# المستخدم 163: 0125283394
:do {
    /ip hotspot user add name="0125283394" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125283394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125283394";
};

# المستخدم 164: 0119154989
:do {
    /ip hotspot user add name="0119154989" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119154989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119154989";
};

# المستخدم 165: 0186074191
:do {
    /ip hotspot user add name="0186074191" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186074191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186074191";
};

# المستخدم 166: 0199810905
:do {
    /ip hotspot user add name="0199810905" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199810905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199810905";
};

# المستخدم 167: 0148176910
:do {
    /ip hotspot user add name="0148176910" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148176910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148176910";
};

# المستخدم 168: 0183251289
:do {
    /ip hotspot user add name="0183251289" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183251289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183251289";
};

# المستخدم 169: 0199148859
:do {
    /ip hotspot user add name="0199148859" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199148859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199148859";
};

# المستخدم 170: 0108652587
:do {
    /ip hotspot user add name="0108652587" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108652587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108652587";
};

# المستخدم 171: 0110012697
:do {
    /ip hotspot user add name="0110012697" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110012697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110012697";
};

# المستخدم 172: 0196216779
:do {
    /ip hotspot user add name="0196216779" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196216779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196216779";
};

# المستخدم 173: 0141069843
:do {
    /ip hotspot user add name="0141069843" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141069843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141069843";
};

# المستخدم 174: 0158177182
:do {
    /ip hotspot user add name="0158177182" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158177182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158177182";
};

# المستخدم 175: 0134719335
:do {
    /ip hotspot user add name="0134719335" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134719335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134719335";
};

# المستخدم 176: 0105291236
:do {
    /ip hotspot user add name="0105291236" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105291236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105291236";
};

# المستخدم 177: 0138054286
:do {
    /ip hotspot user add name="0138054286" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138054286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138054286";
};

# المستخدم 178: 0100376183
:do {
    /ip hotspot user add name="0100376183" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100376183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100376183";
};

# المستخدم 179: 0197757616
:do {
    /ip hotspot user add name="0197757616" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197757616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197757616";
};

# المستخدم 180: 0179808070
:do {
    /ip hotspot user add name="0179808070" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179808070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179808070";
};

# المستخدم 181: 0162421543
:do {
    /ip hotspot user add name="0162421543" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162421543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162421543";
};

# المستخدم 182: 0141778350
:do {
    /ip hotspot user add name="0141778350" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141778350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141778350";
};

# المستخدم 183: 0146924911
:do {
    /ip hotspot user add name="0146924911" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146924911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146924911";
};

# المستخدم 184: 0150664677
:do {
    /ip hotspot user add name="0150664677" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150664677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150664677";
};

# المستخدم 185: 0145356984
:do {
    /ip hotspot user add name="0145356984" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145356984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145356984";
};

# المستخدم 186: 0121585719
:do {
    /ip hotspot user add name="0121585719" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121585719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121585719";
};

# المستخدم 187: 0177349632
:do {
    /ip hotspot user add name="0177349632" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177349632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177349632";
};

# المستخدم 188: 0102984202
:do {
    /ip hotspot user add name="0102984202" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102984202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102984202";
};

# المستخدم 189: 0193935931
:do {
    /ip hotspot user add name="0193935931" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193935931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193935931";
};

# المستخدم 190: 0141249815
:do {
    /ip hotspot user add name="0141249815" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141249815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141249815";
};

# المستخدم 191: 0154742128
:do {
    /ip hotspot user add name="0154742128" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154742128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154742128";
};

# المستخدم 192: 0146197908
:do {
    /ip hotspot user add name="0146197908" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146197908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146197908";
};

# المستخدم 193: 0186794792
:do {
    /ip hotspot user add name="0186794792" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186794792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186794792";
};

# المستخدم 194: 0139596450
:do {
    /ip hotspot user add name="0139596450" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139596450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139596450";
};

# المستخدم 195: 0163143613
:do {
    /ip hotspot user add name="0163143613" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163143613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163143613";
};

# المستخدم 196: 0106219790
:do {
    /ip hotspot user add name="0106219790" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106219790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106219790";
};

# المستخدم 197: 0101338723
:do {
    /ip hotspot user add name="0101338723" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101338723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101338723";
};

# المستخدم 198: 0132751886
:do {
    /ip hotspot user add name="0132751886" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132751886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132751886";
};

# المستخدم 199: 0104999433
:do {
    /ip hotspot user add name="0104999433" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104999433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104999433";
};

# المستخدم 200: 0175582402
:do {
    /ip hotspot user add name="0175582402" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175582402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175582402";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
