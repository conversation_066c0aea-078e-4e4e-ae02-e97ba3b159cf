#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test MikroTik API Delete Command Fix
اختبار إصلاح أمر الحذف في MikroTik API

This test verifies:
1. Correct RouterOS API syntax for user deletion
2. Proper error handling for API syntax errors
3. Success/failure counting accuracy
4. Telegram notification correctness
"""

import sys
import os

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockLogger:
    """Mock logger for testing"""
    def __init__(self):
        self.debug_messages = []
        self.warning_messages = []
        self.error_messages = []
    
    def debug(self, message):
        self.debug_messages.append(message)
        print(f"[DEBUG] {message}")
    
    def warning(self, message):
        self.warning_messages.append(message)
        print(f"[WARNING] {message}")
    
    def error(self, message):
        self.error_messages.append(message)
        print(f"[ERROR] {message}")

class MockMikroTikAPI:
    """Mock MikroTik API that simulates RouterOS behavior"""
    
    def __init__(self, simulate_scenarios=None):
        self.simulate_scenarios = simulate_scenarios or {}
        self.users_db = {
            'user001': {'id': '*1', 'name': 'user001'},
            'user002': {'id': '*2', 'name': 'user002'},
            'user003': {'id': '*3', 'name': 'user003'},
            # user004 will not exist (simulate not found)
            'user005': {'id': '*5', 'name': 'user005'},
        }
        self.removed_users = []
    
    def get_resource(self, path):
        return MockResource(self.users_db, self.removed_users, self.simulate_scenarios)

class MockResource:
    """Mock RouterOS API Resource"""
    
    def __init__(self, users_db, removed_users, simulate_scenarios):
        self.users_db = users_db
        self.removed_users = removed_users
        self.simulate_scenarios = simulate_scenarios
    
    def get(self, name=None):
        """Simulate getting user by name"""
        if name in self.users_db:
            return [self.users_db[name]]
        return []  # User not found
    
    def remove(self, user_id=None, name=None):
        """Simulate RouterOS API remove behavior"""
        
        # Simulate the actual RouterOS API behavior
        if name is not None:
            # RouterOS API doesn't accept 'name' parameter in remove()
            raise Exception("unknown parameter")
        
        if user_id is None:
            raise Exception("missing required parameter")
        
        # Simulate connection errors for specific scenarios
        if user_id in self.simulate_scenarios.get('connection_errors', []):
            raise Exception("connection timeout")
        
        # Find and remove user by ID
        for username, user_data in list(self.users_db.items()):
            if user_data['id'] == user_id:
                del self.users_db[username]
                self.removed_users.append(username)
                return True
        
        # User ID not found
        raise Exception("no such item")

class TestMikroTikAPIDeleteFix:
    """Test the MikroTik API delete command fix"""
    
    def __init__(self):
        self.logger = MockLogger()
    
    def test_code_structure_fix(self):
        """Test that the code structure has been fixed correctly"""
        
        print("=" * 80)
        print("🧪 Testing Code Structure Fix")
        print("=" * 80)
        
        try:
            with open('اخر حاجة  - كروت وبوت.py', 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Find the delete function
            function_start = content.find('def delete_successful_cards_from_mikrotik(')
            if function_start == -1:
                print("❌ Function delete_successful_cards_from_mikrotik not found")
                return False
            
            # Extract function content (approximately)
            function_end = content.find('\n    def ', function_start + 1)
            if function_end == -1:
                function_end = len(content)
            
            function_content = content[function_start:function_end]
            
            print("🔍 Checking function content:")
            
            # Check for correct API usage
            has_get_by_name = '.get(name=' in function_content
            print(f"   - Uses .get(name=username): {'✅ Present' if has_get_by_name else '❌ Missing'}")
            
            has_user_id_extraction = "user_id = users[0]['id']" in function_content
            print(f"   - Extracts user_id: {'✅ Present' if has_user_id_extraction else '❌ Missing'}")
            
            has_remove_by_id = '.remove(user_id)' in function_content
            print(f"   - Uses .remove(user_id): {'✅ Present' if has_remove_by_id else '❌ Missing'}")
            
            # Check that incorrect usage is removed
            has_remove_by_name = '.remove(name=' in function_content
            print(f"   - Uses .remove(name=): {'❌ Present (should be removed)' if has_remove_by_name else '✅ Not present'}")
            
            # Check error handling
            has_error_handling = 'except Exception as user_error:' in function_content
            print(f"   - Has error handling: {'✅ Present' if has_error_handling else '❌ Missing'}")
            
            has_api_error_detection = 'unknown parameter' in function_content
            print(f"   - Detects API syntax errors: {'✅ Present' if has_api_error_detection else '❌ Missing'}")
            
            # Check result tracking
            has_deleted_count = 'deleted_count += 1' in function_content
            has_failed_count = 'failed_count += 1' in function_content
            print(f"   - Tracks deleted_count: {'✅ Present' if has_deleted_count else '❌ Missing'}")
            print(f"   - Tracks failed_count: {'✅ Present' if has_failed_count else '❌ Missing'}")
            
            all_correct = (
                has_get_by_name and 
                has_user_id_extraction and 
                has_remove_by_id and 
                not has_remove_by_name and 
                has_error_handling and 
                has_api_error_detection and
                has_deleted_count and 
                has_failed_count
            )
            
            print(f"\n📊 Overall Result:")
            if all_correct:
                print("   ✅ All fixes applied correctly!")
            else:
                print("   ❌ Some fixes are missing or incorrect!")
            
            return all_correct
            
        except Exception as e:
            print(f"❌ Error checking code: {str(e)}")
            return False
    
    def simulate_delete_operation(self):
        """Simulate the delete operation with correct API usage"""
        
        print("\n" + "=" * 80)
        print("🧪 Simulating Delete Operation with Correct API")
        print("=" * 80)
        
        # Test usernames
        test_usernames = [
            'user001',  # Should succeed
            'user002',  # Should succeed
            'user003',  # Should succeed
            'user004',  # Should fail (not found)
            'user005',  # Should succeed
        ]
        
        print(f"📋 Test usernames: {test_usernames}")
        
        # Simulate the corrected delete operation
        api = MockMikroTikAPI()
        deleted_count = 0
        failed_count = 0
        
        print(f"\n🗑️ Starting delete simulation:")
        
        for username in test_usernames:
            try:
                # Step 1: Search for user by name (correct approach)
                users = api.get_resource('/ip/hotspot/user').get(name=username)
                
                if users:
                    # Step 2: Get user ID
                    user_id = users[0]['id']
                    # Step 3: Remove by ID (correct RouterOS API syntax)
                    api.get_resource('/ip/hotspot/user').remove(user_id)
                    deleted_count += 1
                    self.logger.debug(f"✅ Successfully deleted user: {username} (ID: {user_id})")
                else:
                    # User not found
                    failed_count += 1
                    self.logger.warning(f"⚠️ User not found: {username}")
                
            except Exception as user_error:
                failed_count += 1
                error_msg = str(user_error)
                if "unknown parameter" in error_msg.lower():
                    self.logger.error(f"❌ MikroTik API syntax error for user {username}: {error_msg}")
                else:
                    self.logger.error(f"❌ Failed to delete user {username}: {error_msg}")
        
        # Results
        total_users = len(test_usernames)
        success_rate = (deleted_count / total_users) * 100
        
        print(f"\n📊 Simulation Results:")
        print(f"   - Total users: {total_users}")
        print(f"   - Successfully deleted: {deleted_count}")
        print(f"   - Failed to delete: {failed_count}")
        print(f"   - Success rate: {success_rate:.1f}%")
        print(f"   - Removed users: {api.removed_users}")
        
        # Expected results: 4 success (user001-003, user005), 1 failure (user004 not found)
        expected_deleted = 4
        expected_failed = 1
        
        results_correct = (deleted_count == expected_deleted and failed_count == expected_failed)
        
        print(f"\n✅ Results Verification:")
        print(f"   - Expected deleted: {expected_deleted}, Actual: {deleted_count}")
        print(f"   - Expected failed: {expected_failed}, Actual: {failed_count}")
        print(f"   - Results correct: {'✅ Yes' if results_correct else '❌ No'}")
        
        return results_correct
    
    def test_api_error_scenarios(self):
        """Test various API error scenarios"""
        
        print("\n" + "=" * 80)
        print("🧪 Testing API Error Scenarios")
        print("=" * 80)
        
        test_scenarios = [
            {
                'name': 'Correct API usage',
                'method': 'correct',
                'should_succeed': True
            },
            {
                'name': 'Incorrect API usage (remove by name)',
                'method': 'incorrect_name',
                'should_succeed': False,
                'expected_error': 'unknown parameter'
            },
            {
                'name': 'Missing parameter',
                'method': 'missing_param',
                'should_succeed': False,
                'expected_error': 'missing required parameter'
            },
            {
                'name': 'Connection timeout',
                'method': 'connection_error',
                'should_succeed': False,
                'expected_error': 'connection timeout'
            }
        ]
        
        print("🔍 Testing different scenarios:")
        
        all_tests_passed = True
        
        for scenario in test_scenarios:
            print(f"\n📋 Testing: {scenario['name']}")
            
            # Setup API with specific scenario
            simulate_scenarios = {}
            if scenario['method'] == 'connection_error':
                simulate_scenarios['connection_errors'] = ['*1']
            
            api = MockMikroTikAPI(simulate_scenarios)
            
            try:
                if scenario['method'] == 'correct':
                    # Correct usage: get user first, then remove by ID
                    users = api.get_resource('/ip/hotspot/user').get(name='user001')
                    if users:
                        user_id = users[0]['id']
                        api.get_resource('/ip/hotspot/user').remove(user_id)
                    result = "Success"
                    
                elif scenario['method'] == 'incorrect_name':
                    # Incorrect usage: try to remove by name directly
                    api.get_resource('/ip/hotspot/user').remove(name='user001')
                    result = "Success"
                    
                elif scenario['method'] == 'missing_param':
                    # Missing parameter
                    api.get_resource('/ip/hotspot/user').remove()
                    result = "Success"
                    
                elif scenario['method'] == 'connection_error':
                    # Connection error scenario
                    users = api.get_resource('/ip/hotspot/user').get(name='user001')
                    if users:
                        user_id = users[0]['id']
                        api.get_resource('/ip/hotspot/user').remove(user_id)
                    result = "Success"
                
            except Exception as e:
                result = f"Error: {str(e)}"
            
            # Verify results
            if scenario['should_succeed']:
                test_passed = "Error:" not in result
                print(f"   - Result: {result}")
                print(f"   - Expected: Success")
            else:
                test_passed = scenario['expected_error'] in result
                print(f"   - Result: {result}")
                print(f"   - Expected error: {scenario['expected_error']}")
            
            print(f"   - Test: {'✅ Passed' if test_passed else '❌ Failed'}")
            
            if not test_passed:
                all_tests_passed = False
        
        return all_tests_passed
    
    def test_error_message_handling(self):
        """Test proper error message handling and categorization"""
        
        print("\n" + "=" * 80)
        print("🧪 Testing Error Message Handling")
        print("=" * 80)
        
        # Simulate different error types
        error_scenarios = [
            {
                'error': Exception("unknown parameter"),
                'expected_category': 'API syntax error',
                'should_contain': 'MikroTik API syntax error'
            },
            {
                'error': Exception("connection timeout"),
                'expected_category': 'Connection error',
                'should_contain': 'Failed to delete user'
            },
            {
                'error': Exception("no such item"),
                'expected_category': 'User not found error',
                'should_contain': 'Failed to delete user'
            }
        ]
        
        print("🔍 Testing error categorization:")
        
        all_tests_passed = True
        
        for i, scenario in enumerate(error_scenarios, 1):
            print(f"\n📋 Test {i}: {scenario['expected_category']}")
            
            # Simulate error handling logic
            error_msg = str(scenario['error'])
            username = f"test_user_{i}"
            
            if "unknown parameter" in error_msg.lower():
                logged_message = f"❌ MikroTik API syntax error for user {username}: {error_msg}"
            else:
                logged_message = f"❌ Failed to delete user {username}: {error_msg}"
            
            print(f"   - Original error: {error_msg}")
            print(f"   - Logged message: {logged_message}")
            
            # Check if the expected content is in the logged message
            test_passed = scenario['should_contain'] in logged_message
            print(f"   - Expected to contain: {scenario['should_contain']}")
            print(f"   - Test: {'✅ Passed' if test_passed else '❌ Failed'}")
            
            if not test_passed:
                all_tests_passed = False
        
        return all_tests_passed

def main():
    """Main test function"""
    
    print("🚀 Starting MikroTik API Delete Command Fix Test")
    
    tester = TestMikroTikAPIDeleteFix()
    
    # Run all tests
    test1 = tester.test_code_structure_fix()
    test2 = tester.simulate_delete_operation()
    test3 = tester.test_api_error_scenarios()
    test4 = tester.test_error_message_handling()
    
    # Final results
    print("\n" + "=" * 80)
    print("🎯 Final Test Results")
    print("=" * 80)
    
    all_tests_passed = test1 and test2 and test3 and test4
    
    print(f"📊 Test Results:")
    print(f"   1. Code Structure Fix: {'✅ Passed' if test1 else '❌ Failed'}")
    print(f"   2. Delete Operation Simulation: {'✅ Passed' if test2 else '❌ Failed'}")
    print(f"   3. API Error Scenarios: {'✅ Passed' if test3 else '❌ Failed'}")
    print(f"   4. Error Message Handling: {'✅ Passed' if test4 else '❌ Failed'}")
    
    if all_tests_passed:
        print(f"\n🎉 All tests passed! The MikroTik API delete fix is working correctly.")
        print(f"💡 Key fixes applied:")
        print(f"   ✅ Correct RouterOS API syntax: get user first, then remove by ID")
        print(f"   ✅ Proper error handling for 'unknown parameter' errors")
        print(f"   ✅ Accurate success/failure counting")
        print(f"   ✅ Enhanced error message categorization")
    else:
        print(f"\n❌ Some tests failed. Please review the results above.")
    
    print(f"\n🔍 The correct MikroTik API usage pattern:")
    print(f"   1. users = api.get_resource('/ip/hotspot/user').get(name=username)")
    print(f"   2. user_id = users[0]['id']")
    print(f"   3. api.get_resource('/ip/hotspot/user').remove(user_id)")
    print(f"\n⚠️  Never use: api.get_resource('/ip/hotspot/user').remove(name=username)")
    print(f"   This causes 'unknown parameter' error in RouterOS API!")

if __name__ == '__main__':
    main()
