#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة تغيير الهوست المتكاملة مع إعادة التشغيل
Test Integrated Host Change with Restart Functionality
"""

import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

class TestIntegratedHostChange(unittest.TestCase):
    """اختبارات وظيفة تغيير الهوست المتكاملة"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء mock للبرنامج الرئيسي
        self.mock_app = Mock()
        self.mock_app.api_ip_entry = Mock()
        self.mock_app.api_username_entry = Mock()
        self.mock_app.api_password_entry = Mock()
        self.mock_app.api_port_entry = Mock()
        self.mock_app.use_ssl_var = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.root = Mock()
        self.mock_app.system_type = "user_manager"
        
        # محاكاة القيم الحالية
        self.mock_app.api_ip_entry.get.return_value = "***********"
        self.mock_app.api_username_entry.get.return_value = "admin"
        self.mock_app.api_password_entry.get.return_value = "password"
        self.mock_app.api_port_entry.get.return_value = "8728"
        self.mock_app.use_ssl_var.get.return_value = False
        
        # محاكاة دوال أخرى
        self.mock_app.save_settings = Mock()
        self.mock_app.send_telegram_message_direct = Mock()
        self.mock_app.execute_safe_restart = Mock()
        
    def test_integrated_host_change_workflow(self):
        """اختبار سير العمل الكامل لتغيير الهوست المتكامل"""
        print("🧪 اختبار سير العمل الكامل لتغيير الهوست المتكامل...")
        
        # محاكاة دالة تغيير الهوست المتكاملة
        def mock_handle_set_host_request(message_text, bot_token, chat_id):
            parts = message_text.split()
            if len(parts) != 2:
                return False
            
            new_host = parts[1].strip()
            
            # التحقق من صحة Host
            if not new_host or len(new_host) < 3:
                return False
            
            # حفظ Host القديم
            old_host = self.mock_app.api_ip_entry.get()
            
            # التحقق من وجود تغيير
            if old_host == new_host:
                return False
            
            # تطبيق Host الجديد
            self.mock_app.api_ip_entry.delete(0, 'end')
            self.mock_app.api_ip_entry.insert(0, new_host)
            
            # حفظ الإعدادات
            self.mock_app.save_settings()
            
            # إرسال رسالة التقدم
            self.mock_app.send_telegram_message_direct(bot_token, chat_id, "progress_message")
            
            # جدولة إعادة التشغيل
            self.mock_app.root.after(5000, lambda: self.mock_execute_integrated_restart(bot_token, chat_id, old_host, new_host))
            
            return True
        
        def mock_execute_integrated_restart(bot_token, chat_id, old_host, new_host):
            # إرسال رسالة تأكيد نهائية
            self.mock_app.send_telegram_message_direct(bot_token, chat_id, "final_confirmation")
            
            # تنفيذ إعادة التشغيل
            self.mock_app.execute_safe_restart(bot_token, chat_id)
            
            return True
        
        # اختبار العملية
        result = mock_handle_set_host_request("/sethost ********", "test_token", "test_chat")
        
        # التحقق من النتائج
        self.assertTrue(result, "يجب أن تنجح عملية تغيير الهوست")
        
        # التحقق من استدعاء الدوال
        self.mock_app.api_ip_entry.delete.assert_called()
        self.mock_app.api_ip_entry.insert.assert_called_with(0, "********")
        self.mock_app.save_settings.assert_called()
        self.mock_app.send_telegram_message_direct.assert_called()
        self.mock_app.root.after.assert_called()
        
        print("✅ نجح اختبار سير العمل الكامل لتغيير الهوست المتكامل")
    
    def test_host_validation(self):
        """اختبار التحقق من صحة الهوست"""
        print("🧪 اختبار التحقق من صحة الهوست...")
        
        # محاكاة دالة التحقق من صحة الهوست
        def mock_validate_host(host):
            if not host or len(host) < 3:
                return False
            
            # فحص أساسي للتنسيق
            if host.count('.') == 3:  # IP address
                parts = host.split('.')
                try:
                    for part in parts:
                        num = int(part)
                        if num < 0 or num > 255:
                            return False
                    return True
                except ValueError:
                    return False
            
            # فحص DDNS/hostname
            if '.' in host and len(host) > 3:
                return True
            
            return False
        
        # اختبار عناوين IP صحيحة
        valid_ips = ["***********", "********", "**********", "*******"]
        for ip in valid_ips:
            self.assertTrue(mock_validate_host(ip), f"IP صحيح يجب أن يمر: {ip}")
        
        # اختبار DDNS صحيح
        valid_ddns = ["router.local", "mikrotik.company.com", "server.example.org"]
        for ddns in valid_ddns:
            self.assertTrue(mock_validate_host(ddns), f"DDNS صحيح يجب أن يمر: {ddns}")
        
        # اختبار عناوين غير صحيحة
        invalid_hosts = ["", "ab", "999.999.999.999", "192.168.1", "invalid..host"]
        for host in invalid_hosts:
            self.assertFalse(mock_validate_host(host), f"Host غير صحيح يجب أن يفشل: {host}")
        
        print("✅ نجح اختبار التحقق من صحة الهوست")
    
    def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        print("🧪 اختبار معالجة الأخطاء...")
        
        # محاكاة دالة معالجة الأخطاء
        def mock_handle_host_change_error(error_type, bot_token, chat_id):
            error_messages = {
                "invalid_host": "❌ Host غير صحيح",
                "no_change": "ℹ️ Host لم يتغير",
                "save_error": "❌ فشل في حفظ الإعدادات",
                "restart_error": "❌ فشل في إعادة التشغيل"
            }
            
            if error_type in error_messages:
                self.mock_app.send_telegram_message_direct(bot_token, chat_id, error_messages[error_type])
                return True
            
            return False
        
        # اختبار أنواع الأخطاء المختلفة
        error_types = ["invalid_host", "no_change", "save_error", "restart_error"]
        
        for error_type in error_types:
            result = mock_handle_host_change_error(error_type, "test_token", "test_chat")
            self.assertTrue(result, f"يجب معالجة خطأ: {error_type}")
        
        # التحقق من استدعاء إرسال الرسائل
        self.assertEqual(self.mock_app.send_telegram_message_direct.call_count, len(error_types))
        
        print("✅ نجح اختبار معالجة الأخطاء")
    
    def test_restart_integration(self):
        """اختبار دمج إعادة التشغيل"""
        print("🧪 اختبار دمج إعادة التشغيل...")
        
        # محاكاة دالة إعادة التشغيل المتكاملة
        def mock_execute_integrated_restart(bot_token, chat_id, old_host, new_host):
            try:
                # إرسال رسالة تأكيد نهائية
                final_msg = f"✅ تمت عملية تغيير Host بنجاح من {old_host} إلى {new_host}"
                self.mock_app.send_telegram_message_direct(bot_token, chat_id, final_msg)
                
                # تنفيذ إعادة التشغيل
                self.mock_app.execute_safe_restart(bot_token, chat_id)
                
                return True
                
            except Exception as e:
                # معالجة خطأ إعادة التشغيل
                error_msg = f"❌ فشل في إعادة التشغيل: {str(e)}"
                self.mock_app.send_telegram_message_direct(bot_token, chat_id, error_msg)
                return False
        
        # اختبار إعادة التشغيل الناجحة
        result = mock_execute_integrated_restart("test_token", "test_chat", "***********", "********")
        
        # التحقق من النتائج
        self.assertTrue(result, "يجب أن تنجح عملية إعادة التشغيل المتكاملة")
        
        # التحقق من استدعاء الدوال
        self.mock_app.send_telegram_message_direct.assert_called()
        self.mock_app.execute_safe_restart.assert_called_with("test_token", "test_chat")
        
        print("✅ نجح اختبار دمج إعادة التشغيل")
    
    def test_complete_integration_workflow(self):
        """اختبار سير العمل الكامل للتكامل"""
        print("🧪 اختبار سير العمل الكامل للتكامل...")
        
        try:
            # 1. محاكاة طلب تغيير الهوست
            print("  📝 محاكاة طلب تغيير الهوست...")
            message = "/sethost **********00"
            
            # 2. محاكاة التحقق من صحة الطلب
            print("  ✅ محاكاة التحقق من صحة الطلب...")
            parts = message.split()
            self.assertEqual(len(parts), 2, "يجب أن يحتوي الأمر على جزأين")
            
            # 3. محاكاة تطبيق التغيير
            print("  🔄 محاكاة تطبيق التغيير...")
            old_host = self.mock_app.api_ip_entry.get()
            new_host = parts[1]
            
            # 4. محاكاة حفظ الإعدادات
            print("  💾 محاكاة حفظ الإعدادات...")
            self.mock_app.save_settings()
            
            # 5. محاكاة إرسال رسالة التقدم
            print("  📨 محاكاة إرسال رسالة التقدم...")
            self.mock_app.send_telegram_message_direct("test_token", "test_chat", "progress")
            
            # 6. محاكاة جدولة إعادة التشغيل
            print("  ⏰ محاكاة جدولة إعادة التشغيل...")
            self.mock_app.root.after(5000, lambda: None)
            
            # 7. محاكاة إعادة التشغيل
            print("  🔄 محاكاة إعادة التشغيل...")
            self.mock_app.execute_safe_restart("test_token", "test_chat")
            
            # التحقق من جميع المراحل
            self.mock_app.save_settings.assert_called()
            self.mock_app.send_telegram_message_direct.assert_called()
            self.mock_app.root.after.assert_called()
            self.mock_app.execute_safe_restart.assert_called()
            
            print("✅ نجح اختبار سير العمل الكامل للتكامل")
            return True
            
        except Exception as e:
            print(f"❌ فشل اختبار سير العمل الكامل: {str(e)}")
            return False

def run_integrated_host_change_tests():
    """تشغيل جميع اختبارات تغيير الهوست المتكامل"""
    print("🚀 بدء اختبارات تغيير الهوست المتكامل")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("=" * 60)
    print("✅ انتهت جميع الاختبارات")
    
    print("\n📋 ملخص الوظائف المختبرة:")
    print("• ✅ سير العمل الكامل لتغيير الهوست المتكامل")
    print("• ✅ التحقق من صحة الهوست")
    print("• ✅ معالجة الأخطاء")
    print("• ✅ دمج إعادة التشغيل")
    print("• ✅ سير العمل الكامل للتكامل")

if __name__ == "__main__":
    run_integrated_host_change_tests()
