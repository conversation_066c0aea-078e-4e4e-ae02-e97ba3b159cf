#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح وظيفة إدخال IP المخصص
Test Custom IP Input Fix
"""

import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

class TestCustomIPInputFix(unittest.TestCase):
    """اختبارات إصلاح وظيفة إدخال IP المخصص"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء mock للبرنامج الرئيسي
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.api_ip_entry = Mock()
        self.mock_app.api_username_entry = Mock()
        self.mock_app.api_password_entry = Mock()
        self.mock_app.api_port_entry = Mock()
        self.mock_app.use_ssl_var = Mock()
        self.mock_app.send_telegram_message_direct = Mock()
        self.mock_app.send_telegram_message_with_keyboard = Mock()
        self.mock_app.save_settings = Mock()
        self.mock_app.root = Mock()
        
        # محاكاة القيم الحالية
        self.mock_app.api_ip_entry.get.return_value = "***********"
        self.mock_app.api_username_entry.get.return_value = "admin"
        self.mock_app.api_password_entry.get.return_value = "password"
        self.mock_app.api_port_entry.get.return_value = "8728"
        self.mock_app.use_ssl_var.get.return_value = False
        
    def test_custom_ip_waiting_state_management(self):
        """اختبار إدارة حالة انتظار IP المخصص"""
        print("🧪 اختبار إدارة حالة انتظار IP المخصص...")
        
        # محاكاة دوال إدارة حالة انتظار IP المخصص
        def mock_is_waiting_for_custom_ip(chat_id, waiting_state):
            if not waiting_state:
                return False
            return (waiting_state.get('chat_id') == chat_id and 
                    waiting_state.get('active', False))
        
        def mock_start_custom_ip_input_mode(chat_id):
            return {
                'chat_id': chat_id,
                'active': True
            }
        
        def mock_clear_custom_ip_waiting_state():
            return {'active': False}
        
        # اختبار بدء وضع انتظار IP مخصص
        chat_id = 12345
        waiting_state = mock_start_custom_ip_input_mode(chat_id)
        
        self.assertEqual(waiting_state['chat_id'], chat_id)
        self.assertTrue(waiting_state['active'])
        
        # اختبار التحقق من حالة الانتظار
        is_waiting = mock_is_waiting_for_custom_ip(chat_id, waiting_state)
        self.assertTrue(is_waiting, "يجب أن تكون حالة الانتظار نشطة")
        
        # اختبار التحقق مع محادثة مختلفة
        is_waiting_different = mock_is_waiting_for_custom_ip(54321, waiting_state)
        self.assertFalse(is_waiting_different, "يجب ألا تكون حالة الانتظار نشطة لمحادثة مختلفة")
        
        # اختبار إلغاء حالة الانتظار
        cleared_state = mock_clear_custom_ip_waiting_state()
        self.assertFalse(cleared_state['active'], "يجب أن تكون حالة الانتظار معطلة بعد الإلغاء")
        
        print("✅ نجح اختبار إدارة حالة انتظار IP المخصص")
    
    def test_custom_ip_input_validation(self):
        """اختبار التحقق من صحة IP المخصص"""
        print("🧪 اختبار التحقق من صحة IP المخصص...")
        
        # محاكاة دالة التحقق من صحة Host
        def mock_validate_host_format(host):
            if not host or len(host) < 3:
                return False
            
            # فحص IP address
            if host.count('.') == 3:
                parts = host.split('.')
                try:
                    for part in parts:
                        num = int(part)
                        if num < 0 or num > 255:
                            return False
                    return True
                except ValueError:
                    return False
            
            # فحص domain/hostname
            if '.' in host and len(host) > 3:
                allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-')
                if all(c in allowed_chars for c in host):
                    return True
            
            return False
        
        # اختبار IP صحيحة
        valid_ips = [
            "***********00", "********", "**********", 
            "*******", "*******"
        ]
        
        for ip in valid_ips:
            result = mock_validate_host_format(ip)
            self.assertTrue(result, f"IP صحيح يجب أن يمر: {ip}")
        
        # اختبار domains صحيحة
        valid_domains = [
            "router.local", "mikrotik.company.com", 
            "server.example.org"
        ]
        
        for domain in valid_domains:
            result = mock_validate_host_format(domain)
            self.assertTrue(result, f"Domain صحيح يجب أن يمر: {domain}")
        
        # اختبار IP غير صحيحة
        invalid_ips = [
            "999.999.999.999", "192.168.1", "192.168.1.256",
            "abc", "", "   ", "invalid..host"
        ]
        
        for ip in invalid_ips:
            result = mock_validate_host_format(ip)
            self.assertFalse(result, f"IP غير صحيح يجب أن يفشل: {ip}")
        
        print("✅ نجح اختبار التحقق من صحة IP المخصص")
    
    def test_custom_ip_input_workflow(self):
        """اختبار سير عمل إدخال IP المخصص"""
        print("🧪 اختبار سير عمل إدخال IP المخصص...")
        
        # محاكاة سير عمل إدخال IP المخصص
        def mock_custom_ip_input_workflow(chat_id, ip_text, current_host, waiting_state):
            workflow_steps = []
            
            try:
                # 1. التحقق من حالة الانتظار
                workflow_steps.append("check_waiting_state")
                if not waiting_state or not waiting_state.get('active'):
                    return {
                        "success": False,
                        "reason": "no_waiting_state",
                        "steps": workflow_steps
                    }
                
                if waiting_state.get('chat_id') != chat_id:
                    return {
                        "success": False,
                        "reason": "wrong_chat_id",
                        "steps": workflow_steps
                    }
                
                # 2. تنظيف وتحقق من صحة IP
                workflow_steps.append("validate_ip")
                ip_text = ip_text.strip()
                
                # محاكاة التحقق من صحة IP
                if len(ip_text) < 7 or not '.' in ip_text:
                    return {
                        "success": False,
                        "reason": "invalid_ip",
                        "steps": workflow_steps
                    }
                
                # 3. التحقق من وجود تغيير
                workflow_steps.append("check_change")
                if current_host == ip_text:
                    return {
                        "success": False,
                        "reason": "no_change",
                        "steps": workflow_steps
                    }
                
                # 4. إنشاء رسالة التأكيد
                workflow_steps.append("create_confirmation")
                
                # 5. إلغاء حالة الانتظار
                workflow_steps.append("clear_waiting_state")
                
                return {
                    "success": True,
                    "ip_text": ip_text,
                    "steps": workflow_steps,
                    "message": "تم إنشاء تأكيد IP مخصص بنجاح"
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "steps": workflow_steps
                }
        
        # اختبار سير عمل ناجح
        chat_id = 12345
        waiting_state = {'chat_id': chat_id, 'active': True}
        
        result_success = mock_custom_ip_input_workflow(
            chat_id, "*************", "***********", waiting_state
        )
        
        self.assertTrue(result_success["success"], "يجب أن ينجح سير العمل")
        self.assertEqual(result_success["ip_text"], "*************")
        
        expected_steps = [
            "check_waiting_state", "validate_ip", "check_change",
            "create_confirmation", "clear_waiting_state"
        ]
        self.assertEqual(result_success["steps"], expected_steps)
        
        # اختبار عدم وجود حالة انتظار
        result_no_waiting = mock_custom_ip_input_workflow(
            chat_id, "*************", "***********", None
        )
        
        self.assertFalse(result_no_waiting["success"])
        self.assertEqual(result_no_waiting["reason"], "no_waiting_state")
        
        # اختبار IP غير صحيح
        result_invalid_ip = mock_custom_ip_input_workflow(
            chat_id, "invalid", "***********", waiting_state
        )
        
        self.assertFalse(result_invalid_ip["success"])
        self.assertEqual(result_invalid_ip["reason"], "invalid_ip")
        
        # اختبار عدم وجود تغيير
        result_no_change = mock_custom_ip_input_workflow(
            chat_id, "***********", "***********", waiting_state
        )
        
        self.assertFalse(result_no_change["success"])
        self.assertEqual(result_no_change["reason"], "no_change")
        
        print("✅ نجح اختبار سير عمل إدخال IP المخصص")
    
    def test_custom_ip_callback_handling(self):
        """اختبار معالجة callback queries للـ IP المخصص"""
        print("🧪 اختبار معالجة callback queries للـ IP المخصص...")
        
        # محاكاة معالجة callback queries للـ IP المخصص
        def mock_handle_custom_ip_callbacks(callback_data):
            if callback_data.startswith("apply_custom_ip_"):
                ip = callback_data.replace("apply_custom_ip_", "")
                return {
                    "action": "apply_custom_ip",
                    "ip": ip,
                    "next_step": "execute_host_change_with_restart"
                }
            
            elif callback_data.startswith("cancel_custom_ip_"):
                ip = callback_data.replace("cancel_custom_ip_", "")
                return {
                    "action": "cancel_custom_ip",
                    "ip": ip,
                    "next_step": "show_cancel_message"
                }
            
            elif callback_data == "cancel_custom_ip_input":
                return {
                    "action": "cancel_custom_ip_input",
                    "next_step": "clear_waiting_state"
                }
            
            return {"action": "unknown"}
        
        # اختبار تطبيق IP مخصص
        apply_callbacks = [
            ("apply_custom_ip_*************", "*************"),
            ("apply_custom_ip_********", "********"),
            ("apply_custom_ip_router.local", "router.local")
        ]
        
        for callback, expected_ip in apply_callbacks:
            result = mock_handle_custom_ip_callbacks(callback)
            self.assertEqual(result["action"], "apply_custom_ip")
            self.assertEqual(result["ip"], expected_ip)
            self.assertEqual(result["next_step"], "execute_host_change_with_restart")
        
        # اختبار إلغاء IP مخصص
        cancel_callbacks = [
            ("cancel_custom_ip_*************", "*************"),
            ("cancel_custom_ip_mikrotik.local", "mikrotik.local")
        ]
        
        for callback, expected_ip in cancel_callbacks:
            result = mock_handle_custom_ip_callbacks(callback)
            self.assertEqual(result["action"], "cancel_custom_ip")
            self.assertEqual(result["ip"], expected_ip)
            self.assertEqual(result["next_step"], "show_cancel_message")
        
        # اختبار إلغاء إدخال IP مخصص
        result_cancel_input = mock_handle_custom_ip_callbacks("cancel_custom_ip_input")
        self.assertEqual(result_cancel_input["action"], "cancel_custom_ip_input")
        self.assertEqual(result_cancel_input["next_step"], "clear_waiting_state")
        
        print("✅ نجح اختبار معالجة callback queries للـ IP المخصص")
    
    def test_error_handling_and_recovery(self):
        """اختبار معالجة الأخطاء والاستعادة"""
        print("🧪 اختبار معالجة الأخطاء والاستعادة...")
        
        # محاكاة معالجة الأخطاء
        def mock_error_handling_workflow(error_type, chat_id):
            try:
                if error_type == "invalid_ip":
                    return {
                        "error_handled": True,
                        "user_message": "IP غير صحيح",
                        "recovery_options": [
                            "/newip للمحاولة مرة أخرى",
                            "/sethost [IP] لتغيير IP مباشرة",
                            "اكتب IP مباشرة في المحادثة"
                        ],
                        "waiting_state_cleared": True
                    }
                
                elif error_type == "no_waiting_state":
                    return {
                        "error_handled": True,
                        "user_message": "انتهت جلسة إدخال IP",
                        "recovery_options": [
                            "/newip لبدء جلسة جديدة"
                        ],
                        "waiting_state_cleared": False
                    }
                
                elif error_type == "system_error":
                    return {
                        "error_handled": True,
                        "user_message": "حدث خطأ في النظام",
                        "recovery_options": [
                            "/newip للمحاولة مرة أخرى",
                            "/sethost [IP] لتغيير IP مباشرة"
                        ],
                        "waiting_state_cleared": True
                    }
                
                return {
                    "error_handled": False,
                    "user_message": "خطأ غير معروف"
                }
                
            except Exception as e:
                return {
                    "error_handled": False,
                    "system_error": str(e)
                }
        
        # اختبار معالجة IP غير صحيح
        result_invalid_ip = mock_error_handling_workflow("invalid_ip", 12345)
        self.assertTrue(result_invalid_ip["error_handled"])
        self.assertIn("IP غير صحيح", result_invalid_ip["user_message"])
        self.assertTrue(result_invalid_ip["waiting_state_cleared"])
        self.assertEqual(len(result_invalid_ip["recovery_options"]), 3)
        
        # اختبار معالجة عدم وجود حالة انتظار
        result_no_waiting = mock_error_handling_workflow("no_waiting_state", 12345)
        self.assertTrue(result_no_waiting["error_handled"])
        self.assertIn("انتهت جلسة", result_no_waiting["user_message"])
        self.assertFalse(result_no_waiting["waiting_state_cleared"])
        
        # اختبار معالجة خطأ النظام
        result_system_error = mock_error_handling_workflow("system_error", 12345)
        self.assertTrue(result_system_error["error_handled"])
        self.assertIn("خطأ في النظام", result_system_error["user_message"])
        self.assertTrue(result_system_error["waiting_state_cleared"])
        
        print("✅ نجح اختبار معالجة الأخطاء والاستعادة")

def run_custom_ip_input_fix_tests():
    """تشغيل جميع اختبارات إصلاح إدخال IP المخصص"""
    print("🔧 بدء اختبارات إصلاح إدخال IP المخصص")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("=" * 60)
    print("✅ انتهت جميع الاختبارات")
    
    print("\n📋 ملخص الإصلاحات المختبرة:")
    print("• ✅ إدارة حالة انتظار IP المخصص")
    print("• ✅ التحقق من صحة IP المخصص")
    print("• ✅ سير عمل إدخال IP المخصص")
    print("• ✅ معالجة callback queries للـ IP المخصص")
    print("• ✅ معالجة الأخطاء والاستعادة")

if __name__ == "__main__":
    run_custom_ip_input_fix_tests()
