#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محدد لمشكلة إعادة المحاولة للكروت الفاشلة
"""

import re
import os

def analyze_failed_cards_cleanup_logic():
    """تحليل منطق تنظيف الكروت الفاشلة"""
    print("🔍 تحليل منطق تنظيف الكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    print("\n📋 تحليل منطق حفظ وحذف الكروت الفاشلة:")
    
    # 1. البحث عن شرط حفظ الكروت الفاشلة
    save_condition_match = re.search(r'if failed_count > 0 and getattr\(self, \'system_type\', \'\'\) == \'hotspot\':(.*?)else:(.*?)# مسح أي بيانات سابقة للكروت الفاشلة', func_code, re.DOTALL)
    
    if save_condition_match:
        save_block = save_condition_match.group(1).strip()
        cleanup_block = save_condition_match.group(2).strip()
        
        print("✅ تم العثور على منطق حفظ/حذف الكروت الفاشلة")
        print(f"\n📝 شرط الحفظ: if failed_count > 0 and system_type == 'hotspot'")
        print(f"📝 كتلة الحفظ: {save_block[:100]}...")
        print(f"📝 كتلة الحذف: {cleanup_block[:100]}...")
        
        # تحليل الشرط الفرعي
        sub_condition_match = re.search(r'if hasattr\(self, \'single_card_failed_cards\'\) and self\.single_card_failed_cards:', save_block)
        if sub_condition_match:
            print("✅ يوجد شرط فرعي للتحقق من وجود single_card_failed_cards")
        else:
            print("❌ لا يوجد شرط فرعي للتحقق من وجود single_card_failed_cards")
        
        # تحليل رسالة التحذير
        warning_match = re.search(r'لا توجد كروت فاشلة محفوظة للكرت الواحد رغم وجود failed_count', save_block)
        if warning_match:
            print("✅ يوجد تحذير عند عدم وجود كروت فاشلة محفوظة")
        else:
            print("❌ لا يوجد تحذير عند عدم وجود كروت فاشلة محفوظة")
    
    else:
        print("❌ لم يتم العثور على منطق حفظ/حذف الكروت الفاشلة")
    
    return True

def check_single_card_failed_cards_initialization():
    """فحص تهيئة single_card_failed_cards"""
    print("\n🔍 فحص تهيئة single_card_failed_cards...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # البحث عن تهيئة single_card_failed_cards
    init_matches = re.findall(r'if not hasattr\(self, \'single_card_failed_cards\'\):.*?self\.single_card_failed_cards = \[\]', func_code, re.DOTALL)
    
    print(f"📊 عدد أماكن تهيئة single_card_failed_cards: {len(init_matches)}")
    
    if init_matches:
        for i, match in enumerate(init_matches, 1):
            print(f"   📝 مكان {i}: {match[:50]}...")
    
    # البحث عن استخدام single_card_failed_cards
    usage_matches = re.findall(r'self\.single_card_failed_cards\.append\(', func_code)
    print(f"📊 عدد أماكن إضافة كروت فاشلة: {len(usage_matches)}")
    
    # البحث عن تنظيف single_card_failed_cards في بداية الدالة
    start_cleanup = re.search(r'def send_single_card_to_mikrotik_silent.*?for i, cred in enumerate', func_code, re.DOTALL)
    if start_cleanup:
        start_section = start_cleanup.group(0)
        if 'single_card_failed_cards' in start_section and 'delattr' in start_section:
            print("⚠️ يتم تنظيف single_card_failed_cards في بداية الدالة")
        else:
            print("✅ لا يتم تنظيف single_card_failed_cards في بداية الدالة")
    
    return True

def check_failed_cards_info_timing():
    """فحص توقيت حفظ failed_cards_info"""
    print("\n🔍 فحص توقيت حفظ failed_cards_info...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # تحليل ترتيب العمليات
    operations = []
    
    # البحث عن مكان حفظ failed_cards_info
    failed_info_pos = func_code.find('self.failed_cards_info = {')
    if failed_info_pos != -1:
        operations.append(("حفظ failed_cards_info", failed_info_pos))
    
    # البحث عن مكان return
    return_pos = func_code.find('return True')
    if return_pos != -1:
        operations.append(("return True", return_pos))
    
    return_false_pos = func_code.find('return False')
    if return_false_pos != -1:
        operations.append(("return False", return_false_pos))
    
    # ترتيب العمليات حسب الموقع
    operations.sort(key=lambda x: x[1])
    
    print("📋 ترتيب العمليات في الدالة:")
    for i, (op, pos) in enumerate(operations, 1):
        print(f"   {i}. {op} (موقع: {pos})")
    
    # التحقق من أن حفظ failed_cards_info يحدث قبل return
    failed_info_before_return = any(op[0] == "حفظ failed_cards_info" for op in operations[:-1])
    if failed_info_before_return:
        print("✅ يتم حفظ failed_cards_info قبل return")
    else:
        print("❌ لا يتم حفظ failed_cards_info قبل return")
    
    return True

def check_process_single_card_creation_cleanup():
    """فحص تنظيف البيانات في process_single_card_creation"""
    print("\n🔍 فحص تنظيف البيانات في process_single_card_creation...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة process_single_card_creation
    func_match = re.search(r'def process_single_card_creation.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة process_single_card_creation")
        return False
    
    func_code = func_match.group(0)
    
    # البحث عن تنظيف البيانات
    cleanup_patterns = [
        'delattr(self, \'single_card_failed_cards\')',
        'delattr(self, \'failed_cards_info\')',
        'مسح أي بيانات سابقة'
    ]
    
    cleanup_found = False
    for pattern in cleanup_patterns:
        if pattern in func_code:
            cleanup_found = True
            print(f"⚠️ تم العثور على تنظيف: {pattern}")
    
    if not cleanup_found:
        print("✅ لا يتم تنظيف البيانات في process_single_card_creation")
    
    # البحث عن ترتيب العمليات
    send_to_mikrotik_pos = func_code.find('send_single_card_to_mikrotik_silent()')
    send_to_telegram_pos = func_code.find('send_single_card_details_to_telegram(')
    
    if send_to_mikrotik_pos != -1 and send_to_telegram_pos != -1:
        if send_to_mikrotik_pos < send_to_telegram_pos:
            print("✅ ترتيب العمليات صحيح: MikroTik ثم Telegram")
        else:
            print("⚠️ ترتيب العمليات غير صحيح: Telegram قبل MikroTik")
    
    return True

def run_specific_diagnosis():
    """تشغيل التشخيص المحدد"""
    print("🚀 بدء التشخيص المحدد لمشكلة إعادة المحاولة\n")
    
    tests = [
        analyze_failed_cards_cleanup_logic,
        check_single_card_failed_cards_initialization,
        check_failed_cards_info_timing,
        check_process_single_card_creation_cleanup
    ]
    
    for test in tests:
        try:
            test()
            print("✅ التشخيص مكتمل\n")
        except Exception as e:
            print(f"❌ خطأ في التشخيص: {str(e)}\n")
    
    print("🎯 خلاصة التشخيص المحدد:")
    print("   المشكلة المحتملة: منطق الشروط في حفظ/حذف البيانات")
    print("   الحل المقترح: مراجعة شروط حفظ failed_cards_info")

if __name__ == "__main__":
    run_specific_diagnosis()
