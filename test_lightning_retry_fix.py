#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة إعادة المحاولة للبرق (Lightning)
"""

import re
import os

def test_lightning_failed_cards_saving():
    """اختبار حفظ معلومات الكروت الفاشلة للبرق"""
    print("🔍 اختبار حفظ معلومات الكروت الفاشلة للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_to_mikrotik_silent (دالة البرق)
    func_match = re.search(r'def send_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ معلومات الكروت الفاشلة للبرق
    lightning_patterns = [
        'حفظ الكروت الفاشلة لخيار "إعادة المحاولة للكروت الفاشلة" - البرق',
        'حفظ {failed_count} كرت فاشل لعرض خيار إعادة المحاولة للبرق',
        'if failed_cards:  # استخدام قائمة failed_cards المحلية مباشرة',
        'تحويل failed_cards إلى تنسيق مناسب لإعادة المحاولة',
        'converted_failed_cards = []',
        'for failed_card in failed_cards:',
        'for cred in self.generated_credentials:',
        'if cred.get(\'username\') == failed_card.get(\'name\'):',
        '\'card_type\': \'lightning\',',
        '\'operation_type\': \'lightning\'',
        'تم حفظ معلومات الكروت الفاشلة للبرق:'
    ]
    
    missing_patterns = []
    for pattern in lightning_patterns:
        if pattern not in func_code:
            missing_patterns.append(pattern)
        else:
            print(f"✅ نمط البرق موجود: {pattern[:50]}...")
    
    if missing_patterns:
        print(f"❌ أنماط البرق المفقودة ({len(missing_patterns)}):")
        for pattern in missing_patterns:
            print(f"   - {pattern}")
        return False
    
    print("✅ جميع أنماط حفظ الكروت الفاشلة للبرق موجودة")
    return True

def test_lightning_retry_function():
    """اختبار دالة إعادة المحاولة للبرق"""
    print("\n🔍 اختبار دالة إعادة المحاولة للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة retry_lightning_failed_cards
    func_match = re.search(r'def retry_lightning_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة retry_lightning_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من تنفيذ دالة إعادة المحاولة للبرق
    retry_patterns = [
        'إنشاء قائمة جديدة من الكروت للمحاولة مرة أخرى',
        'retry_credentials = []',
        'for failed_card in failed_cards:',
        'retry_cred = {',
        '\'username\': failed_card.get(\'username\', \'\'),',
        '\'password\': failed_card.get(\'password\', \'\'),',
        'retry_credentials.append(retry_cred)',
        'original_credentials = self.generated_credentials.copy()',
        'self.generated_credentials = retry_credentials',
        'success = self.send_to_mikrotik_silent()',
        'self.generated_credentials = original_credentials',
        'نجحت إعادة المحاولة للبرق:',
        'فشلت إعادة المحاولة للبرق:'
    ]
    
    for pattern in retry_patterns:
        if pattern not in func_code:
            print(f"❌ نمط إعادة المحاولة غير موجود: {pattern}")
            return False
        print(f"✅ نمط إعادة المحاولة موجود: {pattern[:40]}...")
    
    return True

def test_lightning_callback_handling():
    """اختبار معالجة callback للبرق"""
    print("\n🔍 اختبار معالجة callback للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة execute_retry_failed_cards
    func_match = re.search(r'def execute_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من معالجة نوع البرق
    lightning_callback_patterns = [
        'elif card_type == \'lightning\':',
        'success = self.retry_lightning_failed_cards(failed_cards)'
    ]
    
    for pattern in lightning_callback_patterns:
        if pattern not in func_code:
            print(f"❌ نمط معالجة callback للبرق غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback للبرق موجود: {pattern}")
    
    return True

def test_lightning_data_conversion():
    """اختبار تحويل البيانات للبرق"""
    print("\n🔍 اختبار تحويل البيانات للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_to_mikrotik_silent
    func_match = re.search(r'def send_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من تحويل البيانات للبرق
    conversion_patterns = [
        'البحث عن الكرت الأصلي في generated_credentials',
        'original_card = None',
        'if cred.get(\'username\') == failed_card.get(\'name\'):',
        'original_card = cred',
        'break',
        'if original_card:',
        'converted_card = {',
        '\'username\': original_card.get(\'username\', \'\'),',
        '\'server\': server,',
        '\'error\': failed_card.get(\'error\', \'خطأ غير محدد\')',
        'converted_failed_cards.append(converted_card)'
    ]
    
    for pattern in conversion_patterns:
        if pattern not in func_code:
            print(f"❌ نمط تحويل البيانات للبرق غير موجود: {pattern}")
            return False
        print(f"✅ نمط تحويل البيانات للبرق موجود: {pattern[:40]}...")
    
    return True

def test_lightning_error_handling():
    """اختبار معالجة الأخطاء للبرق"""
    print("\n🔍 اختبار معالجة الأخطاء للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_to_mikrotik_silent
    func_match = re.search(r'def send_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من معالجة الأخطاء للبرق
    error_patterns = [
        'لا توجد كروت فاشلة في قائمة failed_cards رغم وجود failed_count=',
        'لم يتم حفظ الكروت الفاشلة للبرق - الشروط غير مستوفاة',
        'التحقق من أن failed_cards_info خاص بالبرق قبل الحذف',
        'if getattr(self, \'failed_cards_info\', {}).get(\'operation_type\') == \'lightning\':'
    ]
    
    for pattern in error_patterns:
        if pattern not in func_code:
            print(f"❌ نمط معالجة الأخطاء للبرق غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة الأخطاء للبرق موجود: {pattern[:50]}...")
    
    return True

def test_lightning_logging():
    """اختبار تسجيل السجلات للبرق"""
    print("\n🔍 اختبار تسجيل السجلات للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل السجلات للبرق
    logging_patterns = [
        'تم حفظ معلومات الكروت الفاشلة للبرق:',
        'بدء إعادة محاولة البرق للكروت الفاشلة:',
        'نجحت إعادة المحاولة للبرق:',
        'فشلت إعادة المحاولة للبرق:',
        'خطأ في إعادة محاولة البرق:'
    ]
    
    for pattern in logging_patterns:
        if pattern not in content:
            print(f"❌ نمط السجلات للبرق غير موجود: {pattern}")
            return False
        print(f"✅ نمط السجلات للبرق موجود: {pattern}")
    
    return True

def run_lightning_retry_test():
    """تشغيل اختبار إصلاح إعادة المحاولة للبرق"""
    print("🚀 بدء اختبار إصلاح إعادة المحاولة للبرق (Lightning)\n")
    
    tests = [
        test_lightning_failed_cards_saving,
        test_lightning_retry_function,
        test_lightning_callback_handling,
        test_lightning_data_conversion,
        test_lightning_error_handling,
        test_lightning_logging
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات إصلاح البرق نجحت!")
        print("✅ تم إصلاح مشكلة إعادة المحاولة للبرق بنجاح")
        print("\n🎯 ملخص الإصلاح للبرق:")
        print("   • تم إضافة حفظ معلومات الكروت الفاشلة في دالة send_to_mikrotik_silent")
        print("   • تم تطبيق دالة retry_lightning_failed_cards بشكل كامل")
        print("   • تم إضافة تحويل ذكي للبيانات من failed_cards إلى تنسيق مناسب")
        print("   • تم تحسين معالجة الأخطاء والرسائل التشخيصية")
        print("   • تم ضمان سلامة البيانات أثناء إعادة المحاولة")
        print("\n💡 النتيجة المتوقعة:")
        print("   • لن تظهر رسالة 'لا يوجد معلومات محفوظة للكروت الفاشلة' للبرق")
        print("   • زر إعادة المحاولة سيظهر عند وجود كروت فاشلة في البرق")
        print("   • وظيفة إعادة المحاولة ستعمل بشكل صحيح للبرق")
        return True
    else:
        print("⚠️ بعض اختبارات إصلاح البرق فشلت")
        return False

if __name__ == "__main__":
    run_lightning_retry_test()
