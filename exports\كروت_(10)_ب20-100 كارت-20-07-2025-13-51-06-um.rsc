# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-20 13:51:06
# القالب: 10
# النظام: user_manager
# عدد الكروت: 100
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 100";

:local success 0;
:local errors 0;
:local total 100;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 100 مستخدم User Manager...";

# المستخدم 1: 2063264338
:do {
    /tool user-manager user add customer="admin" username="2063264338" password="41025446" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063264338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063264338";
};

# المستخدم 2: 2043154960
:do {
    /tool user-manager user add customer="admin" username="2043154960" password="92072416" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043154960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043154960";
};

# المستخدم 3: 2018370508
:do {
    /tool user-manager user add customer="admin" username="2018370508" password="64931685" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018370508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018370508";
};

# المستخدم 4: 2062142795
:do {
    /tool user-manager user add customer="admin" username="2062142795" password="37594145" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062142795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062142795";
};

# المستخدم 5: 2046332993
:do {
    /tool user-manager user add customer="admin" username="2046332993" password="03743519" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046332993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046332993";
};

# المستخدم 6: 2030246383
:do {
    /tool user-manager user add customer="admin" username="2030246383" password="96627270" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030246383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030246383";
};

# المستخدم 7: 2074060255
:do {
    /tool user-manager user add customer="admin" username="2074060255" password="06122610" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074060255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074060255";
};

# المستخدم 8: 2062516403
:do {
    /tool user-manager user add customer="admin" username="2062516403" password="87280200" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062516403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062516403";
};

# المستخدم 9: 2029317817
:do {
    /tool user-manager user add customer="admin" username="2029317817" password="23722099" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029317817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029317817";
};

# المستخدم 10: 2094573862
:do {
    /tool user-manager user add customer="admin" username="2094573862" password="06685264" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094573862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094573862";
};

# المستخدم 11: 2007174696
:do {
    /tool user-manager user add customer="admin" username="2007174696" password="47233239" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007174696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007174696";
};

# المستخدم 12: 2070183377
:do {
    /tool user-manager user add customer="admin" username="2070183377" password="92556677" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070183377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070183377";
};

# المستخدم 13: 2010323670
:do {
    /tool user-manager user add customer="admin" username="2010323670" password="64150438" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010323670";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010323670";
};

# المستخدم 14: 2044384160
:do {
    /tool user-manager user add customer="admin" username="2044384160" password="83582919" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044384160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044384160";
};

# المستخدم 15: 2046644114
:do {
    /tool user-manager user add customer="admin" username="2046644114" password="62038836" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046644114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046644114";
};

# المستخدم 16: 2098655405
:do {
    /tool user-manager user add customer="admin" username="2098655405" password="36675270" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098655405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098655405";
};

# المستخدم 17: 2043383291
:do {
    /tool user-manager user add customer="admin" username="2043383291" password="15576489" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043383291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043383291";
};

# المستخدم 18: 2074816269
:do {
    /tool user-manager user add customer="admin" username="2074816269" password="51226142" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074816269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074816269";
};

# المستخدم 19: 2004765794
:do {
    /tool user-manager user add customer="admin" username="2004765794" password="40888304" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004765794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004765794";
};

# المستخدم 20: 2056806837
:do {
    /tool user-manager user add customer="admin" username="2056806837" password="39908316" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056806837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056806837";
};

# المستخدم 21: 2011254617
:do {
    /tool user-manager user add customer="admin" username="2011254617" password="24146998" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011254617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011254617";
};

# المستخدم 22: 2075829077
:do {
    /tool user-manager user add customer="admin" username="2075829077" password="37851191" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075829077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075829077";
};

# المستخدم 23: 2093196610
:do {
    /tool user-manager user add customer="admin" username="2093196610" password="97467109" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093196610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093196610";
};

# المستخدم 24: 2004870469
:do {
    /tool user-manager user add customer="admin" username="2004870469" password="72875245" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004870469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004870469";
};

# المستخدم 25: 2088792463
:do {
    /tool user-manager user add customer="admin" username="2088792463" password="20628542" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088792463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088792463";
};

# المستخدم 26: 2093529463
:do {
    /tool user-manager user add customer="admin" username="2093529463" password="60480257" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093529463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093529463";
};

# المستخدم 27: 2048596727
:do {
    /tool user-manager user add customer="admin" username="2048596727" password="55672671" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048596727";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048596727";
};

# المستخدم 28: 2008966908
:do {
    /tool user-manager user add customer="admin" username="2008966908" password="11067285" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008966908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008966908";
};

# المستخدم 29: 2018168609
:do {
    /tool user-manager user add customer="admin" username="2018168609" password="64747535" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018168609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018168609";
};

# المستخدم 30: 2081895217
:do {
    /tool user-manager user add customer="admin" username="2081895217" password="28498188" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081895217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081895217";
};

# المستخدم 31: 2099777550
:do {
    /tool user-manager user add customer="admin" username="2099777550" password="50318331" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099777550";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099777550";
};

# المستخدم 32: 2051165915
:do {
    /tool user-manager user add customer="admin" username="2051165915" password="24637634" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051165915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051165915";
};

# المستخدم 33: 2040131800
:do {
    /tool user-manager user add customer="admin" username="2040131800" password="11536340" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040131800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040131800";
};

# المستخدم 34: 2093328126
:do {
    /tool user-manager user add customer="admin" username="2093328126" password="12802390" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093328126";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093328126";
};

# المستخدم 35: 2011210669
:do {
    /tool user-manager user add customer="admin" username="2011210669" password="19967912" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011210669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011210669";
};

# المستخدم 36: 2089562194
:do {
    /tool user-manager user add customer="admin" username="2089562194" password="70546142" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089562194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089562194";
};

# المستخدم 37: 2082167684
:do {
    /tool user-manager user add customer="admin" username="2082167684" password="13255597" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082167684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082167684";
};

# المستخدم 38: 2093524170
:do {
    /tool user-manager user add customer="admin" username="2093524170" password="44084099" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093524170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093524170";
};

# المستخدم 39: 2081467991
:do {
    /tool user-manager user add customer="admin" username="2081467991" password="75296346" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081467991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081467991";
};

# المستخدم 40: 2034350741
:do {
    /tool user-manager user add customer="admin" username="2034350741" password="41868464" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034350741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034350741";
};

# المستخدم 41: 2067949813
:do {
    /tool user-manager user add customer="admin" username="2067949813" password="60270477" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067949813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067949813";
};

# المستخدم 42: 2064289797
:do {
    /tool user-manager user add customer="admin" username="2064289797" password="92637375" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064289797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064289797";
};

# المستخدم 43: 2084282176
:do {
    /tool user-manager user add customer="admin" username="2084282176" password="28823032" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084282176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084282176";
};

# المستخدم 44: 2050912047
:do {
    /tool user-manager user add customer="admin" username="2050912047" password="81251460" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050912047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050912047";
};

# المستخدم 45: 2039798520
:do {
    /tool user-manager user add customer="admin" username="2039798520" password="68753800" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039798520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039798520";
};

# المستخدم 46: 2055118588
:do {
    /tool user-manager user add customer="admin" username="2055118588" password="80678019" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055118588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055118588";
};

# المستخدم 47: 2042176536
:do {
    /tool user-manager user add customer="admin" username="2042176536" password="78782012" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042176536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042176536";
};

# المستخدم 48: 2072235509
:do {
    /tool user-manager user add customer="admin" username="2072235509" password="13324379" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072235509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072235509";
};

# المستخدم 49: 2091800469
:do {
    /tool user-manager user add customer="admin" username="2091800469" password="93223711" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091800469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091800469";
};

# المستخدم 50: 2091125908
:do {
    /tool user-manager user add customer="admin" username="2091125908" password="04218001" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091125908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091125908";
};

# المستخدم 51: 2096511193
:do {
    /tool user-manager user add customer="admin" username="2096511193" password="65797467" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096511193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096511193";
};

# المستخدم 52: 2069152308
:do {
    /tool user-manager user add customer="admin" username="2069152308" password="76052662" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069152308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069152308";
};

# المستخدم 53: 2019756967
:do {
    /tool user-manager user add customer="admin" username="2019756967" password="05266340" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019756967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019756967";
};

# المستخدم 54: 2070938827
:do {
    /tool user-manager user add customer="admin" username="2070938827" password="49218737" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070938827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070938827";
};

# المستخدم 55: 2015763072
:do {
    /tool user-manager user add customer="admin" username="2015763072" password="47616976" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015763072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015763072";
};

# المستخدم 56: 2098916897
:do {
    /tool user-manager user add customer="admin" username="2098916897" password="18765299" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098916897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098916897";
};

# المستخدم 57: 2001540238
:do {
    /tool user-manager user add customer="admin" username="2001540238" password="74329375" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001540238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001540238";
};

# المستخدم 58: 2088475238
:do {
    /tool user-manager user add customer="admin" username="2088475238" password="78634491" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088475238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088475238";
};

# المستخدم 59: 2030942955
:do {
    /tool user-manager user add customer="admin" username="2030942955" password="56954239" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030942955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030942955";
};

# المستخدم 60: 2016258924
:do {
    /tool user-manager user add customer="admin" username="2016258924" password="54053189" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016258924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016258924";
};

# المستخدم 61: 2036578701
:do {
    /tool user-manager user add customer="admin" username="2036578701" password="58372273" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036578701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036578701";
};

# المستخدم 62: 2083217610
:do {
    /tool user-manager user add customer="admin" username="2083217610" password="99851920" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083217610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083217610";
};

# المستخدم 63: 2013408314
:do {
    /tool user-manager user add customer="admin" username="2013408314" password="09610954" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013408314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013408314";
};

# المستخدم 64: 2060506398
:do {
    /tool user-manager user add customer="admin" username="2060506398" password="70679140" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060506398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060506398";
};

# المستخدم 65: 2023161604
:do {
    /tool user-manager user add customer="admin" username="2023161604" password="45679475" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023161604";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023161604";
};

# المستخدم 66: 2031510234
:do {
    /tool user-manager user add customer="admin" username="2031510234" password="57872409" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031510234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031510234";
};

# المستخدم 67: 2047319130
:do {
    /tool user-manager user add customer="admin" username="2047319130" password="75976732" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047319130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047319130";
};

# المستخدم 68: 2046557167
:do {
    /tool user-manager user add customer="admin" username="2046557167" password="13591166" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046557167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046557167";
};

# المستخدم 69: 2009167941
:do {
    /tool user-manager user add customer="admin" username="2009167941" password="88302813" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009167941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009167941";
};

# المستخدم 70: 2016308958
:do {
    /tool user-manager user add customer="admin" username="2016308958" password="93293058" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016308958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016308958";
};

# المستخدم 71: 2016424883
:do {
    /tool user-manager user add customer="admin" username="2016424883" password="21091169" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016424883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016424883";
};

# المستخدم 72: 2089226867
:do {
    /tool user-manager user add customer="admin" username="2089226867" password="87655952" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089226867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089226867";
};

# المستخدم 73: 2082868896
:do {
    /tool user-manager user add customer="admin" username="2082868896" password="56995075" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082868896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082868896";
};

# المستخدم 74: 2076768581
:do {
    /tool user-manager user add customer="admin" username="2076768581" password="09402307" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076768581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076768581";
};

# المستخدم 75: 2065096354
:do {
    /tool user-manager user add customer="admin" username="2065096354" password="57335078" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065096354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065096354";
};

# المستخدم 76: 2036581128
:do {
    /tool user-manager user add customer="admin" username="2036581128" password="94610378" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036581128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036581128";
};

# المستخدم 77: 2051498693
:do {
    /tool user-manager user add customer="admin" username="2051498693" password="21935616" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051498693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051498693";
};

# المستخدم 78: 2031680558
:do {
    /tool user-manager user add customer="admin" username="2031680558" password="60942469" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031680558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031680558";
};

# المستخدم 79: 2008057562
:do {
    /tool user-manager user add customer="admin" username="2008057562" password="78145681" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008057562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008057562";
};

# المستخدم 80: 2055187657
:do {
    /tool user-manager user add customer="admin" username="2055187657" password="11354184" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055187657";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055187657";
};

# المستخدم 81: 2092194993
:do {
    /tool user-manager user add customer="admin" username="2092194993" password="62720711" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092194993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092194993";
};

# المستخدم 82: 2047376614
:do {
    /tool user-manager user add customer="admin" username="2047376614" password="71061013" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047376614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047376614";
};

# المستخدم 83: 2043649394
:do {
    /tool user-manager user add customer="admin" username="2043649394" password="64622360" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043649394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043649394";
};

# المستخدم 84: 2045335650
:do {
    /tool user-manager user add customer="admin" username="2045335650" password="91015491" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045335650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045335650";
};

# المستخدم 85: 2066899395
:do {
    /tool user-manager user add customer="admin" username="2066899395" password="94163117" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066899395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066899395";
};

# المستخدم 86: 2063817677
:do {
    /tool user-manager user add customer="admin" username="2063817677" password="23851495" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063817677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063817677";
};

# المستخدم 87: 2022200040
:do {
    /tool user-manager user add customer="admin" username="2022200040" password="28499036" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022200040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022200040";
};

# المستخدم 88: 2053214532
:do {
    /tool user-manager user add customer="admin" username="2053214532" password="44294011" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053214532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053214532";
};

# المستخدم 89: 2074505566
:do {
    /tool user-manager user add customer="admin" username="2074505566" password="30983816" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074505566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074505566";
};

# المستخدم 90: 2025963235
:do {
    /tool user-manager user add customer="admin" username="2025963235" password="81116013" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025963235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025963235";
};

# المستخدم 91: 2094598556
:do {
    /tool user-manager user add customer="admin" username="2094598556" password="11680765" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094598556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094598556";
};

# المستخدم 92: 2033774274
:do {
    /tool user-manager user add customer="admin" username="2033774274" password="62391555" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033774274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033774274";
};

# المستخدم 93: 2085158970
:do {
    /tool user-manager user add customer="admin" username="2085158970" password="59849134" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085158970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085158970";
};

# المستخدم 94: 2062121728
:do {
    /tool user-manager user add customer="admin" username="2062121728" password="19773072" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062121728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062121728";
};

# المستخدم 95: 2043382613
:do {
    /tool user-manager user add customer="admin" username="2043382613" password="74713766" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043382613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043382613";
};

# المستخدم 96: 2096320250
:do {
    /tool user-manager user add customer="admin" username="2096320250" password="59035561" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096320250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096320250";
};

# المستخدم 97: 2071303138
:do {
    /tool user-manager user add customer="admin" username="2071303138" password="96069372" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071303138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071303138";
};

# المستخدم 98: 2069115212
:do {
    /tool user-manager user add customer="admin" username="2069115212" password="45384148" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069115212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069115212";
};

# المستخدم 99: 2097095567
:do {
    /tool user-manager user add customer="admin" username="2097095567" password="51540782" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097095567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097095567";
};

# المستخدم 100: 2040960496
:do {
    /tool user-manager user add customer="admin" username="2040960496" password="17506604" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040960496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040960496";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
