# ميزة حد تقسيم البرق في قوالب اليوزر منجير

## 📋 نظرة عامة

تم إضافة ميزة **حد تقسيم البرق (Lightning Batch Limit)** إلى نظام القوالب في مولد كروت MikroTik لجعل كل قالب يحتوي على إعدادات البرق الخاصة به بدلاً من استخدام إعداد عام واحد لجميع القوالب.

## ✨ المميزات الجديدة

### 1. حقل حد تقسيم البرق في القوالب
- **حقل جديد**: `lightning_batch_size` في جميع القوالب
- **نوع البيانات**: نص يحتوي على رقم موجب
- **القيمة الافتراضية**: "100"
- **الغرض**: تحديد عدد الكروت في كل مجموعة عند استخدام ميزة البرق

### 2. حفظ مستقل لكل قالب
- **استقلالية**: كل قالب له حد تقسيم البرق الخاص به
- **مرونة**: يمكن تخصيص حد مختلف لكل قالب حسب الحاجة
- **توافق**: القوالب الموجودة تحصل على القيمة الافتراضية تلقائياً

### 3. التحقق من صحة القيم
- **رقم موجب**: يجب أن تكون القيمة رقم أكبر من صفر
- **حد أقصى**: يُنصح بعدم تجاوز 1000 لتجنب مشاكل الأداء
- **معالجة الأخطاء**: قيم غير صحيحة يتم تجاهلها واستخدام القيمة الافتراضية

## 🔧 التطبيق التقني

### 1. تحديث بنية البيانات

#### في ملفات القوالب (JSON):
```json
{
  "قالب_مثال": {
    "system_type": "user_manager",
    "profile": "student",
    "location": "المدرسة الابتدائية",
    "division": "التعليم الابتدائي",
    "lightning_batch_size": "50",
    // باقي الحقول...
  }
}
```

#### في الكود:
```python
# إضافة حقل حد تقسيم البرق عند حفظ القالب
template = {
    "system_type": self.system_type,
    "location": self.location_entry.get(),
    "division": self.division_entry.get(),
    "lightning_batch_size": self.lightning_batch_size_entry.get() if hasattr(self, 'lightning_batch_size_entry') else "100",
    // باقي الحقول...
}
```

### 2. تحديث دوال الحفظ والتحميل

#### حفظ القالب:
```python
def save_template(self):
    template = {
        # ... باقي الحقول
        "lightning_batch_size": self.lightning_batch_size_entry.get() if hasattr(self, 'lightning_batch_size_entry') else "100"
    }
```

#### تحميل القالب:
```python
def load_template(self):
    # ... تحميل باقي الحقول
    if "lightning_batch_size" in template and hasattr(self, 'lightning_batch_size_entry'):
        self.lightning_batch_size_entry.delete(0, tk.END)
        self.lightning_batch_size_entry.insert(0, template["lightning_batch_size"])
```

### 3. تحديث المزامنة الشاملة

#### مزامنة حد تقسيم البرق:
```python
def sync_connection_settings(self, template):
    # ... مزامنة باقي الإعدادات
    
    # مزامنة حد تقسيم البرق
    if 'lightning_batch_size' in template:
        if hasattr(self, 'lightning_batch_size_entry') and self.lightning_batch_size_entry:
            current_batch_size = self.lightning_batch_size_entry.get().strip()
            new_batch_size = str(template['lightning_batch_size']).strip()
            if current_batch_size != new_batch_size:
                # التحقق من صحة القيمة
                try:
                    batch_size_int = int(new_batch_size)
                    if batch_size_int > 0:
                        self.lightning_batch_size_entry.delete(0, tk.END)
                        self.lightning_batch_size_entry.insert(0, new_batch_size)
                        applied += 1
                except ValueError:
                    # قيمة غير صحيحة - تجاهل
                    pass
```

## 📊 أمثلة على الاستخدام

### 1. قوالب بحدود مختلفة للبرق
```json
{
  "قالب_مكتب_صغير": {
    "division": "الإدارة",
    "location": "مكتب صغير",
    "lightning_batch_size": "50",
    "comment": "مكتب صغير - حد برق منخفض"
  },
  "قالب_مدرسة_متوسطة": {
    "division": "التعليم",
    "location": "مدرسة متوسطة", 
    "lightning_batch_size": "100",
    "comment": "مدرسة متوسطة - حد برق عادي"
  },
  "قالب_جامعة_كبيرة": {
    "division": "التعليم العالي",
    "location": "جامعة كبيرة",
    "lightning_batch_size": "200",
    "comment": "جامعة كبيرة - حد برق عالي"
  }
}
```

### 2. استخدام الحد في ميزة البرق
```python
def get_lightning_batch_size_from_template(template_data, default_size=100):
    """الحصول على حد تقسيم البرق من القالب"""
    if 'lightning_batch_size' in template_data:
        try:
            batch_size = int(template_data['lightning_batch_size'])
            if batch_size > 0:
                return batch_size
        except ValueError:
            pass
    return default_size

# استخدام الحد عند تقسيم الكروت
template_data = load_template("قالب_مدرسة_متوسطة")
batch_size = get_lightning_batch_size_from_template(template_data)
# batch_size = 100

# تقسيم 500 كرت إلى مجموعات حسب الحد
total_cards = 500
batches = []
for i in range(0, total_cards, batch_size):
    batch = cards[i:i + batch_size]
    batches.append(batch)
# النتيجة: 5 مجموعات، كل مجموعة 100 كرت
```

## 🎯 الفوائد

### 1. مرونة أكبر
- **تخصيص مستقل**: كل قالب له إعدادات البرق الخاصة به
- **تحكم دقيق**: يمكن ضبط الحد حسب حجم المؤسسة أو نوع الاستخدام
- **أداء محسن**: حدود مناسبة تحسن من أداء النظام

### 2. سهولة الإدارة
- **حفظ تلقائي**: الحد يُحفظ مع القالب تلقائياً
- **تحميل تلقائي**: عند اختيار قالب، يتم تطبيق حده تلقائياً
- **مزامنة شاملة**: الحد يتم مزامنته مع باقي إعدادات القالب

### 3. توافق مع النسخة السابقة
- **القوالب القديمة**: تحصل على القيمة الافتراضية (100)
- **لا تأثير**: القوالب الموجودة تعمل بدون تغيير
- **ترقية سلسة**: التحديث يتم تلقائياً عند أول حفظ

## 🔄 التوافق مع النسخة السابقة

### 1. القوالب الموجودة
- **قيمة افتراضية**: القوالب التي لا تحتوي على الحقل تحصل على "100"
- **عدم التأثير**: ميزة البرق تعمل كما هو متوقع
- **ترقية تدريجية**: يمكن إضافة الحقل للقوالب عند الحاجة

### 2. الملفات المحدثة
- ✅ `اخر حاجة  - كروت وبوت.py` - الكود الرئيسي
- ✅ `config/mikrotik_user_manager_templates.json` - قوالب User Manager
- ✅ `config/mikrotik_hotspot_templates.json` - قوالب Hotspot (إذا لزم الأمر)

## 🧪 الاختبارات

تم إنشاء ملف اختبار شامل: `test_lightning_batch_size.py`

### اختبارات مشمولة:
1. **تحميل الحد**: التحقق من تحميل حد تقسيم البرق من القوالب
2. **التحقق من الصحة**: التحقق من صحة القيم المدخلة
3. **الاستخدام**: اختبار استخدام الحد في ميزة البرق
4. **قوالب Hotspot**: اختبار الحد في قوالب Hotspot
5. **الإحصائيات**: حساب إحصائيات حدود البرق

### تشغيل الاختبارات:
```bash
python test_lightning_batch_size.py
```

## 📝 ملاحظات مهمة

### 1. قيود القيم
- **الحد الأدنى**: 1 (رقم موجب)
- **الحد الأقصى المنصوح**: 1000 (لتجنب مشاكل الأداء)
- **نوع البيانات**: نص يحتوي على رقم صحيح

### 2. أفضل الممارسات
- **المكاتب الصغيرة**: 25-50 كرت لكل مجموعة
- **المدارس المتوسطة**: 50-100 كرت لكل مجموعة  
- **الجامعات الكبيرة**: 100-200 كرت لكل مجموعة
- **المؤسسات الضخمة**: 200-500 كرت لكل مجموعة

### 3. اعتبارات الأداء
- **حدود صغيرة**: مزيد من المجموعات، معالجة أبطأ
- **حدود كبيرة**: مجموعات أقل، ضغط أكبر على النظام
- **التوازن**: اختيار حد مناسب حسب قدرة الخادم

## 🚀 الخطوات التالية

### 1. تحسينات مقترحة
- [ ] إضافة واجهة لتعديل الحد مباشرة من البرنامج
- [ ] عرض الحد الحالي في معلومات القالب
- [ ] إحصائيات مرئية لحدود البرق في القوالب
- [ ] تحذيرات عند استخدام حدود غير مناسبة

### 2. ميزات إضافية
- [ ] حدود ديناميكية حسب وقت اليوم
- [ ] حدود تكيفية حسب أداء الخادم
- [ ] تسجيل مفصل لأداء البرق مع الحدود المختلفة
- [ ] توصيات تلقائية للحد الأمثل

---

**تم التطوير بواسطة**: فريق تطوير مولد كروت MikroTik  
**التاريخ**: 2025-07-16  
**الإصدار**: 1.0
