#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة التزامن التلقائي لحذف المستخدمين بالإيميل
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن التزامن التلقائي يعمل بشكل صحيح
"""

import re

def test_auto_sync_feature():
    """اختبار ميزة التزامن التلقائي"""
    print("🔍 اختبار ميزة التزامن التلقائي لحذف المستخدمين بالإيميل...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود التزامن التلقائي في دالة handle_delete_users_by_email_request
    sync_patterns = [
        'if self.system_type != \'hotspot\':',
        'تبديل النظام تلقائياً لحذف المستخدمين',
        'switch_success = self.switch_system_from_telegram(\'hotspot\'',
        'تزامن تلقائي مع البرنامج الرئيسي'
    ]
    
    for pattern in sync_patterns:
        if pattern not in content:
            print(f"❌ نمط التزامن التلقائي غير موجود: {pattern}")
            return False
        print(f"✅ نمط التزامن التلقائي موجود: {pattern}")
    
    return True

def test_interface_sync_functions():
    """اختبار دوال مزامنة الواجهة"""
    print("\n🔍 اختبار دوال مزامنة الواجهة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدوال المطلوبة
    required_functions = [
        'def sync_main_interface_with_email_search(',
        'def _update_users_display_with_filter(',
        'def _refresh_filtered_users(',
        'def _export_filtered_users('
    ]
    
    for func in required_functions:
        if func not in content:
            print(f"❌ الدالة غير موجودة: {func}")
            return False
        print(f"✅ الدالة موجودة: {func}")
    
    return True

def test_interface_display_features():
    """اختبار ميزات عرض الواجهة"""
    print("\n🔍 اختبار ميزات عرض الواجهة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من ميزات العرض
    display_features = [
        'tk.Toplevel(self.root)',
        'المستخدمين المطابقين للنمط',
        'ttk.Treeview(table_frame',
        'columns = (\'اسم المستخدم\', \'الإيميل\'',
        'tree.tag_configure(\'evenrow\'',
        'refresh_btn = tk.Button',
        'export_btn = tk.Button'
    ]
    
    for feature in display_features:
        if feature not in content:
            print(f"❌ ميزة العرض غير موجودة: {feature}")
            return False
        print(f"✅ ميزة العرض موجودة: {feature}")
    
    return True

def test_search_integration():
    """اختبار تكامل البحث مع التزامن"""
    print("\n🔍 اختبار تكامل البحث مع التزامن...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة search_and_confirm_delete_users
    func_match = re.search(r'def search_and_confirm_delete_users.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة search_and_confirm_delete_users")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التكامل
    integration_patterns = [
        'sync_success = self.sync_main_interface_with_email_search',
        'تم تزامن الواجهة الرئيسية مع نتائج البحث',
        'لم يتم تزامن الواجهة الرئيسية',
        'التزامن مع البرنامج الرئيسي',
        'تم عرضهم في البرنامج الرئيسي'
    ]
    
    for pattern in integration_patterns:
        if pattern not in func_code:
            print(f"❌ نمط التكامل غير موجود: {pattern}")
            return False
        print(f"✅ نمط التكامل موجود: {pattern}")
    
    return True

def test_system_display_name():
    """اختبار دالة عرض اسم النظام"""
    print("\n🔍 اختبار دالة عرض اسم النظام...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة get_system_display_name
    func_match = re.search(r'def get_system_display_name.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة get_system_display_name")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من المحتوى
    required_elements = [
        'if system_type == \'user_manager\':',
        'return "User Manager (يوزر منجر)"',
        'elif system_type == \'hotspot\':',
        'return "HotSpot (هوت اسبوت)"',
        'return "غير محدد"'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ العنصر المطلوب غير موجود: {element}")
            return False
        print(f"✅ العنصر المطلوب موجود: {element}")
    
    return True

def test_export_functionality():
    """اختبار وظيفة التصدير"""
    print("\n🔍 اختبار وظيفة التصدير...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة _export_filtered_users
    func_match = re.search(r'def _export_filtered_users.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة _export_filtered_users")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من ميزات التصدير
    export_features = [
        'import csv',
        'from datetime import datetime',
        'filename = f"filtered_users_',
        'with open(filename, \'w\', newline=\'\', encoding=\'utf-8-sig\')',
        'writer.writerow([\'اسم المستخدم\', \'الإيميل\'',
        'messagebox.showinfo("تصدير ناجح"'
    ]
    
    for feature in export_features:
        if feature not in func_code:
            print(f"❌ ميزة التصدير غير موجودة: {feature}")
            return False
        print(f"✅ ميزة التصدير موجودة: {feature}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار ميزة التزامن التلقائي لحذف المستخدمين بالإيميل")
    print("="*70)
    
    tests = [
        ("التزامن التلقائي", test_auto_sync_feature),
        ("دوال مزامنة الواجهة", test_interface_sync_functions),
        ("ميزات عرض الواجهة", test_interface_display_features),
        ("تكامل البحث مع التزامن", test_search_integration),
        ("دالة عرض اسم النظام", test_system_display_name),
        ("وظيفة التصدير", test_export_functionality)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تطبيق ميزة التزامن التلقائي بنجاح!")
        print("💡 الميزة جاهزة للاستخدام مع التزامن الكامل")
        
        print("\n🎯 الميزات المطبقة:")
        print("✅ التبديل التلقائي إلى نظام HotSpot")
        print("✅ عرض المستخدمين المطابقين في البرنامج الرئيسي")
        print("✅ تمييز وتصفية المستخدمين")
        print("✅ واجهة تفاعلية مع أزرار التحكم")
        print("✅ إمكانية التصدير والتحديث")
        print("✅ رسائل تأكيد التزامن في البوت")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
