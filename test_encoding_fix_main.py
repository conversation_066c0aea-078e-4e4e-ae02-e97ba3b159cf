#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة التشفير في الملف الأصلي
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن إصلاح التشفير تم تطبيقه على الملف الأصلي
"""

import re

def test_main_file_encoding_fix():
    """اختبار إصلاح التشفير في الملف الأصلي"""
    print("🔍 اختبار إصلاح التشفير في الملف الأصلي...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود دوال معالجة التشفير
    required_functions = [
        'def safe_decode_text(',
        'def clean_mikrotik_data('
    ]
    
    for func in required_functions:
        if func not in content:
            print(f"❌ الدالة غير موجودة: {func}")
            return False
        print(f"✅ الدالة موجودة: {func}")
    
    # التحقق من استخدام دوال التنظيف في جلب البيانات
    fetch_patterns = [
        'raw_users = resource.get()',
        'all_users = self.clean_mikrotik_data(raw_users)',
        'except Exception as fetch_error:'
    ]
    
    for pattern in fetch_patterns:
        if pattern not in content:
            print(f"❌ نمط جلب البيانات غير موجود: {pattern}")
            return False
        print(f"✅ نمط جلب البيانات موجود: {pattern}")
    
    # التحقق من استخدام safe_decode_text في أسماء المستخدمين
    sample_pattern = 'self.safe_decode_text(user.get(\'name\', \'غير محدد\'))'
    if sample_pattern not in content:
        print(f"❌ معالجة أسماء المستخدمين غير موجودة")
        return False
    print("✅ معالجة أسماء المستخدمين موجودة")
    
    # التحقق من استخدام clean_mikrotik_data في البحث
    search_patterns = [
        'clean_user = self.clean_mikrotik_data(user)',
        'str(comment).strip()',
        'str(email).lower()'
    ]
    
    for pattern in search_patterns:
        if pattern not in content:
            print(f"❌ نمط البحث غير موجود: {pattern}")
            return False
        print(f"✅ نمط البحث موجود: {pattern}")
    
    print("✅ جميع إصلاحات التشفير موجودة في الملف الأصلي")
    return True

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n🔍 اختبار معالجة الأخطاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    error_patterns = [
        'except UnicodeDecodeError:',
        'except Exception as user_error:',
        'except Exception as fetch_error:',
        'self.logger.warning(f"خطأ في معالجة مستخدم',
        'self.logger.warning(f"خطأ في معالجة التشفير'
    ]
    
    for pattern in error_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة الأخطاء غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة الأخطاء موجود: {pattern}")
    
    print("✅ معالجة الأخطاء شاملة")
    return True

def test_safe_decode_implementation():
    """اختبار تنفيذ دالة safe_decode_text"""
    print("\n🔍 اختبار تنفيذ دالة safe_decode_text...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة safe_decode_text
    func_match = re.search(r'def safe_decode_text.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة safe_decode_text")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة
    required_elements = [
        'isinstance(text, bytes)',
        'text.decode(\'utf-8\')',
        'text.decode(\'latin-1\', errors=\'ignore\')',
        'text.decode(\'cp1252\', errors=\'ignore\')',
        'UnicodeDecodeError'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ العنصر المطلوب غير موجود: {element}")
            return False
    
    print("✅ دالة safe_decode_text مكتملة")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح التشفير في الملف الأصلي")
    print("="*60)
    
    tests = [
        ("إصلاح التشفير الأساسي", test_main_file_encoding_fix),
        ("معالجة الأخطاء", test_error_handling),
        ("تنفيذ safe_decode_text", test_safe_decode_implementation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*60)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح مشكلة التشفير في الملف الأصلي!")
        print("💡 الميزة جاهزة للاستخدام بدون أخطاء تشفير")
    else:
        print("⚠️ بعض المكونات تحتاج إلى إصلاح.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
