

local ta [ /system clock get time ]; 

local date [ /system clock get date ]; 
:global dates (([pick [sys clock get date ] 4 6]) -1 )
:if ($dates < 10) do={:set dates ("0" . $dates);}
:global month (([pick [sys clock get date ] 0 3]) )



:global ss $"user"

:global macs [ip h ac get [ find user=$ss] mac-address] 

:if ([/ip h use find name=$macs] !="") do={
#/ip h use en [find name=$macs ]

:local mutm [/ip hotspot active get [find user=$ss] mac-address]
:local num [/ip hotspot active get [find user=$ss] user]
:local oldemaildm [ /ip hotspot user get $ss email ];

:local limitTotalm [/ip hotspot active get [find user=$ss] limit-bytes-total];
/ip hot host remove [find mac-address=$mutm]

:local commnm [/ip hotspot user get value-name=comment [find name="$mutm"]  ]; 
:if ( $commnm != "")  do={

#/ip hotspot user en [find name=$mutm ]
#/ip hotspot user rese [find name=$mutm ]
:delay 1s
/ip hot user remove $num 
:global tjm ( "$month/$dates/$[:pick $date 7 11]" )
:global tkm ( [:find $commnm "-" ])
:global tktm ( [:find $commnm "*" ])
:global tkttm ( [:find $commnm ">>" ])
:global dotmartkqom ([ :find $commnm "." ])
:global vtm ([ :pick $commnm 0 $tkm ]) 
:global votm ([ :pick $commnm $tkm $tktm ])
:global vottm ([ :pick $commnm $tktm $tkttm ])
:set votm  ""
:set vottm  ""

:global vom ([ :pick $commnm $tkttm  $dotmartkqom ])
:global voom ([ :pick $commnm $tktm  $tkttm ])
:set voom  ""
:global  prof [ /ip hotspot user get $mutm profile];
/ip hot user remove [find name=$mutm ]

/ip hot user add name=$mutm password=11 comment="$tjm - $ta  * $num $vom."  profile=$prof email=$oldemaildm  limit-bytes-total=$limitTotalm  } 

/ip hot host remove [find mac-address=$mutm] 



local textm $"mac-address";

local text1m "$user";
:global hosttm [/ip dhcp leas get [find mac-address="$textm"] host-name]
local text2m [ /system clock get date ]; 
local text3m [ /system clock get time ]; 
tool fetch url="https://api.telegram.org/bot5016548069:AAGRgspX1ZrZDk6CZETRqwLlYKBhaMRMSp4/sendMessage?chat_id=998535391&text=( ( Mac = $textm )                          ( Name = $text1m )                                                                 ( Date = $text2m )                                                                             (Time = $text3m )                                           (comment =$vom)                         Email=$oldemaildm))) &chat_id=998535391"
} else={



local ta [ /system clock get time ]; 

local date [ /system clock get date ]; 
:global dates (([pick [sys clock get date ] 4 6]) -1 )
:if ($dates < 10) do={:set dates ("0" . $dates);}
:global month (([pick [sys clock get date ] 0 3]) )

:global ss $"user"

:global macs [ip h ac get [ find user=$ss] mac-address] 
:local oldemaildm [ /ip hotspot user get $ss email ];

:local limitTotalm [/ip hotspot active get [find user=$ss] limit-bytes-total];

#/ip h use add name=$macs profile=default password=11

:global hostta [/ip dhcp leas get [find mac-address="$macs"] host-name]
/ip hotspot user add name=$macs password=11 profile="default"  email=$oldemaildm  limit-bytes-total=$limitTotalm comment="$month/$dates/$[:pick $date 7 11] - $ta * $ss ." 
:global commnz [/ip hotspot user get  [find name=$macs] comment ]; 
:global dotmarkqoz ([ :find $commnz "." ])
:global voz ([ :pick $commnz 0 $dotmarkqoz ])
/ip hot user set $macs comment="$voz  >>  $hostta  ."
:delay 1s
/ip hot user remove $ss 
/ip hot host remove [find mac-address=$macs]

 
local macs $"mac-address";

local text1z "$user";
#:global host [/ip dhcp leas get [find mac-address="$text"] host-name]
local text2z [ /system clock get date ]; 
local text3z [ /system clock get time ]; 
tool fetch url="https://api.telegram.org/bot5016548069:AAGRgspX1ZrZDk6CZETRqwLlYKBhaMRMSp4/sendMessage?chat_id=998535391&text=( ( Mac = $macs )                          ( Name = $text1z )                                                                 ( Date = $text2z )                                                                             (Time = $text3z )                                                         (host = $hostta )                                                             Email=$oldemaildz)) &chat_id=998535391"



}}}}

===============================

:global sq "comment"
:global ssq "chat_id"
:global sssq "token"

:local nameq [/ip hotspot user get [find comment=$sq] name]

/ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""

tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=(                ( Mac = $nameq )                          ( Name = $sq )                                  ))) &chat_id=$chat_id"
}}}}}


============================================

:global ss "comment"

:local nameq [ip hotspot user get value-name=name $ss ]
/ip hot user set [find $nameq] comment="" name=$ss profile=card passowrd=""
}}

tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=(                ( Mac = $nameq )                          ( Name = $ss )                                  ))) &chat_id=$chat_id"
    
}}}}}



===================
{


:global saq "comment"
:global ssaq "chat_id"
:global sssaq "token"

foreach btonq in=[ip hotspot user find where disabled name=$saq] do={
local commtnq [ip hotspot user get value-name=comment $btonq ]

/ip hot user reset $btonq
/ip hot user en $btonq
tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=( Reset                ( com = $commtnq )                          ( Name = $btonq )               ))) &chat_id=$chat_id"
    
}}}}}

==============================


{

:global sawq "comment"
:global ssawq "chat_id"
:global sssawq "token"

:global daten [ /system clock get date ]; 
:global datesun (([pick [sys clock get date ] 4 6]) )
:global datessn (([pick [sys clock get date ] 4 6]) -1 )
:if ($datessn < 10) do={:set datessn ("0" . $datessn);}
:global monthun (([pick [sys clock get date ] 0 3]) )
:local etwq [ /system clock get time ]; 


foreach btonwq in=[ip hotspot user find where disabled name="sawq"] do={
local commtnwq [ip hotspot user get value-name=comment $btonwq ]
:global tjmwq ( "$monthun/$datessn/$[:pick $daten 7 11]" )
:global tkmwq ( [:find $commtnwq "-" ])
:global tktmwq ( [:find $commtnwq "*" ])
:global tkttmwq ( [:find $commtnwq ">>" ])
:global dotmartkqomwq ([ :find $commtnwq "." ])
:global vtmwq ([ :pick $commtnwq 0 $tkmwq ]) 
:global votmwq ([ :pick $commtnwq $tkmwq $tktmwq ])
:global vottmwq ([ :pick $commtnwq $tktmwq $tkttmwq ])
:set votmwq  ""
:set vottmwq  ""

:global vomwq ([ :pick $commtnwq $tkttmwq  $dotmartkqomwq ])
:global voomwq ([ :pick $commtnwq $tktmwq  $tkttmwq ])
:set voomwq  ""
/ip hot user set $btonwq email=<EMAIL> comment="$tjmwq - $etwq  * $vottmwq $vomwq." 
/ip hot user reset $btonwq
/ip hot user en $btonwq
/ip hot h remove $btonwq
tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=( Reset                ( Mac = $commtnwq )                          ( Name = $btonwq )                                                                 ( Date = $datenwq )                                                                             (Time = $etwq )                                           (comment =$commtnwq)                         ))) &chat_id=$chat_id"
    
}}}}}

