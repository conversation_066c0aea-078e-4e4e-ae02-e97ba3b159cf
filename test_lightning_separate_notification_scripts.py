#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للاسكربتين المنفصلين لإرسال إشعارات Telegram في نظام Lightning User Manager
يختبر:
1. إنشاء اسكربت الإشعار قبل الإنشاء
2. إنشاء اسكربت الإشعار بعد الإنشاء
3. دمج الاسكربتين في دالة lightning_generate_large_batch_telegram
4. التأكد من أن الاسكربتين منفصلان عن اسكربتات إنشاء الكروت
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import sys
import json
from datetime import datetime

class TestLightningSeparateNotificationScripts(unittest.TestCase):
    """اختبار الاسكربتين المنفصلين لإشعارات Lightning User Manager"""
    
    def setUp(self):
        """إعداد الاختبار"""
        print("🧪 إعداد اختبار الاسكربتين المنفصلين...")
        
        # إنشاء مجلد مؤقت للاختبار
        self.test_dir = tempfile.mkdtemp()
        
        # محاكاة التطبيق
        self.mock_app = Mock()
        self.mock_app.system_type = 'user_manager'
        self.mock_app.logger = Mock()
        self.mock_app.telegram_bot_token = "test_bot_token_123"
        self.mock_app.telegram_chat_id = "998535391"
        
        # محاكاة بيانات الكروت المولدة
        self.mock_app.generated_credentials = [
            {
                'username': f'test_user_{i:03d}',
                'password': f'test_pass_{i:03d}',
                'profile': 'test_profile',
                'comment': 'test comment',
                'days': '30'
            } for i in range(1, 31)  # 30 كرت للاختبار
        ]
        
        # إضافة الدوال المطلوبة
        self.mock_app.create_lightning_pre_notification_script = self.mock_create_lightning_pre_notification_script
        self.mock_app.create_lightning_post_notification_script = self.mock_create_lightning_post_notification_script
        
        # متغيرات لتتبع الاختبار
        self.created_scripts = []
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
        print("🧹 تم تنظيف ملفات الاختبار")

    def mock_create_lightning_pre_notification_script(self, bot_token, chat_id, timestamp):
        """محاكاة إنشاء اسكربت الإشعار قبل الإنشاء"""
        script_name = f"lightning_pre_notification_{timestamp}"
        
        script_content = f'''# ===== اسكربت إشعار البرق قبل الإنشاء =====
# تم إنشاؤه تلقائياً بواسطة برنامج كروت وبوت
# التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

:put "📱 ===== بدء اسكربت إشعار البرق قبل الإنشاء =====";

# الحصول على عدد الكروت الحالي في User Manager
:local currentCardsCount 0;
:do {{
    :set currentCardsCount [len [/user-manager/user find]];
    :put ("📊 عدد الكروت الحالي في User Manager: " . $currentCardsCount);
}} on-error={{
    :put "❌ خطأ في الحصول على عدد الكروت الحالي";
    :set currentCardsCount "غير معروف";
}};

# الحصول على التاريخ والوقت الحالي
:local currentDate [/system clock get date];
:local currentTime [/system clock get time];

# إنشاء رسالة الإشعار
:local notificationMessage ("⚡ البرق - User Manager\\n\\n📊 إشعار قبل إنشاء الكروت:\\n\\n🔢 عدد الكروت الحالي: " . $currentCardsCount . " كرت\\n📅 التاريخ: " . $currentDate . "\\n⏰ الوقت: " . $currentTime . "\\n\\n⏳ جاري بدء عملية إنشاء الكروت الجديدة...\\n\\n🚀 البرق - أسرع طريقة لإنشاء الكروت في User Manager!");

# إرسال الإشعار عبر Telegram
:put "📤 إرسال إشعار قبل الإنشاء عبر Telegram...";
:do {{
    /tool fetch url="https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text=$notificationMessage" mode=https;
    :put "✅ تم إرسال إشعار قبل الإنشاء بنجاح";
}} on-error={{
    :put "❌ فشل في إرسال إشعار قبل الإنشاء - سيتم المتابعة";
}};

# تأخير قصير لضمان الإرسال
:delay 2s;

:put "🎯 تم إكمال اسكربت إشعار البرق قبل الإنشاء";
:put "📱 ===== انتهاء اسكربت إشعار البرق قبل الإنشاء =====";
'''
        
        self.created_scripts.append({
            'name': script_name,
            'content': script_content,
            'type': 'pre_notification'
        })
        
        self.mock_app.logger.info(f"تم إنشاء اسكربت الإشعار قبل الإنشاء: {script_name}")
        return script_name, script_content

    def mock_create_lightning_post_notification_script(self, bot_token, chat_id, timestamp):
        """محاكاة إنشاء اسكربت الإشعار بعد الإنشاء"""
        script_name = f"lightning_post_notification_{timestamp}"
        
        script_content = f'''# ===== اسكربت إشعار البرق بعد الإنشاء =====
# تم إنشاؤه تلقائياً بواسطة برنامج كروت وبوت
# التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

:put "📱 ===== بدء اسكربت إشعار البرق بعد الإنشاء =====";

# الحصول على عدد الكروت النهائي في User Manager
:local finalCardsCount 0;
:do {{
    :set finalCardsCount [len [/user-manager/user find]];
    :put ("📊 عدد الكروت النهائي في User Manager: " . $finalCardsCount);
}} on-error={{
    :put "❌ خطأ في الحصول على عدد الكروت النهائي";
    :set finalCardsCount "غير معروف";
}};

# الحصول على التاريخ والوقت الحالي
:local currentDate [/system clock get date];
:local currentTime [/system clock get time];

# إنشاء رسالة الإشعار
:local notificationMessage ("⚡ البرق - User Manager\\n\\n📊 إشعار بعد إكمال الإنشاء:\\n\\n🔢 عدد الكروت النهائي: " . $finalCardsCount . " كرت\\n📅 التاريخ: " . $currentDate . "\\n⏰ الوقت: " . $currentTime . "\\n\\n✅ تم إكمال إنشاء الكروت الجديدة بنجاح!\\n🗑️ جاري بدء عملية التنظيف وحذف الجدولة...\\n\\n🚀 البرق - تم إكمال العملية بنجاح!");

# إرسال الإشعار عبر Telegram
:put "📤 إرسال إشعار بعد الإنشاء عبر Telegram...";
:do {{
    /tool fetch url="https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text=$notificationMessage" mode=https;
    :put "✅ تم إرسال إشعار بعد الإنشاء بنجاح";
}} on-error={{
    :put "❌ فشل في إرسال إشعار بعد الإنشاء - سيتم المتابعة";
}};

# تأخير قصير لضمان الإرسال
:delay 2s;

:put "🎯 تم إكمال اسكربت إشعار البرق بعد الإنشاء";
:put "📱 ===== انتهاء اسكربت إشعار البرق بعد الإنشاء =====";
'''
        
        self.created_scripts.append({
            'name': script_name,
            'content': script_content,
            'type': 'post_notification'
        })
        
        self.mock_app.logger.info(f"تم إنشاء اسكربت الإشعار بعد الإنشاء: {script_name}")
        return script_name, script_content

    def test_create_pre_notification_script(self):
        """اختبار إنشاء اسكربت الإشعار قبل الإنشاء"""
        print("🧪 اختبار إنشاء اسكربت الإشعار قبل الإنشاء...")
        
        # إنشاء timestamp للاختبار
        timestamp = "20250724_143000"
        
        # تشغيل الدالة
        script_name, script_content = self.mock_app.create_lightning_pre_notification_script(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id,
            timestamp
        )
        
        # التحقق من النتائج
        self.assertIsNotNone(script_name, "يجب أن يتم إرجاع اسم الاسكربت")
        self.assertIsNotNone(script_content, "يجب أن يتم إرجاع محتوى الاسكربت")
        self.assertEqual(script_name, f"lightning_pre_notification_{timestamp}")
        
        # التحقق من محتوى الاسكربت
        self.assertIn("اسكربت إشعار البرق قبل الإنشاء", script_content)
        self.assertIn("currentCardsCount", script_content)
        self.assertIn("/user-manager/user find", script_content)
        self.assertIn("telegram.org/bot", script_content)
        self.assertIn(self.mock_app.telegram_bot_token, script_content)
        self.assertIn(self.mock_app.telegram_chat_id, script_content)
        self.assertIn("إشعار قبل إنشاء الكروت", script_content)
        
        print("✅ تم إنشاء اسكربت الإشعار قبل الإنشاء بنجاح")

    def test_create_post_notification_script(self):
        """اختبار إنشاء اسكربت الإشعار بعد الإنشاء"""
        print("🧪 اختبار إنشاء اسكربت الإشعار بعد الإنشاء...")
        
        # إنشاء timestamp للاختبار
        timestamp = "20250724_143000"
        
        # تشغيل الدالة
        script_name, script_content = self.mock_app.create_lightning_post_notification_script(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id,
            timestamp
        )
        
        # التحقق من النتائج
        self.assertIsNotNone(script_name, "يجب أن يتم إرجاع اسم الاسكربت")
        self.assertIsNotNone(script_content, "يجب أن يتم إرجاع محتوى الاسكربت")
        self.assertEqual(script_name, f"lightning_post_notification_{timestamp}")
        
        # التحقق من محتوى الاسكربت
        self.assertIn("اسكربت إشعار البرق بعد الإنشاء", script_content)
        self.assertIn("finalCardsCount", script_content)
        self.assertIn("/user-manager/user find", script_content)
        self.assertIn("telegram.org/bot", script_content)
        self.assertIn(self.mock_app.telegram_bot_token, script_content)
        self.assertIn(self.mock_app.telegram_chat_id, script_content)
        self.assertIn("إشعار بعد إكمال الإنشاء", script_content)
        
        print("✅ تم إنشاء اسكربت الإشعار بعد الإنشاء بنجاح")

    def test_scripts_are_separate_and_independent(self):
        """اختبار أن الاسكربتين منفصلان ومستقلان"""
        print("🧪 اختبار استقلالية الاسكربتين...")
        
        timestamp = "20250724_143000"
        
        # إنشاء الاسكربتين
        pre_name, pre_content = self.mock_app.create_lightning_pre_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        post_name, post_content = self.mock_app.create_lightning_post_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        # التحقق من أن الاسكربتين مختلفان
        self.assertNotEqual(pre_name, post_name, "يجب أن يكون للاسكربتين أسماء مختلفة")
        self.assertNotEqual(pre_content, post_content, "يجب أن يكون للاسكربتين محتوى مختلف")
        
        # التحقق من أن كل اسكربت مستقل
        self.assertIn("قبل الإنشاء", pre_content)
        self.assertNotIn("قبل الإنشاء", post_content)
        
        self.assertIn("بعد الإنشاء", post_content)
        self.assertNotIn("بعد الإنشاء", pre_content)
        
        # التحقق من أن كل اسكربت يحتوي على منطق منفصل
        self.assertIn("currentCardsCount", pre_content)
        self.assertNotIn("currentCardsCount", post_content)
        
        self.assertIn("finalCardsCount", post_content)
        self.assertNotIn("finalCardsCount", pre_content)
        
        print("✅ الاسكربتان منفصلان ومستقلان بشكل صحيح")

    def test_scripts_contain_proper_mikrotik_commands(self):
        """اختبار أن الاسكربتين يحتويان على أوامر MikroTik صحيحة"""
        print("🧪 اختبار أوامر MikroTik في الاسكربتين...")
        
        timestamp = "20250724_143000"
        
        # إنشاء الاسكربتين
        pre_name, pre_content = self.mock_app.create_lightning_pre_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        post_name, post_content = self.mock_app.create_lightning_post_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        # أوامر MikroTik المطلوبة في كلا الاسكربتين
        required_commands = [
            ":put",
            ":do",
            ":local",
            ":set",
            ":delay",
            "on-error",
            "/user-manager/user find",
            "/system clock get date",
            "/system clock get time",
            "/tool fetch url",
            "mode=https"
        ]
        
        for command in required_commands:
            self.assertIn(command, pre_content, f"الاسكربت الأول يجب أن يحتوي على: {command}")
            self.assertIn(command, post_content, f"الاسكربت الثاني يجب أن يحتوي على: {command}")
        
        # التحقق من معالجة الأخطاء
        self.assertIn("on-error", pre_content)
        self.assertIn("on-error", post_content)
        
        print("✅ الاسكربتان يحتويان على أوامر MikroTik صحيحة")

    def test_scripts_work_only_with_user_manager(self):
        """اختبار أن الاسكربتين يعملان فقط مع User Manager"""
        print("🧪 اختبار خصوصية User Manager...")
        
        # تغيير نوع النظام إلى HotSpot
        self.mock_app.system_type = 'hotspot'
        
        timestamp = "20250724_143000"
        
        # محاولة إنشاء الاسكربتين مع HotSpot
        pre_name, pre_content = self.mock_app.create_lightning_pre_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        # التحقق من أن الاسكربتين يحتويان على أوامر User Manager فقط
        self.assertIn("/user-manager/user", pre_content)
        self.assertNotIn("/ip/hotspot/user", pre_content)
        
        # إعادة تعيين النظام إلى User Manager
        self.mock_app.system_type = 'user_manager'
        
        print("✅ الاسكربتان خاصان بـ User Manager فقط")

    def test_error_handling_in_scripts(self):
        """اختبار معالجة الأخطاء في الاسكربتين"""
        print("🧪 اختبار معالجة الأخطاء...")
        
        timestamp = "20250724_143000"
        
        # إنشاء الاسكربتين
        pre_name, pre_content = self.mock_app.create_lightning_pre_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        post_name, post_content = self.mock_app.create_lightning_post_notification_script(
            self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, timestamp
        )
        
        # التحقق من وجود معالجة الأخطاء
        error_handling_patterns = [
            "on-error",
            "فشل في إرسال إشعار",
            "سيتم المتابعة",
            "خطأ في الحصول على عدد الكروت"
        ]
        
        for pattern in error_handling_patterns:
            self.assertIn(pattern, pre_content, f"الاسكربت الأول يجب أن يحتوي على معالجة خطأ: {pattern}")
            self.assertIn(pattern, post_content, f"الاسكربت الثاني يجب أن يحتوي على معالجة خطأ: {pattern}")
        
        print("✅ معالجة الأخطاء موجودة في كلا الاسكربتين")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار الاسكربتين المنفصلين لإشعارات Lightning User Manager")
    print("=" * 80)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestLightningSeparateNotificationScripts)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 80)
    
    # عرض النتائج
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت! الاسكربتان المنفصلان يعملان بشكل صحيح.")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
        return True
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
        
        # عرض تفاصيل الأخطاء
        for test, error in result.failures + result.errors:
            print(f"\n❌ فشل الاختبار: {test}")
            print(f"   الخطأ: {error}")
        
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
