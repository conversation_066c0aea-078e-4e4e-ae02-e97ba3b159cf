# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 13:53:13
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0134509168
:do {
    /tool user-manager user add customer="adm8n" username="0134509168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134509168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134509168";
};

# المستخدم 2: 0185731465
:do {
    /tool user-manager user add customer="adm8n" username="0185731465" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185731465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185731465";
};

# المستخدم 3: 0104637959
:do {
    /tool user-manager user add customer="adm8n" username="0104637959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104637959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104637959";
};

# المستخدم 4: 0155260610
:do {
    /tool user-manager user add customer="adm8n" username="0155260610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155260610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155260610";
};

# المستخدم 5: 0126465367
:do {
    /tool user-manager user add customer="adm8n" username="0126465367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126465367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126465367";
};

# المستخدم 6: 0151945829
:do {
    /tool user-manager user add customer="adm8n" username="0151945829" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151945829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151945829";
};

# المستخدم 7: 0181538506
:do {
    /tool user-manager user add customer="adm8n" username="0181538506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181538506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181538506";
};

# المستخدم 8: 0176030492
:do {
    /tool user-manager user add customer="adm8n" username="0176030492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176030492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176030492";
};

# المستخدم 9: 0152869919
:do {
    /tool user-manager user add customer="adm8n" username="0152869919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152869919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152869919";
};

# المستخدم 10: 0119283507
:do {
    /tool user-manager user add customer="adm8n" username="0119283507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119283507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119283507";
};

# المستخدم 11: 0146041974
:do {
    /tool user-manager user add customer="adm8n" username="0146041974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146041974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146041974";
};

# المستخدم 12: 0116683023
:do {
    /tool user-manager user add customer="adm8n" username="0116683023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116683023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116683023";
};

# المستخدم 13: 0181991039
:do {
    /tool user-manager user add customer="adm8n" username="0181991039" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181991039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181991039";
};

# المستخدم 14: 0128387395
:do {
    /tool user-manager user add customer="adm8n" username="0128387395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128387395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128387395";
};

# المستخدم 15: 0180731900
:do {
    /tool user-manager user add customer="adm8n" username="0180731900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180731900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180731900";
};

# المستخدم 16: 0152164234
:do {
    /tool user-manager user add customer="adm8n" username="0152164234" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152164234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152164234";
};

# المستخدم 17: 0101175890
:do {
    /tool user-manager user add customer="adm8n" username="0101175890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101175890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101175890";
};

# المستخدم 18: 0163973755
:do {
    /tool user-manager user add customer="adm8n" username="0163973755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163973755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163973755";
};

# المستخدم 19: 0149257847
:do {
    /tool user-manager user add customer="adm8n" username="0149257847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149257847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149257847";
};

# المستخدم 20: 0172037954
:do {
    /tool user-manager user add customer="adm8n" username="0172037954" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172037954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172037954";
};

# المستخدم 21: 0142731237
:do {
    /tool user-manager user add customer="adm8n" username="0142731237" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142731237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142731237";
};

# المستخدم 22: 0164659318
:do {
    /tool user-manager user add customer="adm8n" username="0164659318" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164659318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164659318";
};

# المستخدم 23: 0171009597
:do {
    /tool user-manager user add customer="adm8n" username="0171009597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171009597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171009597";
};

# المستخدم 24: 0145195367
:do {
    /tool user-manager user add customer="adm8n" username="0145195367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145195367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145195367";
};

# المستخدم 25: 0172806317
:do {
    /tool user-manager user add customer="adm8n" username="0172806317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172806317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172806317";
};

# المستخدم 26: 0195542860
:do {
    /tool user-manager user add customer="adm8n" username="0195542860" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195542860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195542860";
};

# المستخدم 27: 0191727119
:do {
    /tool user-manager user add customer="adm8n" username="0191727119" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191727119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191727119";
};

# المستخدم 28: 0192862394
:do {
    /tool user-manager user add customer="adm8n" username="0192862394" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192862394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192862394";
};

# المستخدم 29: 0170259594
:do {
    /tool user-manager user add customer="adm8n" username="0170259594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170259594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170259594";
};

# المستخدم 30: 0101841723
:do {
    /tool user-manager user add customer="adm8n" username="0101841723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101841723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101841723";
};

# المستخدم 31: 0152641682
:do {
    /tool user-manager user add customer="adm8n" username="0152641682" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152641682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152641682";
};

# المستخدم 32: 0164449450
:do {
    /tool user-manager user add customer="adm8n" username="0164449450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164449450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164449450";
};

# المستخدم 33: 0196178692
:do {
    /tool user-manager user add customer="adm8n" username="0196178692" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196178692";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196178692";
};

# المستخدم 34: 0110531132
:do {
    /tool user-manager user add customer="adm8n" username="0110531132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110531132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110531132";
};

# المستخدم 35: 0180391043
:do {
    /tool user-manager user add customer="adm8n" username="0180391043" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180391043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180391043";
};

# المستخدم 36: 0192538144
:do {
    /tool user-manager user add customer="adm8n" username="0192538144" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192538144";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192538144";
};

# المستخدم 37: 0198259009
:do {
    /tool user-manager user add customer="adm8n" username="0198259009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198259009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198259009";
};

# المستخدم 38: 0131842726
:do {
    /tool user-manager user add customer="adm8n" username="0131842726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131842726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131842726";
};

# المستخدم 39: 0178869529
:do {
    /tool user-manager user add customer="adm8n" username="0178869529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178869529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178869529";
};

# المستخدم 40: 0107900946
:do {
    /tool user-manager user add customer="adm8n" username="0107900946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107900946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107900946";
};

# المستخدم 41: 0125576725
:do {
    /tool user-manager user add customer="adm8n" username="0125576725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125576725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125576725";
};

# المستخدم 42: 0166641908
:do {
    /tool user-manager user add customer="adm8n" username="0166641908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166641908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166641908";
};

# المستخدم 43: 0167093431
:do {
    /tool user-manager user add customer="adm8n" username="0167093431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167093431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167093431";
};

# المستخدم 44: 0144007184
:do {
    /tool user-manager user add customer="adm8n" username="0144007184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144007184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144007184";
};

# المستخدم 45: 0166340118
:do {
    /tool user-manager user add customer="adm8n" username="0166340118" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166340118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166340118";
};

# المستخدم 46: 0147631940
:do {
    /tool user-manager user add customer="adm8n" username="0147631940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147631940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147631940";
};

# المستخدم 47: 0109252720
:do {
    /tool user-manager user add customer="adm8n" username="0109252720" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109252720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109252720";
};

# المستخدم 48: 0123264614
:do {
    /tool user-manager user add customer="adm8n" username="0123264614" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123264614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123264614";
};

# المستخدم 49: 0133789582
:do {
    /tool user-manager user add customer="adm8n" username="0133789582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133789582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133789582";
};

# المستخدم 50: 0199565950
:do {
    /tool user-manager user add customer="adm8n" username="0199565950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199565950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199565950";
};

# المستخدم 51: 0177807901
:do {
    /tool user-manager user add customer="adm8n" username="0177807901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177807901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177807901";
};

# المستخدم 52: 0111509488
:do {
    /tool user-manager user add customer="adm8n" username="0111509488" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111509488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111509488";
};

# المستخدم 53: 0169067202
:do {
    /tool user-manager user add customer="adm8n" username="0169067202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169067202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169067202";
};

# المستخدم 54: 0132643486
:do {
    /tool user-manager user add customer="adm8n" username="0132643486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132643486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132643486";
};

# المستخدم 55: 0145346416
:do {
    /tool user-manager user add customer="adm8n" username="0145346416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145346416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145346416";
};

# المستخدم 56: 0158945900
:do {
    /tool user-manager user add customer="adm8n" username="0158945900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158945900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158945900";
};

# المستخدم 57: 0104219464
:do {
    /tool user-manager user add customer="adm8n" username="0104219464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104219464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104219464";
};

# المستخدم 58: 0165369523
:do {
    /tool user-manager user add customer="adm8n" username="0165369523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165369523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165369523";
};

# المستخدم 59: 0198047965
:do {
    /tool user-manager user add customer="adm8n" username="0198047965" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198047965";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198047965";
};

# المستخدم 60: 0180996611
:do {
    /tool user-manager user add customer="adm8n" username="0180996611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180996611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180996611";
};

# المستخدم 61: 0189436308
:do {
    /tool user-manager user add customer="adm8n" username="0189436308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189436308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189436308";
};

# المستخدم 62: 0133350683
:do {
    /tool user-manager user add customer="adm8n" username="0133350683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133350683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133350683";
};

# المستخدم 63: 0155868176
:do {
    /tool user-manager user add customer="adm8n" username="0155868176" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155868176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155868176";
};

# المستخدم 64: 0136346013
:do {
    /tool user-manager user add customer="adm8n" username="0136346013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136346013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136346013";
};

# المستخدم 65: 0166545857
:do {
    /tool user-manager user add customer="adm8n" username="0166545857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166545857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166545857";
};

# المستخدم 66: 0119683393
:do {
    /tool user-manager user add customer="adm8n" username="0119683393" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119683393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119683393";
};

# المستخدم 67: 0136516497
:do {
    /tool user-manager user add customer="adm8n" username="0136516497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136516497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136516497";
};

# المستخدم 68: 0131620940
:do {
    /tool user-manager user add customer="adm8n" username="0131620940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131620940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131620940";
};

# المستخدم 69: 0105824796
:do {
    /tool user-manager user add customer="adm8n" username="0105824796" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105824796";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105824796";
};

# المستخدم 70: 0197715954
:do {
    /tool user-manager user add customer="adm8n" username="0197715954" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197715954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197715954";
};

# المستخدم 71: 0174897322
:do {
    /tool user-manager user add customer="adm8n" username="0174897322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174897322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174897322";
};

# المستخدم 72: 0170711909
:do {
    /tool user-manager user add customer="adm8n" username="0170711909" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170711909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170711909";
};

# المستخدم 73: 0144788052
:do {
    /tool user-manager user add customer="adm8n" username="0144788052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144788052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144788052";
};

# المستخدم 74: 0175738403
:do {
    /tool user-manager user add customer="adm8n" username="0175738403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175738403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175738403";
};

# المستخدم 75: 0118961013
:do {
    /tool user-manager user add customer="adm8n" username="0118961013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118961013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118961013";
};

# المستخدم 76: 0159452522
:do {
    /tool user-manager user add customer="adm8n" username="0159452522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159452522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159452522";
};

# المستخدم 77: 0111292579
:do {
    /tool user-manager user add customer="adm8n" username="0111292579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111292579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111292579";
};

# المستخدم 78: 0115736943
:do {
    /tool user-manager user add customer="adm8n" username="0115736943" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115736943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115736943";
};

# المستخدم 79: 0174485213
:do {
    /tool user-manager user add customer="adm8n" username="0174485213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174485213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174485213";
};

# المستخدم 80: 0135979842
:do {
    /tool user-manager user add customer="adm8n" username="0135979842" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135979842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135979842";
};

# المستخدم 81: 0157366720
:do {
    /tool user-manager user add customer="adm8n" username="0157366720" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157366720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157366720";
};

# المستخدم 82: 0140920664
:do {
    /tool user-manager user add customer="adm8n" username="0140920664" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140920664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140920664";
};

# المستخدم 83: 0115382210
:do {
    /tool user-manager user add customer="adm8n" username="0115382210" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115382210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115382210";
};

# المستخدم 84: 0169893686
:do {
    /tool user-manager user add customer="adm8n" username="0169893686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169893686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169893686";
};

# المستخدم 85: 0185394243
:do {
    /tool user-manager user add customer="adm8n" username="0185394243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185394243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185394243";
};

# المستخدم 86: 0191634748
:do {
    /tool user-manager user add customer="adm8n" username="0191634748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191634748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191634748";
};

# المستخدم 87: 0173199760
:do {
    /tool user-manager user add customer="adm8n" username="0173199760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173199760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173199760";
};

# المستخدم 88: 0126643829
:do {
    /tool user-manager user add customer="adm8n" username="0126643829" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126643829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126643829";
};

# المستخدم 89: 0193030808
:do {
    /tool user-manager user add customer="adm8n" username="0193030808" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193030808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193030808";
};

# المستخدم 90: 0173423691
:do {
    /tool user-manager user add customer="adm8n" username="0173423691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173423691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173423691";
};

# المستخدم 91: 0116455893
:do {
    /tool user-manager user add customer="adm8n" username="0116455893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116455893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116455893";
};

# المستخدم 92: 0154773835
:do {
    /tool user-manager user add customer="adm8n" username="0154773835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154773835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154773835";
};

# المستخدم 93: 0115895096
:do {
    /tool user-manager user add customer="adm8n" username="0115895096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115895096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115895096";
};

# المستخدم 94: 0156997408
:do {
    /tool user-manager user add customer="adm8n" username="0156997408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156997408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156997408";
};

# المستخدم 95: 0144312353
:do {
    /tool user-manager user add customer="adm8n" username="0144312353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144312353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144312353";
};

# المستخدم 96: 0185854328
:do {
    /tool user-manager user add customer="adm8n" username="0185854328" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185854328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185854328";
};

# المستخدم 97: 0186391175
:do {
    /tool user-manager user add customer="adm8n" username="0186391175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186391175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186391175";
};

# المستخدم 98: 0147657423
:do {
    /tool user-manager user add customer="adm8n" username="0147657423" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147657423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147657423";
};

# المستخدم 99: 0158425907
:do {
    /tool user-manager user add customer="adm8n" username="0158425907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158425907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158425907";
};

# المستخدم 100: 0154440366
:do {
    /tool user-manager user add customer="adm8n" username="0154440366" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154440366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154440366";
};

# المستخدم 101: 0106279131
:do {
    /tool user-manager user add customer="adm8n" username="0106279131" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106279131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106279131";
};

# المستخدم 102: 0167771034
:do {
    /tool user-manager user add customer="adm8n" username="0167771034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167771034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167771034";
};

# المستخدم 103: 0192824376
:do {
    /tool user-manager user add customer="adm8n" username="0192824376" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192824376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192824376";
};

# المستخدم 104: 0175845259
:do {
    /tool user-manager user add customer="adm8n" username="0175845259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175845259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175845259";
};

# المستخدم 105: 0115181035
:do {
    /tool user-manager user add customer="adm8n" username="0115181035" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115181035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115181035";
};

# المستخدم 106: 0104847361
:do {
    /tool user-manager user add customer="adm8n" username="0104847361" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104847361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104847361";
};

# المستخدم 107: 0129275640
:do {
    /tool user-manager user add customer="adm8n" username="0129275640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129275640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129275640";
};

# المستخدم 108: 0179279271
:do {
    /tool user-manager user add customer="adm8n" username="0179279271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179279271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179279271";
};

# المستخدم 109: 0115350408
:do {
    /tool user-manager user add customer="adm8n" username="0115350408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115350408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115350408";
};

# المستخدم 110: 0149190432
:do {
    /tool user-manager user add customer="adm8n" username="0149190432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149190432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149190432";
};

# المستخدم 111: 0131414841
:do {
    /tool user-manager user add customer="adm8n" username="0131414841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131414841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131414841";
};

# المستخدم 112: 0157845213
:do {
    /tool user-manager user add customer="adm8n" username="0157845213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157845213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157845213";
};

# المستخدم 113: 0143344648
:do {
    /tool user-manager user add customer="adm8n" username="0143344648" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143344648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143344648";
};

# المستخدم 114: 0116821637
:do {
    /tool user-manager user add customer="adm8n" username="0116821637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116821637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116821637";
};

# المستخدم 115: 0142744454
:do {
    /tool user-manager user add customer="adm8n" username="0142744454" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142744454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142744454";
};

# المستخدم 116: 0140706521
:do {
    /tool user-manager user add customer="adm8n" username="0140706521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140706521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140706521";
};

# المستخدم 117: 0183405040
:do {
    /tool user-manager user add customer="adm8n" username="0183405040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183405040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183405040";
};

# المستخدم 118: 0155587838
:do {
    /tool user-manager user add customer="adm8n" username="0155587838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155587838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155587838";
};

# المستخدم 119: 0130025182
:do {
    /tool user-manager user add customer="adm8n" username="0130025182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130025182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130025182";
};

# المستخدم 120: 0182688337
:do {
    /tool user-manager user add customer="adm8n" username="0182688337" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182688337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182688337";
};

# المستخدم 121: 0171989423
:do {
    /tool user-manager user add customer="adm8n" username="0171989423" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171989423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171989423";
};

# المستخدم 122: 0185227757
:do {
    /tool user-manager user add customer="adm8n" username="0185227757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185227757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185227757";
};

# المستخدم 123: 0191463722
:do {
    /tool user-manager user add customer="adm8n" username="0191463722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191463722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191463722";
};

# المستخدم 124: 0145238708
:do {
    /tool user-manager user add customer="adm8n" username="0145238708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145238708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145238708";
};

# المستخدم 125: 0186541888
:do {
    /tool user-manager user add customer="adm8n" username="0186541888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186541888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186541888";
};

# المستخدم 126: 0122572325
:do {
    /tool user-manager user add customer="adm8n" username="0122572325" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122572325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122572325";
};

# المستخدم 127: 0157902558
:do {
    /tool user-manager user add customer="adm8n" username="0157902558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157902558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157902558";
};

# المستخدم 128: 0182317971
:do {
    /tool user-manager user add customer="adm8n" username="0182317971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182317971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182317971";
};

# المستخدم 129: 0115757239
:do {
    /tool user-manager user add customer="adm8n" username="0115757239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115757239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115757239";
};

# المستخدم 130: 0172044961
:do {
    /tool user-manager user add customer="adm8n" username="0172044961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172044961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172044961";
};

# المستخدم 131: 0172325067
:do {
    /tool user-manager user add customer="adm8n" username="0172325067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172325067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172325067";
};

# المستخدم 132: 0184777194
:do {
    /tool user-manager user add customer="adm8n" username="0184777194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184777194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184777194";
};

# المستخدم 133: 0134878103
:do {
    /tool user-manager user add customer="adm8n" username="0134878103" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134878103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134878103";
};

# المستخدم 134: 0189783543
:do {
    /tool user-manager user add customer="adm8n" username="0189783543" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189783543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189783543";
};

# المستخدم 135: 0114059792
:do {
    /tool user-manager user add customer="adm8n" username="0114059792" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114059792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114059792";
};

# المستخدم 136: 0107221923
:do {
    /tool user-manager user add customer="adm8n" username="0107221923" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107221923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107221923";
};

# المستخدم 137: 0106569047
:do {
    /tool user-manager user add customer="adm8n" username="0106569047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106569047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106569047";
};

# المستخدم 138: 0140437213
:do {
    /tool user-manager user add customer="adm8n" username="0140437213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140437213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140437213";
};

# المستخدم 139: 0102752975
:do {
    /tool user-manager user add customer="adm8n" username="0102752975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102752975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102752975";
};

# المستخدم 140: 0171449553
:do {
    /tool user-manager user add customer="adm8n" username="0171449553" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171449553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171449553";
};

# المستخدم 141: 0100872569
:do {
    /tool user-manager user add customer="adm8n" username="0100872569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100872569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100872569";
};

# المستخدم 142: 0173786427
:do {
    /tool user-manager user add customer="adm8n" username="0173786427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173786427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173786427";
};

# المستخدم 143: 0106507563
:do {
    /tool user-manager user add customer="adm8n" username="0106507563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106507563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106507563";
};

# المستخدم 144: 0127038420
:do {
    /tool user-manager user add customer="adm8n" username="0127038420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127038420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127038420";
};

# المستخدم 145: 0150080989
:do {
    /tool user-manager user add customer="adm8n" username="0150080989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150080989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150080989";
};

# المستخدم 146: 0128089806
:do {
    /tool user-manager user add customer="adm8n" username="0128089806" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128089806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128089806";
};

# المستخدم 147: 0128218467
:do {
    /tool user-manager user add customer="adm8n" username="0128218467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128218467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128218467";
};

# المستخدم 148: 0101860822
:do {
    /tool user-manager user add customer="adm8n" username="0101860822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101860822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101860822";
};

# المستخدم 149: 0183025280
:do {
    /tool user-manager user add customer="adm8n" username="0183025280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183025280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183025280";
};

# المستخدم 150: 0145117233
:do {
    /tool user-manager user add customer="adm8n" username="0145117233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145117233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145117233";
};

# المستخدم 151: 0155200164
:do {
    /tool user-manager user add customer="adm8n" username="0155200164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155200164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155200164";
};

# المستخدم 152: 0114492772
:do {
    /tool user-manager user add customer="adm8n" username="0114492772" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114492772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114492772";
};

# المستخدم 153: 0176363478
:do {
    /tool user-manager user add customer="adm8n" username="0176363478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176363478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176363478";
};

# المستخدم 154: 0199148382
:do {
    /tool user-manager user add customer="adm8n" username="0199148382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199148382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199148382";
};

# المستخدم 155: 0124899783
:do {
    /tool user-manager user add customer="adm8n" username="0124899783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124899783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124899783";
};

# المستخدم 156: 0142687309
:do {
    /tool user-manager user add customer="adm8n" username="0142687309" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142687309";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142687309";
};

# المستخدم 157: 0141978293
:do {
    /tool user-manager user add customer="adm8n" username="0141978293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141978293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141978293";
};

# المستخدم 158: 0164012356
:do {
    /tool user-manager user add customer="adm8n" username="0164012356" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164012356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164012356";
};

# المستخدم 159: 0119923928
:do {
    /tool user-manager user add customer="adm8n" username="0119923928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119923928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119923928";
};

# المستخدم 160: 0189068685
:do {
    /tool user-manager user add customer="adm8n" username="0189068685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189068685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189068685";
};

# المستخدم 161: 0130053168
:do {
    /tool user-manager user add customer="adm8n" username="0130053168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130053168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130053168";
};

# المستخدم 162: 0193140403
:do {
    /tool user-manager user add customer="adm8n" username="0193140403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193140403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193140403";
};

# المستخدم 163: 0151135644
:do {
    /tool user-manager user add customer="adm8n" username="0151135644" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151135644";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151135644";
};

# المستخدم 164: 0155182770
:do {
    /tool user-manager user add customer="adm8n" username="0155182770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155182770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155182770";
};

# المستخدم 165: 0186700198
:do {
    /tool user-manager user add customer="adm8n" username="0186700198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186700198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186700198";
};

# المستخدم 166: 0167824473
:do {
    /tool user-manager user add customer="adm8n" username="0167824473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167824473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167824473";
};

# المستخدم 167: 0107393310
:do {
    /tool user-manager user add customer="adm8n" username="0107393310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107393310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107393310";
};

# المستخدم 168: 0119635579
:do {
    /tool user-manager user add customer="adm8n" username="0119635579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119635579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119635579";
};

# المستخدم 169: 0134106872
:do {
    /tool user-manager user add customer="adm8n" username="0134106872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134106872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134106872";
};

# المستخدم 170: 0166362138
:do {
    /tool user-manager user add customer="adm8n" username="0166362138" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166362138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166362138";
};

# المستخدم 171: 0184741570
:do {
    /tool user-manager user add customer="adm8n" username="0184741570" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184741570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184741570";
};

# المستخدم 172: 0175588396
:do {
    /tool user-manager user add customer="adm8n" username="0175588396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175588396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175588396";
};

# المستخدم 173: 0148248649
:do {
    /tool user-manager user add customer="adm8n" username="0148248649" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148248649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148248649";
};

# المستخدم 174: 0155329310
:do {
    /tool user-manager user add customer="adm8n" username="0155329310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155329310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155329310";
};

# المستخدم 175: 0198193867
:do {
    /tool user-manager user add customer="adm8n" username="0198193867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198193867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198193867";
};

# المستخدم 176: 0168691477
:do {
    /tool user-manager user add customer="adm8n" username="0168691477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168691477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168691477";
};

# المستخدم 177: 0160853978
:do {
    /tool user-manager user add customer="adm8n" username="0160853978" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160853978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160853978";
};

# المستخدم 178: 0166569143
:do {
    /tool user-manager user add customer="adm8n" username="0166569143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166569143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166569143";
};

# المستخدم 179: 0102547824
:do {
    /tool user-manager user add customer="adm8n" username="0102547824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102547824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102547824";
};

# المستخدم 180: 0195433906
:do {
    /tool user-manager user add customer="adm8n" username="0195433906" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195433906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195433906";
};

# المستخدم 181: 0137838413
:do {
    /tool user-manager user add customer="adm8n" username="0137838413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137838413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137838413";
};

# المستخدم 182: 0151387374
:do {
    /tool user-manager user add customer="adm8n" username="0151387374" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151387374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151387374";
};

# المستخدم 183: 0139774254
:do {
    /tool user-manager user add customer="adm8n" username="0139774254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139774254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139774254";
};

# المستخدم 184: 0197699100
:do {
    /tool user-manager user add customer="adm8n" username="0197699100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197699100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197699100";
};

# المستخدم 185: 0153893970
:do {
    /tool user-manager user add customer="adm8n" username="0153893970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153893970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153893970";
};

# المستخدم 186: 0126911156
:do {
    /tool user-manager user add customer="adm8n" username="0126911156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126911156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126911156";
};

# المستخدم 187: 0114854595
:do {
    /tool user-manager user add customer="adm8n" username="0114854595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114854595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114854595";
};

# المستخدم 188: 0101149444
:do {
    /tool user-manager user add customer="adm8n" username="0101149444" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101149444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101149444";
};

# المستخدم 189: 0123397620
:do {
    /tool user-manager user add customer="adm8n" username="0123397620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123397620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123397620";
};

# المستخدم 190: 0179453943
:do {
    /tool user-manager user add customer="adm8n" username="0179453943" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179453943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179453943";
};

# المستخدم 191: 0188269093
:do {
    /tool user-manager user add customer="adm8n" username="0188269093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188269093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188269093";
};

# المستخدم 192: 0110333073
:do {
    /tool user-manager user add customer="adm8n" username="0110333073" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110333073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110333073";
};

# المستخدم 193: 0164555282
:do {
    /tool user-manager user add customer="adm8n" username="0164555282" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164555282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164555282";
};

# المستخدم 194: 0102700933
:do {
    /tool user-manager user add customer="adm8n" username="0102700933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102700933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102700933";
};

# المستخدم 195: 0110462359
:do {
    /tool user-manager user add customer="adm8n" username="0110462359" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110462359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110462359";
};

# المستخدم 196: 0157543802
:do {
    /tool user-manager user add customer="adm8n" username="0157543802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157543802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157543802";
};

# المستخدم 197: 0174750111
:do {
    /tool user-manager user add customer="adm8n" username="0174750111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174750111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174750111";
};

# المستخدم 198: 0124443853
:do {
    /tool user-manager user add customer="adm8n" username="0124443853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124443853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124443853";
};

# المستخدم 199: 0187841169
:do {
    /tool user-manager user add customer="adm8n" username="0187841169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187841169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187841169";
};

# المستخدم 200: 0108160822
:do {
    /tool user-manager user add customer="adm8n" username="0108160822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108160822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108160822";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
