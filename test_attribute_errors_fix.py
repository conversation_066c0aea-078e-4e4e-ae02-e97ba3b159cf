#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح أخطاء AttributeError
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن جميع أخطاء AttributeError تم إصلاحها
"""

import re

def test_connection_status_label_fixes():
    """اختبار إصلاح أخطاء connection_status_label"""
    print("🔍 اختبار إصلاح أخطاء connection_status_label...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن استخدام connection_status_label بدون hasattr
    unsafe_patterns = [
        r'self\.connection_status_label\.configure\(',
        r'self\.telegram_connection_status_label\.configure\('
    ]
    
    unsafe_count = 0
    for pattern in unsafe_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            # التحقق من أن الاستخدام محاط بـ hasattr
            context_pattern = f"if hasattr.*{re.escape(match)}"
            if not re.search(context_pattern, content, re.DOTALL):
                unsafe_count += 1
    
    if unsafe_count > 0:
        print(f"❌ وجد {unsafe_count} استخدام غير آمن لـ connection_status_label")
        return False
    
    # البحث عن الاستخدامات الآمنة
    safe_patterns = [
        r'if hasattr\(self, \'connection_status_label\'\):',
        r'if hasattr\(self, \'telegram_connection_status_label\'\):'
    ]
    
    safe_count = 0
    for pattern in safe_patterns:
        matches = re.findall(pattern, content)
        safe_count += len(matches)
    
    print(f"✅ تم العثور على {safe_count} استخدام آمن مع hasattr")
    return True

def test_time_import_fixes():
    """اختبار إصلاح مشاكل استيراد time"""
    print("\n🔍 اختبار إصلاح مشاكل استيراد time...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن استخدام time.time() في دالة handle_delete_users_by_email_request
    function_match = re.search(r'def handle_delete_users_by_email_request.*?(?=def|\Z)', content, re.DOTALL)
    if not function_match:
        print("❌ لم يتم العثور على دالة handle_delete_users_by_email_request")
        return False
    
    function_code = function_match.group(0)
    
    # التحقق من وجود import time قبل استخدام time.time()
    if 'time.time()' in function_code:
        if 'import time' not in function_code:
            print("❌ استخدام time.time() بدون import time")
            return False
        else:
            print("✅ تم العثور على import time قبل استخدام time.time()")
    
    # التحقق من استخدام time.strftime بشكل صحيح
    strftime_pattern = r'time\.strftime\([^)]+\)'
    strftime_matches = re.findall(strftime_pattern, content)
    
    for match in strftime_matches:
        if 'time.localtime()' not in match and '%Y-%m-%d %H:%M:%S' in match:
            print(f"❌ استخدام غير صحيح لـ time.strftime: {match}")
            return False
    
    print("✅ جميع استخدامات time تم إصلاحها بشكل صحيح")
    return True

def test_function_completeness():
    """اختبار اكتمال الدوال الجديدة"""
    print("\n🔍 اختبار اكتمال الدوال الجديدة...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        'handle_delete_users_by_email_request',
        'handle_email_pattern_input',
        'search_and_confirm_delete_users',
        'handle_confirm_delete_users_by_email',
        'handle_cancel_delete_users_by_email',
        'execute_delete_users_by_email'
    ]
    
    for func_name in required_functions:
        # البحث عن تعريف الدالة
        func_pattern = f'def {func_name}\\('
        if not re.search(func_pattern, content):
            print(f"❌ الدالة غير موجودة: {func_name}")
            return False
        
        # البحث عن محتوى الدالة
        func_match = re.search(f'def {func_name}.*?(?=def|\\Z)', content, re.DOTALL)
        if not func_match:
            print(f"❌ لم يتم العثور على محتوى الدالة: {func_name}")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من وجود معالجة الأخطاء
        if 'except Exception as e:' not in func_code:
            print(f"❌ الدالة {func_name} لا تحتوي على معالجة أخطاء")
            return False
        
        # التحقق من وجود تسجيل العمليات
        if 'self.logger.' not in func_code:
            print(f"❌ الدالة {func_name} لا تحتوي على تسجيل العمليات")
            return False
    
    print("✅ جميع الدوال مكتملة ومعرفة بشكل صحيح")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح أخطاء AttributeError")
    print("="*60)
    
    tests = [
        ("إصلاح connection_status_label", test_connection_status_label_fixes),
        ("إصلاح مشاكل time", test_time_import_fixes),
        ("اكتمال الدوال", test_function_completeness)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*60)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح جميع الأخطاء بنجاح!")
        print("💡 الميزة جاهزة للاستخدام الآن")
    else:
        print("⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
