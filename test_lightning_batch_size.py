#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حفظ وتحميل حقل Lightning Batch Size في قوالب اليوزر منجير
"""

import unittest
import json
import os
import tempfile
import shutil
from unittest.mock import Mock, patch

class TestLightningBatchSize(unittest.TestCase):
    """اختبارات حقل Lightning Batch Size في القوالب"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء مجلد مؤقت للاختبار
        self.test_dir = tempfile.mkdtemp()
        self.test_config_dir = os.path.join(self.test_dir, 'config')
        os.makedirs(self.test_config_dir, exist_ok=True)
        
        # إنشاء قوالب تجريبية مع حد تقسيم البرق
        self.create_test_templates_with_lightning_batch()
        
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def create_test_templates_with_lightning_batch(self):
        """إنشاء قوالب تجريبية مع حد تقسيم البرق مختلف"""
        
        # قوالب User Manager مع حدود مختلفة للبرق
        um_templates = {
            "قالب_برق_صغير": {
                "system_type": "user_manager",
                "profile": "small_batch",
                "customer": "admin",
                "location": "مكتب صغير",
                "division": "الإدارة",
                "lightning_batch_size": "50",
                "comment": "قالب للمكاتب الصغيرة - حد برق 50"
            },
            "قالب_برق_متوسط": {
                "system_type": "user_manager", 
                "profile": "medium_batch",
                "customer": "admin",
                "location": "مدرسة متوسطة",
                "division": "التعليم",
                "lightning_batch_size": "100",
                "comment": "قالب للمدارس المتوسطة - حد برق 100"
            },
            "قالب_برق_كبير": {
                "system_type": "user_manager",
                "profile": "large_batch",
                "customer": "admin", 
                "location": "جامعة كبيرة",
                "division": "التعليم العالي",
                "lightning_batch_size": "200",
                "comment": "قالب للجامعات الكبيرة - حد برق 200"
            },
            "قالب_برق_ضخم": {
                "system_type": "user_manager",
                "profile": "huge_batch",
                "customer": "admin",
                "location": "مؤسسة ضخمة", 
                "division": "المؤسسات",
                "lightning_batch_size": "500",
                "comment": "قالب للمؤسسات الضخمة - حد برق 500"
            },
            "قالب_بدون_برق": {
                "system_type": "user_manager",
                "profile": "no_lightning",
                "customer": "admin",
                "location": "مكتب بسيط",
                "division": "أخرى",
                "comment": "قالب بدون حد برق محدد"
                # لا يحتوي على lightning_batch_size
            }
        }
        
        # قوالب Hotspot مع حدود مختلفة للبرق
        hs_templates = {
            "قالب_كافيه_برق": {
                "system_type": "hotspot",
                "profile": "cafe_lightning",
                "server": "all",
                "location": "كافيه سريع",
                "division": "المطاعم والكافيهات",
                "lightning_batch_size": "75",
                "comment": "قالب كافيه مع برق سريع - حد 75"
            },
            "قالب_فندق_برق": {
                "system_type": "hotspot",
                "profile": "hotel_lightning", 
                "server": "all",
                "location": "فندق كبير",
                "division": "الفنادق",
                "lightning_batch_size": "150",
                "comment": "قالب فندق مع برق متوسط - حد 150"
            }
        }
        
        # حفظ القوالب في ملفات
        with open(f"{self.test_config_dir}/mikrotik_user_manager_templates.json", 'w', encoding='utf-8') as f:
            json.dump(um_templates, f, ensure_ascii=False, indent=2)
            
        with open(f"{self.test_config_dir}/mikrotik_hotspot_templates.json", 'w', encoding='utf-8') as f:
            json.dump(hs_templates, f, ensure_ascii=False, indent=2)
    
    def test_lightning_batch_size_loading(self):
        """اختبار تحميل حد تقسيم البرق من القوالب"""
        print("🧪 اختبار تحميل حد تقسيم البرق من القوالب...")
        
        # تحميل قوالب User Manager
        with open(f"{self.test_config_dir}/mikrotik_user_manager_templates.json", 'r', encoding='utf-8') as f:
            um_templates = json.load(f)
        
        # التحقق من وجود حقل lightning_batch_size في القوالب التي تحتويه
        templates_with_lightning = ["قالب_برق_صغير", "قالب_برق_متوسط", "قالب_برق_كبير", "قالب_برق_ضخم"]
        
        for template_name in templates_with_lightning:
            self.assertIn(template_name, um_templates, f"القالب مفقود: {template_name}")
            template_data = um_templates[template_name]
            self.assertIn('lightning_batch_size', template_data, f"حقل lightning_batch_size مفقود في القالب: {template_name}")
            self.assertIsInstance(template_data['lightning_batch_size'], str, f"حقل lightning_batch_size يجب أن يكون نص في القالب: {template_name}")
        
        # التحقق من القيم المحددة
        expected_values = {
            "قالب_برق_صغير": "50",
            "قالب_برق_متوسط": "100", 
            "قالب_برق_كبير": "200",
            "قالب_برق_ضخم": "500"
        }
        
        for template_name, expected_value in expected_values.items():
            actual_value = um_templates[template_name]['lightning_batch_size']
            self.assertEqual(actual_value, expected_value, f"قيمة lightning_batch_size غير صحيحة في {template_name}: متوقع {expected_value}, فعلي {actual_value}")
        
        # التحقق من القالب الذي لا يحتوي على lightning_batch_size
        template_without_lightning = um_templates["قالب_بدون_برق"]
        self.assertNotIn('lightning_batch_size', template_without_lightning, "القالب يجب ألا يحتوي على lightning_batch_size")
        
        print("✅ تم تحميل حد تقسيم البرق بنجاح من جميع القوالب")
    
    def test_lightning_batch_size_validation(self):
        """اختبار التحقق من صحة قيم حد تقسيم البرق"""
        print("🧪 اختبار التحقق من صحة قيم حد تقسيم البرق...")
        
        # محاكاة دالة التحقق من صحة حد تقسيم البرق
        def validate_lightning_batch_size(value):
            """التحقق من صحة قيمة حد تقسيم البرق"""
            if not value:
                return False, "القيمة فارغة"
            
            try:
                int_value = int(value)
                if int_value <= 0:
                    return False, "يجب أن تكون القيمة رقم موجب"
                if int_value > 1000:
                    return False, "القيمة كبيرة جداً (أكثر من 1000)"
                return True, "القيمة صحيحة"
            except ValueError:
                return False, "القيمة ليست رقم صحيح"
        
        # اختبار قيم صحيحة
        valid_values = ["1", "50", "100", "200", "500", "1000"]
        for value in valid_values:
            is_valid, message = validate_lightning_batch_size(value)
            self.assertTrue(is_valid, f"القيمة '{value}' يجب أن تكون صحيحة: {message}")
        
        # اختبار قيم غير صحيحة
        invalid_values = ["", "0", "-1", "-50", "abc", "50.5", "1001", "2000"]
        for value in invalid_values:
            is_valid, message = validate_lightning_batch_size(value)
            self.assertFalse(is_valid, f"القيمة '{value}' يجب أن تكون غير صحيحة")
        
        print("✅ تم اختبار التحقق من صحة قيم حد تقسيم البرق بنجاح")
    
    def test_lightning_batch_size_usage(self):
        """اختبار استخدام حد تقسيم البرق من القالب"""
        print("🧪 اختبار استخدام حد تقسيم البرق من القالب...")
        
        # محاكاة دالة استخدام حد تقسيم البرق
        def get_lightning_batch_size_from_template(template_data, default_size=100):
            """الحصول على حد تقسيم البرق من القالب"""
            if 'lightning_batch_size' in template_data:
                try:
                    batch_size = int(template_data['lightning_batch_size'])
                    if batch_size > 0:
                        return batch_size
                except ValueError:
                    pass
            return default_size
        
        # تحميل القوالب
        with open(f"{self.test_config_dir}/mikrotik_user_manager_templates.json", 'r', encoding='utf-8') as f:
            um_templates = json.load(f)
        
        # اختبار الحصول على حد تقسيم البرق من القوالب المختلفة
        test_cases = [
            ("قالب_برق_صغير", 50),
            ("قالب_برق_متوسط", 100),
            ("قالب_برق_كبير", 200),
            ("قالب_برق_ضخم", 500),
            ("قالب_بدون_برق", 100)  # القيمة الافتراضية
        ]
        
        for template_name, expected_size in test_cases:
            template_data = um_templates[template_name]
            actual_size = get_lightning_batch_size_from_template(template_data)
            self.assertEqual(actual_size, expected_size, f"حد تقسيم البرق غير صحيح للقالب {template_name}: متوقع {expected_size}, فعلي {actual_size}")
        
        print("✅ تم اختبار استخدام حد تقسيم البرق من القالب بنجاح")
    
    def test_lightning_batch_size_in_hotspot_templates(self):
        """اختبار حد تقسيم البرق في قوالب Hotspot"""
        print("🧪 اختبار حد تقسيم البرق في قوالب Hotspot...")
        
        # تحميل قوالب Hotspot
        with open(f"{self.test_config_dir}/mikrotik_hotspot_templates.json", 'r', encoding='utf-8') as f:
            hs_templates = json.load(f)
        
        # التحقق من وجود حقل lightning_batch_size
        expected_values = {
            "قالب_كافيه_برق": "75",
            "قالب_فندق_برق": "150"
        }
        
        for template_name, expected_value in expected_values.items():
            self.assertIn(template_name, hs_templates, f"قالب Hotspot مفقود: {template_name}")
            template_data = hs_templates[template_name]
            self.assertIn('lightning_batch_size', template_data, f"حقل lightning_batch_size مفقود في قالب Hotspot: {template_name}")
            
            actual_value = template_data['lightning_batch_size']
            self.assertEqual(actual_value, expected_value, f"قيمة lightning_batch_size غير صحيحة في قالب Hotspot {template_name}: متوقع {expected_value}, فعلي {actual_value}")
        
        print("✅ تم اختبار حد تقسيم البرق في قوالب Hotspot بنجاح")
    
    def test_lightning_batch_size_statistics(self):
        """اختبار إحصائيات حد تقسيم البرق"""
        print("🧪 اختبار إحصائيات حد تقسيم البرق...")
        
        # محاكاة دالة حساب إحصائيات حد تقسيم البرق
        def get_lightning_batch_statistics(templates):
            """حساب إحصائيات حد تقسيم البرق"""
            stats = {
                'total_templates': len(templates),
                'templates_with_lightning': 0,
                'templates_without_lightning': 0,
                'batch_sizes': [],
                'min_batch_size': None,
                'max_batch_size': None,
                'avg_batch_size': None
            }
            
            for template_name, template_data in templates.items():
                if 'lightning_batch_size' in template_data:
                    stats['templates_with_lightning'] += 1
                    try:
                        batch_size = int(template_data['lightning_batch_size'])
                        stats['batch_sizes'].append(batch_size)
                    except ValueError:
                        pass
                else:
                    stats['templates_without_lightning'] += 1
            
            if stats['batch_sizes']:
                stats['min_batch_size'] = min(stats['batch_sizes'])
                stats['max_batch_size'] = max(stats['batch_sizes'])
                stats['avg_batch_size'] = sum(stats['batch_sizes']) / len(stats['batch_sizes'])
            
            return stats
        
        # تحميل القوالب وحساب الإحصائيات
        with open(f"{self.test_config_dir}/mikrotik_user_manager_templates.json", 'r', encoding='utf-8') as f:
            um_templates = json.load(f)
        
        stats = get_lightning_batch_statistics(um_templates)
        
        # التحقق من الإحصائيات
        self.assertEqual(stats['total_templates'], 5, "إجمالي عدد القوالب غير صحيح")
        self.assertEqual(stats['templates_with_lightning'], 4, "عدد القوالب مع حد البرق غير صحيح")
        self.assertEqual(stats['templates_without_lightning'], 1, "عدد القوالب بدون حد البرق غير صحيح")
        
        # التحقق من القيم الإحصائية
        self.assertEqual(stats['min_batch_size'], 50, "أصغر حد برق غير صحيح")
        self.assertEqual(stats['max_batch_size'], 500, "أكبر حد برق غير صحيح")
        self.assertEqual(stats['avg_batch_size'], 212.5, "متوسط حد البرق غير صحيح")  # (50+100+200+500)/4
        
        print(f"✅ تم حساب إحصائيات حد تقسيم البرق: {stats['templates_with_lightning']} قالب مع البرق، متوسط الحد: {stats['avg_batch_size']}")

if __name__ == '__main__':
    print("🚀 بدء اختبارات حقل Lightning Batch Size في القوالب")
    print("=" * 60)
    
    unittest.main(verbosity=2)
