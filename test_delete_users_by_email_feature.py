#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة حذف المستخدمين بناءً على نمط الإيميل في بوت التلجرام
تاريخ الإنشاء: 2025-07-21
الهدف: اختبار الميزة الجديدة لحذف المستخدمين من HotSpot بناءً على نمط الإيميل
"""

import json
import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch

class TestDeleteUsersByEmailFeature:
    """اختبار ميزة حذف المستخدمين بنمط الإيميل"""
    
    def __init__(self):
        self.test_config_dir = "test_config_delete_email"
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        print("🔧 إعداد بيئة الاختبار...")
        
        # إنشاء مجلد الاختبار
        if os.path.exists(self.test_config_dir):
            shutil.rmtree(self.test_config_dir)
        os.makedirs(self.test_config_dir)
        
        print(f"✅ تم إنشاء مجلد الاختبار: {self.test_config_dir}")
    
    def test_email_pattern_validation(self):
        """اختبار التحقق من صحة نمط الإيميل"""
        print("\n" + "="*80)
        print("🧪 اختبار التحقق من صحة نمط الإيميل")
        print("="*80)
        
        # أنماط صحيحة
        valid_patterns = [
            "10@2025-07-21",
            "@pro.pro",
            "test@",
            "<EMAIL>",
            "2025-07-21",
            "pro.pro"
        ]
        
        # أنماط غير صحيحة
        invalid_patterns = [
            "",           # فارغ
            " ",          # مسافة فقط
            "a",          # قصير جداً
            "  a  "       # قصير مع مسافات
        ]
        
        print("✅ أنماط صحيحة:")
        for pattern in valid_patterns:
            pattern_clean = pattern.strip()
            is_valid = len(pattern_clean) >= 2
            print(f"  • '{pattern}' -> {'✅ صحيح' if is_valid else '❌ خطأ'}")
            assert is_valid, f"النمط '{pattern}' يجب أن يكون صحيحاً"
        
        print("\n❌ أنماط غير صحيحة:")
        for pattern in invalid_patterns:
            pattern_clean = pattern.strip()
            is_valid = len(pattern_clean) >= 2
            print(f"  • '{pattern}' -> {'✅ صحيح' if is_valid else '❌ خطأ'}")
            assert not is_valid, f"النمط '{pattern}' يجب أن يكون غير صحيح"
        
        print("\n✅ جميع اختبارات التحقق من النمط نجحت")
        return True
    
    def test_user_filtering_logic(self):
        """اختبار منطق تصفية المستخدمين"""
        print("\n" + "="*80)
        print("🧪 اختبار منطق تصفية المستخدمين")
        print("="*80)
        
        # بيانات مستخدمين تجريبية
        test_users = [
            {
                'name': 'user1',
                'email': '10@2025-07-21',
                'comment': ''  # فارغ - يجب أن يطابق
            },
            {
                'name': 'user2', 
                'email': '20@2025-07-21',
                'comment': 'VIP User'  # ليس فارغ - لا يطابق
            },
            {
                'name': 'user3',
                'email': '<EMAIL>',
                'comment': ''  # فارغ - يجب أن يطابق
            },
            {
                'name': 'user4',
                'email': '<EMAIL>',
                'comment': ''  # فارغ لكن الإيميل لا يطابق النمط
            },
            {
                'name': 'user5',
                'email': '30@2025-07-21',
                # لا يحتوي على comment - يعتبر فارغ
            }
        ]
        
        # اختبار نمط "10@2025-07-21"
        pattern1 = "10@2025-07-21"
        matching_users1 = []
        
        for user in test_users:
            comment = user.get('comment', '')
            if comment and comment.strip():
                continue  # تخطي المستخدمين الذين لديهم comment
            
            email = user.get('email', '')
            if email and pattern1.lower() in email.lower():
                matching_users1.append(user)
        
        expected_count1 = 1  # user1 فقط
        assert len(matching_users1) == expected_count1, f"متوقع {expected_count1} مستخدم، وجد {len(matching_users1)}"
        assert matching_users1[0]['name'] == 'user1', "المستخدم المطابق يجب أن يكون user1"
        
        print(f"✅ نمط '{pattern1}': وجد {len(matching_users1)} مستخدم مطابق")
        
        # اختبار نمط "@2025-07-21"
        pattern2 = "@2025-07-21"
        matching_users2 = []
        
        for user in test_users:
            comment = user.get('comment', '')
            if comment and comment.strip():
                continue
            
            email = user.get('email', '')
            if email and pattern2.lower() in email.lower():
                matching_users2.append(user)
        
        expected_count2 = 2  # user1 و user5
        assert len(matching_users2) == expected_count2, f"متوقع {expected_count2} مستخدم، وجد {len(matching_users2)}"
        
        print(f"✅ نمط '{pattern2}': وجد {len(matching_users2)} مستخدم مطابق")
        
        # اختبار نمط "@pro.pro"
        pattern3 = "@pro.pro"
        matching_users3 = []
        
        for user in test_users:
            comment = user.get('comment', '')
            if comment and comment.strip():
                continue
            
            email = user.get('email', '')
            if email and pattern3.lower() in email.lower():
                matching_users3.append(user)
        
        expected_count3 = 1  # user3 فقط
        assert len(matching_users3) == expected_count3, f"متوقع {expected_count3} مستخدم، وجد {len(matching_users3)}"
        assert matching_users3[0]['name'] == 'user3', "المستخدم المطابق يجب أن يكون user3"
        
        print(f"✅ نمط '{pattern3}': وجد {len(matching_users3)} مستخدم مطابق")
        
        print("\n✅ جميع اختبارات منطق التصفية نجحت")
        return True
    
    def test_callback_data_parsing(self):
        """اختبار تحليل بيانات callback"""
        print("\n" + "="*80)
        print("🧪 اختبار تحليل بيانات callback")
        print("="*80)
        
        # اختبار callback_data للتأكيد
        test_callbacks = [
            {
                'data': 'confirm_delete_email_5_10@2025-07-21',
                'expected_count': 5,
                'expected_pattern': '10@2025-07-21'
            },
            {
                'data': '<EMAIL>',
                'expected_count': 25,
                'expected_pattern': '@pro.pro'
            },
            {
                'data': '<EMAIL>',
                'expected_count': 1,
                'expected_pattern': '<EMAIL>'
            }
        ]
        
        for test_case in test_callbacks:
            callback_data = test_case['data']
            expected_count = test_case['expected_count']
            expected_pattern = test_case['expected_pattern']
            
            # محاكاة تحليل callback_data
            parts = callback_data.replace("confirm_delete_email_", "").split("_", 1)
            
            assert len(parts) >= 2, f"callback_data غير صحيح: {callback_data}"
            
            actual_count = int(parts[0])
            actual_pattern = parts[1]
            
            assert actual_count == expected_count, f"العدد المتوقع {expected_count}، الفعلي {actual_count}"
            assert actual_pattern == expected_pattern, f"النمط المتوقع '{expected_pattern}'، الفعلي '{actual_pattern}'"
            
            print(f"✅ '{callback_data}' -> العدد: {actual_count}, النمط: '{actual_pattern}'")
        
        print("\n✅ جميع اختبارات تحليل callback_data نجحت")
        return True
    
    def test_telegram_message_formatting(self):
        """اختبار تنسيق رسائل التلجرام"""
        print("\n" + "="*80)
        print("🧪 اختبار تنسيق رسائل التلجرام")
        print("="*80)
        
        # اختبار رسالة طلب النمط
        request_msg = """🗑️ **حذف المستخدمين بنمط الإيميل**

🎯 **الوظيفة:**
• البحث في نظام HotSpot فقط
• حذف المستخدمين الذين يحققون الشروط:
  ✓ حقل Comment فارغ أو null
  ✓ الإيميل يحتوي على النمط المحدد

📝 **أدخل نمط الإيميل للبحث:**

**مثال:** `10@2025-07-21`
**مثال:** `@pro.pro`
**مثال:** `test@`

⚠️ **تحذير:** هذه العملية لا يمكن التراجع عنها!

💡 **أرسل النمط الآن:**"""
        
        assert "🗑️" in request_msg, "رسالة الطلب يجب أن تحتوي على أيقونة سلة المهملات"
        assert "HotSpot" in request_msg, "رسالة الطلب يجب أن تذكر HotSpot"
        assert "Comment فارغ" in request_msg, "رسالة الطلب يجب أن تذكر شرط Comment الفارغ"
        
        print("✅ رسالة طلب النمط منسقة بشكل صحيح")
        
        # اختبار رسالة التأكيد
        email_pattern = "10@2025-07-21"
        users_count = 25
        sample_usernames = ["user1", "user2", "user3"]
        
        confirm_msg = f"""🔍 **نتيجة البحث**

📧 **النمط:** `{email_pattern}`
📊 **عدد المستخدمين المطابقين:** {users_count}

👥 **أمثلة على المستخدمين:**
{chr(10).join([f"• {username}" for username in sample_usernames])}

⚠️ **تحذير مهم:**
• سيتم حذف جميع هؤلاء المستخدمين نهائياً
• هذه العملية لا يمكن التراجع عنها
• سيتم الحذف من نظام HotSpot فقط

❓ **هل تريد المتابعة مع الحذف؟**"""
        
        assert email_pattern in confirm_msg, "رسالة التأكيد يجب أن تحتوي على النمط"
        assert str(users_count) in confirm_msg, "رسالة التأكيد يجب أن تحتوي على العدد"
        assert "user1" in confirm_msg, "رسالة التأكيد يجب أن تحتوي على أمثلة المستخدمين"
        
        print("✅ رسالة التأكيد منسقة بشكل صحيح")
        
        # اختبار رسالة التقرير النهائي
        success_count = 23
        failed_count = 2
        total_count = success_count + failed_count
        success_rate = (success_count / total_count) * 100
        
        final_report = f"""🗑️ **تقرير عملية الحذف النهائي**

📧 **نمط الإيميل:** `{email_pattern}`
👥 **إجمالي المستخدمين:** {total_count}

📊 **النتائج:**
✅ **تم حذفهم بنجاح:** {success_count}
❌ **فشل في حذفهم:** {failed_count}
📈 **معدل النجاح:** {success_rate:.1f}%

💡 **ملاحظة:** تم حذف المستخدمين من نظام HotSpot فقط"""
        
        assert str(success_count) in final_report, "التقرير يجب أن يحتوي على عدد النجاح"
        assert str(failed_count) in final_report, "التقرير يجب أن يحتوي على عدد الفشل"
        assert f"{success_rate:.1f}%" in final_report, "التقرير يجب أن يحتوي على معدل النجاح"
        
        print("✅ رسالة التقرير النهائي منسقة بشكل صحيح")
        
        print("\n✅ جميع اختبارات تنسيق الرسائل نجحت")
        return True
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار ميزة حذف المستخدمين بنمط الإيميل")
        print("="*80)
        
        tests = [
            ("اختبار التحقق من النمط", self.test_email_pattern_validation),
            ("اختبار منطق التصفية", self.test_user_filtering_logic),
            ("اختبار تحليل callback", self.test_callback_data_parsing),
            ("اختبار تنسيق الرسائل", self.test_telegram_message_formatting)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 تشغيل: {test_name}")
                result = test_func()
                if result:
                    print(f"✅ نجح: {test_name}")
                    passed += 1
                else:
                    print(f"❌ فشل: {test_name}")
                    failed += 1
            except Exception as e:
                print(f"❌ خطأ في {test_name}: {str(e)}")
                failed += 1
        
        print("\n" + "="*80)
        print("📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("🎉 جميع الاختبارات نجحت! ميزة حذف المستخدمين بالإيميل تعمل بشكل صحيح.")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        
        return failed == 0
    
    def cleanup(self):
        """تنظيف ملفات الاختبار"""
        try:
            if os.path.exists(self.test_config_dir):
                shutil.rmtree(self.test_config_dir)
            print(f"🧹 تم تنظيف مجلد الاختبار: {self.test_config_dir}")
        except Exception as e:
            print(f"⚠️ تحذير: فشل في تنظيف مجلد الاختبار: {str(e)}")

def main():
    """الدالة الرئيسية"""
    tester = TestDeleteUsersByEmailFeature()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
