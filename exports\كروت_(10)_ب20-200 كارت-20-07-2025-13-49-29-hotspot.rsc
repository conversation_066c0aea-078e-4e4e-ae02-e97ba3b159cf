# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-20 13:49:33
# القالب: 10
# النظام: hotspot
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 200 مستخدم Hotspot...";

# المستخدم 1: 0198355936
:do {
    /ip hotspot user add name="0198355936" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198355936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198355936";
};

# المستخدم 2: 0182097223
:do {
    /ip hotspot user add name="0182097223" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182097223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182097223";
};

# المستخدم 3: 0102610776
:do {
    /ip hotspot user add name="0102610776" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102610776";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102610776";
};

# المستخدم 4: 0134775576
:do {
    /ip hotspot user add name="0134775576" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134775576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134775576";
};

# المستخدم 5: 0174346458
:do {
    /ip hotspot user add name="0174346458" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174346458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174346458";
};

# المستخدم 6: 0158704559
:do {
    /ip hotspot user add name="0158704559" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158704559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158704559";
};

# المستخدم 7: 0150214343
:do {
    /ip hotspot user add name="0150214343" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150214343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150214343";
};

# المستخدم 8: 0134499654
:do {
    /ip hotspot user add name="0134499654" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134499654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134499654";
};

# المستخدم 9: 0113807262
:do {
    /ip hotspot user add name="0113807262" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113807262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113807262";
};

# المستخدم 10: 0108569429
:do {
    /ip hotspot user add name="0108569429" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108569429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108569429";
};

# المستخدم 11: 0102775303
:do {
    /ip hotspot user add name="0102775303" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102775303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102775303";
};

# المستخدم 12: 0141790577
:do {
    /ip hotspot user add name="0141790577" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141790577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141790577";
};

# المستخدم 13: 0146034558
:do {
    /ip hotspot user add name="0146034558" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146034558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146034558";
};

# المستخدم 14: 0147138892
:do {
    /ip hotspot user add name="0147138892" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147138892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147138892";
};

# المستخدم 15: 0176332974
:do {
    /ip hotspot user add name="0176332974" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176332974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176332974";
};

# المستخدم 16: 0179685394
:do {
    /ip hotspot user add name="0179685394" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179685394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179685394";
};

# المستخدم 17: 0129717384
:do {
    /ip hotspot user add name="0129717384" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129717384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129717384";
};

# المستخدم 18: 0135063533
:do {
    /ip hotspot user add name="0135063533" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135063533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135063533";
};

# المستخدم 19: 0114917043
:do {
    /ip hotspot user add name="0114917043" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114917043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114917043";
};

# المستخدم 20: 0186066065
:do {
    /ip hotspot user add name="0186066065" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186066065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186066065";
};

# المستخدم 21: 0127410294
:do {
    /ip hotspot user add name="0127410294" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127410294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127410294";
};

# المستخدم 22: 0157159116
:do {
    /ip hotspot user add name="0157159116" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157159116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157159116";
};

# المستخدم 23: 0139218425
:do {
    /ip hotspot user add name="0139218425" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139218425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139218425";
};

# المستخدم 24: 0141452002
:do {
    /ip hotspot user add name="0141452002" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141452002";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141452002";
};

# المستخدم 25: 0114586306
:do {
    /ip hotspot user add name="0114586306" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114586306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114586306";
};

# المستخدم 26: 0172507654
:do {
    /ip hotspot user add name="0172507654" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172507654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172507654";
};

# المستخدم 27: 0166295638
:do {
    /ip hotspot user add name="0166295638" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166295638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166295638";
};

# المستخدم 28: 0122701197
:do {
    /ip hotspot user add name="0122701197" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122701197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122701197";
};

# المستخدم 29: 0146459497
:do {
    /ip hotspot user add name="0146459497" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146459497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146459497";
};

# المستخدم 30: 0185825395
:do {
    /ip hotspot user add name="0185825395" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185825395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185825395";
};

# المستخدم 31: 0116044671
:do {
    /ip hotspot user add name="0116044671" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116044671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116044671";
};

# المستخدم 32: 0182291405
:do {
    /ip hotspot user add name="0182291405" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182291405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182291405";
};

# المستخدم 33: 0117835836
:do {
    /ip hotspot user add name="0117835836" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117835836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117835836";
};

# المستخدم 34: 0122265910
:do {
    /ip hotspot user add name="0122265910" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122265910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122265910";
};

# المستخدم 35: 0112505718
:do {
    /ip hotspot user add name="0112505718" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112505718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112505718";
};

# المستخدم 36: 0175701549
:do {
    /ip hotspot user add name="0175701549" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175701549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175701549";
};

# المستخدم 37: 0162878243
:do {
    /ip hotspot user add name="0162878243" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162878243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162878243";
};

# المستخدم 38: 0144095042
:do {
    /ip hotspot user add name="0144095042" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144095042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144095042";
};

# المستخدم 39: 0153535940
:do {
    /ip hotspot user add name="0153535940" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153535940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153535940";
};

# المستخدم 40: 0130136791
:do {
    /ip hotspot user add name="0130136791" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130136791";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130136791";
};

# المستخدم 41: 0143573092
:do {
    /ip hotspot user add name="0143573092" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143573092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143573092";
};

# المستخدم 42: 0167747499
:do {
    /ip hotspot user add name="0167747499" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167747499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167747499";
};

# المستخدم 43: 0139070729
:do {
    /ip hotspot user add name="0139070729" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139070729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139070729";
};

# المستخدم 44: 0168367344
:do {
    /ip hotspot user add name="0168367344" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168367344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168367344";
};

# المستخدم 45: 0199853711
:do {
    /ip hotspot user add name="0199853711" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199853711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199853711";
};

# المستخدم 46: 0143716593
:do {
    /ip hotspot user add name="0143716593" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143716593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143716593";
};

# المستخدم 47: 0125041646
:do {
    /ip hotspot user add name="0125041646" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125041646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125041646";
};

# المستخدم 48: 0144002215
:do {
    /ip hotspot user add name="0144002215" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144002215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144002215";
};

# المستخدم 49: 0118103907
:do {
    /ip hotspot user add name="0118103907" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118103907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118103907";
};

# المستخدم 50: 0177910429
:do {
    /ip hotspot user add name="0177910429" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177910429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177910429";
};

# المستخدم 51: 0132264055
:do {
    /ip hotspot user add name="0132264055" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132264055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132264055";
};

# المستخدم 52: 0143482402
:do {
    /ip hotspot user add name="0143482402" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143482402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143482402";
};

# المستخدم 53: 0160329226
:do {
    /ip hotspot user add name="0160329226" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160329226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160329226";
};

# المستخدم 54: 0142253630
:do {
    /ip hotspot user add name="0142253630" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142253630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142253630";
};

# المستخدم 55: 0142385394
:do {
    /ip hotspot user add name="0142385394" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142385394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142385394";
};

# المستخدم 56: 0131430287
:do {
    /ip hotspot user add name="0131430287" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131430287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131430287";
};

# المستخدم 57: 0168547264
:do {
    /ip hotspot user add name="0168547264" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168547264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168547264";
};

# المستخدم 58: 0165693532
:do {
    /ip hotspot user add name="0165693532" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165693532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165693532";
};

# المستخدم 59: 0135550851
:do {
    /ip hotspot user add name="0135550851" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135550851";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135550851";
};

# المستخدم 60: 0150337666
:do {
    /ip hotspot user add name="0150337666" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150337666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150337666";
};

# المستخدم 61: 0198511185
:do {
    /ip hotspot user add name="0198511185" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198511185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198511185";
};

# المستخدم 62: 0142251614
:do {
    /ip hotspot user add name="0142251614" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142251614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142251614";
};

# المستخدم 63: 0108256736
:do {
    /ip hotspot user add name="0108256736" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108256736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108256736";
};

# المستخدم 64: 0141654786
:do {
    /ip hotspot user add name="0141654786" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141654786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141654786";
};

# المستخدم 65: 0105320330
:do {
    /ip hotspot user add name="0105320330" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105320330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105320330";
};

# المستخدم 66: 0161623857
:do {
    /ip hotspot user add name="0161623857" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161623857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161623857";
};

# المستخدم 67: 0152813565
:do {
    /ip hotspot user add name="0152813565" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152813565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152813565";
};

# المستخدم 68: 0185062613
:do {
    /ip hotspot user add name="0185062613" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185062613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185062613";
};

# المستخدم 69: 0134261512
:do {
    /ip hotspot user add name="0134261512" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134261512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134261512";
};

# المستخدم 70: 0173695527
:do {
    /ip hotspot user add name="0173695527" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173695527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173695527";
};

# المستخدم 71: 0146367121
:do {
    /ip hotspot user add name="0146367121" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146367121";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146367121";
};

# المستخدم 72: 0173485061
:do {
    /ip hotspot user add name="0173485061" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173485061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173485061";
};

# المستخدم 73: 0104509838
:do {
    /ip hotspot user add name="0104509838" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104509838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104509838";
};

# المستخدم 74: 0137604208
:do {
    /ip hotspot user add name="0137604208" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137604208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137604208";
};

# المستخدم 75: 0124724064
:do {
    /ip hotspot user add name="0124724064" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124724064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124724064";
};

# المستخدم 76: 0147016860
:do {
    /ip hotspot user add name="0147016860" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147016860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147016860";
};

# المستخدم 77: 0186401861
:do {
    /ip hotspot user add name="0186401861" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186401861";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186401861";
};

# المستخدم 78: 0174084985
:do {
    /ip hotspot user add name="0174084985" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174084985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174084985";
};

# المستخدم 79: 0118057030
:do {
    /ip hotspot user add name="0118057030" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118057030";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118057030";
};

# المستخدم 80: 0160838830
:do {
    /ip hotspot user add name="0160838830" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160838830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160838830";
};

# المستخدم 81: 0134141371
:do {
    /ip hotspot user add name="0134141371" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134141371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134141371";
};

# المستخدم 82: 0119654591
:do {
    /ip hotspot user add name="0119654591" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119654591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119654591";
};

# المستخدم 83: 0104419098
:do {
    /ip hotspot user add name="0104419098" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104419098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104419098";
};

# المستخدم 84: 0185507157
:do {
    /ip hotspot user add name="0185507157" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185507157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185507157";
};

# المستخدم 85: 0147757762
:do {
    /ip hotspot user add name="0147757762" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147757762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147757762";
};

# المستخدم 86: 0102555558
:do {
    /ip hotspot user add name="0102555558" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102555558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102555558";
};

# المستخدم 87: 0147050632
:do {
    /ip hotspot user add name="0147050632" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147050632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147050632";
};

# المستخدم 88: 0102357611
:do {
    /ip hotspot user add name="0102357611" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102357611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102357611";
};

# المستخدم 89: 0102991164
:do {
    /ip hotspot user add name="0102991164" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102991164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102991164";
};

# المستخدم 90: 0128081415
:do {
    /ip hotspot user add name="0128081415" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128081415";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128081415";
};

# المستخدم 91: 0182110329
:do {
    /ip hotspot user add name="0182110329" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182110329";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182110329";
};

# المستخدم 92: 0137625808
:do {
    /ip hotspot user add name="0137625808" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137625808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137625808";
};

# المستخدم 93: 0177749539
:do {
    /ip hotspot user add name="0177749539" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177749539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177749539";
};

# المستخدم 94: 0141722893
:do {
    /ip hotspot user add name="0141722893" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141722893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141722893";
};

# المستخدم 95: 0152075878
:do {
    /ip hotspot user add name="0152075878" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152075878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152075878";
};

# المستخدم 96: 0192477247
:do {
    /ip hotspot user add name="0192477247" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192477247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192477247";
};

# المستخدم 97: 0139182023
:do {
    /ip hotspot user add name="0139182023" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139182023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139182023";
};

# المستخدم 98: 0158862332
:do {
    /ip hotspot user add name="0158862332" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158862332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158862332";
};

# المستخدم 99: 0195843135
:do {
    /ip hotspot user add name="0195843135" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195843135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195843135";
};

# المستخدم 100: 0125627788
:do {
    /ip hotspot user add name="0125627788" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125627788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125627788";
};

# المستخدم 101: 0174647994
:do {
    /ip hotspot user add name="0174647994" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174647994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174647994";
};

# المستخدم 102: 0128637886
:do {
    /ip hotspot user add name="0128637886" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128637886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128637886";
};

# المستخدم 103: 0188810998
:do {
    /ip hotspot user add name="0188810998" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188810998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188810998";
};

# المستخدم 104: 0160414447
:do {
    /ip hotspot user add name="0160414447" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160414447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160414447";
};

# المستخدم 105: 0167057684
:do {
    /ip hotspot user add name="0167057684" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167057684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167057684";
};

# المستخدم 106: 0173821001
:do {
    /ip hotspot user add name="0173821001" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173821001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173821001";
};

# المستخدم 107: 0190144701
:do {
    /ip hotspot user add name="0190144701" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190144701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190144701";
};

# المستخدم 108: 0182669360
:do {
    /ip hotspot user add name="0182669360" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182669360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182669360";
};

# المستخدم 109: 0183336514
:do {
    /ip hotspot user add name="0183336514" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183336514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183336514";
};

# المستخدم 110: 0123738114
:do {
    /ip hotspot user add name="0123738114" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123738114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123738114";
};

# المستخدم 111: 0113949993
:do {
    /ip hotspot user add name="0113949993" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113949993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113949993";
};

# المستخدم 112: 0151752437
:do {
    /ip hotspot user add name="0151752437" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151752437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151752437";
};

# المستخدم 113: 0164971787
:do {
    /ip hotspot user add name="0164971787" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164971787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164971787";
};

# المستخدم 114: 0179903480
:do {
    /ip hotspot user add name="0179903480" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179903480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179903480";
};

# المستخدم 115: 0170419516
:do {
    /ip hotspot user add name="0170419516" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170419516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170419516";
};

# المستخدم 116: 0167558881
:do {
    /ip hotspot user add name="0167558881" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167558881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167558881";
};

# المستخدم 117: 0186192542
:do {
    /ip hotspot user add name="0186192542" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186192542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186192542";
};

# المستخدم 118: 0114206256
:do {
    /ip hotspot user add name="0114206256" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114206256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114206256";
};

# المستخدم 119: 0144641794
:do {
    /ip hotspot user add name="0144641794" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144641794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144641794";
};

# المستخدم 120: 0116940220
:do {
    /ip hotspot user add name="0116940220" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116940220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116940220";
};

# المستخدم 121: 0176774295
:do {
    /ip hotspot user add name="0176774295" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176774295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176774295";
};

# المستخدم 122: 0196631655
:do {
    /ip hotspot user add name="0196631655" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196631655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196631655";
};

# المستخدم 123: 0188103903
:do {
    /ip hotspot user add name="0188103903" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188103903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188103903";
};

# المستخدم 124: 0129819100
:do {
    /ip hotspot user add name="0129819100" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129819100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129819100";
};

# المستخدم 125: 0135339140
:do {
    /ip hotspot user add name="0135339140" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135339140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135339140";
};

# المستخدم 126: 0194103742
:do {
    /ip hotspot user add name="0194103742" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194103742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194103742";
};

# المستخدم 127: 0121529342
:do {
    /ip hotspot user add name="0121529342" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121529342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121529342";
};

# المستخدم 128: 0102010206
:do {
    /ip hotspot user add name="0102010206" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102010206";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102010206";
};

# المستخدم 129: 0154844473
:do {
    /ip hotspot user add name="0154844473" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154844473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154844473";
};

# المستخدم 130: 0155663874
:do {
    /ip hotspot user add name="0155663874" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155663874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155663874";
};

# المستخدم 131: 0145414343
:do {
    /ip hotspot user add name="0145414343" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145414343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145414343";
};

# المستخدم 132: 0103989608
:do {
    /ip hotspot user add name="0103989608" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103989608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103989608";
};

# المستخدم 133: 0139668973
:do {
    /ip hotspot user add name="0139668973" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139668973";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139668973";
};

# المستخدم 134: 0190868771
:do {
    /ip hotspot user add name="0190868771" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190868771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190868771";
};

# المستخدم 135: 0198661063
:do {
    /ip hotspot user add name="0198661063" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198661063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198661063";
};

# المستخدم 136: 0190167976
:do {
    /ip hotspot user add name="0190167976" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190167976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190167976";
};

# المستخدم 137: 0102433142
:do {
    /ip hotspot user add name="0102433142" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102433142";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102433142";
};

# المستخدم 138: 0116245175
:do {
    /ip hotspot user add name="0116245175" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116245175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116245175";
};

# المستخدم 139: 0141946542
:do {
    /ip hotspot user add name="0141946542" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141946542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141946542";
};

# المستخدم 140: 0105843045
:do {
    /ip hotspot user add name="0105843045" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105843045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105843045";
};

# المستخدم 141: 0107252858
:do {
    /ip hotspot user add name="0107252858" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107252858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107252858";
};

# المستخدم 142: 0194027517
:do {
    /ip hotspot user add name="0194027517" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194027517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194027517";
};

# المستخدم 143: 0132612436
:do {
    /ip hotspot user add name="0132612436" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132612436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132612436";
};

# المستخدم 144: 0190501903
:do {
    /ip hotspot user add name="0190501903" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190501903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190501903";
};

# المستخدم 145: 0141756203
:do {
    /ip hotspot user add name="0141756203" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141756203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141756203";
};

# المستخدم 146: 0170939291
:do {
    /ip hotspot user add name="0170939291" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170939291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170939291";
};

# المستخدم 147: 0165036199
:do {
    /ip hotspot user add name="0165036199" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165036199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165036199";
};

# المستخدم 148: 0142549266
:do {
    /ip hotspot user add name="0142549266" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142549266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142549266";
};

# المستخدم 149: 0160803206
:do {
    /ip hotspot user add name="0160803206" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160803206";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160803206";
};

# المستخدم 150: 0184446082
:do {
    /ip hotspot user add name="0184446082" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184446082";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184446082";
};

# المستخدم 151: 0184325546
:do {
    /ip hotspot user add name="0184325546" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184325546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184325546";
};

# المستخدم 152: 0198873424
:do {
    /ip hotspot user add name="0198873424" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198873424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198873424";
};

# المستخدم 153: 0192965596
:do {
    /ip hotspot user add name="0192965596" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192965596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192965596";
};

# المستخدم 154: 0150632942
:do {
    /ip hotspot user add name="0150632942" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150632942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150632942";
};

# المستخدم 155: 0194622306
:do {
    /ip hotspot user add name="0194622306" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194622306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194622306";
};

# المستخدم 156: 0134513465
:do {
    /ip hotspot user add name="0134513465" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134513465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134513465";
};

# المستخدم 157: 0128457697
:do {
    /ip hotspot user add name="0128457697" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128457697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128457697";
};

# المستخدم 158: 0156533097
:do {
    /ip hotspot user add name="0156533097" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156533097";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156533097";
};

# المستخدم 159: 0145314091
:do {
    /ip hotspot user add name="0145314091" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145314091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145314091";
};

# المستخدم 160: 0178099762
:do {
    /ip hotspot user add name="0178099762" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178099762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178099762";
};

# المستخدم 161: 0108001282
:do {
    /ip hotspot user add name="0108001282" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108001282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108001282";
};

# المستخدم 162: 0135450975
:do {
    /ip hotspot user add name="0135450975" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135450975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135450975";
};

# المستخدم 163: 0134464775
:do {
    /ip hotspot user add name="0134464775" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134464775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134464775";
};

# المستخدم 164: 0184293549
:do {
    /ip hotspot user add name="0184293549" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184293549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184293549";
};

# المستخدم 165: 0183642325
:do {
    /ip hotspot user add name="0183642325" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183642325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183642325";
};

# المستخدم 166: 0197817759
:do {
    /ip hotspot user add name="0197817759" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197817759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197817759";
};

# المستخدم 167: 0187328520
:do {
    /ip hotspot user add name="0187328520" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187328520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187328520";
};

# المستخدم 168: 0128049327
:do {
    /ip hotspot user add name="0128049327" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128049327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128049327";
};

# المستخدم 169: 0109629508
:do {
    /ip hotspot user add name="0109629508" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109629508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109629508";
};

# المستخدم 170: 0198376245
:do {
    /ip hotspot user add name="0198376245" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198376245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198376245";
};

# المستخدم 171: 0161495005
:do {
    /ip hotspot user add name="0161495005" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161495005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161495005";
};

# المستخدم 172: 0113553553
:do {
    /ip hotspot user add name="0113553553" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113553553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113553553";
};

# المستخدم 173: 0101516073
:do {
    /ip hotspot user add name="0101516073" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101516073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101516073";
};

# المستخدم 174: 0138200949
:do {
    /ip hotspot user add name="0138200949" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138200949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138200949";
};

# المستخدم 175: 0124760946
:do {
    /ip hotspot user add name="0124760946" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124760946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124760946";
};

# المستخدم 176: 0100705607
:do {
    /ip hotspot user add name="0100705607" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100705607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100705607";
};

# المستخدم 177: 0199520035
:do {
    /ip hotspot user add name="0199520035" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199520035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199520035";
};

# المستخدم 178: 0193402161
:do {
    /ip hotspot user add name="0193402161" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193402161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193402161";
};

# المستخدم 179: 0112841993
:do {
    /ip hotspot user add name="0112841993" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112841993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112841993";
};

# المستخدم 180: 0174770794
:do {
    /ip hotspot user add name="0174770794" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174770794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174770794";
};

# المستخدم 181: 0182867668
:do {
    /ip hotspot user add name="0182867668" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182867668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182867668";
};

# المستخدم 182: 0198221461
:do {
    /ip hotspot user add name="0198221461" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198221461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198221461";
};

# المستخدم 183: 0115623045
:do {
    /ip hotspot user add name="0115623045" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115623045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115623045";
};

# المستخدم 184: 0163300107
:do {
    /ip hotspot user add name="0163300107" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163300107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163300107";
};

# المستخدم 185: 0172211558
:do {
    /ip hotspot user add name="0172211558" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172211558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172211558";
};

# المستخدم 186: 0173492509
:do {
    /ip hotspot user add name="0173492509" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173492509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173492509";
};

# المستخدم 187: 0111787817
:do {
    /ip hotspot user add name="0111787817" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111787817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111787817";
};

# المستخدم 188: 0185896148
:do {
    /ip hotspot user add name="0185896148" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185896148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185896148";
};

# المستخدم 189: 0194662680
:do {
    /ip hotspot user add name="0194662680" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194662680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194662680";
};

# المستخدم 190: 0123171054
:do {
    /ip hotspot user add name="0123171054" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123171054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123171054";
};

# المستخدم 191: 0170420461
:do {
    /ip hotspot user add name="0170420461" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170420461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170420461";
};

# المستخدم 192: 0128342391
:do {
    /ip hotspot user add name="0128342391" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128342391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128342391";
};

# المستخدم 193: 0154973127
:do {
    /ip hotspot user add name="0154973127" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154973127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154973127";
};

# المستخدم 194: 0171201772
:do {
    /ip hotspot user add name="0171201772" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171201772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171201772";
};

# المستخدم 195: 0101029068
:do {
    /ip hotspot user add name="0101029068" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101029068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101029068";
};

# المستخدم 196: 0193362740
:do {
    /ip hotspot user add name="0193362740" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193362740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193362740";
};

# المستخدم 197: 0197914597
:do {
    /ip hotspot user add name="0197914597" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197914597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197914597";
};

# المستخدم 198: 0132261680
:do {
    /ip hotspot user add name="0132261680" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132261680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132261680";
};

# المستخدم 199: 0145090447
:do {
    /ip hotspot user add name="0145090447" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145090447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145090447";
};

# المستخدم 200: 0126290978
:do {
    /ip hotspot user add name="0126290978" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126290978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126290978";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
