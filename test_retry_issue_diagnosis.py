#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة إعادة المحاولة للكروت الفاشلة في نظام HotSpot
"""

import re
import os

def analyze_failed_cards_flow():
    """تحليل تدفق حفظ واستخدام معلومات الكروت الفاشلة"""
    print("🔍 تحليل تدفق حفظ واستخدام معلومات الكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n📋 تحليل مراحل العملية:")
    
    # 1. تحليل مرحلة حفظ الكروت الفاشلة أثناء الإرسال
    print("\n1️⃣ مرحلة حفظ الكروت الفاشلة أثناء الإرسال:")
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # البحث عن مكان حفظ الكروت الفاشلة
        failed_save_match = re.search(r'except Exception as user_error:.*?self\.single_card_failed_cards\.append\({.*?}\)', func_code, re.DOTALL)
        if failed_save_match:
            print("✅ يتم حفظ الكروت الفاشلة أثناء الإرسال")
            print(f"   📝 الكود: {failed_save_match.group(0)[:100]}...")
        else:
            print("❌ لا يتم حفظ الكروت الفاشلة أثناء الإرسال")
        
        # البحث عن مكان حفظ failed_cards_info
        failed_info_match = re.search(r'if failed_count > 0 and getattr\(self, \'system_type\', \'\'\) == \'hotspot\'.*?self\.failed_cards_info = {.*?}', func_code, re.DOTALL)
        if failed_info_match:
            print("✅ يتم حفظ failed_cards_info في نهاية العملية")
            print(f"   📝 الكود: {failed_info_match.group(0)[:100]}...")
        else:
            print("❌ لا يتم حفظ failed_cards_info في نهاية العملية")
    
    # 2. تحليل مرحلة إظهار زر إعادة المحاولة
    print("\n2️⃣ مرحلة إظهار زر إعادة المحاولة:")
    
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # البحث عن شروط إظهار الزر
        button_condition_match = re.search(r'show_retry_failed_button = \(.*?\)', func_code, re.DOTALL)
        if button_condition_match:
            print("✅ شروط إظهار زر إعادة المحاولة موجودة")
            print(f"   📝 الشروط: {button_condition_match.group(0)}")
        else:
            print("❌ شروط إظهار زر إعادة المحاولة غير موجودة")
        
        # البحث عن إنشاء الزر
        button_creation_match = re.search(r'if show_retry_failed_button:.*?retry_failed_cards_single_', func_code, re.DOTALL)
        if button_creation_match:
            print("✅ يتم إنشاء زر إعادة المحاولة")
        else:
            print("❌ لا يتم إنشاء زر إعادة المحاولة")
    
    # 3. تحليل مرحلة معالجة الضغط على الزر
    print("\n3️⃣ مرحلة معالجة الضغط على زر إعادة المحاولة:")
    
    # البحث عن معالج callback
    callback_match = re.search(r'elif callback_data\.startswith\("retry_failed_cards_"\):.*?self\.handle_retry_failed_cards', content, re.DOTALL)
    if callback_match:
        print("✅ معالج callback موجود")
    else:
        print("❌ معالج callback غير موجود")
    
    # 4. تحليل دالة handle_retry_failed_cards
    print("\n4️⃣ مرحلة معالجة طلب إعادة المحاولة:")
    
    func_match = re.search(r'def handle_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # البحث عن التحقق من وجود البيانات
        check_match = re.search(r'if not has_failed_info or not failed_info_exists:', func_code)
        if check_match:
            print("✅ يتم التحقق من وجود معلومات الكروت الفاشلة")
        else:
            print("❌ لا يتم التحقق من وجود معلومات الكروت الفاشلة")
        
        # البحث عن رسالة الخطأ
        error_msg_match = re.search(r'لا توجد معلومات محفوظة عن الكروت الفاشلة', func_code)
        if error_msg_match:
            print("✅ رسالة الخطأ موجودة")
        else:
            print("❌ رسالة الخطأ غير موجودة")
    
    return True

def check_data_persistence_issue():
    """فحص مشكلة استمرارية البيانات"""
    print("\n🔍 فحص مشكلة استمرارية البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن أماكن حذف البيانات
    print("\n📋 البحث عن أماكن حذف البيانات:")
    
    # البحث عن حذف single_card_failed_cards
    delete_patterns = [
        r'if hasattr\(self, \'single_card_failed_cards\'\):.*?delattr\(self, \'single_card_failed_cards\'\)',
        r'delattr\(self, \'single_card_failed_cards\'\)',
        r'if hasattr\(self, \'failed_cards_info\'\):.*?delattr\(self, \'failed_cards_info\'\)',
        r'delattr\(self, \'failed_cards_info\'\)'
    ]
    
    for i, pattern in enumerate(delete_patterns, 1):
        matches = re.findall(pattern, content, re.DOTALL)
        if matches:
            print(f"⚠️ نمط حذف {i}: تم العثور على {len(matches)} مكان لحذف البيانات")
            for j, match in enumerate(matches[:2]):  # عرض أول مكانين فقط
                print(f"   📝 مكان {j+1}: {match[:80]}...")
        else:
            print(f"✅ نمط حذف {i}: لا يوجد حذف غير مرغوب")
    
    # البحث عن تنظيف البيانات في بداية العمليات
    print("\n📋 البحث عن تنظيف البيانات في بداية العمليات:")
    
    # البحث عن دالة create_single_card
    func_match = re.search(r'def create_single_card.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # البحث عن تنظيف البيانات
        cleanup_patterns = [
            'مسح أي بيانات سابقة للكروت الفاشلة',
            'if hasattr(self, \'single_card_failed_cards\'):',
            'if hasattr(self, \'failed_cards_info\'):'
        ]
        
        cleanup_found = False
        for pattern in cleanup_patterns:
            if pattern in func_code:
                cleanup_found = True
                break
        
        if cleanup_found:
            print("⚠️ يتم تنظيف البيانات في بداية create_single_card")
        else:
            print("✅ لا يتم تنظيف البيانات في بداية create_single_card")
    
    return True

def check_timing_issue():
    """فحص مشكلة التوقيت"""
    print("\n🔍 فحص مشكلة التوقيت...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n📋 تحليل تسلسل العمليات:")
    
    # البحث عن دالة create_single_card
    func_match = re.search(r'def create_single_card.*?(?=def|\Z)', content, re.DOTALL)
    if func_match:
        func_code = func_match.group(0)
        
        # البحث عن ترتيب العمليات
        operations = []
        
        if 'self.send_single_card_to_mikrotik_silent()' in func_code:
            operations.append("1. إرسال الكرت إلى MikroTik")
        
        if 'self.send_single_card_details_to_telegram(' in func_code:
            operations.append("2. إرسال التفاصيل إلى التلجرام")
        
        print("📋 ترتيب العمليات:")
        for op in operations:
            print(f"   {op}")
        
        # التحقق من وجود تنظيف بين العمليات
        send_to_mikrotik_pos = func_code.find('self.send_single_card_to_mikrotik_silent()')
        send_to_telegram_pos = func_code.find('self.send_single_card_details_to_telegram(')
        
        if send_to_mikrotik_pos != -1 and send_to_telegram_pos != -1:
            between_operations = func_code[send_to_mikrotik_pos:send_to_telegram_pos]
            
            if 'delattr' in between_operations or 'مسح' in between_operations:
                print("⚠️ يتم تنظيف البيانات بين إرسال MikroTik وإرسال التلجرام")
                print(f"   📝 الكود بين العمليات: {between_operations[:100]}...")
            else:
                print("✅ لا يتم تنظيف البيانات بين العمليات")
    
    return True

def run_diagnosis():
    """تشغيل التشخيص الكامل"""
    print("🚀 بدء تشخيص مشكلة إعادة المحاولة للكروت الفاشلة في HotSpot\n")
    
    analyze_failed_cards_flow()
    check_data_persistence_issue()
    check_timing_issue()
    
    print("\n🎯 خلاصة التشخيص:")
    print("   المشكلة المحتملة: قد يتم حذف البيانات قبل استخدامها")
    print("   الحل المقترح: التأكد من عدم حذف البيانات قبل الحاجة إليها")

if __name__ == "__main__":
    run_diagnosis()
