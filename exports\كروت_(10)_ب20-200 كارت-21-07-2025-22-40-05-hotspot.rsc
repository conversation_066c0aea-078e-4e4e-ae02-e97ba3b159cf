# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-21 22:40:07
# القالب: 10
# النظام: hotspot
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 200 مستخدم Hotspot...";

# المستخدم 1: 0188018011
:do {
    /ip hotspot user add name="0188018011" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188018011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188018011";
};

# المستخدم 2: 0148059574
:do {
    /ip hotspot user add name="0148059574" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148059574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148059574";
};

# المستخدم 3: 0151057525
:do {
    /ip hotspot user add name="0151057525" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151057525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151057525";
};

# المستخدم 4: 0104865775
:do {
    /ip hotspot user add name="0104865775" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104865775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104865775";
};

# المستخدم 5: 0133073092
:do {
    /ip hotspot user add name="0133073092" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133073092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133073092";
};

# المستخدم 6: 0184886684
:do {
    /ip hotspot user add name="0184886684" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184886684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184886684";
};

# المستخدم 7: 0128282091
:do {
    /ip hotspot user add name="0128282091" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128282091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128282091";
};

# المستخدم 8: 0133375519
:do {
    /ip hotspot user add name="0133375519" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133375519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133375519";
};

# المستخدم 9: 0161619384
:do {
    /ip hotspot user add name="0161619384" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161619384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161619384";
};

# المستخدم 10: 0128181235
:do {
    /ip hotspot user add name="0128181235" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128181235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128181235";
};

# المستخدم 11: 0194165659
:do {
    /ip hotspot user add name="0194165659" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194165659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194165659";
};

# المستخدم 12: 0110567863
:do {
    /ip hotspot user add name="0110567863" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110567863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110567863";
};

# المستخدم 13: 0175317752
:do {
    /ip hotspot user add name="0175317752" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175317752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175317752";
};

# المستخدم 14: 0129173201
:do {
    /ip hotspot user add name="0129173201" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129173201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129173201";
};

# المستخدم 15: 0125760530
:do {
    /ip hotspot user add name="0125760530" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125760530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125760530";
};

# المستخدم 16: 0191267408
:do {
    /ip hotspot user add name="0191267408" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191267408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191267408";
};

# المستخدم 17: 0122345966
:do {
    /ip hotspot user add name="0122345966" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122345966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122345966";
};

# المستخدم 18: 0165271180
:do {
    /ip hotspot user add name="0165271180" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165271180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165271180";
};

# المستخدم 19: 0146120131
:do {
    /ip hotspot user add name="0146120131" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146120131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146120131";
};

# المستخدم 20: 0158776122
:do {
    /ip hotspot user add name="0158776122" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158776122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158776122";
};

# المستخدم 21: 0113939479
:do {
    /ip hotspot user add name="0113939479" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113939479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113939479";
};

# المستخدم 22: 0155863295
:do {
    /ip hotspot user add name="0155863295" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155863295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155863295";
};

# المستخدم 23: 0167001057
:do {
    /ip hotspot user add name="0167001057" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167001057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167001057";
};

# المستخدم 24: 0187680073
:do {
    /ip hotspot user add name="0187680073" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187680073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187680073";
};

# المستخدم 25: 0196260788
:do {
    /ip hotspot user add name="0196260788" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196260788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196260788";
};

# المستخدم 26: 0150097938
:do {
    /ip hotspot user add name="0150097938" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150097938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150097938";
};

# المستخدم 27: 0177243322
:do {
    /ip hotspot user add name="0177243322" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177243322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177243322";
};

# المستخدم 28: 0185743843
:do {
    /ip hotspot user add name="0185743843" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185743843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185743843";
};

# المستخدم 29: 0163737234
:do {
    /ip hotspot user add name="0163737234" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163737234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163737234";
};

# المستخدم 30: 0188336278
:do {
    /ip hotspot user add name="0188336278" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188336278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188336278";
};

# المستخدم 31: 0120598477
:do {
    /ip hotspot user add name="0120598477" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120598477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120598477";
};

# المستخدم 32: 0134819955
:do {
    /ip hotspot user add name="0134819955" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134819955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134819955";
};

# المستخدم 33: 0181885499
:do {
    /ip hotspot user add name="0181885499" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181885499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181885499";
};

# المستخدم 34: 0173196435
:do {
    /ip hotspot user add name="0173196435" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173196435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173196435";
};

# المستخدم 35: 0127198970
:do {
    /ip hotspot user add name="0127198970" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127198970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127198970";
};

# المستخدم 36: 0138323197
:do {
    /ip hotspot user add name="0138323197" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138323197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138323197";
};

# المستخدم 37: 0190936404
:do {
    /ip hotspot user add name="0190936404" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190936404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190936404";
};

# المستخدم 38: 0189926686
:do {
    /ip hotspot user add name="0189926686" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189926686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189926686";
};

# المستخدم 39: 0123891648
:do {
    /ip hotspot user add name="0123891648" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123891648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123891648";
};

# المستخدم 40: 0178772917
:do {
    /ip hotspot user add name="0178772917" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178772917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178772917";
};

# المستخدم 41: 0159338396
:do {
    /ip hotspot user add name="0159338396" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159338396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159338396";
};

# المستخدم 42: 0167658282
:do {
    /ip hotspot user add name="0167658282" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167658282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167658282";
};

# المستخدم 43: 0196395681
:do {
    /ip hotspot user add name="0196395681" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196395681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196395681";
};

# المستخدم 44: 0170517839
:do {
    /ip hotspot user add name="0170517839" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170517839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170517839";
};

# المستخدم 45: 0199107985
:do {
    /ip hotspot user add name="0199107985" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199107985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199107985";
};

# المستخدم 46: 0128285284
:do {
    /ip hotspot user add name="0128285284" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128285284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128285284";
};

# المستخدم 47: 0148038591
:do {
    /ip hotspot user add name="0148038591" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148038591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148038591";
};

# المستخدم 48: 0199084941
:do {
    /ip hotspot user add name="0199084941" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199084941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199084941";
};

# المستخدم 49: 0181571866
:do {
    /ip hotspot user add name="0181571866" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181571866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181571866";
};

# المستخدم 50: 0148312496
:do {
    /ip hotspot user add name="0148312496" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148312496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148312496";
};

# المستخدم 51: 0176010092
:do {
    /ip hotspot user add name="0176010092" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176010092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176010092";
};

# المستخدم 52: 0151800789
:do {
    /ip hotspot user add name="0151800789" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151800789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151800789";
};

# المستخدم 53: 0133724848
:do {
    /ip hotspot user add name="0133724848" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133724848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133724848";
};

# المستخدم 54: 0138408920
:do {
    /ip hotspot user add name="0138408920" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138408920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138408920";
};

# المستخدم 55: 0159343315
:do {
    /ip hotspot user add name="0159343315" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159343315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159343315";
};

# المستخدم 56: 0194214201
:do {
    /ip hotspot user add name="0194214201" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194214201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194214201";
};

# المستخدم 57: 0146936377
:do {
    /ip hotspot user add name="0146936377" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146936377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146936377";
};

# المستخدم 58: 0159738201
:do {
    /ip hotspot user add name="0159738201" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159738201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159738201";
};

# المستخدم 59: 0198801318
:do {
    /ip hotspot user add name="0198801318" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198801318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198801318";
};

# المستخدم 60: 0179805123
:do {
    /ip hotspot user add name="0179805123" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179805123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179805123";
};

# المستخدم 61: 0153249385
:do {
    /ip hotspot user add name="0153249385" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153249385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153249385";
};

# المستخدم 62: 0152214390
:do {
    /ip hotspot user add name="0152214390" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152214390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152214390";
};

# المستخدم 63: 0176063230
:do {
    /ip hotspot user add name="0176063230" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176063230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176063230";
};

# المستخدم 64: 0161241060
:do {
    /ip hotspot user add name="0161241060" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161241060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161241060";
};

# المستخدم 65: 0101971869
:do {
    /ip hotspot user add name="0101971869" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101971869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101971869";
};

# المستخدم 66: 0183883573
:do {
    /ip hotspot user add name="0183883573" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183883573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183883573";
};

# المستخدم 67: 0161160918
:do {
    /ip hotspot user add name="0161160918" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161160918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161160918";
};

# المستخدم 68: 0111004585
:do {
    /ip hotspot user add name="0111004585" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111004585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111004585";
};

# المستخدم 69: 0164708937
:do {
    /ip hotspot user add name="0164708937" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164708937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164708937";
};

# المستخدم 70: 0175010697
:do {
    /ip hotspot user add name="0175010697" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175010697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175010697";
};

# المستخدم 71: 0160982011
:do {
    /ip hotspot user add name="0160982011" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160982011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160982011";
};

# المستخدم 72: 0125100861
:do {
    /ip hotspot user add name="0125100861" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125100861";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125100861";
};

# المستخدم 73: 0124204186
:do {
    /ip hotspot user add name="0124204186" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124204186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124204186";
};

# المستخدم 74: 0138842629
:do {
    /ip hotspot user add name="0138842629" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138842629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138842629";
};

# المستخدم 75: 0126969123
:do {
    /ip hotspot user add name="0126969123" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126969123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126969123";
};

# المستخدم 76: 0143173873
:do {
    /ip hotspot user add name="0143173873" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143173873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143173873";
};

# المستخدم 77: 0104175045
:do {
    /ip hotspot user add name="0104175045" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104175045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104175045";
};

# المستخدم 78: 0140792999
:do {
    /ip hotspot user add name="0140792999" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140792999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140792999";
};

# المستخدم 79: 0172456874
:do {
    /ip hotspot user add name="0172456874" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172456874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172456874";
};

# المستخدم 80: 0132618955
:do {
    /ip hotspot user add name="0132618955" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132618955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132618955";
};

# المستخدم 81: 0183599383
:do {
    /ip hotspot user add name="0183599383" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183599383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183599383";
};

# المستخدم 82: 0155410246
:do {
    /ip hotspot user add name="0155410246" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155410246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155410246";
};

# المستخدم 83: 0170114592
:do {
    /ip hotspot user add name="0170114592" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170114592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170114592";
};

# المستخدم 84: 0156649985
:do {
    /ip hotspot user add name="0156649985" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156649985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156649985";
};

# المستخدم 85: 0147711512
:do {
    /ip hotspot user add name="0147711512" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147711512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147711512";
};

# المستخدم 86: 0121592534
:do {
    /ip hotspot user add name="0121592534" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121592534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121592534";
};

# المستخدم 87: 0128580127
:do {
    /ip hotspot user add name="0128580127" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128580127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128580127";
};

# المستخدم 88: 0160879728
:do {
    /ip hotspot user add name="0160879728" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160879728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160879728";
};

# المستخدم 89: 0123990225
:do {
    /ip hotspot user add name="0123990225" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123990225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123990225";
};

# المستخدم 90: 0157321684
:do {
    /ip hotspot user add name="0157321684" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157321684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157321684";
};

# المستخدم 91: 0159265111
:do {
    /ip hotspot user add name="0159265111" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159265111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159265111";
};

# المستخدم 92: 0131945230
:do {
    /ip hotspot user add name="0131945230" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131945230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131945230";
};

# المستخدم 93: 0170835831
:do {
    /ip hotspot user add name="0170835831" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170835831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170835831";
};

# المستخدم 94: 0138219463
:do {
    /ip hotspot user add name="0138219463" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138219463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138219463";
};

# المستخدم 95: 0106782338
:do {
    /ip hotspot user add name="0106782338" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106782338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106782338";
};

# المستخدم 96: 0161038241
:do {
    /ip hotspot user add name="0161038241" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161038241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161038241";
};

# المستخدم 97: 0198702355
:do {
    /ip hotspot user add name="0198702355" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198702355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198702355";
};

# المستخدم 98: 0140995766
:do {
    /ip hotspot user add name="0140995766" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140995766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140995766";
};

# المستخدم 99: 0146786343
:do {
    /ip hotspot user add name="0146786343" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146786343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146786343";
};

# المستخدم 100: 0169758919
:do {
    /ip hotspot user add name="0169758919" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169758919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169758919";
};

# المستخدم 101: 0126807457
:do {
    /ip hotspot user add name="0126807457" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126807457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126807457";
};

# المستخدم 102: 0121360979
:do {
    /ip hotspot user add name="0121360979" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121360979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121360979";
};

# المستخدم 103: 0100432500
:do {
    /ip hotspot user add name="0100432500" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100432500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100432500";
};

# المستخدم 104: 0169462813
:do {
    /ip hotspot user add name="0169462813" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169462813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169462813";
};

# المستخدم 105: 0189797056
:do {
    /ip hotspot user add name="0189797056" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189797056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189797056";
};

# المستخدم 106: 0161678610
:do {
    /ip hotspot user add name="0161678610" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161678610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161678610";
};

# المستخدم 107: 0186327581
:do {
    /ip hotspot user add name="0186327581" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186327581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186327581";
};

# المستخدم 108: 0111711549
:do {
    /ip hotspot user add name="0111711549" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111711549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111711549";
};

# المستخدم 109: 0179243748
:do {
    /ip hotspot user add name="0179243748" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179243748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179243748";
};

# المستخدم 110: 0175008175
:do {
    /ip hotspot user add name="0175008175" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175008175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175008175";
};

# المستخدم 111: 0132169977
:do {
    /ip hotspot user add name="0132169977" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132169977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132169977";
};

# المستخدم 112: 0145144539
:do {
    /ip hotspot user add name="0145144539" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145144539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145144539";
};

# المستخدم 113: 0156457921
:do {
    /ip hotspot user add name="0156457921" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156457921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156457921";
};

# المستخدم 114: 0123651569
:do {
    /ip hotspot user add name="0123651569" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123651569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123651569";
};

# المستخدم 115: 0143554107
:do {
    /ip hotspot user add name="0143554107" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143554107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143554107";
};

# المستخدم 116: 0196969721
:do {
    /ip hotspot user add name="0196969721" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196969721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196969721";
};

# المستخدم 117: 0165249776
:do {
    /ip hotspot user add name="0165249776" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165249776";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165249776";
};

# المستخدم 118: 0128652981
:do {
    /ip hotspot user add name="0128652981" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128652981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128652981";
};

# المستخدم 119: 0153221870
:do {
    /ip hotspot user add name="0153221870" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153221870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153221870";
};

# المستخدم 120: 0130499047
:do {
    /ip hotspot user add name="0130499047" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130499047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130499047";
};

# المستخدم 121: 0169325935
:do {
    /ip hotspot user add name="0169325935" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169325935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169325935";
};

# المستخدم 122: 0159671737
:do {
    /ip hotspot user add name="0159671737" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159671737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159671737";
};

# المستخدم 123: 0127906094
:do {
    /ip hotspot user add name="0127906094" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127906094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127906094";
};

# المستخدم 124: 0119866161
:do {
    /ip hotspot user add name="0119866161" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119866161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119866161";
};

# المستخدم 125: 0196132196
:do {
    /ip hotspot user add name="0196132196" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196132196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196132196";
};

# المستخدم 126: 0163221177
:do {
    /ip hotspot user add name="0163221177" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163221177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163221177";
};

# المستخدم 127: 0152365344
:do {
    /ip hotspot user add name="0152365344" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152365344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152365344";
};

# المستخدم 128: 0173277529
:do {
    /ip hotspot user add name="0173277529" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173277529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173277529";
};

# المستخدم 129: 0166096744
:do {
    /ip hotspot user add name="0166096744" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166096744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166096744";
};

# المستخدم 130: 0145546679
:do {
    /ip hotspot user add name="0145546679" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145546679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145546679";
};

# المستخدم 131: 0188978354
:do {
    /ip hotspot user add name="0188978354" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188978354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188978354";
};

# المستخدم 132: 0167212053
:do {
    /ip hotspot user add name="0167212053" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167212053";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167212053";
};

# المستخدم 133: 0186547980
:do {
    /ip hotspot user add name="0186547980" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186547980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186547980";
};

# المستخدم 134: 0169921911
:do {
    /ip hotspot user add name="0169921911" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169921911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169921911";
};

# المستخدم 135: 0121969334
:do {
    /ip hotspot user add name="0121969334" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121969334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121969334";
};

# المستخدم 136: 0161675625
:do {
    /ip hotspot user add name="0161675625" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161675625";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161675625";
};

# المستخدم 137: 0141029498
:do {
    /ip hotspot user add name="0141029498" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141029498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141029498";
};

# المستخدم 138: 0114117356
:do {
    /ip hotspot user add name="0114117356" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114117356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114117356";
};

# المستخدم 139: 0172057948
:do {
    /ip hotspot user add name="0172057948" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172057948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172057948";
};

# المستخدم 140: 0109282831
:do {
    /ip hotspot user add name="0109282831" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109282831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109282831";
};

# المستخدم 141: 0133426510
:do {
    /ip hotspot user add name="0133426510" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133426510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133426510";
};

# المستخدم 142: 0144977238
:do {
    /ip hotspot user add name="0144977238" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144977238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144977238";
};

# المستخدم 143: 0113727930
:do {
    /ip hotspot user add name="0113727930" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113727930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113727930";
};

# المستخدم 144: 0192856980
:do {
    /ip hotspot user add name="0192856980" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192856980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192856980";
};

# المستخدم 145: 0127805480
:do {
    /ip hotspot user add name="0127805480" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127805480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127805480";
};

# المستخدم 146: 0180846997
:do {
    /ip hotspot user add name="0180846997" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180846997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180846997";
};

# المستخدم 147: 0188782515
:do {
    /ip hotspot user add name="0188782515" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188782515";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188782515";
};

# المستخدم 148: 0108532425
:do {
    /ip hotspot user add name="0108532425" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108532425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108532425";
};

# المستخدم 149: 0110012140
:do {
    /ip hotspot user add name="0110012140" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110012140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110012140";
};

# المستخدم 150: 0193526108
:do {
    /ip hotspot user add name="0193526108" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193526108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193526108";
};

# المستخدم 151: 0103016165
:do {
    /ip hotspot user add name="0103016165" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103016165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103016165";
};

# المستخدم 152: 0193684425
:do {
    /ip hotspot user add name="0193684425" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193684425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193684425";
};

# المستخدم 153: 0130959921
:do {
    /ip hotspot user add name="0130959921" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130959921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130959921";
};

# المستخدم 154: 0127844191
:do {
    /ip hotspot user add name="0127844191" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127844191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127844191";
};

# المستخدم 155: 0179983772
:do {
    /ip hotspot user add name="0179983772" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179983772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179983772";
};

# المستخدم 156: 0138544941
:do {
    /ip hotspot user add name="0138544941" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138544941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138544941";
};

# المستخدم 157: 0134059400
:do {
    /ip hotspot user add name="0134059400" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134059400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134059400";
};

# المستخدم 158: 0181496931
:do {
    /ip hotspot user add name="0181496931" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181496931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181496931";
};

# المستخدم 159: 0190603577
:do {
    /ip hotspot user add name="0190603577" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190603577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190603577";
};

# المستخدم 160: 0131167559
:do {
    /ip hotspot user add name="0131167559" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131167559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131167559";
};

# المستخدم 161: 0135362472
:do {
    /ip hotspot user add name="0135362472" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135362472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135362472";
};

# المستخدم 162: 0165408577
:do {
    /ip hotspot user add name="0165408577" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165408577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165408577";
};

# المستخدم 163: 0109605403
:do {
    /ip hotspot user add name="0109605403" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109605403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109605403";
};

# المستخدم 164: 0145733098
:do {
    /ip hotspot user add name="0145733098" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145733098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145733098";
};

# المستخدم 165: 0191689756
:do {
    /ip hotspot user add name="0191689756" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191689756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191689756";
};

# المستخدم 166: 0145910032
:do {
    /ip hotspot user add name="0145910032" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145910032";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145910032";
};

# المستخدم 167: 0169255277
:do {
    /ip hotspot user add name="0169255277" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169255277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169255277";
};

# المستخدم 168: 0137462453
:do {
    /ip hotspot user add name="0137462453" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137462453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137462453";
};

# المستخدم 169: 0132165904
:do {
    /ip hotspot user add name="0132165904" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132165904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132165904";
};

# المستخدم 170: 0134033208
:do {
    /ip hotspot user add name="0134033208" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134033208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134033208";
};

# المستخدم 171: 0107089291
:do {
    /ip hotspot user add name="0107089291" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107089291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107089291";
};

# المستخدم 172: 0191969830
:do {
    /ip hotspot user add name="0191969830" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191969830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191969830";
};

# المستخدم 173: 0183491780
:do {
    /ip hotspot user add name="0183491780" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183491780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183491780";
};

# المستخدم 174: 0101094165
:do {
    /ip hotspot user add name="0101094165" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101094165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101094165";
};

# المستخدم 175: 0140048720
:do {
    /ip hotspot user add name="0140048720" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140048720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140048720";
};

# المستخدم 176: 0143773714
:do {
    /ip hotspot user add name="0143773714" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143773714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143773714";
};

# المستخدم 177: 0183114024
:do {
    /ip hotspot user add name="0183114024" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183114024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183114024";
};

# المستخدم 178: 0130243940
:do {
    /ip hotspot user add name="0130243940" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130243940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130243940";
};

# المستخدم 179: 0187900438
:do {
    /ip hotspot user add name="0187900438" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187900438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187900438";
};

# المستخدم 180: 0178879329
:do {
    /ip hotspot user add name="0178879329" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178879329";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178879329";
};

# المستخدم 181: 0122006632
:do {
    /ip hotspot user add name="0122006632" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122006632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122006632";
};

# المستخدم 182: 0162919004
:do {
    /ip hotspot user add name="0162919004" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162919004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162919004";
};

# المستخدم 183: 0128245461
:do {
    /ip hotspot user add name="0128245461" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128245461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128245461";
};

# المستخدم 184: 0194445762
:do {
    /ip hotspot user add name="0194445762" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194445762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194445762";
};

# المستخدم 185: 0175299188
:do {
    /ip hotspot user add name="0175299188" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175299188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175299188";
};

# المستخدم 186: 0136563912
:do {
    /ip hotspot user add name="0136563912" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136563912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136563912";
};

# المستخدم 187: 0114000184
:do {
    /ip hotspot user add name="0114000184" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114000184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114000184";
};

# المستخدم 188: 0197480054
:do {
    /ip hotspot user add name="0197480054" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197480054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197480054";
};

# المستخدم 189: 0139987624
:do {
    /ip hotspot user add name="0139987624" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139987624";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139987624";
};

# المستخدم 190: 0176874691
:do {
    /ip hotspot user add name="0176874691" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176874691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176874691";
};

# المستخدم 191: 0186399962
:do {
    /ip hotspot user add name="0186399962" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186399962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186399962";
};

# المستخدم 192: 0156464388
:do {
    /ip hotspot user add name="0156464388" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156464388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156464388";
};

# المستخدم 193: 0158923389
:do {
    /ip hotspot user add name="0158923389" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158923389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158923389";
};

# المستخدم 194: 0129728406
:do {
    /ip hotspot user add name="0129728406" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129728406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129728406";
};

# المستخدم 195: 0115822274
:do {
    /ip hotspot user add name="0115822274" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115822274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115822274";
};

# المستخدم 196: 0124228434
:do {
    /ip hotspot user add name="0124228434" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124228434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124228434";
};

# المستخدم 197: 0175904654
:do {
    /ip hotspot user add name="0175904654" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175904654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175904654";
};

# المستخدم 198: 0114926040
:do {
    /ip hotspot user add name="0114926040" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114926040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114926040";
};

# المستخدم 199: 0181106898
:do {
    /ip hotspot user add name="0181106898" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181106898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181106898";
};

# المستخدم 200: 0188828161
:do {
    /ip hotspot user add name="0188828161" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188828161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188828161";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
