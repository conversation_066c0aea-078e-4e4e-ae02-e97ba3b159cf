#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة التراجع عن عملية البرق (Lightning Undo)
Test Lightning Undo Feature

هذا الاختبار يتحقق من:
1. إظهار زر التراجع فقط عند وجود كروت فاشلة وناجحة
2. حفظ قائمة الكروت المرسلة بنجاح
3. إرسال رسالة تأكيد قبل الحذف
4. تنفيذ عملية الحذف من خادم MikroTik
5. إرسال تقرير النتائج النهائية
6. العمل فقط مع نظام الهوت سبوت والبرق
"""

import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

class TestLightningUndoFeature(unittest.TestCase):
    """اختبار ميزة التراجع عن عملية البرق"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء mock للتطبيق الرئيسي
        self.mock_app = Mock()
        
        # إعداد logger
        self.mock_app.logger = Mock()
        
        # إعداد بيانات التلجرام
        self.mock_app.telegram_bot_token = "test_bot_token"
        self.mock_app.telegram_chat_id = "123456789"
        
        # إعداد نوع النظام
        self.mock_app.system_type = 'hotspot'
        
        # إعداد إحصائيات البرق مع كروت ناجحة وفاشلة
        self.mock_app.last_send_stats = {
            'success': 8,
            'failed': 2,
            'duplicates': 1,
            'total': 10,
            'successful_usernames': [
                'user001', 'user002', 'user003', 'user004', 'user005',
                'user006', 'user007', 'user008'
            ]
        }
        
        # إضافة الدوال المطلوبة
        self.mock_app.save_lightning_successful_cards = self.save_lightning_successful_cards
        self.mock_app.send_lightning_notification_with_undo_button = self.send_lightning_notification_with_undo_button
        self.mock_app.handle_lightning_undo_request = self.handle_lightning_undo_request
        self.mock_app.send_lightning_undo_confirmation = self.send_lightning_undo_confirmation
        self.mock_app.execute_lightning_undo = self.execute_lightning_undo
        self.mock_app.cancel_lightning_undo = self.cancel_lightning_undo
        self.mock_app.send_telegram_message_direct = self.mock_send_telegram_message_direct
        self.mock_app.connect_api = self.mock_connect_api

    def mock_send_telegram_message_direct(self, bot_token, chat_id, message, max_retries=3):
        """Mock لدالة إرسال رسائل التلجرام"""
        self.mock_app.logger.info(f"✅ تم إرسال رسالة التلجرام بنجاح")
        return True

    def mock_connect_api(self):
        """Mock للاتصال بـ MikroTik API"""
        mock_api = Mock()
        
        # محاكاة قائمة المستخدمين الموجودين
        mock_users = []
        for username in self.mock_app.last_send_stats['successful_usernames']:
            mock_users.append({'id': f'*{len(mock_users)+1}', 'name': username})
        
        # محاكاة resource للمستخدمين
        mock_resource = Mock()
        mock_resource.get.return_value = mock_users
        mock_resource.remove = Mock()
        
        mock_api.get_resource.return_value = mock_resource
        mock_api.disconnect = Mock()
        
        return mock_api

    def save_lightning_successful_cards(self):
        """حفظ قائمة الكروت التي تم إرسالها بنجاح في عملية البرق الحالية"""
        try:
            # قراءة قائمة المستخدمين الناجحين من last_send_stats
            successful_usernames = []
            
            if hasattr(self.mock_app, 'last_send_stats'):
                successful_usernames = self.mock_app.last_send_stats.get('successful_usernames', [])
            
            # حفظ القائمة في متغير للاستخدام لاحقاً
            self.mock_app.lightning_successful_cards = successful_usernames.copy()
            
            # حفظ معلومات إضافية للتراجع
            self.mock_app.lightning_undo_info = {
                'timestamp': datetime.now().isoformat(),
                'total_successful': len(successful_usernames),
                'system_type': 'hotspot',
                'operation_type': 'lightning'
            }
            
            self.mock_app.logger.info(f"🗑️ تم حفظ {len(successful_usernames)} كرت للتراجع المحتمل")
            
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في حفظ قائمة الكروت الناجحة للتراجع: {str(e)}")
            self.mock_app.lightning_successful_cards = []

    def send_lightning_notification_with_undo_button(self, bot_token, chat_id, message, success_count):
        """إرسال إشعار البرق مع زر التراجع عن العملية"""
        try:
            # محاكاة إرسال الرسالة مع الزر
            self.mock_app.logger.info(f"✅ تم إرسال إشعار البرق مع زر التراجع بنجاح")
            return True
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إرسال إشعار البرق مع زر التراجع: {str(e)}")
            return False

    def handle_lightning_undo_request(self, bot_token, chat_id, success_count):
        """معالجة طلب التراجع عن عملية البرق - حذف الكروت المرسلة بنجاح"""
        try:
            self.mock_app.logger.info(f"🗑️ بدء معالجة طلب التراجع عن البرق: {success_count} كرت")
            
            # التحقق من وجود قائمة الكروت المرسلة بنجاح
            if not hasattr(self.mock_app, 'lightning_successful_cards') or not self.mock_app.lightning_successful_cards:
                self.mock_app.send_telegram_message_direct(
                    bot_token, chat_id,
                    "❌ **خطأ في التراجع**\n\n"
                    "لا توجد معلومات عن الكروت المرسلة بنجاح في هذه العملية."
                )
                return
            
            # التحقق من أن هذا نظام هوت سبوت فقط
            if self.mock_app.system_type != 'hotspot':
                self.mock_app.send_telegram_message_direct(
                    bot_token, chat_id,
                    "❌ **خطأ في التراجع**\n\n"
                    "ميزة التراجع تعمل فقط مع نظام الهوت سبوت (HotSpot)."
                )
                return
            
            # إرسال رسالة تأكيد قبل الحذف
            cards_to_delete = len(self.mock_app.lightning_successful_cards)
            confirmation_message = f"⚠️ تأكيد التراجع عن العملية - سيتم حذف {cards_to_delete} كرت"
            
            # إرسال رسالة التأكيد مع أزرار الاختيار
            self.mock_app.send_lightning_undo_confirmation(bot_token, chat_id, confirmation_message, cards_to_delete)
            
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في معالجة طلب التراجع عن البرق: {str(e)}")

    def send_lightning_undo_confirmation(self, bot_token, chat_id, message, cards_count):
        """إرسال رسالة تأكيد التراجع مع أزرار الاختيار"""
        try:
            # محاكاة إرسال رسالة التأكيد
            self.mock_app.logger.info(f"✅ تم إرسال رسالة تأكيد التراجع بنجاح")
            return True
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إرسال رسالة تأكيد التراجع: {str(e)}")
            return False

    def execute_lightning_undo(self, bot_token, chat_id, cards_count):
        """تنفيذ عملية التراجع - حذف الكروت من خادم MikroTik"""
        try:
            self.mock_app.logger.info(f"🗑️ بدء تنفيذ التراجع: حذف {cards_count} كرت من MikroTik")
            
            # التحقق من وجود قائمة الكروت
            if not hasattr(self.mock_app, 'lightning_successful_cards') or not self.mock_app.lightning_successful_cards:
                self.mock_app.send_telegram_message_direct(
                    bot_token, chat_id,
                    "❌ **خطأ في التنفيذ**\n\n"
                    "لا توجد معلومات عن الكروت المطلوب حذفها."
                )
                return
            
            # الاتصال بـ MikroTik
            api = self.mock_app.connect_api()
            if not api:
                self.mock_app.send_telegram_message_direct(
                    bot_token, chat_id,
                    "❌ **فشل في الاتصال**\n\n"
                    "لا يمكن الاتصال بخادم MikroTik لتنفيذ عملية الحذف."
                )
                return
            
            # تنفيذ عملية الحذف
            deleted_count = 0
            failed_count = 0
            
            for username in self.mock_app.lightning_successful_cards:
                try:
                    # البحث عن المستخدم في HotSpot
                    users = api.get_resource('/ip/hotspot/user').get(name=username)
                    
                    if users:
                        # حذف المستخدم
                        user_id = users[0]['id']
                        api.get_resource('/ip/hotspot/user').remove(user_id)
                        deleted_count += 1
                        self.mock_app.logger.info(f"✅ تم حذف المستخدم: {username}")
                    else:
                        failed_count += 1
                        self.mock_app.logger.warning(f"⚠️ المستخدم غير موجود: {username}")
                        
                except Exception as user_error:
                    failed_count += 1
                    self.mock_app.logger.error(f"❌ فشل في حذف المستخدم {username}: {str(user_error)}")
            
            # إنشاء رسالة النتيجة
            success_rate = (deleted_count / max(1, len(self.mock_app.lightning_successful_cards))) * 100
            
            result_message = f"""✅ تم تنفيذ عملية التراجع!
            
📊 إحصائيات الحذف:
• إجمالي الكروت المطلوب حذفها: {len(self.mock_app.lightning_successful_cards)}
• الكروت المحذوفة بنجاح: {deleted_count}
• الكروت الفاشلة: {failed_count}
• معدل نجاح الحذف: {success_rate:.1f}%"""

            # إرسال رسالة النتيجة
            self.mock_app.send_telegram_message_direct(bot_token, chat_id, result_message)
            
            # تنظيف البيانات المحفوظة
            self.mock_app.lightning_successful_cards = []
            
            self.mock_app.logger.info(f"🗑️ تم تنفيذ التراجع: حُذف {deleted_count}/{len(self.mock_app.lightning_successful_cards)} كرت")
            
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في تنفيذ التراجع: {str(e)}")

    def cancel_lightning_undo(self, bot_token, chat_id):
        """إلغاء عملية التراجع"""
        try:
            self.mock_app.logger.info("❌ تم إلغاء عملية التراجع عن البرق")
            
            cancel_message = """❌ تم إلغاء عملية التراجع
            
✅ الحالة: لم يتم حذف أي كروت

💡 ملاحظة: جميع الكروت المرسلة بنجاح ما زالت موجودة على خادم MikroTik."""

            self.mock_app.send_telegram_message_direct(bot_token, chat_id, cancel_message)
            
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إلغاء التراجع: {str(e)}")

    def test_undo_button_appears_with_failures(self):
        """اختبار ظهور زر التراجع عند وجود كروت فاشلة وناجحة"""
        
        # إعداد إحصائيات مع كروت ناجحة وفاشلة
        success_count = 8
        failed_count = 2
        
        # التحقق من شرط إظهار الزر
        show_undo_button = (failed_count > 0 and success_count > 0)
        
        self.assertTrue(show_undo_button)
        print("✅ اختبار ظهور زر التراجع مع وجود فشل نجح")

    def test_undo_button_hidden_with_no_failures(self):
        """اختبار عدم ظهور زر التراجع عند عدم وجود كروت فاشلة"""
        
        # إعداد إحصائيات بدون كروت فاشلة
        success_count = 10
        failed_count = 0
        
        # التحقق من شرط إظهار الزر
        show_undo_button = (failed_count > 0 and success_count > 0)
        
        self.assertFalse(show_undo_button)
        print("✅ اختبار عدم ظهور زر التراجع بدون فشل نجح")

    def test_save_successful_cards(self):
        """اختبار حفظ قائمة الكروت الناجحة"""
        
        # تنفيذ حفظ الكروت الناجحة
        self.mock_app.save_lightning_successful_cards()
        
        # التحقق من النتيجة
        self.assertTrue(hasattr(self.mock_app, 'lightning_successful_cards'))
        self.assertEqual(len(self.mock_app.lightning_successful_cards), 8)
        self.assertIn('user001', self.mock_app.lightning_successful_cards)
        self.assertIn('user008', self.mock_app.lightning_successful_cards)
        
        print("✅ اختبار حفظ قائمة الكروت الناجحة نجح")

    def test_undo_request_handling(self):
        """اختبار معالجة طلب التراجع"""
        
        # حفظ الكروت الناجحة أولاً
        self.mock_app.save_lightning_successful_cards()
        
        # معالجة طلب التراجع
        bot_token = "test_token"
        chat_id = "123456789"
        success_count = 8
        
        # تنفيذ الاختبار
        self.mock_app.handle_lightning_undo_request(bot_token, chat_id, success_count)
        
        # التحقق من أن العملية تمت بدون أخطاء
        self.assertTrue(True)  # إذا وصلنا هنا فالاختبار نجح
        
        print("✅ اختبار معالجة طلب التراجع نجح")

    def test_undo_execution(self):
        """اختبار تنفيذ عملية التراجع"""
        
        # حفظ الكروت الناجحة أولاً
        self.mock_app.save_lightning_successful_cards()
        
        # تنفيذ عملية التراجع
        bot_token = "test_token"
        chat_id = "123456789"
        cards_count = 8
        
        # تنفيذ الاختبار
        self.mock_app.execute_lightning_undo(bot_token, chat_id, cards_count)
        
        # التحقق من تنظيف البيانات
        self.assertEqual(len(self.mock_app.lightning_successful_cards), 0)
        
        print("✅ اختبار تنفيذ عملية التراجع نجح")

    def test_undo_cancellation(self):
        """اختبار إلغاء عملية التراجع"""
        
        # تنفيذ إلغاء التراجع
        bot_token = "test_token"
        chat_id = "123456789"
        
        # تنفيذ الاختبار
        self.mock_app.cancel_lightning_undo(bot_token, chat_id)
        
        # التحقق من أن العملية تمت بدون أخطاء
        self.assertTrue(True)  # إذا وصلنا هنا فالاختبار نجح
        
        print("✅ اختبار إلغاء عملية التراجع نجح")

    def test_hotspot_only_restriction(self):
        """اختبار أن الميزة تعمل فقط مع نظام الهوت سبوت"""
        
        # تغيير نوع النظام إلى User Manager
        self.mock_app.system_type = 'user_manager'
        
        # حفظ الكروت الناجحة
        self.mock_app.save_lightning_successful_cards()
        
        # محاولة معالجة طلب التراجع
        bot_token = "test_token"
        chat_id = "123456789"
        success_count = 8
        
        # تنفيذ الاختبار
        self.mock_app.handle_lightning_undo_request(bot_token, chat_id, success_count)
        
        # التحقق من أن العملية تمت بدون أخطاء (سيتم رفضها داخلياً)
        self.assertTrue(True)
        
        print("✅ اختبار قيد الهوت سبوت فقط نجح")

if __name__ == '__main__':
    print("🧪 بدء اختبارات ميزة التراجع عن عملية البرق...")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 60)
    print("🎉 انتهت جميع الاختبارات بنجاح!")
