#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة استيراد time
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن مشكلة time.time() تم حلها
"""

import re

def test_time_import_in_functions():
    """اختبار استيراد time في الدوال المطلوبة"""
    print("🔍 اختبار استيراد time في الدوال...")
    
    original_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # الدوال التي تستخدم time
    functions_using_time = [
        'handle_delete_users_by_email_request',
        'execute_delete_users_by_email'
    ]
    
    for func_name in functions_using_time:
        # البحث عن الدالة
        func_match = re.search(f'def {func_name}.*?(?=def|\\Z)', content, re.DOTALL)
        if not func_match:
            print(f"❌ لم يتم العثور على الدالة: {func_name}")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من وجود import time
        if 'import time' not in func_code:
            print(f"❌ الدالة {func_name} لا تحتوي على import time")
            return False
        
        # التحقق من استخدام time.time()
        if 'time.time()' in func_code:
            print(f"✅ الدالة {func_name} تحتوي على import time و time.time()")
        else:
            print(f"✅ الدالة {func_name} تحتوي على import time")
    
    return True

def test_time_strftime_usage():
    """اختبار استخدام time.strftime"""
    print("\n🔍 اختبار استخدام time.strftime...")
    
    original_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن استخدام time.strftime
    strftime_pattern = r'time\.strftime\([^)]+\)'
    strftime_matches = re.findall(strftime_pattern, content)
    
    for match in strftime_matches:
        # التحقق من الاستخدام الصحيح
        if 'time.localtime()' in match or '%Y-%m-%d %H:%M:%S' not in match:
            print(f"✅ استخدام صحيح لـ time.strftime: {match}")
        else:
            print(f"⚠️ قد يحتاج تحسين: {match}")
    
    if strftime_matches:
        print(f"✅ تم العثور على {len(strftime_matches)} استخدام لـ time.strftime")
    else:
        print("ℹ️ لم يتم العثور على استخدام لـ time.strftime")
    
    return True

def test_no_undefined_time_usage():
    """اختبار عدم وجود استخدام غير معرف لـ time"""
    print("\n🔍 اختبار عدم وجود استخدام غير معرف لـ time...")
    
    original_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # تقسيم المحتوى إلى دوال
    functions = re.findall(r'def [^(]+\([^)]*\):.*?(?=def|\Z)', content, re.DOTALL)
    
    problematic_functions = []
    
    for func in functions:
        # البحث عن استخدام time. في الدالة
        time_usage = re.findall(r'time\.[a-zA-Z_]+', func)
        
        if time_usage:
            # التحقق من وجود import time في الدالة
            if 'import time' not in func:
                # استخراج اسم الدالة
                func_name_match = re.search(r'def ([^(]+)\(', func)
                if func_name_match:
                    func_name = func_name_match.group(1)
                    problematic_functions.append(func_name)
    
    if problematic_functions:
        print(f"❌ دوال تستخدم time بدون import: {', '.join(problematic_functions)}")
        return False
    else:
        print("✅ جميع الدوال التي تستخدم time تحتوي على import time")
        return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح مشكلة استيراد time")
    print("="*50)
    
    tests = [
        ("استيراد time في الدوال", test_time_import_in_functions),
        ("استخدام time.strftime", test_time_strftime_usage),
        ("عدم وجود استخدام غير معرف", test_no_undefined_time_usage)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*50)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح مشكلة استيراد time بنجاح!")
        print("💡 الميزة جاهزة للاستخدام بدون أخطاء time")
    else:
        print("⚠️ لا تزال هناك مشاكل في استيراد time.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
