# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 16:37:35
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0189469904
:do {
    /tool user-manager user add customer="adm8n" username="0189469904" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189469904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189469904";
};

# المستخدم 2: 0108045668
:do {
    /tool user-manager user add customer="adm8n" username="0108045668" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108045668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108045668";
};

# المستخدم 3: 0185773775
:do {
    /tool user-manager user add customer="adm8n" username="0185773775" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185773775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185773775";
};

# المستخدم 4: 0133281742
:do {
    /tool user-manager user add customer="adm8n" username="0133281742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133281742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133281742";
};

# المستخدم 5: 0192958889
:do {
    /tool user-manager user add customer="adm8n" username="0192958889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192958889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192958889";
};

# المستخدم 6: 0104994790
:do {
    /tool user-manager user add customer="adm8n" username="0104994790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104994790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104994790";
};

# المستخدم 7: 0118988661
:do {
    /tool user-manager user add customer="adm8n" username="0118988661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118988661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118988661";
};

# المستخدم 8: 0196941256
:do {
    /tool user-manager user add customer="adm8n" username="0196941256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196941256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196941256";
};

# المستخدم 9: 0189263711
:do {
    /tool user-manager user add customer="adm8n" username="0189263711" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189263711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189263711";
};

# المستخدم 10: 0164367586
:do {
    /tool user-manager user add customer="adm8n" username="0164367586" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164367586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164367586";
};

# المستخدم 11: 0162129524
:do {
    /tool user-manager user add customer="adm8n" username="0162129524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162129524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162129524";
};

# المستخدم 12: 0100600906
:do {
    /tool user-manager user add customer="adm8n" username="0100600906" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100600906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100600906";
};

# المستخدم 13: 0177635276
:do {
    /tool user-manager user add customer="adm8n" username="0177635276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177635276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177635276";
};

# المستخدم 14: 0155379141
:do {
    /tool user-manager user add customer="adm8n" username="0155379141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155379141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155379141";
};

# المستخدم 15: 0170847645
:do {
    /tool user-manager user add customer="adm8n" username="0170847645" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170847645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170847645";
};

# المستخدم 16: 0111210566
:do {
    /tool user-manager user add customer="adm8n" username="0111210566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111210566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111210566";
};

# المستخدم 17: 0141621759
:do {
    /tool user-manager user add customer="adm8n" username="0141621759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141621759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141621759";
};

# المستخدم 18: 0159872158
:do {
    /tool user-manager user add customer="adm8n" username="0159872158" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159872158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159872158";
};

# المستخدم 19: 0131683046
:do {
    /tool user-manager user add customer="adm8n" username="0131683046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131683046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131683046";
};

# المستخدم 20: 0187820227
:do {
    /tool user-manager user add customer="adm8n" username="0187820227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187820227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187820227";
};

# المستخدم 21: 0147310379
:do {
    /tool user-manager user add customer="adm8n" username="0147310379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147310379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147310379";
};

# المستخدم 22: 0147542377
:do {
    /tool user-manager user add customer="adm8n" username="0147542377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147542377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147542377";
};

# المستخدم 23: 0105061827
:do {
    /tool user-manager user add customer="adm8n" username="0105061827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105061827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105061827";
};

# المستخدم 24: 0119315536
:do {
    /tool user-manager user add customer="adm8n" username="0119315536" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119315536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119315536";
};

# المستخدم 25: 0162065654
:do {
    /tool user-manager user add customer="adm8n" username="0162065654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162065654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162065654";
};

# المستخدم 26: 0164836217
:do {
    /tool user-manager user add customer="adm8n" username="0164836217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164836217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164836217";
};

# المستخدم 27: 0192022556
:do {
    /tool user-manager user add customer="adm8n" username="0192022556" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192022556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192022556";
};

# المستخدم 28: 0103587655
:do {
    /tool user-manager user add customer="adm8n" username="0103587655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103587655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103587655";
};

# المستخدم 29: 0136676821
:do {
    /tool user-manager user add customer="adm8n" username="0136676821" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136676821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136676821";
};

# المستخدم 30: 0126583799
:do {
    /tool user-manager user add customer="adm8n" username="0126583799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126583799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126583799";
};

# المستخدم 31: 0140606955
:do {
    /tool user-manager user add customer="adm8n" username="0140606955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140606955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140606955";
};

# المستخدم 32: 0116628093
:do {
    /tool user-manager user add customer="adm8n" username="0116628093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116628093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116628093";
};

# المستخدم 33: 0127772510
:do {
    /tool user-manager user add customer="adm8n" username="0127772510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127772510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127772510";
};

# المستخدم 34: 0101669515
:do {
    /tool user-manager user add customer="adm8n" username="0101669515" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101669515";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101669515";
};

# المستخدم 35: 0188278286
:do {
    /tool user-manager user add customer="adm8n" username="0188278286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188278286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188278286";
};

# المستخدم 36: 0118382760
:do {
    /tool user-manager user add customer="adm8n" username="0118382760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118382760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118382760";
};

# المستخدم 37: 0185458588
:do {
    /tool user-manager user add customer="adm8n" username="0185458588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185458588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185458588";
};

# المستخدم 38: 0194857072
:do {
    /tool user-manager user add customer="adm8n" username="0194857072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194857072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194857072";
};

# المستخدم 39: 0152111724
:do {
    /tool user-manager user add customer="adm8n" username="0152111724" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152111724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152111724";
};

# المستخدم 40: 0107874686
:do {
    /tool user-manager user add customer="adm8n" username="0107874686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107874686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107874686";
};

# المستخدم 41: 0142221123
:do {
    /tool user-manager user add customer="adm8n" username="0142221123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142221123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142221123";
};

# المستخدم 42: 0194652241
:do {
    /tool user-manager user add customer="adm8n" username="0194652241" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194652241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194652241";
};

# المستخدم 43: 0174851805
:do {
    /tool user-manager user add customer="adm8n" username="0174851805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174851805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174851805";
};

# المستخدم 44: 0178071464
:do {
    /tool user-manager user add customer="adm8n" username="0178071464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178071464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178071464";
};

# المستخدم 45: 0115184004
:do {
    /tool user-manager user add customer="adm8n" username="0115184004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115184004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115184004";
};

# المستخدم 46: 0144972603
:do {
    /tool user-manager user add customer="adm8n" username="0144972603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144972603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144972603";
};

# المستخدم 47: 0130192524
:do {
    /tool user-manager user add customer="adm8n" username="0130192524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130192524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130192524";
};

# المستخدم 48: 0101841812
:do {
    /tool user-manager user add customer="adm8n" username="0101841812" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101841812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101841812";
};

# المستخدم 49: 0150984902
:do {
    /tool user-manager user add customer="adm8n" username="0150984902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150984902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150984902";
};

# المستخدم 50: 0178242085
:do {
    /tool user-manager user add customer="adm8n" username="0178242085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178242085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178242085";
};

# المستخدم 51: 0157633834
:do {
    /tool user-manager user add customer="adm8n" username="0157633834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157633834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157633834";
};

# المستخدم 52: 0172279086
:do {
    /tool user-manager user add customer="adm8n" username="0172279086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172279086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172279086";
};

# المستخدم 53: 0181780688
:do {
    /tool user-manager user add customer="adm8n" username="0181780688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181780688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181780688";
};

# المستخدم 54: 0103513184
:do {
    /tool user-manager user add customer="adm8n" username="0103513184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103513184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103513184";
};

# المستخدم 55: 0137006127
:do {
    /tool user-manager user add customer="adm8n" username="0137006127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137006127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137006127";
};

# المستخدم 56: 0162339824
:do {
    /tool user-manager user add customer="adm8n" username="0162339824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162339824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162339824";
};

# المستخدم 57: 0197572905
:do {
    /tool user-manager user add customer="adm8n" username="0197572905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197572905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197572905";
};

# المستخدم 58: 0122217795
:do {
    /tool user-manager user add customer="adm8n" username="0122217795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122217795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122217795";
};

# المستخدم 59: 0169463126
:do {
    /tool user-manager user add customer="adm8n" username="0169463126" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169463126";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169463126";
};

# المستخدم 60: 0161925780
:do {
    /tool user-manager user add customer="adm8n" username="0161925780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161925780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161925780";
};

# المستخدم 61: 0142764167
:do {
    /tool user-manager user add customer="adm8n" username="0142764167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142764167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142764167";
};

# المستخدم 62: 0169477098
:do {
    /tool user-manager user add customer="adm8n" username="0169477098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169477098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169477098";
};

# المستخدم 63: 0198345270
:do {
    /tool user-manager user add customer="adm8n" username="0198345270" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198345270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198345270";
};

# المستخدم 64: 0154896815
:do {
    /tool user-manager user add customer="adm8n" username="0154896815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154896815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154896815";
};

# المستخدم 65: 0180379591
:do {
    /tool user-manager user add customer="adm8n" username="0180379591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180379591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180379591";
};

# المستخدم 66: 0133258702
:do {
    /tool user-manager user add customer="adm8n" username="0133258702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133258702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133258702";
};

# المستخدم 67: 0101062704
:do {
    /tool user-manager user add customer="adm8n" username="0101062704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101062704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101062704";
};

# المستخدم 68: 0127194136
:do {
    /tool user-manager user add customer="adm8n" username="0127194136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127194136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127194136";
};

# المستخدم 69: 0105580227
:do {
    /tool user-manager user add customer="adm8n" username="0105580227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105580227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105580227";
};

# المستخدم 70: 0146189222
:do {
    /tool user-manager user add customer="adm8n" username="0146189222" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146189222";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146189222";
};

# المستخدم 71: 0198679287
:do {
    /tool user-manager user add customer="adm8n" username="0198679287" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198679287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198679287";
};

# المستخدم 72: 0128270427
:do {
    /tool user-manager user add customer="adm8n" username="0128270427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128270427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128270427";
};

# المستخدم 73: 0172551994
:do {
    /tool user-manager user add customer="adm8n" username="0172551994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172551994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172551994";
};

# المستخدم 74: 0102172895
:do {
    /tool user-manager user add customer="adm8n" username="0102172895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102172895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102172895";
};

# المستخدم 75: 0199915831
:do {
    /tool user-manager user add customer="adm8n" username="0199915831" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199915831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199915831";
};

# المستخدم 76: 0111244473
:do {
    /tool user-manager user add customer="adm8n" username="0111244473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111244473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111244473";
};

# المستخدم 77: 0136070893
:do {
    /tool user-manager user add customer="adm8n" username="0136070893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136070893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136070893";
};

# المستخدم 78: 0132395016
:do {
    /tool user-manager user add customer="adm8n" username="0132395016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132395016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132395016";
};

# المستخدم 79: 0184898561
:do {
    /tool user-manager user add customer="adm8n" username="0184898561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184898561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184898561";
};

# المستخدم 80: 0113974565
:do {
    /tool user-manager user add customer="adm8n" username="0113974565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113974565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113974565";
};

# المستخدم 81: 0170321139
:do {
    /tool user-manager user add customer="adm8n" username="0170321139" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170321139";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170321139";
};

# المستخدم 82: 0170022930
:do {
    /tool user-manager user add customer="adm8n" username="0170022930" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170022930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170022930";
};

# المستخدم 83: 0163722504
:do {
    /tool user-manager user add customer="adm8n" username="0163722504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163722504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163722504";
};

# المستخدم 84: 0107632549
:do {
    /tool user-manager user add customer="adm8n" username="0107632549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107632549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107632549";
};

# المستخدم 85: 0177382408
:do {
    /tool user-manager user add customer="adm8n" username="0177382408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177382408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177382408";
};

# المستخدم 86: 0109810590
:do {
    /tool user-manager user add customer="adm8n" username="0109810590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109810590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109810590";
};

# المستخدم 87: 0185324574
:do {
    /tool user-manager user add customer="adm8n" username="0185324574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185324574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185324574";
};

# المستخدم 88: 0135914820
:do {
    /tool user-manager user add customer="adm8n" username="0135914820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135914820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135914820";
};

# المستخدم 89: 0158688258
:do {
    /tool user-manager user add customer="adm8n" username="0158688258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158688258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158688258";
};

# المستخدم 90: 0144657108
:do {
    /tool user-manager user add customer="adm8n" username="0144657108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144657108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144657108";
};

# المستخدم 91: 0118517520
:do {
    /tool user-manager user add customer="adm8n" username="0118517520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118517520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118517520";
};

# المستخدم 92: 0199002420
:do {
    /tool user-manager user add customer="adm8n" username="0199002420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199002420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199002420";
};

# المستخدم 93: 0184857046
:do {
    /tool user-manager user add customer="adm8n" username="0184857046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184857046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184857046";
};

# المستخدم 94: 0160959033
:do {
    /tool user-manager user add customer="adm8n" username="0160959033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160959033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160959033";
};

# المستخدم 95: 0173293173
:do {
    /tool user-manager user add customer="adm8n" username="0173293173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173293173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173293173";
};

# المستخدم 96: 0164451597
:do {
    /tool user-manager user add customer="adm8n" username="0164451597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164451597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164451597";
};

# المستخدم 97: 0145786718
:do {
    /tool user-manager user add customer="adm8n" username="0145786718" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145786718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145786718";
};

# المستخدم 98: 0180496091
:do {
    /tool user-manager user add customer="adm8n" username="0180496091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180496091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180496091";
};

# المستخدم 99: 0190663651
:do {
    /tool user-manager user add customer="adm8n" username="0190663651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190663651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190663651";
};

# المستخدم 100: 0155974964
:do {
    /tool user-manager user add customer="adm8n" username="0155974964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155974964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155974964";
};

# المستخدم 101: 0181020826
:do {
    /tool user-manager user add customer="adm8n" username="0181020826" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181020826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181020826";
};

# المستخدم 102: 0108541818
:do {
    /tool user-manager user add customer="adm8n" username="0108541818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108541818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108541818";
};

# المستخدم 103: 0177800524
:do {
    /tool user-manager user add customer="adm8n" username="0177800524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177800524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177800524";
};

# المستخدم 104: 0130135259
:do {
    /tool user-manager user add customer="adm8n" username="0130135259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130135259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130135259";
};

# المستخدم 105: 0130537018
:do {
    /tool user-manager user add customer="adm8n" username="0130537018" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130537018";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130537018";
};

# المستخدم 106: 0116603701
:do {
    /tool user-manager user add customer="adm8n" username="0116603701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116603701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116603701";
};

# المستخدم 107: 0116205737
:do {
    /tool user-manager user add customer="adm8n" username="0116205737" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116205737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116205737";
};

# المستخدم 108: 0173269719
:do {
    /tool user-manager user add customer="adm8n" username="0173269719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173269719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173269719";
};

# المستخدم 109: 0131500319
:do {
    /tool user-manager user add customer="adm8n" username="0131500319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131500319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131500319";
};

# المستخدم 110: 0154642065
:do {
    /tool user-manager user add customer="adm8n" username="0154642065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154642065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154642065";
};

# المستخدم 111: 0168545629
:do {
    /tool user-manager user add customer="adm8n" username="0168545629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168545629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168545629";
};

# المستخدم 112: 0155887565
:do {
    /tool user-manager user add customer="adm8n" username="0155887565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155887565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155887565";
};

# المستخدم 113: 0158330736
:do {
    /tool user-manager user add customer="adm8n" username="0158330736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158330736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158330736";
};

# المستخدم 114: 0100963066
:do {
    /tool user-manager user add customer="adm8n" username="0100963066" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100963066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100963066";
};

# المستخدم 115: 0171008697
:do {
    /tool user-manager user add customer="adm8n" username="0171008697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171008697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171008697";
};

# المستخدم 116: 0171527029
:do {
    /tool user-manager user add customer="adm8n" username="0171527029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171527029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171527029";
};

# المستخدم 117: 0104645674
:do {
    /tool user-manager user add customer="adm8n" username="0104645674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104645674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104645674";
};

# المستخدم 118: 0199165674
:do {
    /tool user-manager user add customer="adm8n" username="0199165674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199165674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199165674";
};

# المستخدم 119: 0156619174
:do {
    /tool user-manager user add customer="adm8n" username="0156619174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156619174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156619174";
};

# المستخدم 120: 0169362867
:do {
    /tool user-manager user add customer="adm8n" username="0169362867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169362867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169362867";
};

# المستخدم 121: 0154271937
:do {
    /tool user-manager user add customer="adm8n" username="0154271937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154271937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154271937";
};

# المستخدم 122: 0152485561
:do {
    /tool user-manager user add customer="adm8n" username="0152485561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152485561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152485561";
};

# المستخدم 123: 0172434357
:do {
    /tool user-manager user add customer="adm8n" username="0172434357" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172434357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172434357";
};

# المستخدم 124: 0154775896
:do {
    /tool user-manager user add customer="adm8n" username="0154775896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154775896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154775896";
};

# المستخدم 125: 0180897468
:do {
    /tool user-manager user add customer="adm8n" username="0180897468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180897468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180897468";
};

# المستخدم 126: 0160034166
:do {
    /tool user-manager user add customer="adm8n" username="0160034166" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160034166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160034166";
};

# المستخدم 127: 0167859003
:do {
    /tool user-manager user add customer="adm8n" username="0167859003" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167859003";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167859003";
};

# المستخدم 128: 0193897468
:do {
    /tool user-manager user add customer="adm8n" username="0193897468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193897468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193897468";
};

# المستخدم 129: 0169912120
:do {
    /tool user-manager user add customer="adm8n" username="0169912120" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169912120";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169912120";
};

# المستخدم 130: 0102523722
:do {
    /tool user-manager user add customer="adm8n" username="0102523722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102523722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102523722";
};

# المستخدم 131: 0172984114
:do {
    /tool user-manager user add customer="adm8n" username="0172984114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172984114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172984114";
};

# المستخدم 132: 0196279672
:do {
    /tool user-manager user add customer="adm8n" username="0196279672" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196279672";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196279672";
};

# المستخدم 133: 0152083637
:do {
    /tool user-manager user add customer="adm8n" username="0152083637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152083637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152083637";
};

# المستخدم 134: 0124423316
:do {
    /tool user-manager user add customer="adm8n" username="0124423316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124423316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124423316";
};

# المستخدم 135: 0179410427
:do {
    /tool user-manager user add customer="adm8n" username="0179410427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179410427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179410427";
};

# المستخدم 136: 0198254789
:do {
    /tool user-manager user add customer="adm8n" username="0198254789" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198254789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198254789";
};

# المستخدم 137: 0126278180
:do {
    /tool user-manager user add customer="adm8n" username="0126278180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126278180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126278180";
};

# المستخدم 138: 0158622281
:do {
    /tool user-manager user add customer="adm8n" username="0158622281" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158622281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158622281";
};

# المستخدم 139: 0108367946
:do {
    /tool user-manager user add customer="adm8n" username="0108367946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108367946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108367946";
};

# المستخدم 140: 0195252799
:do {
    /tool user-manager user add customer="adm8n" username="0195252799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195252799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195252799";
};

# المستخدم 141: 0116074952
:do {
    /tool user-manager user add customer="adm8n" username="0116074952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116074952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116074952";
};

# المستخدم 142: 0199097231
:do {
    /tool user-manager user add customer="adm8n" username="0199097231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199097231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199097231";
};

# المستخدم 143: 0145245313
:do {
    /tool user-manager user add customer="adm8n" username="0145245313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145245313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145245313";
};

# المستخدم 144: 0114623664
:do {
    /tool user-manager user add customer="adm8n" username="0114623664" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114623664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114623664";
};

# المستخدم 145: 0191830175
:do {
    /tool user-manager user add customer="adm8n" username="0191830175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191830175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191830175";
};

# المستخدم 146: 0139887791
:do {
    /tool user-manager user add customer="adm8n" username="0139887791" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139887791";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139887791";
};

# المستخدم 147: 0166890009
:do {
    /tool user-manager user add customer="adm8n" username="0166890009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166890009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166890009";
};

# المستخدم 148: 0148075346
:do {
    /tool user-manager user add customer="adm8n" username="0148075346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148075346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148075346";
};

# المستخدم 149: 0191622477
:do {
    /tool user-manager user add customer="adm8n" username="0191622477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191622477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191622477";
};

# المستخدم 150: 0167186820
:do {
    /tool user-manager user add customer="adm8n" username="0167186820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167186820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167186820";
};

# المستخدم 151: 0169770388
:do {
    /tool user-manager user add customer="adm8n" username="0169770388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169770388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169770388";
};

# المستخدم 152: 0155946187
:do {
    /tool user-manager user add customer="adm8n" username="0155946187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155946187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155946187";
};

# المستخدم 153: 0179062004
:do {
    /tool user-manager user add customer="adm8n" username="0179062004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179062004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179062004";
};

# المستخدم 154: 0190392866
:do {
    /tool user-manager user add customer="adm8n" username="0190392866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190392866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190392866";
};

# المستخدم 155: 0142875231
:do {
    /tool user-manager user add customer="adm8n" username="0142875231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142875231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142875231";
};

# المستخدم 156: 0152730251
:do {
    /tool user-manager user add customer="adm8n" username="0152730251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152730251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152730251";
};

# المستخدم 157: 0127342198
:do {
    /tool user-manager user add customer="adm8n" username="0127342198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127342198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127342198";
};

# المستخدم 158: 0104774220
:do {
    /tool user-manager user add customer="adm8n" username="0104774220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104774220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104774220";
};

# المستخدم 159: 0107271746
:do {
    /tool user-manager user add customer="adm8n" username="0107271746" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107271746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107271746";
};

# المستخدم 160: 0151409392
:do {
    /tool user-manager user add customer="adm8n" username="0151409392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151409392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151409392";
};

# المستخدم 161: 0134001494
:do {
    /tool user-manager user add customer="adm8n" username="0134001494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134001494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134001494";
};

# المستخدم 162: 0169601208
:do {
    /tool user-manager user add customer="adm8n" username="0169601208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169601208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169601208";
};

# المستخدم 163: 0107737348
:do {
    /tool user-manager user add customer="adm8n" username="0107737348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107737348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107737348";
};

# المستخدم 164: 0138220776
:do {
    /tool user-manager user add customer="adm8n" username="0138220776" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138220776";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138220776";
};

# المستخدم 165: 0139709357
:do {
    /tool user-manager user add customer="adm8n" username="0139709357" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139709357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139709357";
};

# المستخدم 166: 0180341517
:do {
    /tool user-manager user add customer="adm8n" username="0180341517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180341517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180341517";
};

# المستخدم 167: 0101926230
:do {
    /tool user-manager user add customer="adm8n" username="0101926230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101926230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101926230";
};

# المستخدم 168: 0120124493
:do {
    /tool user-manager user add customer="adm8n" username="0120124493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120124493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120124493";
};

# المستخدم 169: 0123710675
:do {
    /tool user-manager user add customer="adm8n" username="0123710675" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123710675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123710675";
};

# المستخدم 170: 0185373922
:do {
    /tool user-manager user add customer="adm8n" username="0185373922" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185373922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185373922";
};

# المستخدم 171: 0165651098
:do {
    /tool user-manager user add customer="adm8n" username="0165651098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165651098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165651098";
};

# المستخدم 172: 0192191600
:do {
    /tool user-manager user add customer="adm8n" username="0192191600" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192191600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192191600";
};

# المستخدم 173: 0124574401
:do {
    /tool user-manager user add customer="adm8n" username="0124574401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124574401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124574401";
};

# المستخدم 174: 0139824856
:do {
    /tool user-manager user add customer="adm8n" username="0139824856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139824856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139824856";
};

# المستخدم 175: 0170147901
:do {
    /tool user-manager user add customer="adm8n" username="0170147901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170147901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170147901";
};

# المستخدم 176: 0178147760
:do {
    /tool user-manager user add customer="adm8n" username="0178147760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178147760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178147760";
};

# المستخدم 177: 0105558701
:do {
    /tool user-manager user add customer="adm8n" username="0105558701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105558701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105558701";
};

# المستخدم 178: 0165575707
:do {
    /tool user-manager user add customer="adm8n" username="0165575707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165575707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165575707";
};

# المستخدم 179: 0128016335
:do {
    /tool user-manager user add customer="adm8n" username="0128016335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128016335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128016335";
};

# المستخدم 180: 0181610433
:do {
    /tool user-manager user add customer="adm8n" username="0181610433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181610433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181610433";
};

# المستخدم 181: 0181475812
:do {
    /tool user-manager user add customer="adm8n" username="0181475812" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181475812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181475812";
};

# المستخدم 182: 0149092364
:do {
    /tool user-manager user add customer="adm8n" username="0149092364" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149092364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149092364";
};

# المستخدم 183: 0178625216
:do {
    /tool user-manager user add customer="adm8n" username="0178625216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178625216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178625216";
};

# المستخدم 184: 0192631805
:do {
    /tool user-manager user add customer="adm8n" username="0192631805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192631805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192631805";
};

# المستخدم 185: 0112152569
:do {
    /tool user-manager user add customer="adm8n" username="0112152569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112152569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112152569";
};

# المستخدم 186: 0179926885
:do {
    /tool user-manager user add customer="adm8n" username="0179926885" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179926885";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179926885";
};

# المستخدم 187: 0147434166
:do {
    /tool user-manager user add customer="adm8n" username="0147434166" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147434166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147434166";
};

# المستخدم 188: 0138653562
:do {
    /tool user-manager user add customer="adm8n" username="0138653562" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138653562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138653562";
};

# المستخدم 189: 0180289792
:do {
    /tool user-manager user add customer="adm8n" username="0180289792" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180289792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180289792";
};

# المستخدم 190: 0166315114
:do {
    /tool user-manager user add customer="adm8n" username="0166315114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166315114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166315114";
};

# المستخدم 191: 0109783016
:do {
    /tool user-manager user add customer="adm8n" username="0109783016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109783016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109783016";
};

# المستخدم 192: 0126936376
:do {
    /tool user-manager user add customer="adm8n" username="0126936376" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126936376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126936376";
};

# المستخدم 193: 0174487020
:do {
    /tool user-manager user add customer="adm8n" username="0174487020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174487020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174487020";
};

# المستخدم 194: 0120373422
:do {
    /tool user-manager user add customer="adm8n" username="0120373422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120373422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120373422";
};

# المستخدم 195: 0131509335
:do {
    /tool user-manager user add customer="adm8n" username="0131509335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131509335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131509335";
};

# المستخدم 196: 0168485894
:do {
    /tool user-manager user add customer="adm8n" username="0168485894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168485894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168485894";
};

# المستخدم 197: 0175331510
:do {
    /tool user-manager user add customer="adm8n" username="0175331510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175331510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175331510";
};

# المستخدم 198: 0129652053
:do {
    /tool user-manager user add customer="adm8n" username="0129652053" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129652053";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129652053";
};

# المستخدم 199: 0143430659
:do {
    /tool user-manager user add customer="adm8n" username="0143430659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143430659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143430659";
};

# المستخدم 200: 0186759756
:do {
    /tool user-manager user add customer="adm8n" username="0186759756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186759756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186759756";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
