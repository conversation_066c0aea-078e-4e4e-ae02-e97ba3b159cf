#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة زر "حذف الكروت الناجحة المرسلة للميكروتيك" في التقرير النهائي للكرت الواحد
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من إضافة الزر الجديد وجميع الدوال المرتبطة به
"""

import re

def test_button_added_to_final_report():
    """اختبار إضافة الزر في التقرير النهائي"""
    print("🔍 اختبار إضافة الزر في التقرير النهائي...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_final_report
    func_match = re.search(r'def send_cards_final_report.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_final_report")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود الزر الجديد
    button_elements = [
        'if card_type == "single" and success_count > 0:',
        '🗑️ حذف الكروت الناجحة المرسلة للميكروتيك',
        'delete_successful_single_',
        'keyboard_buttons.append'
    ]
    
    for element in button_elements:
        if element not in func_code:
            print(f"❌ عنصر الزر غير موجود: {element}")
            return False
        print(f"✅ عنصر الزر موجود: {element}")
    
    return True

def test_callback_handling_added():
    """اختبار إضافة معالجة callback"""
    print("\n🔍 اختبار إضافة معالجة callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback
    callback_handlers = [
        'elif callback_data.startswith("delete_successful_single_"):',
        'self.handle_delete_successful_single_request(bot_token, chat_id, callback_data)',
        'elif callback_data.startswith("confirm_delete_successful_single_"):',
        'self.execute_delete_successful_single(bot_token, chat_id, callback_data)',
        'elif callback_data == "cancel_delete_successful_single":',
        'self.cancel_delete_successful_single(bot_token, chat_id)'
    ]
    
    for handler in callback_handlers:
        if handler not in content:
            print(f"❌ معالج callback غير موجود: {handler}")
            return False
        print(f"✅ معالج callback موجود: {handler}")
    
    return True

def test_handle_request_function():
    """اختبار دالة handle_delete_successful_single_request"""
    print("\n🔍 اختبار دالة handle_delete_successful_single_request...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def handle_delete_successful_single_request.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_delete_successful_single_request")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة
    required_elements = [
        'معالجة طلب حذف الكروت الناجحة للكرت الواحد من التقرير النهائي',
        'if not hasattr(self, \'single_card_successful_cards\')',
        'لا توجد معلومات محفوظة عن الكروت الناجحة',
        'تأكيد حذف الكروت الناجحة المرسلة للميكروتيك',
        'حذف الكروت الناجحة من خادم MikroTik',
        'سيتم حذف الكروت الناجحة من خادم MikroTik نهائياً',
        'تنظيف خادم MikroTik من الكروت غير المكتملة',
        'نعم، احذف الكروت الناجحة',
        'إلغاء - الاحتفاظ بالكروت الناجحة',
        'confirm_delete_successful_single_',
        'cancel_delete_successful_single'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود في الدالة: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود في الدالة: {element}")
    
    return True

def test_execute_function():
    """اختبار دالة execute_delete_successful_single"""
    print("\n🔍 اختبار دالة execute_delete_successful_single...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def execute_delete_successful_single.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_delete_successful_single")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة
    required_elements = [
        'تنفيذ عملية حذف الكروت الناجحة للكرت الواحد من MikroTik',
        'if not hasattr(self, \'single_card_successful_cards\')',
        'بدء عملية حذف الكروت الناجحة',
        'جاري حذف {len(self.single_card_successful_cards)} كرت ناجح من خادم MikroTik',
        'api = self.connect_api()',
        'فشل في الاتصال',
        'لا يمكن الاتصال بخادم MikroTik لتنفيذ عملية الحذف',
        'deleted_count = self.delete_successful_cards_from_mikrotik',
        'تم حذف الكروت الناجحة!',
        'إحصائيات الحذف:',
        'إجمالي الكروت المطلوب حذفها:',
        'الكروت المحذوفة بنجاح:',
        'معدل نجاح الحذف:',
        'self.single_card_successful_cards = []'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود في الدالة: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود في الدالة: {element}")
    
    return True

def test_cancel_function():
    """اختبار دالة cancel_delete_successful_single"""
    print("\n🔍 اختبار دالة cancel_delete_successful_single...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def cancel_delete_successful_single.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة cancel_delete_successful_single")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة
    required_elements = [
        'إلغاء عملية حذف الكروت الناجحة للكرت الواحد',
        'تم إلغاء حذف الكروت الناجحة',
        'لم يتم حذف أي كروت',
        'جميع الكروت الناجحة من عملية الكرت الواحد الحالية ما زالت موجودة',
        'يمكنك طلب حذف الكروت الناجحة مرة أخرى'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود في الدالة: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود في الدالة: {element}")
    
    return True

def test_button_conditions():
    """اختبار شروط ظهور الزر"""
    print("\n🔍 اختبار شروط ظهور الزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_final_report
    func_match = re.search(r'def send_cards_final_report.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_final_report")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الشروط
    conditions = [
        'if failed_count > 0:',  # يظهر عند وجود كروت فاشلة
        'if card_type == "single" and success_count > 0:',  # يظهر فقط للكرت الواحد عند وجود كروت ناجحة
    ]
    
    for condition in conditions:
        if condition not in func_code:
            print(f"❌ شرط ظهور الزر غير موجود: {condition}")
            return False
        print(f"✅ شرط ظهور الزر موجود: {condition}")
    
    return True

def test_button_text_and_callback():
    """اختبار نص الزر وcallback_data"""
    print("\n🔍 اختبار نص الزر وcallback_data...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من النص الصحيح للزر
    button_text = '🗑️ حذف الكروت الناجحة المرسلة للميكروتيك ({success_count} كرت)'
    if button_text not in content:
        print(f"❌ نص الزر غير صحيح أو غير موجود: {button_text}")
        return False
    print(f"✅ نص الزر صحيح: {button_text}")
    
    # التحقق من callback_data الصحيح
    callback_data = 'delete_successful_single_{success_count}'
    if callback_data not in content:
        print(f"❌ callback_data غير صحيح أو غير موجود: {callback_data}")
        return False
    print(f"✅ callback_data صحيح: {callback_data}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إضافة زر حذف الكروت الناجحة في التقرير النهائي للكرت الواحد")
    print("="*80)
    
    tests = [
        ("إضافة الزر في التقرير النهائي", test_button_added_to_final_report),
        ("إضافة معالجة callback", test_callback_handling_added),
        ("دالة handle_delete_successful_single_request", test_handle_request_function),
        ("دالة execute_delete_successful_single", test_execute_function),
        ("دالة cancel_delete_successful_single", test_cancel_function),
        ("شروط ظهور الزر", test_button_conditions),
        ("نص الزر وcallback_data", test_button_text_and_callback)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*80)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إضافة زر حذف الكروت الناجحة في التقرير النهائي بنجاح!")
        print("💡 الميزات المضافة:")
        print("✅ زر جديد: 🗑️ حذف الكروت الناجحة المرسلة للميكروتيك")
        print("✅ يظهر فقط في الكرت الواحد (Single Card)")
        print("✅ يظهر فقط عند وجود كروت ناجحة وفاشلة")
        print("✅ رسالة تأكيد شاملة مع تحذيرات")
        print("✅ تنفيذ حذف فعلي من خادم MikroTik")
        print("✅ معالجة callback كاملة")
        print("✅ رسائل نجاح/فشل مفصلة")
        print("✅ إمكانية إلغاء العملية")
        
        print("\n🎯 الآن التقرير النهائي للكرت الواحد يحتوي على:")
        print("🔄 إعادة المحاولة (للكروت الفاشلة)")
        print("🗑️ حذف الكروت الناجحة المرسلة للميكروتيك (جديد)")
        print("📊 تقرير مفصل")
        
        print("\n💡 الهدف:")
        print("تنظيف خادم MikroTik من الكروت غير المكتملة عند فشل بعض الكروت")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
