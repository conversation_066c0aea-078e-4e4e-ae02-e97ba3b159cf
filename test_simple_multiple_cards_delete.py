#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مبسط لزر حذف الكروت الناجحة للكروت المتعددة
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن التعديل الأساسي تم بنجاح
"""

import re

def test_basic_addition():
    """اختبار الإضافة الأساسية"""
    print("🔍 اختبار الإضافة الأساسية...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    additions_found = 0
    total_additions = 8
    
    # 1. التحقق من وجود تشخيص الشروط في دالة send_cards_details_to_telegram
    if re.search(r'تشخيص شروط زر حذف الكروت الناجحة للكروت المتعددة', content):
        print("✅ 1. تم إضافة تشخيص الشروط للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 1. لم يتم إضافة تشخيص الشروط للكروت المتعددة")
    
    # 2. التحقق من وجود متغير show_delete_successful_button في دالة send_cards_details_to_telegram
    if re.search(r'send_cards_details_to_telegram.*show_delete_successful_button.*=', content, re.DOTALL):
        print("✅ 2. تم إضافة متغير show_delete_successful_button للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 2. لم يتم إضافة متغير show_delete_successful_button للكروت المتعددة")
    
    # 3. التحقق من وجود النص التوضيحي
    if re.search(r'خيارات إدارة الكروت', content):
        print("✅ 3. تم إضافة النص التوضيحي")
        additions_found += 1
    else:
        print("❌ 3. لم يتم إضافة النص التوضيحي")
    
    # 4. التحقق من وجود الزر
    if re.search(r'send_cards_details_to_telegram.*حذف الكروت المرسلة بنجاح من هذه العملية', content, re.DOTALL):
        print("✅ 4. تم إضافة الزر للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 4. لم يتم إضافة الزر للكروت المتعددة")
    
    # 5. التحقق من وجود callback_data
    if re.search(r'send_cards_details_to_telegram.*single_card_delete_successful_', content, re.DOTALL):
        print("✅ 5. تم إضافة callback_data للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 5. لم يتم إضافة callback_data للكروت المتعددة")
    
    # 6. التحقق من وجود send_telegram_message_with_keyboard
    if re.search(r'send_cards_details_to_telegram.*send_telegram_message_with_keyboard', content, re.DOTALL):
        print("✅ 6. تم إضافة إرسال الرسالة مع الزر للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 6. لم يتم إضافة إرسال الرسالة مع الزر للكروت المتعددة")
    
    # 7. التحقق من وجود النص الشرطي
    if re.search(r'send_cards_details_to_telegram.*تحديد النص المناسب حسب حالة العملية', content, re.DOTALL):
        print("✅ 7. تم إضافة النص الشرطي للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 7. لم يتم إضافة النص الشرطي للكروت المتعددة")
    
    # 8. التحقق من وجود رسالة السجل
    if re.search(r'تم إضافة زر حذف الكروت المرسلة بنجاح للكروت المتعددة', content):
        print("✅ 8. تم إضافة رسالة السجل للكروت المتعددة")
        additions_found += 1
    else:
        print("❌ 8. لم يتم إضافة رسالة السجل للكروت المتعددة")
    
    print(f"\n📊 النتيجة: {additions_found}/{total_additions} إضافات تمت بنجاح")
    return additions_found == total_additions

def test_function_structure():
    """اختبار بنية الدالة"""
    print("\n🔍 اختبار بنية الدالة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_details_to_telegram
    func_match = re.search(r'def send_cards_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    structure_checks = 0
    total_checks = 5
    
    # 1. التحقق من وجود الشروط
    if 'show_delete_successful_button' in func_code:
        print("✅ 1. متغير show_delete_successful_button موجود")
        structure_checks += 1
    else:
        print("❌ 1. متغير show_delete_successful_button مفقود")
    
    # 2. التحقق من وجود الشرط if show_delete_successful_button
    if 'if show_delete_successful_button:' in func_code:
        print("✅ 2. شرط if show_delete_successful_button موجود")
        structure_checks += 1
    else:
        print("❌ 2. شرط if show_delete_successful_button مفقود")
    
    # 3. التحقق من وجود keyboard_buttons
    if 'keyboard_buttons' in func_code:
        print("✅ 3. متغير keyboard_buttons موجود")
        structure_checks += 1
    else:
        print("❌ 3. متغير keyboard_buttons مفقود")
    
    # 4. التحقق من وجود inline_keyboard
    if 'inline_keyboard' in func_code:
        print("✅ 4. inline_keyboard موجود")
        structure_checks += 1
    else:
        print("❌ 4. inline_keyboard مفقود")
    
    # 5. التحقق من وجود else للإرسال بدون زر
    if 'else:' in func_code and 'send_telegram_message_direct' in func_code:
        print("✅ 5. else للإرسال بدون زر موجود")
        structure_checks += 1
    else:
        print("❌ 5. else للإرسال بدون زر مفقود")
    
    print(f"\n📊 النتيجة: {structure_checks}/{total_checks} فحوصات البنية نجحت")
    return structure_checks == total_checks

def test_consistency_with_single_card():
    """اختبار التناسق مع دالة الكرت الواحد"""
    print("\n🔍 اختبار التناسق مع دالة الكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    consistency_checks = 0
    total_checks = 4
    
    # 1. كلا الدالتين تستخدم single_card_successful_cards
    single_card_uses = len(re.findall(r'send_single_card_details_to_telegram.*?single_card_successful_cards', content, re.DOTALL))
    multiple_cards_uses = len(re.findall(r'send_cards_details_to_telegram.*?single_card_successful_cards', content, re.DOTALL))
    
    if single_card_uses > 0 and multiple_cards_uses > 0:
        print("✅ 1. كلا الدالتين تستخدم single_card_successful_cards")
        consistency_checks += 1
    else:
        print(f"❌ 1. استخدام single_card_successful_cards غير متناسق (single: {single_card_uses}, multiple: {multiple_cards_uses})")
    
    # 2. كلا الدالتين تستخدم نفس callback_data
    single_card_callback = 'single_card_delete_successful_' in content and 'send_single_card_details_to_telegram' in content
    multiple_cards_callback = 'single_card_delete_successful_' in content and 'send_cards_details_to_telegram' in content
    
    if single_card_callback and multiple_cards_callback:
        print("✅ 2. كلا الدالتين تستخدم نفس callback_data")
        consistency_checks += 1
    else:
        print("❌ 2. callback_data غير متناسق")
    
    # 3. كلا الدالتين تستخدم نفس الشروط الأساسية
    single_card_conditions = re.search(r'send_single_card_details_to_telegram.*success_count > 0.*system_type.*hotspot', content, re.DOTALL)
    multiple_cards_conditions = re.search(r'send_cards_details_to_telegram.*success_count > 0.*system_type.*hotspot', content, re.DOTALL)
    
    if single_card_conditions and multiple_cards_conditions:
        print("✅ 3. كلا الدالتين تستخدم نفس الشروط الأساسية")
        consistency_checks += 1
    else:
        print("❌ 3. الشروط الأساسية غير متناسقة")
    
    # 4. كلا الدالتين تستخدم نفس النصوص
    single_card_texts = re.search(r'send_single_card_details_to_telegram.*إذا لم تعد بحاجة إليها', content, re.DOTALL)
    multiple_cards_texts = re.search(r'send_cards_details_to_telegram.*إذا لم تعد بحاجة إليها', content, re.DOTALL)
    
    if single_card_texts and multiple_cards_texts:
        print("✅ 4. كلا الدالتين تستخدم نفس النصوص")
        consistency_checks += 1
    else:
        print("❌ 4. النصوص غير متناسقة")
    
    print(f"\n📊 النتيجة: {consistency_checks}/{total_checks} فحوصات التناسق نجحت")
    return consistency_checks == total_checks

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء الاختبار المبسط لزر حذف الكروت الناجحة للكروت المتعددة")
    print("="*75)
    
    tests = [
        ("الإضافة الأساسية", test_basic_addition),
        ("بنية الدالة", test_function_structure),
        ("التناسق مع دالة الكرت الواحد", test_consistency_with_single_card)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 60)
    
    print("\n" + "="*75)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إضافة زر حذف الكروت الناجحة للكروت المتعددة بنجاح!")
        print("💡 الزر الآن يظهر عند استخدام خيار 'كرت واحد' من البوت بغض النظر عن عدد الكروت")
        
        print("\n🎯 الحالات المدعومة الآن:")
        print("✅ كرت واحد من البوت → يظهر الزر")
        print("✅ عدة كروت من البوت → يظهر الزر الآن")
        print("✅ النجاح الكامل → يظهر الزر مع نص مناسب")
        print("✅ الفشل الجزئي → يظهر الزر مع نص مناسب")
        print("❌ الفشل الكامل → لا يظهر الزر")
        
        print("\n🔧 ما تم إضافته:")
        print("✅ شروط إظهار الزر في دالة send_cards_details_to_telegram")
        print("✅ النص التوضيحي المناسب لكل حالة")
        print("✅ الزر مع callback_data صحيح")
        print("✅ إرسال الرسالة مع لوحة المفاتيح")
        print("✅ رسائل السجل المناسبة")
        
        print("\n🎮 كيفية الاختبار:")
        print("1. استخدم خيار 'كرت واحد' من البوت")
        print("2. اختر قالب وعدد كروت أكبر من 1")
        print("3. تأكد من حدوث نجاح كامل أو فشل جزئي")
        print("4. ابحث عن الزر في رسالة التفاصيل")
        print("5. جرب التأكيد والإلغاء")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة إضافية.")
    
    return failed == 0

if __name__ == "__main__":
    main()
