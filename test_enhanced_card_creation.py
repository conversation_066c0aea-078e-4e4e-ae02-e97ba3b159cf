#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة إنشاء الكروت المحسنة مع تحديد العدد
Test Enhanced Card Creation with Count Selection
"""

import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

class TestEnhancedCardCreation(unittest.TestCase):
    """اختبارات وظيفة إنشاء الكروت المحسنة"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء mock للبرنامج الرئيسي
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.send_telegram_message_direct = Mock()
        self.mock_app.send_telegram_message_with_keyboard = Mock()
        self.mock_app.hotspot_templates_file = "test_hotspot_templates.json"
        
        # محاكاة قوالب Hotspot
        self.mock_templates = {
            "قالب_اختبار_1": {"api_host": "***********", "username": "admin"},
            "قالب_اختبار_2": {"api_host": "********", "username": "user"},
            "قالب_اختبار_3": {"api_host": "**********", "username": "test"}
        }
        
    def test_card_count_selection_display(self):
        """اختبار عرض خيارات عدد الكروت"""
        print("🧪 اختبار عرض خيارات عدد الكروت...")
        
        # محاكاة دالة عرض خيارات العدد
        def mock_show_card_count_selection(templates_count):
            # الخيارات المتاحة للعدد
            count_options = [1, 2, 3, 5, 10, 20, 50, 100]
            
            # التحقق من وجود قوالب
            if templates_count == 0:
                return False
            
            # بناء رسالة اختيار العدد
            message_parts = [
                "🎴 **إنشاء كروت Hotspot**",
                f"📋 **القوالب المتاحة:** {templates_count} قالب",
                "🔢 **كم كرت تريد إنشاؤه؟**"
            ]
            
            return {
                "success": True,
                "options": count_options,
                "message": "\n".join(message_parts),
                "templates_count": templates_count
            }
        
        # اختبار مع قوالب متاحة
        result = mock_show_card_count_selection(3)
        self.assertTrue(result["success"], "يجب أن ينجح عرض خيارات العدد مع وجود قوالب")
        self.assertEqual(len(result["options"]), 8, "يجب أن يكون هناك 8 خيارات للعدد")
        self.assertIn("🎴", result["message"], "يجب أن تحتوي الرسالة على رمز الكرت")
        
        # اختبار بدون قوالب
        result_no_templates = mock_show_card_count_selection(0)
        self.assertFalse(result_no_templates, "يجب أن يفشل عرض خيارات العدد بدون قوالب")
        
        print("✅ نجح اختبار عرض خيارات عدد الكروت")
    
    def test_custom_card_count_validation(self):
        """اختبار التحقق من صحة العدد المخصص"""
        print("🧪 اختبار التحقق من صحة العدد المخصص...")
        
        # محاكاة دالة التحقق من العدد المخصص
        def mock_validate_custom_count(command_text):
            parts = command_text.split()
            
            # التحقق من وجود العدد
            if len(parts) < 2:
                return {"valid": False, "error": "missing_count", "message": "يرجى إدخال العدد"}
            
            # محاولة تحويل العدد
            try:
                count = int(parts[1])
            except ValueError:
                return {"valid": False, "error": "invalid_number", "message": "عدد غير صحيح"}
            
            # التحقق من حدود العدد
            if count < 1:
                return {"valid": False, "error": "too_small", "message": "العدد صغير جداً"}
            
            if count > 100:
                return {"valid": False, "error": "too_large", "message": "العدد كبير جداً"}
            
            return {"valid": True, "count": count, "message": "العدد صحيح"}
        
        # اختبار أعداد صحيحة
        valid_commands = ["/card 1", "/card 5", "/card 25", "/card 50", "/card 100"]
        for cmd in valid_commands:
            result = mock_validate_custom_count(cmd)
            self.assertTrue(result["valid"], f"يجب أن يكون العدد صحيح: {cmd}")
        
        # اختبار أعداد غير صحيحة
        invalid_commands = [
            ("/card", "missing_count"),
            ("/card abc", "invalid_number"),
            ("/card 0", "too_small"),
            ("/card -5", "too_small"),
            ("/card 150", "too_large")
        ]
        
        for cmd, expected_error in invalid_commands:
            result = mock_validate_custom_count(cmd)
            self.assertFalse(result["valid"], f"يجب أن يفشل العدد: {cmd}")
            self.assertEqual(result["error"], expected_error, f"نوع الخطأ غير صحيح: {cmd}")
        
        print("✅ نجح اختبار التحقق من صحة العدد المخصص")
    
    def test_template_selection_with_count(self):
        """اختبار اختيار القالب مع العدد"""
        print("🧪 اختبار اختيار القالب مع العدد...")
        
        # محاكاة دالة اختيار القالب مع العدد
        def mock_template_selection_with_count(card_count, templates):
            if not templates:
                return {"success": False, "error": "no_templates"}
            
            if card_count < 1 or card_count > 100:
                return {"success": False, "error": "invalid_count"}
            
            # تحديد النص حسب العدد
            if card_count == 1:
                count_text = "كرت واحد"
                count_emoji = "🎴"
            elif card_count == 2:
                count_text = "كرتان"
                count_emoji = "🎴🎴"
            elif card_count <= 10:
                count_text = f"{card_count} كروت"
                count_emoji = "📦"
            else:
                count_text = f"{card_count} كرت"
                count_emoji = "📦📦"
            
            # بناء callback data للقوالب
            template_callbacks = []
            for template_name in templates.keys():
                callback_data = f"cards_template_{card_count}_{template_name}"
                template_callbacks.append({
                    "template": template_name,
                    "callback": callback_data
                })
            
            return {
                "success": True,
                "count": card_count,
                "count_text": count_text,
                "count_emoji": count_emoji,
                "templates": template_callbacks,
                "templates_count": len(templates)
            }
        
        # اختبار مع أعداد مختلفة
        test_counts = [1, 2, 5, 10, 25, 50, 100]
        
        for count in test_counts:
            result = mock_template_selection_with_count(count, self.mock_templates)
            
            self.assertTrue(result["success"], f"يجب أن ينجح اختيار القالب للعدد: {count}")
            self.assertEqual(result["count"], count, f"العدد غير صحيح: {count}")
            self.assertEqual(len(result["templates"]), 3, f"عدد القوالب غير صحيح للعدد: {count}")
            
            # التحقق من callback data
            for template_data in result["templates"]:
                expected_callback = f"cards_template_{count}_{template_data['template']}"
                self.assertEqual(template_data["callback"], expected_callback, 
                               f"callback data غير صحيح للعدد {count}")
        
        # اختبار بدون قوالب
        result_no_templates = mock_template_selection_with_count(5, {})
        self.assertFalse(result_no_templates["success"], "يجب أن يفشل بدون قوالب")
        self.assertEqual(result_no_templates["error"], "no_templates", "نوع الخطأ غير صحيح")
        
        print("✅ نجح اختبار اختيار القالب مع العدد")
    
    def test_callback_data_parsing(self):
        """اختبار تحليل callback data"""
        print("🧪 اختبار تحليل callback data...")
        
        # محاكاة دالة تحليل callback data
        def mock_parse_callback_data(callback_data):
            if callback_data.startswith("card_count_"):
                count = int(callback_data.replace("card_count_", ""))
                return {"type": "count_selection", "count": count}
            
            elif callback_data.startswith("cards_template_"):
                parts = callback_data.replace("cards_template_", "").split("_", 1)
                if len(parts) == 2:
                    count = int(parts[0])
                    template_name = parts[1]
                    return {"type": "template_selection", "count": count, "template": template_name}
            
            elif callback_data == "custom_card_count_help":
                return {"type": "help", "topic": "custom_count"}
            
            return {"type": "unknown"}
        
        # اختبار callback data لاختيار العدد
        count_callbacks = ["card_count_1", "card_count_5", "card_count_25", "card_count_100"]
        for callback in count_callbacks:
            result = mock_parse_callback_data(callback)
            self.assertEqual(result["type"], "count_selection", f"نوع callback غير صحيح: {callback}")
            expected_count = int(callback.split("_")[-1])
            self.assertEqual(result["count"], expected_count, f"العدد غير صحيح: {callback}")
        
        # اختبار callback data لاختيار القالب
        template_callbacks = [
            ("cards_template_5_قالب_اختبار_1", 5, "قالب_اختبار_1"),
            ("cards_template_25_قالب_اختبار_2", 25, "قالب_اختبار_2"),
            ("cards_template_100_قالب_اختبار_3", 100, "قالب_اختبار_3")
        ]
        
        for callback, expected_count, expected_template in template_callbacks:
            result = mock_parse_callback_data(callback)
            self.assertEqual(result["type"], "template_selection", f"نوع callback غير صحيح: {callback}")
            self.assertEqual(result["count"], expected_count, f"العدد غير صحيح: {callback}")
            self.assertEqual(result["template"], expected_template, f"القالب غير صحيح: {callback}")
        
        # اختبار callback data للمساعدة
        help_result = mock_parse_callback_data("custom_card_count_help")
        self.assertEqual(help_result["type"], "help", "نوع callback المساعدة غير صحيح")
        self.assertEqual(help_result["topic"], "custom_count", "موضوع المساعدة غير صحيح")
        
        print("✅ نجح اختبار تحليل callback data")
    
    def test_card_creation_workflow(self):
        """اختبار سير العمل الكامل لإنشاء الكروت"""
        print("🧪 اختبار سير العمل الكامل لإنشاء الكروت...")
        
        # محاكاة سير العمل الكامل
        def mock_card_creation_workflow(card_count, template_name):
            workflow_steps = []
            
            try:
                # 1. التحقق من صحة المدخلات
                workflow_steps.append("validate_inputs")
                if card_count < 1 or card_count > 100:
                    raise ValueError("عدد غير صحيح")
                
                if not template_name or template_name not in self.mock_templates:
                    raise ValueError("قالب غير موجود")
                
                # 2. تطبيق القالب
                workflow_steps.append("apply_template")
                template_data = self.mock_templates[template_name]
                
                # 3. تعيين عدد الكروت
                workflow_steps.append("set_card_count")
                
                # 4. توليد الكروت
                workflow_steps.append("generate_cards")
                generated_cards = []
                for i in range(card_count):
                    card = {
                        "username": f"user_{i+1:03d}",
                        "password": f"pass_{i+1:03d}",
                        "profile": "default"
                    }
                    generated_cards.append(card)
                
                # 5. إرسال إلى MikroTik
                workflow_steps.append("send_to_mikrotik")
                send_success = True  # محاكاة نجاح الإرسال
                
                # 6. إرسال تفاصيل عبر التلجرام
                workflow_steps.append("send_telegram_details")
                
                return {
                    "success": True,
                    "steps": workflow_steps,
                    "cards": generated_cards,
                    "send_success": send_success,
                    "template": template_name,
                    "count": card_count
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "steps": workflow_steps
                }
        
        # اختبار سير العمل الناجح
        successful_tests = [
            (1, "قالب_اختبار_1"),
            (5, "قالب_اختبار_2"),
            (25, "قالب_اختبار_3"),
            (100, "قالب_اختبار_1")
        ]
        
        for count, template in successful_tests:
            result = mock_card_creation_workflow(count, template)
            
            self.assertTrue(result["success"], f"يجب أن ينجح سير العمل: {count} كرت، {template}")
            self.assertEqual(len(result["cards"]), count, f"عدد الكروت المولدة غير صحيح: {count}")
            self.assertEqual(result["template"], template, f"القالب غير صحيح: {template}")
            
            # التحقق من خطوات سير العمل
            expected_steps = [
                "validate_inputs", "apply_template", "set_card_count", 
                "generate_cards", "send_to_mikrotik", "send_telegram_details"
            ]
            self.assertEqual(result["steps"], expected_steps, f"خطوات سير العمل غير صحيحة: {count}")
        
        # اختبار سير العمل الفاشل
        failed_tests = [
            (0, "قالب_اختبار_1"),  # عدد غير صحيح
            (150, "قالب_اختبار_1"),  # عدد كبير جداً
            (5, "قالب_غير_موجود")  # قالب غير موجود
        ]
        
        for count, template in failed_tests:
            result = mock_card_creation_workflow(count, template)
            self.assertFalse(result["success"], f"يجب أن يفشل سير العمل: {count} كرت، {template}")
            self.assertIn("error", result, f"يجب أن يحتوي على خطأ: {count} كرت، {template}")
        
        print("✅ نجح اختبار سير العمل الكامل لإنشاء الكروت")

def run_enhanced_card_creation_tests():
    """تشغيل جميع اختبارات إنشاء الكروت المحسنة"""
    print("🚀 بدء اختبارات إنشاء الكروت المحسنة")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("=" * 60)
    print("✅ انتهت جميع الاختبارات")
    
    print("\n📋 ملخص الوظائف المختبرة:")
    print("• ✅ عرض خيارات عدد الكروت")
    print("• ✅ التحقق من صحة العدد المخصص")
    print("• ✅ اختيار القالب مع العدد")
    print("• ✅ تحليل callback data")
    print("• ✅ سير العمل الكامل لإنشاء الكروت")

if __name__ == "__main__":
    run_enhanced_card_creation_tests()
