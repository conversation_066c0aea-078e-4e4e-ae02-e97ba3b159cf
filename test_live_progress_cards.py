#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة مؤشر التقدم المباشر لإنشاء الكروت
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن مؤشر التقدم المباشر يعمل بشكل صحيح لجميع أنواع الكروت
"""

import re
import time

def test_progress_bar_functions():
    """اختبار دوال شريط التقدم"""
    print("🔍 اختبار دوال شريط التقدم...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود دوال شريط التقدم
    progress_functions = [
        'def create_cards_progress_bar(',
        'def estimate_cards_remaining_time(',
        'def format_elapsed_time(',
        'def send_cards_progress_message(',
        'def update_cards_progress_message(',
        'def send_cards_final_report('
    ]
    
    for func in progress_functions:
        if func not in content:
            print(f"❌ الدالة غير موجودة: {func}")
            return False
        print(f"✅ الدالة موجودة: {func}")
    
    return True

def test_single_card_progress():
    """اختبار مؤشر التقدم للكرت الواحد"""
    print("\n🔍 اختبار مؤشر التقدم للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التحديثات المطلوبة
    single_card_patterns = [
        'failed_count = 0',
        'successful_cards = []',
        'failed_cards = []',
        'start_time = time.time()',
        'progress_message_id = self.send_cards_progress_message',
        'self.update_cards_progress_message',
        'self.send_cards_final_report'
    ]
    
    for pattern in single_card_patterns:
        if pattern not in func_code:
            print(f"❌ نمط الكرت الواحد غير موجود: {pattern}")
            return False
        print(f"✅ نمط الكرت الواحد موجود: {pattern}")
    
    return True

def test_lightning_progress():
    """اختبار مؤشر التقدم للبرق"""
    print("\n🔍 اختبار مؤشر التقدم للبرق...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_to_mikrotik_silent
    func_match = re.search(r'def send_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التحديثات المطلوبة
    lightning_patterns = [
        'failed_count = 0',
        'successful_cards = []',
        'failed_cards = []',
        'start_time = time.time()',
        'progress_message_id = self.send_cards_progress_message',
        '"lightning"',
        'self.update_cards_progress_message',
        'self.send_cards_final_report'
    ]
    
    for pattern in lightning_patterns:
        if pattern not in func_code:
            print(f"❌ نمط البرق غير موجود: {pattern}")
            return False
        print(f"✅ نمط البرق موجود: {pattern}")
    
    return True

def test_regular_cards_progress():
    """اختبار مؤشر التقدم للكروت العادية"""
    print("\n🔍 اختبار مؤشر التقدم للكروت العادية...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_to_mikrotik
    func_match = re.search(r'def send_to_mikrotik\(self\):.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_to_mikrotik")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التحديثات المطلوبة
    regular_patterns = [
        'failed_count = 0',
        'successful_cards = []',
        'failed_cards = []',
        'start_time = time.time()',
        'progress_message_id = self.send_cards_progress_message',
        '"regular"',
        'self.update_cards_progress_message',
        'self.send_cards_final_report'
    ]
    
    for pattern in regular_patterns:
        if pattern not in func_code:
            print(f"❌ نمط الكروت العادية غير موجود: {pattern}")
            return False
        print(f"✅ نمط الكروت العادية موجود: {pattern}")
    
    return True

def test_failed_cards_management():
    """اختبار إدارة الكروت الفاشلة"""
    print("\n🔍 اختبار إدارة الكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود دوال إدارة الكروت الفاشلة
    failed_management_functions = [
        'def handle_retry_failed_cards(',
        'def handle_delete_failed_cards(',
        'def handle_detailed_report_request(',
        'def get_card_type_name('
    ]
    
    for func in failed_management_functions:
        if func not in content:
            print(f"❌ دالة إدارة الكروت الفاشلة غير موجودة: {func}")
            return False
        print(f"✅ دالة إدارة الكروت الفاشلة موجودة: {func}")
    
    return True

def test_callback_handlers():
    """اختبار معالجات callback للكروت الفاشلة"""
    print("\n🔍 اختبار معالجات callback للكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود معالجات callback
    callback_patterns = [
        'elif callback_data.startswith("retry_failed_cards_"):',
        'elif callback_data.startswith("delete_failed_cards_"):',
        'elif callback_data.startswith("detailed_report_"):',
        'self.handle_retry_failed_cards(bot_token, chat_id, callback_data)',
        'self.handle_delete_failed_cards(bot_token, chat_id, callback_data)',
        'self.handle_detailed_report_request(bot_token, chat_id, callback_data)'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ معالج callback غير موجود: {pattern}")
            return False
        print(f"✅ معالج callback موجود: {pattern}")
    
    return True

def test_progress_message_format():
    """اختبار تنسيق رسائل التقدم"""
    print("\n🔍 اختبار تنسيق رسائل التقدم...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_progress_message
    func_match = re.search(r'def send_cards_progress_message.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_progress_message")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر التنسيق المطلوبة
    format_elements = [
        'جاري إنشاء',
        'HotSpot',
        'التقدم:',
        'الإحصائيات:',
        'ناجحة:',
        'فاشلة:',
        'معدل النجاح:',
        'الوقت:',
        'المنقضي:',
        'المتبقي:',
        'جاري المعالجة...'
    ]
    
    for element in format_elements:
        if element not in func_code:
            print(f"❌ عنصر التنسيق غير موجود: {element}")
            return False
        print(f"✅ عنصر التنسيق موجود: {element}")
    
    return True

def test_final_report_format():
    """اختبار تنسيق التقرير النهائي"""
    print("\n🔍 اختبار تنسيق التقرير النهائي...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_cards_final_report
    func_match = re.search(r'def send_cards_final_report.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_cards_final_report")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر التقرير النهائي
    report_elements = [
        'اكتملت عملية',
        'النتائج النهائية:',
        'الوقت الإجمالي:',
        'أمثلة على الكروت الناجحة:',
        'الكروت الفاشلة',
        'العملية مكتملة!',
        'إعادة المحاولة',
        'حذف الكروت الفاشلة',
        'تقرير مفصل'
    ]
    
    for element in report_elements:
        if element not in func_code:
            print(f"❌ عنصر التقرير النهائي غير موجود: {element}")
            return False
        print(f"✅ عنصر التقرير النهائي موجود: {element}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار ميزة مؤشر التقدم المباشر لإنشاء الكروت")
    print("="*70)
    
    tests = [
        ("دوال شريط التقدم", test_progress_bar_functions),
        ("مؤشر التقدم للكرت الواحد", test_single_card_progress),
        ("مؤشر التقدم للبرق", test_lightning_progress),
        ("مؤشر التقدم للكروت العادية", test_regular_cards_progress),
        ("إدارة الكروت الفاشلة", test_failed_cards_management),
        ("معالجات callback", test_callback_handlers),
        ("تنسيق رسائل التقدم", test_progress_message_format),
        ("تنسيق التقرير النهائي", test_final_report_format)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تطبيق ميزة مؤشر التقدم المباشر بنجاح!")
        print("💡 الميزة جاهزة للاستخدام مع جميع أنواع الكروت")
        
        print("\n🎯 الميزات المطبقة:")
        print("✅ مؤشر التقدم المباشر للكرت الواحد")
        print("✅ مؤشر التقدم المباشر للبرق")
        print("✅ مؤشر التقدم المباشر للكروت العادية")
        print("✅ شريط تقدم نصي مع النسبة المئوية")
        print("✅ إحصائيات مفصلة (ناجح/فاشل/معدل النجاح)")
        print("✅ تقدير الوقت المتبقي والمنقضي")
        print("✅ تقرير نهائي شامل مع خيارات إدارة الكروت الفاشلة")
        print("✅ إعادة المحاولة للكروت الفاشلة")
        print("✅ حذف الكروت الفاشلة")
        print("✅ تقرير مفصل للأخطاء")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
