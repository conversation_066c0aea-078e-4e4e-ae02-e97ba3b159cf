#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح رسائل إدارة الكروت للكرت الواحد (Single Card) في نظام HotSpot
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من تصحيح المصطلحات في رسائل إعادة المحاولة وحذف الكروت
"""

import re

def test_retry_messages_terminology():
    """اختبار مصطلحات رسائل إعادة المحاولة"""
    print("🔍 اختبار مصطلحات رسائل إعادة المحاولة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_retry_failed_cards
    func_match = re.search(r'def handle_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من المصطلحات الصحيحة لإعادة المحاولة
    correct_retry_terms = [
        'تأكيد إعادة المحاولة',
        'هل تريد إعادة المحاولة لهذه الكروت؟',
        'نعم، أعد المحاولة',
        'إلغاء',
        'cancel_retry_failed',
        'confirm_retry_'
    ]
    
    # التحقق من عدم وجود مصطلحات خاطئة
    wrong_retry_terms = [
        'تأكيد حذف الكروت الفاشلة',
        'هل تريد المتابعة مع الحذف؟',
        'نعم، احذف الكروت الفاشلة'
    ]
    
    for term in correct_retry_terms:
        if term not in func_code:
            print(f"❌ مصطلح إعادة المحاولة الصحيح غير موجود: {term}")
            return False
        print(f"✅ مصطلح إعادة المحاولة الصحيح موجود: {term}")
    
    for term in wrong_retry_terms:
        if term in func_code:
            print(f"❌ مصطلح خاطئ موجود في دالة إعادة المحاولة: {term}")
            return False
        print(f"✅ مصطلح خاطئ غير موجود في دالة إعادة المحاولة: {term}")
    
    return True

def test_execute_retry_messages():
    """اختبار رسائل تنفيذ إعادة المحاولة"""
    print("\n🔍 اختبار رسائل تنفيذ إعادة المحاولة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة execute_retry_failed_cards
    func_match = re.search(r'def execute_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من المصطلحات الصحيحة لتنفيذ إعادة المحاولة
    correct_execute_terms = [
        'بدء إعادة المحاولة للكروت الفاشلة',
        'جاري إعادة محاولة إنشاء وإرسال',
        'تم إعادة المحاولة بنجاح!',
        'فشل في إعادة المحاولة',
        'تم إعادة محاولة إنشاء وإرسال الكروت الفاشلة',
        'حدث خطأ أثناء إعادة محاولة إنشاء الكروت الفاشلة'
    ]
    
    for term in correct_execute_terms:
        if term not in func_code:
            print(f"❌ مصطلح تنفيذ إعادة المحاولة غير موجود: {term}")
            return False
        print(f"✅ مصطلح تنفيذ إعادة المحاولة موجود: {term}")
    
    return True

def test_cancel_retry_messages():
    """اختبار رسائل إلغاء إعادة المحاولة"""
    print("\n🔍 اختبار رسائل إلغاء إعادة المحاولة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة cancel_retry_failed_cards
    func_match = re.search(r'def cancel_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة cancel_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من المصطلحات الصحيحة لإلغاء إعادة المحاولة
    correct_cancel_terms = [
        'تم إلغاء إعادة المحاولة',
        'لم يتم إعادة محاولة أي كروت',
        'جميع الكروت الفاشلة ما زالت محفوظة',
        'يمكنك طلب إعادة المحاولة للكروت الفاشلة مرة أخرى'
    ]
    
    for term in correct_cancel_terms:
        if term not in func_code:
            print(f"❌ مصطلح إلغاء إعادة المحاولة غير موجود: {term}")
            return False
        print(f"✅ مصطلح إلغاء إعادة المحاولة موجود: {term}")
    
    return True

def test_delete_messages_terminology():
    """اختبار مصطلحات رسائل حذف الكروت الفاشلة"""
    print("\n🔍 اختبار مصطلحات رسائل حذف الكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_delete_failed_cards
    func_match = re.search(r'def handle_delete_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_delete_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من المصطلحات الصحيحة لحذف الكروت الفاشلة
    correct_delete_terms = [
        'تأكيد حذف الكروت الفاشلة من النظام',
        'سيتم حذف معلومات هذه الكروت الفاشلة من النظام نهائياً',
        'لن تتمكن من إعادة المحاولة لهذه الكروت بعد الحذف',
        'هذا الخيار مفيد لتنظيف النظام من الكروت الفاشلة',
        'هل تريد المتابعة مع حذف الكروت الفاشلة من النظام؟',
        'نعم، احذف الكروت الفاشلة من النظام',
        'إلغاء - الاحتفاظ بالكروت الفاشلة'
    ]
    
    for term in correct_delete_terms:
        if term not in func_code:
            print(f"❌ مصطلح حذف الكروت الفاشلة غير موجود: {term}")
            return False
        print(f"✅ مصطلح حذف الكروت الفاشلة موجود: {term}")
    
    return True

def test_callback_handling():
    """اختبار معالجة callback للرسائل المصححة"""
    print("\n🔍 اختبار معالجة callback للرسائل المصححة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback الجديدة
    callback_patterns = [
        'elif callback_data.startswith("confirm_retry_"):',
        'self.execute_retry_failed_cards(bot_token, chat_id, callback_data)',
        'elif callback_data == "cancel_retry_failed":',
        'self.cancel_retry_failed_cards(bot_token, chat_id)'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback موجود: {pattern}")
    
    return True

def test_retry_functions():
    """اختبار دوال إعادة المحاولة المخصصة"""
    print("\n🔍 اختبار دوال إعادة المحاولة المخصصة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود دوال إعادة المحاولة المخصصة
    retry_functions = [
        'def retry_single_card_failed_cards(',
        'def retry_lightning_failed_cards(',
        'def retry_regular_failed_cards('
    ]
    
    for func in retry_functions:
        if func not in content:
            print(f"❌ دالة إعادة المحاولة غير موجودة: {func}")
            return False
        print(f"✅ دالة إعادة المحاولة موجودة: {func}")
    
    return True

def test_single_card_retry_implementation():
    """اختبار تنفيذ إعادة المحاولة للكرت الواحد"""
    print("\n🔍 اختبار تنفيذ إعادة المحاولة للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة retry_single_card_failed_cards
    func_match = re.search(r'def retry_single_card_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة retry_single_card_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من عناصر التنفيذ
    implementation_elements = [
        'بدء إعادة محاولة الكرت الواحد للكروت الفاشلة',
        'إنشاء قائمة جديدة من الكروت للمحاولة مرة أخرى',
        'retry_credentials = []',
        'حفظ الكروت الجديدة مؤقتاً',
        'self.send_single_card_to_mikrotik_silent()',
        'استعادة الكروت الأصلية',
        'نجحت إعادة المحاولة للكرت الواحد',
        'فشلت إعادة المحاولة للكرت الواحد'
    ]
    
    for element in implementation_elements:
        if element not in func_code:
            print(f"❌ عنصر تنفيذ إعادة المحاولة غير موجود: {element}")
            return False
        print(f"✅ عنصر تنفيذ إعادة المحاولة موجود: {element}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح رسائل إدارة الكروت للكرت الواحد")
    print("="*70)
    
    tests = [
        ("مصطلحات رسائل إعادة المحاولة", test_retry_messages_terminology),
        ("رسائل تنفيذ إعادة المحاولة", test_execute_retry_messages),
        ("رسائل إلغاء إعادة المحاولة", test_cancel_retry_messages),
        ("مصطلحات رسائل حذف الكروت الفاشلة", test_delete_messages_terminology),
        ("معالجة callback للرسائل المصححة", test_callback_handling),
        ("دوال إعادة المحاولة المخصصة", test_retry_functions),
        ("تنفيذ إعادة المحاولة للكرت الواحد", test_single_card_retry_implementation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح رسائل إدارة الكروت للكرت الواحد بنجاح!")
        print("💡 الإصلاحات المطبقة:")
        print("✅ تصحيح مصطلحات رسائل إعادة المحاولة")
        print("✅ إضافة دوال تنفيذ إعادة المحاولة")
        print("✅ إضافة دوال إلغاء إعادة المحاولة")
        print("✅ تصحيح مصطلحات رسائل حذف الكروت الفاشلة")
        print("✅ إضافة معالجة callback كاملة")
        print("✅ تنفيذ إعادة المحاولة للكرت الواحد")
        print("✅ فصل واضح بين وظائف الإعادة والحذف")
        
        print("\n🎯 الآن الرسائل صحيحة:")
        print("🔄 زر إعادة المحاولة: يتحدث عن إعادة الإرسال والمحاولة")
        print("🗑️ زر حذف الكروت الفاشلة: يتحدث عن حذف الكروت من النظام")
        print("🗑️ زر حذف الكروت المرسلة بنجاح: يتحدث عن حذف الكروت الناجحة من MikroTik")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
