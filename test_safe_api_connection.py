#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة الاتصال الآمن بـ API
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن دالة create_safe_api_connection تعمل بشكل صحيح
"""

import re

def test_safe_api_connection_function():
    """اختبار وجود دالة create_safe_api_connection"""
    print("🔍 اختبار وجود دالة create_safe_api_connection...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن تعريف الدالة
    func_pattern = r'def create_safe_api_connection\(self\):'
    if not re.search(func_pattern, content):
        print("❌ دالة create_safe_api_connection غير موجودة")
        return False
    
    # البحث عن محتوى الدالة
    func_match = re.search(r'def create_safe_api_connection.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على محتوى دالة create_safe_api_connection")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المهمة في الدالة
    required_elements = [
        'hasattr(self, \'api_ip_entry\')',
        'hasattr(self, \'api_username_entry\')',
        'hasattr(self, \'api_password_entry\')',
        'routeros_api.RouterOsApiPool',
        'except Exception as',
        'self.logger.'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ العنصر المطلوب غير موجود في الدالة: {element}")
            return False
    
    print("✅ دالة create_safe_api_connection موجودة ومكتملة")
    return True

def test_function_usage_updates():
    """اختبار تحديث استخدام الدالة في الدوال الأخرى"""
    print("\n🔍 اختبار تحديث استخدام الدالة...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # الدوال التي يجب أن تستخدم create_safe_api_connection
    functions_to_check = [
        'handle_delete_users_by_email_request',
        'search_and_confirm_delete_users',
        'execute_delete_users_by_email'
    ]
    
    for func_name in functions_to_check:
        func_match = re.search(f'def {func_name}.*?(?=def|\\Z)', content, re.DOTALL)
        if not func_match:
            print(f"❌ لم يتم العثور على الدالة: {func_name}")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من استخدام create_safe_api_connection
        if 'create_safe_api_connection()' not in func_code:
            print(f"❌ الدالة {func_name} لا تستخدم create_safe_api_connection")
            return False
        
        # التحقق من عدم استخدام connect_api القديمة
        if 'self.connect_api()' in func_code:
            print(f"❌ الدالة {func_name} لا تزال تستخدم connect_api القديمة")
            return False
        
        print(f"✅ الدالة {func_name} تستخدم create_safe_api_connection")
    
    return True

def test_error_handling():
    """اختبار معالجة الأخطاء في الدالة الجديدة"""
    print("\n🔍 اختبار معالجة الأخطاء...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة create_safe_api_connection
    func_match = re.search(r'def create_safe_api_connection.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة create_safe_api_connection")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من معالجة الأخطاء
    error_handling_elements = [
        'try:',
        'except Exception as',
        'self.logger.error',
        'return None'
    ]
    
    for element in error_handling_elements:
        if element not in func_code:
            print(f"❌ عنصر معالجة الأخطاء غير موجود: {element}")
            return False
    
    # التحقق من معالجة حالات خاصة
    special_cases = [
        'if not ROUTEROS_AVAILABLE:',
        'if not host or not username or not password:',
        'hasattr(self, \'api\') and self.api:'
    ]
    
    for case in special_cases:
        if case not in func_code:
            print(f"❌ الحالة الخاصة غير موجودة: {case}")
            return False
    
    print("✅ معالجة الأخطاء شاملة ومكتملة")
    return True

def test_backward_compatibility():
    """اختبار التوافق مع الإصدارات السابقة"""
    print("\n🔍 اختبار التوافق مع الإصدارات السابقة...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من أن دالة connect_api الأصلية لا تزال موجودة
    if 'def connect_api(self):' not in content:
        print("❌ دالة connect_api الأصلية غير موجودة")
        return False
    
    # التحقق من أن الدالة الجديدة لا تتداخل مع الأصلية
    func_match = re.search(r'def create_safe_api_connection.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ دالة create_safe_api_connection غير موجودة")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من أن الدالة الجديدة تحاول استخدام الاتصال الحالي أولاً
    if 'hasattr(self, \'api\') and self.api:' not in func_code:
        print("❌ الدالة الجديدة لا تحاول استخدام الاتصال الحالي")
        return False
    
    print("✅ التوافق مع الإصدارات السابقة محفوظ")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح مشكلة الاتصال الآمن")
    print("="*60)
    
    tests = [
        ("وجود الدالة الجديدة", test_safe_api_connection_function),
        ("تحديث الاستخدام", test_function_usage_updates),
        ("معالجة الأخطاء", test_error_handling),
        ("التوافق السابق", test_backward_compatibility)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*60)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح مشكلة الاتصال بنجاح!")
        print("💡 الميزة جاهزة للاستخدام مع بوت التلجرام")
    else:
        print("⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
