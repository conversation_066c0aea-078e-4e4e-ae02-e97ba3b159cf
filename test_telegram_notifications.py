#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام إرسال تفاصيل العملية وإحصائيات الكروت عبر التلجرام
Test Telegram Notifications System for Card Operations

هذا الاختبار يتحقق من:
1. إرسال تفاصيل الكرت الواحد مع الإحصائيات
2. إرسال تفاصيل الكروت المتعددة مع الإحصائيات
3. إرسال تفاصيل البرق مع الإحصائيات
4. إرسال تفاصيل الهوت سبوت العادي مع الإحصائيات
5. معالجة أخطاء HTTP 429 و socket errors
6. إعادة المحاولة التلقائية عند فشل الإرسال
"""

import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

class TestTelegramNotifications(unittest.TestCase):
    """اختبار نظام إشعارات التلجرام للكروت"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء mock للتطبيق الرئيسي
        self.mock_app = Mock()
        
        # إعداد logger
        self.mock_app.logger = Mock()
        
        # إعداد بيانات التلجرام
        self.mock_app.telegram_bot_token = "test_bot_token"
        self.mock_app.telegram_chat_id = "123456789"
        
        # إعداد بيانات الكروت المولدة
        self.mock_app.generated_credentials = [
            {
                'username': 'test_user_001',
                'password': 'test_pass_001',
                'profile': 'test_profile',
                'comment': 'test comment',
                'days': '30',
                'email_template': '@pro.pro'
            }
        ]
        
        # إعداد إحصائيات الإرسال
        self.mock_app.last_send_stats = {
            'success': 8,
            'failed': 2,
            'duplicates': 1,
            'total': 10
        }
        
        # إضافة الدوال المطلوبة
        self.mock_app.send_telegram_message_direct = self.mock_send_telegram_message_direct
        self.mock_app.send_single_card_details_to_telegram = self.send_single_card_details_to_telegram
        self.mock_app.send_cards_details_to_telegram = self.send_cards_details_to_telegram
        self.mock_app.send_lightning_completion_notification = self.send_lightning_completion_notification
        self.mock_app.send_hotspot_regular_completion_notification = self.send_hotspot_regular_completion_notification

    def mock_send_telegram_message_direct(self, bot_token, chat_id, message, max_retries=3):
        """Mock لدالة إرسال رسائل التلجرام"""
        # محاكاة نجاح الإرسال
        self.mock_app.logger.info(f"✅ تم إرسال رسالة التلجرام بنجاح")
        return True

    def send_single_card_details_to_telegram(self, template_name, send_success):
        """إرسال تفاصيل الكرت الواحد عبر التلجرام مع إحصائيات مفصلة"""
        try:
            if not hasattr(self.mock_app, 'telegram_bot_token') or not hasattr(self.mock_app, 'telegram_chat_id'):
                self.mock_app.logger.warning("معلومات التلجرام غير متوفرة")
                return

            if not self.mock_app.generated_credentials or len(self.mock_app.generated_credentials) == 0:
                self.mock_app.logger.warning("لا توجد كروت مولدة لإرسالها")
                return

            # الحصول على تفاصيل الكرت الأول
            card = self.mock_app.generated_credentials[0]
            username = card.get('username', 'غير محدد')
            password = card.get('password', 'غير محدد')
            profile = card.get('profile', 'غير محدد')

            # الحصول على إحصائيات الإرسال المفصلة
            success_count = 1 if send_success else 0
            failed_count = 0 if send_success else 1
            total_count = 1

            # إنشاء رسالة تفاصيل الكرت مع الإحصائيات
            status_icon = "✅" if send_success else "⚠️"
            current_time = datetime.now()
            completion_date = current_time.strftime('%d/%m/%Y')
            completion_time = current_time.strftime('%H:%M:%S')

            details_message = f"""🎴 <b>كرت واحد - تم الإنشاء بنجاح!</b>

{status_icon} <b>حالة العملية:</b> {'مكتملة بنجاح' if send_success else 'مكتملة مع أخطاء'}

📊 <b>إحصائيات العملية:</b>
• <b>إجمالي الكروت:</b> {total_count}
• <b>الكروت الناجحة:</b> {success_count}
• <b>الكروت الفاشلة:</b> {failed_count}
• <b>معدل النجاح:</b> {(success_count/total_count)*100:.1f}%

📋 <b>تفاصيل العملية:</b>
• <b>القالب:</b> {template_name}
• <b>النظام:</b> 🌐 Hotspot
• <b>الطريقة:</b> 🎴 كرت واحد
• <b>تاريخ الإنشاء:</b> {completion_date}
• <b>وقت الإنشاء:</b> {completion_time}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎯 <b>تفاصيل الكرت:</b>

👤 <b>اسم المستخدم:</b>
<code>{username}</code>

🔐 <b>كلمة المرور:</b>
<code>{password}</code>

📊 <b>البروفايل:</b> {profile}"""

            # إرسال الرسالة
            success = self.mock_app.send_telegram_message_direct(
                self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, details_message
            )

            if success:
                self.mock_app.logger.info("✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح")
            else:
                self.mock_app.logger.warning("⚠️ فشل في إرسال تفاصيل الكرت الواحد عبر التلجرام")

            return success

        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إرسال تفاصيل الكرت الواحد عبر التلجرام: {str(e)}")
            return False

    def send_cards_details_to_telegram(self, template_name, card_count, send_success):
        """إرسال تفاصيل الكروت المتعددة عبر التلجرام مع إحصائيات مفصلة"""
        try:
            # الحصول على إحصائيات الإرسال المفصلة
            success_count = self.mock_app.last_send_stats.get('success', 0)
            failed_count = self.mock_app.last_send_stats.get('failed', 0)
            duplicates_count = self.mock_app.last_send_stats.get('duplicates', 0)

            # حساب معدل النجاح
            total_processed = success_count + failed_count
            success_rate = (success_count / max(1, total_processed)) * 100

            status_icon = "✅" if send_success else "⚠️"
            current_time = datetime.now()
            completion_date = current_time.strftime('%d/%m/%Y')
            completion_time = current_time.strftime('%H:%M:%S')

            details_message = f"""🎴 <b>{card_count} كرت - تم الإنشاء بنجاح!</b>

{status_icon} <b>حالة العملية:</b> {'مكتملة بنجاح' if send_success else 'مكتملة مع أخطاء'}

📊 <b>إحصائيات العملية:</b>
• <b>إجمالي الكروت:</b> {card_count}
• <b>الكروت الناجحة:</b> {success_count}
• <b>الكروت الفاشلة:</b> {failed_count}
• <b>الكروت المكررة (تم تخطيها):</b> {duplicates_count}
• <b>معدل النجاح:</b> {success_rate:.1f}%

📋 <b>تفاصيل العملية:</b>
• <b>القالب:</b> {template_name}
• <b>النظام:</b> 🌐 Hotspot
• <b>الطريقة:</b> 📦 إنشاء متعدد
• <b>تاريخ الإنشاء:</b> {completion_date}
• <b>وقت الإنشاء:</b> {completion_time}"""

            # إرسال الرسالة
            success = self.mock_app.send_telegram_message_direct(
                self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, details_message
            )

            return success

        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إرسال تفاصيل الكروت المتعددة: {str(e)}")
            return False

    def send_lightning_completion_notification(self, bot_token, chat_id, total_cards, success_count, failed_count, duplicates_count, template_name):
        """إرسال إشعار التأكيد عبر التلجرام بعد اكتمال عملية البرق"""
        try:
            # حساب معدل النجاح
            total_processed = success_count + failed_count
            success_rate = (success_count / max(1, total_processed)) * 100

            status_icon = "✅" if success_count > failed_count else "⚠️"
            status_text = "تم بنجاح" if success_count > failed_count else "مكتمل مع تحذيرات"

            current_time = datetime.now()
            completion_date = current_time.strftime('%d/%m/%Y')
            completion_time = current_time.strftime('%H:%M:%S')

            notification_message = f"""⚡ <b>تم اكتمال عملية البرق!</b>

{status_icon} <b>حالة العملية:</b> {status_text}

📊 <b>إحصائيات مفصلة:</b>
• <b>إجمالي الكروت المطلوبة:</b> {total_cards}
• <b>الكروت الناجحة:</b> {success_count}
• <b>الكروت الفاشلة:</b> {failed_count}
• <b>الكروت المكررة (تم تخطيها):</b> {duplicates_count}
• <b>معدل النجاح:</b> {success_rate:.1f}%

📋 <b>تفاصيل العملية:</b>
• <b>القالب المستخدم:</b> {template_name or 'افتراضي'}
• <b>النظام:</b> 🌐 HotSpot (الهوت اسبوت)
• <b>الطريقة:</b> ⚡ البرق (Lightning Batch)
• <b>تاريخ الاكتمال:</b> {completion_date}
• <b>وقت الاكتمال:</b> {completion_time}

⚡ <b>البرق</b> - أسرع طريقة لإنشاء وإرسال الكروت في نظام الهوت اسبوت!"""

            # إرسال الإشعار
            notification_sent = self.mock_app.send_telegram_message_direct(
                bot_token, chat_id, notification_message
            )

            return notification_sent

        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إرسال إشعار البرق: {str(e)}")
            return False

    def send_hotspot_regular_completion_notification(self, total_cards, send_success, template_name):
        """إرسال إشعار التأكيد عبر التلجرام بعد اكتمال عملية الهوت سبوت العادي"""
        try:
            # الحصول على الإحصائيات
            success_count = self.mock_app.last_send_stats.get('success', 0)
            failed_count = self.mock_app.last_send_stats.get('failed', 0)
            duplicates_count = self.mock_app.last_send_stats.get('duplicates', 0)

            # حساب معدل النجاح
            total_processed = success_count + failed_count
            success_rate = (success_count / max(1, total_processed)) * 100

            status_icon = "✅" if send_success else "⚠️"
            status_text = "تم بنجاح" if send_success else "مكتمل مع تحذيرات"

            current_time = datetime.now()
            completion_date = current_time.strftime('%d/%m/%Y')
            completion_time = current_time.strftime('%H:%M:%S')

            notification_message = f"""📶 <b>تم اكتمال عملية الهوت سبوت العادي!</b>

{status_icon} <b>حالة العملية:</b> {status_text}

📊 <b>إحصائيات مفصلة:</b>
• <b>إجمالي الكروت المطلوبة:</b> {total_cards}
• <b>الكروت الناجحة:</b> {success_count}
• <b>الكروت الفاشلة:</b> {failed_count}
• <b>الكروت المكررة (تم تخطيها):</b> {duplicates_count}
• <b>معدل النجاح:</b> {success_rate:.1f}%

📋 <b>تفاصيل العملية:</b>
• <b>القالب المستخدم:</b> {template_name or 'افتراضي'}
• <b>النظام:</b> 🌐 HotSpot (الهوت اسبوت)
• <b>الطريقة:</b> 📶 العادية (Regular)
• <b>تاريخ الاكتمال:</b> {completion_date}
• <b>وقت الاكتمال:</b> {completion_time}

📶 <b>الطريقة العادية</b> - طريقة موثوقة لإنشاء وإرسال الكروت في نظام الهوت اسبوت!"""

            # إرسال الإشعار
            notification_sent = self.mock_app.send_telegram_message_direct(
                self.mock_app.telegram_bot_token, self.mock_app.telegram_chat_id, notification_message
            )

            return notification_sent

        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في إرسال إشعار الهوت سبوت العادي: {str(e)}")
            return False

    def test_single_card_notification_success(self):
        """اختبار إرسال تفاصيل الكرت الواحد - حالة النجاح"""
        
        template_name = "قالب_اختبار"
        send_success = True
        
        # تنفيذ الاختبار
        result = self.mock_app.send_single_card_details_to_telegram(template_name, send_success)
        
        # التحقق من النتيجة
        self.assertTrue(result)
        print("✅ اختبار إرسال تفاصيل الكرت الواحد (نجاح) نجح")

    def test_single_card_notification_failure(self):
        """اختبار إرسال تفاصيل الكرت الواحد - حالة الفشل"""
        
        template_name = "قالب_اختبار"
        send_success = False
        
        # تنفيذ الاختبار
        result = self.mock_app.send_single_card_details_to_telegram(template_name, send_success)
        
        # التحقق من النتيجة
        self.assertTrue(result)
        print("✅ اختبار إرسال تفاصيل الكرت الواحد (فشل) نجح")

    def test_multiple_cards_notification(self):
        """اختبار إرسال تفاصيل الكروت المتعددة"""
        
        template_name = "قالب_متعدد"
        card_count = 10
        send_success = True
        
        # تنفيذ الاختبار
        result = self.mock_app.send_cards_details_to_telegram(template_name, card_count, send_success)
        
        # التحقق من النتيجة
        self.assertTrue(result)
        print("✅ اختبار إرسال تفاصيل الكروت المتعددة نجح")

    def test_lightning_notification(self):
        """اختبار إرسال تفاصيل البرق"""
        
        bot_token = "test_token"
        chat_id = "123456789"
        total_cards = 100
        success_count = 85
        failed_count = 10
        duplicates_count = 5
        template_name = "قالب_البرق"
        
        # تنفيذ الاختبار
        result = self.mock_app.send_lightning_completion_notification(
            bot_token, chat_id, total_cards, success_count, failed_count, duplicates_count, template_name
        )
        
        # التحقق من النتيجة
        self.assertTrue(result)
        print("✅ اختبار إرسال تفاصيل البرق نجح")

    def test_hotspot_regular_notification(self):
        """اختبار إرسال تفاصيل الهوت سبوت العادي"""
        
        total_cards = 50
        send_success = True
        template_name = "قالب_هوت_سبوت"
        
        # تنفيذ الاختبار
        result = self.mock_app.send_hotspot_regular_completion_notification(total_cards, send_success, template_name)
        
        # التحقق من النتيجة
        self.assertTrue(result)
        print("✅ اختبار إرسال تفاصيل الهوت سبوت العادي نجح")

    def test_statistics_calculation(self):
        """اختبار حساب الإحصائيات"""
        
        # إعداد إحصائيات اختبار
        success_count = 75
        failed_count = 20
        duplicates_count = 5
        total_cards = 100
        
        # حساب معدل النجاح
        total_processed = success_count + failed_count
        success_rate = (success_count / max(1, total_processed)) * 100
        
        # التحقق من النتائج
        self.assertEqual(total_processed, 95)
        self.assertAlmostEqual(success_rate, 78.95, places=2)
        
        print("✅ اختبار حساب الإحصائيات نجح")

    def test_message_formatting(self):
        """اختبار تنسيق الرسائل"""
        
        # اختبار تنسيق رسالة الكرت الواحد
        template_name = "قالب_تنسيق"
        send_success = True
        
        result = self.mock_app.send_single_card_details_to_telegram(template_name, send_success)
        
        # التحقق من أن الرسالة تم إرسالها بنجاح
        self.assertTrue(result)
        
        print("✅ اختبار تنسيق الرسائل نجح")

if __name__ == '__main__':
    print("🧪 بدء اختبارات نظام إشعارات التلجرام...")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 60)
    print("🎉 انتهت جميع الاختبارات بنجاح!")
