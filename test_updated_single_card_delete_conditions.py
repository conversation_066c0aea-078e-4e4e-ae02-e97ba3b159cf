#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الشروط المحدثة لزر حذف الكروت الناجحة للكرت الواحد
تاريخ الإنشاء: 2025-07-23
الهدف: التحقق من أن الزر يظهر في النجاح الكامل والفشل الجزئي، ولا يظهر في الفشل الكامل
"""

import re

def test_updated_data_saving_conditions():
    """اختبار شروط حفظ البيانات المحدثة"""
    print("🔍 اختبار شروط حفظ البيانات المحدثة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الشروط المحدثة
    updated_conditions = [
        # يجب أن يكون الشرط success_count > 0 فقط (بدون failed_count > 0)
        r'if success_count > 0 and getattr.*system_type.*hotspot',
        # يجب ألا يحتوي على شرط failed_count > 0 في نفس السطر
        r'حفظ.*كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد'
    ]
    
    # التحقق من عدم وجود الشرط القديم
    old_condition = r'if failed_count > 0 and success_count > 0 and getattr.*system_type.*hotspot'
    
    if re.search(old_condition, func_code, re.IGNORECASE):
        print("❌ الشرط القديم ما زال موجود (يجب إزالته)")
        return False
    else:
        print("✅ تم إزالة الشرط القديم (failed_count > 0)")
    
    all_found = True
    for condition in updated_conditions:
        if re.search(condition, func_code, re.IGNORECASE):
            print(f"✅ شرط محدث موجود: {condition}")
        else:
            print(f"❌ شرط محدث مفقود: {condition}")
            all_found = False
    
    return all_found

def test_updated_button_display_conditions():
    """اختبار شروط إظهار الزر المحدثة"""
    print("\n🔍 اختبار شروط إظهار الزر المحدثة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الشروط المحدثة
    updated_conditions = [
        # يجب أن يكون الشرط success_count > 0 فقط (بدون failed_count > 0)
        r'show_delete_successful_button.*=.*\(\s*success_count > 0',
        # يجب ألا يحتوي على failed_count > 0 في تعريف show_delete_successful_button
        r'تم تعديلها لتشمل النجاح الكامل',
        r'success_count > 0 and.*getattr.*system_type.*hotspot.*and.*hasattr.*single_card_successful_cards'
    ]
    
    # التحقق من عدم وجود الشرط القديم في تعريف show_delete_successful_button
    old_condition = r'show_delete_successful_button.*=.*failed_count > 0 and.*success_count > 0'
    
    if re.search(old_condition, func_code, re.IGNORECASE):
        print("❌ الشرط القديم ما زال موجود في تعريف show_delete_successful_button")
        return False
    else:
        print("✅ تم إزالة الشرط القديم من تعريف show_delete_successful_button")
    
    all_found = True
    for condition in updated_conditions:
        if re.search(condition, func_code, re.IGNORECASE):
            print(f"✅ شرط محدث موجود: {condition}")
        else:
            print(f"❌ شرط محدث مفقود: {condition}")
            all_found = False
    
    return all_found

def test_updated_text_content():
    """اختبار النص التوضيحي المحدث"""
    print("\n🔍 اختبار النص التوضيحي المحدث...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود النصوص المحدثة
    updated_text_elements = [
        # النص الشرطي حسب حالة العملية
        r'تحديد النص المناسب حسب حالة العملية',
        r'if failed_count > 0:.*# حالة الفشل الجزئي',
        r'else:.*# حالة النجاح الكامل',
        # النص للفشل الجزئي
        r'نظراً لوجود.*كرت فاشل.*يمكنك اختيار حذف',
        # النص للنجاح الكامل
        r'يمكنك اختيار حذف.*إذا لم تعد بحاجة إليها',
        # استخدام المتغير delete_reason
        r'delete_reason.*=.*f"',
        r'{delete_reason}'
    ]
    
    missing_elements = []
    for element in updated_text_elements:
        if re.search(element, content, re.IGNORECASE):
            print(f"✅ عنصر نص محدث موجود: {element}")
        else:
            print(f"❌ عنصر نص محدث مفقود: {element}")
            missing_elements.append(element)
    
    return len(missing_elements) == 0

def simulate_updated_scenarios():
    """محاكاة السيناريوهات المحدثة"""
    print("\n🧪 محاكاة السيناريوهات المحدثة...")
    
    scenarios = [
        {
            "name": "نجاح كامل في HotSpot - يجب أن يظهر الزر الآن",
            "failed_count": 0,
            "success_count": 3,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "reason": "توجد كروت ناجحة في HotSpot مع بيانات محفوظة",
            "text_type": "النجاح الكامل"
        },
        {
            "name": "فشل جزئي في HotSpot - يجب أن يظهر الزر كما هو",
            "failed_count": 1,
            "success_count": 2,
            "system_type": "hotspot",
            "has_data": True,
            "expected": True,
            "reason": "توجد كروت ناجحة في HotSpot مع بيانات محفوظة",
            "text_type": "الفشل الجزئي"
        },
        {
            "name": "فشل كامل في HotSpot - يجب ألا يظهر الزر",
            "failed_count": 3,
            "success_count": 0,
            "system_type": "hotspot",
            "has_data": False,
            "expected": False,
            "reason": "لا توجد كروت ناجحة للحذف",
            "text_type": "غير مطبق"
        },
        {
            "name": "نجاح كامل في User Manager - يجب ألا يظهر الزر",
            "failed_count": 0,
            "success_count": 3,
            "system_type": "usermanager",
            "has_data": True,
            "expected": False,
            "reason": "ليس نظام HotSpot",
            "text_type": "غير مطبق"
        },
        {
            "name": "نجاح كامل في HotSpot بدون بيانات - يجب ألا يظهر الزر",
            "failed_count": 0,
            "success_count": 3,
            "system_type": "hotspot",
            "has_data": False,
            "expected": False,
            "reason": "لا توجد بيانات محفوظة",
            "text_type": "غير مطبق"
        }
    ]
    
    all_correct = True
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        
        # تطبيق الشروط المحدثة (بدون failed_count > 0)
        condition1 = scenario['success_count'] > 0
        condition2 = scenario['system_type'] == 'hotspot'
        condition3 = scenario['has_data']
        
        result = condition1 and condition2 and condition3
        
        print(f"   - success_count > 0: {condition1}")
        print(f"   - system_type == 'hotspot': {condition2}")
        print(f"   - has_data: {condition3}")
        print(f"   - النتيجة: {result}")
        print(f"   - المتوقع: {scenario['expected']}")
        print(f"   - نوع النص: {scenario['text_type']}")
        
        if result == scenario['expected']:
            print(f"   ✅ صحيح - {scenario['reason']}")
        else:
            print(f"   ❌ خطأ - متوقع {scenario['expected']} لكن حصل على {result}")
            all_correct = False
    
    return all_correct

def test_backward_compatibility():
    """اختبار التوافق مع الميزات الأخرى"""
    print("\n🔍 اختبار التوافق مع الميزات الأخرى...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من أن ميزة البرق لم تتأثر
    lightning_elements = [
        'lightning_delete_successful_',
        'lightning_successful_cards',
        'handle_lightning_delete_successful_request',
        # التحقق من أن شروط البرق لم تتغير
        r'failed_count > 0 and.*success_count > 0.*lightning'
    ]
    
    # التحقق من أن ميزة إعادة المحاولة لم تتأثر
    retry_elements = [
        'show_retry_failed_button',
        'retry_failed_cards_single_',
        'failed_count > 0.*failed_cards_info'
    ]
    
    all_preserved = True
    
    print("   🔍 فحص ميزة البرق:")
    for element in lightning_elements[:3]:  # فحص العناصر الأساسية فقط
        if re.search(element, content, re.IGNORECASE):
            print(f"   ✅ عنصر البرق محفوظ: {element}")
        else:
            print(f"   ⚠️ عنصر البرق قد يكون مفقود: {element}")
    
    print("   🔍 فحص ميزة إعادة المحاولة:")
    for element in retry_elements:
        if re.search(element, content, re.IGNORECASE):
            print(f"   ✅ عنصر إعادة المحاولة محفوظ: {element}")
        else:
            print(f"   ⚠️ عنصر إعادة المحاولة قد يكون مفقود: {element}")
    
    return all_preserved

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار الشروط المحدثة لزر حذف الكروت الناجحة للكرت الواحد")
    print("="*80)
    
    tests = [
        ("شروط حفظ البيانات المحدثة", test_updated_data_saving_conditions),
        ("شروط إظهار الزر المحدثة", test_updated_button_display_conditions),
        ("النص التوضيحي المحدث", test_updated_text_content),
        ("محاكاة السيناريوهات المحدثة", simulate_updated_scenarios),
        ("التوافق مع الميزات الأخرى", test_backward_compatibility)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
            failed += 1
        print("-" * 60)
    
    print("\n" + "="*80)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تحديث شروط زر حذف الكروت الناجحة للكرت الواحد بنجاح!")
        print("💡 الزر الآن يظهر في النجاح الكامل والفشل الجزئي")
        
        print("\n🎯 التحديثات المُطبقة:")
        print("✅ إزالة شرط failed_count > 0 من حفظ البيانات")
        print("✅ إزالة شرط failed_count > 0 من إظهار الزر")
        print("✅ إضافة نص مناسب لحالة النجاح الكامل")
        print("✅ الحفاظ على نص الفشل الجزئي")
        print("✅ الحفاظ على جميع الميزات الأخرى")
        
        print("\n📋 الحالات الجديدة:")
        print("✅ النجاح الكامل في HotSpot - يظهر الزر")
        print("✅ الفشل الجزئي في HotSpot - يظهر الزر (كما هو)")
        print("❌ الفشل الكامل في HotSpot - لا يظهر الزر")
        print("❌ أي حالة في User Manager - لا يظهر الزر")
        print("❌ أي حالة بدون بيانات محفوظة - لا يظهر الزر")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - قد تحتاج مراجعة إضافية.")
    
    return failed == 0

if __name__ == "__main__":
    main()
