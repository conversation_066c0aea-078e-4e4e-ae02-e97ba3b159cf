#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لميزة الإشعارات التلقائية في نظام Lightning User Manager
يختبر إرسال الإشعارات في النقطتين المحددتين:
1. قبل إنشاء الكروت (في بداية الاسكربت الأول)
2. قبل حذف الجدولة (في نهاية الاسكربت الأخير)
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import sys
import json
from datetime import datetime

class TestLightningUserManagerNotifications(unittest.TestCase):
    """اختبار ميزة الإشعارات التلقائية في Lightning User Manager"""
    
    def setUp(self):
        """إعداد الاختبار"""
        print("🧪 إعداد اختبار إشعارات Lightning User Manager...")
        
        # إنشاء مجلد مؤقت للاختبار
        self.test_dir = tempfile.mkdtemp()
        
        # محاكاة التطبيق
        self.mock_app = Mock()
        self.mock_app.system_type = 'user_manager'
        self.mock_app.logger = Mock()
        self.mock_app.telegram_bot_token = "test_bot_token_123"
        self.mock_app.telegram_chat_id = "998535391"
        
        # محاكاة إعدادات MikroTik
        self.mock_app.version_combo = Mock()
        self.mock_app.version_combo.get.return_value = "v7"
        
        # محاكاة بيانات الكروت المولدة
        self.mock_app.generated_credentials = [
            {
                'username': f'test_user_{i:03d}',
                'password': f'test_pass_{i:03d}',
                'profile': 'test_profile',
                'comment': 'test comment',
                'days': '30'
            } for i in range(1, 51)  # 50 كرت للاختبار
        ]
        
        # إضافة الدوال المطلوبة
        self.mock_app.get_user_manager_cards_count = self.mock_get_user_manager_cards_count
        self.mock_app.send_lightning_pre_creation_notification = self.mock_send_lightning_pre_creation_notification
        self.mock_app.send_lightning_pre_cleanup_notification = self.mock_send_lightning_pre_cleanup_notification
        self.mock_app.send_telegram_message_direct = self.mock_send_telegram_message_direct
        self.mock_app.create_lightning_script_content = self.mock_create_lightning_script_content
        self.mock_app.connect_api = self.mock_connect_api
        
        # متغيرات لتتبع الاختبار
        self.notifications_sent = []
        self.api_calls_made = []
        self.current_cards_count = 100  # عدد الكروت الحالي المحاكي
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
        print("🧹 تم تنظيف ملفات الاختبار")

    def mock_connect_api(self):
        """محاكاة الاتصال بـ MikroTik API"""
        mock_api = Mock()
        
        # محاكاة الحصول على المستخدمين
        mock_users = [{'id': f'*{i}', 'name': f'user_{i}'} for i in range(1, self.current_cards_count + 1)]
        mock_api.get_resource.return_value.get.return_value = mock_users
        
        return mock_api

    def mock_get_user_manager_cards_count(self):
        """محاكاة الحصول على عدد الكروت في User Manager"""
        self.api_calls_made.append('get_user_manager_cards_count')
        self.mock_app.logger.info(f"تم الحصول على عدد الكروت في User Manager: {self.current_cards_count}")
        return self.current_cards_count

    def mock_send_telegram_message_direct(self, bot_token, chat_id, message):
        """محاكاة إرسال رسائل Telegram"""
        self.mock_app.logger.info(f"✅ تم إرسال رسالة التلجرام بنجاح")
        
        # تسجيل الرسالة المرسلة
        notification_info = {
            'bot_token': bot_token,
            'chat_id': chat_id,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.notifications_sent.append(notification_info)
        
        return True

    def mock_send_lightning_pre_creation_notification(self, bot_token, chat_id):
        """محاكاة إرسال إشعار قبل إنشاء الكروت"""
        # الحصول على عدد الكروت الحالي
        current_cards_count = self.mock_get_user_manager_cards_count()
        
        if current_cards_count is None:
            self.mock_app.logger.warning("لم يتم الحصول على عدد الكروت الحالي، تخطي الإشعار")
            return False
        
        # إنشاء رسالة الإشعار
        notification_message = f"""⚡ البرق - User Manager

📊 إشعار قبل إنشاء الكروت:

🔢 عدد الكروت الحالي: {current_cards_count:,} كرت
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d')}
⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}

⏳ جاري بدء عملية إنشاء الكروت الجديدة...

🚀 البرق - أسرع طريقة لإنشاء الكروت في User Manager!"""

        # إرسال الإشعار
        success = self.mock_send_telegram_message_direct(bot_token, chat_id, notification_message)
        
        if success:
            self.mock_app.logger.info(f"✅ تم إرسال إشعار البرق قبل الإنشاء - عدد الكروت الحالي: {current_cards_count}")
            return True
        else:
            self.mock_app.logger.error("❌ فشل في إرسال إشعار البرق قبل الإنشاء")
            return False

    def mock_send_lightning_pre_cleanup_notification(self, bot_token, chat_id):
        """محاكاة إرسال إشعار قبل حذف الجدولة"""
        # تحديث عدد الكروت بعد الإنشاء (محاكاة إضافة الكروت الجديدة)
        self.current_cards_count += len(self.mock_app.generated_credentials)
        
        # الحصول على عدد الكروت بعد الإنشاء
        current_cards_count = self.mock_get_user_manager_cards_count()
        
        if current_cards_count is None:
            self.mock_app.logger.warning("لم يتم الحصول على عدد الكروت بعد الإنشاء، تخطي الإشعار")
            return False
        
        # إنشاء رسالة الإشعار
        notification_message = f"""⚡ البرق - User Manager

📊 إشعار قبل حذف الجدولة:

🔢 عدد الكروت بعد الإنشاء: {current_cards_count:,} كرت
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d')}
⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}

✅ تم إكمال إنشاء الكروت الجديدة بنجاح!
🗑️ جاري حذف الجدولة والتنظيف...

🚀 البرق - تم إكمال العملية بنجاح!"""

        # إرسال الإشعار
        success = self.mock_send_telegram_message_direct(bot_token, chat_id, notification_message)
        
        if success:
            self.mock_app.logger.info(f"✅ تم إرسال إشعار البرق قبل التنظيف - عدد الكروت بعد الإنشاء: {current_cards_count}")
            return True
        else:
            self.mock_app.logger.error("❌ فشل في إرسال إشعار البرق قبل التنظيف")
            return False

    def mock_create_lightning_script_content(self, credentials, script_num, total_scripts, script_names, delay_seconds):
        """محاكاة إنشاء محتوى سكريبت البرق مع الإشعارات"""
        script_content = f"""# MikroTik Lightning Script {script_num + 1}/{total_scripts}
# Generated for testing notifications

:put "⚡ بدء تنفيذ سكريبت البرق {script_num + 1}/{total_scripts}";
:put "📊 عدد الكروت في هذا السكريبت: {len(credentials)}";
:put "⏰ وقت البدء: $[/system clock get time]";
"""
        
        # إضافة إشعار قبل إنشاء الكروت في السكريبت الأول فقط (User Manager فقط)
        if script_num == 0 and self.mock_app.system_type == 'user_manager':
            bot_token = getattr(self.mock_app, 'telegram_bot_token', '')
            chat_id = getattr(self.mock_app, 'telegram_chat_id', '')

            if bot_token and chat_id:
                script_content += f"""
# إشعار قبل إنشاء الكروت
:put "📱 إرسال إشعار قبل إنشاء الكروت...";
:local currentCardsCount [len [/user-manager/user find]];
:local currentDate [/system clock get date];
:local currentTime [/system clock get time];
:local notificationMessage ("⚡ البرق - User Manager\\n\\n📊 إشعار قبل إنشاء الكروت:\\n\\n🔢 عدد الكروت الحالي: " . $currentCardsCount . " كرت\\n📅 التاريخ: " . $currentDate . "\\n⏰ الوقت: " . $currentTime . "\\n\\n⏳ جاري بدء عملية إنشاء الكروت الجديدة...\\n\\n🚀 البرق - أسرع طريقة لإنشاء الكروت في User Manager!");
:do {{ /tool fetch url="https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text=$notificationMessage" mode=https; }} on-error={{ :put "❌ فشل في إرسال إشعار قبل الإنشاء"; }};
:put "✅ تم إرسال إشعار قبل إنشاء الكروت";
:delay 2s;
"""
        
        # إضافة محتوى إنشاء الكروت
        for i, cred in enumerate(credentials):
            script_content += f"""
# المستخدم {i + 1}: {cred['username']}
:do {{
    /user-manager/user add customer="admin" username="{cred['username']}" password="{cred['password']}" profile="{cred['profile']}";
    :put "✅ تم إضافة المستخدم: {cred['username']}";
}} on-error={{
    :put "❌ خطأ في إضافة المستخدم: {cred['username']}";
}};
"""
        
        # إضافة إشعار قبل حذف الجدولة في السكريبت الأخير (User Manager فقط)
        if script_num == total_scripts - 1 and self.mock_app.system_type == 'user_manager':
            bot_token = getattr(self.mock_app, 'telegram_bot_token', '')
            chat_id = getattr(self.mock_app, 'telegram_chat_id', '')

            if bot_token and chat_id:
                script_content += f"""
# إشعار قبل حذف الجدولة
:put "📱 إرسال إشعار قبل حذف الجدولة...";
:local finalCardsCount [len [/user-manager/user find]];
:local currentDate [/system clock get date];
:local currentTime [/system clock get time];
:local cleanupNotificationMessage ("⚡ البرق - User Manager\\n\\n📊 إشعار قبل حذف الجدولة:\\n\\n🔢 عدد الكروت بعد الإنشاء: " . $finalCardsCount . " كرت\\n📅 التاريخ: " . $currentDate . "\\n⏰ الوقت: " . $currentTime . "\\n\\n✅ تم إكمال إنشاء الكروت الجديدة بنجاح!\\n🗑️ جاري حذف الجدولة والتنظيف...\\n\\n🚀 البرق - تم إكمال العملية بنجاح!");
:do {{ /tool fetch url="https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text=$cleanupNotificationMessage" mode=https; }} on-error={{ :put "❌ فشل في إرسال إشعار قبل التنظيف"; }};
:put "✅ تم إرسال إشعار قبل حذف الجدولة";
:delay 2s;
"""
        
        # إضافة أوامر التنظيف
        script_content += f"""
:put "🎉 تم الانتهاء من السكريبت {script_num + 1}/{total_scripts}";
:put "🗑️ تنظيف السكريبتات والجدولة...";
"""
        
        return script_content

    def test_get_user_manager_cards_count(self):
        """اختبار دالة الحصول على عدد الكروت في User Manager"""
        print("🧪 اختبار دالة الحصول على عدد الكروت...")
        
        # تشغيل الدالة
        result = self.mock_app.get_user_manager_cards_count()
        
        # التحقق من النتائج
        self.assertIsNotNone(result, "يجب أن ترجع الدالة عدد الكروت")
        self.assertEqual(result, 100, "يجب أن يكون عدد الكروت 100")
        self.assertIn('get_user_manager_cards_count', self.api_calls_made, "يجب أن يتم استدعاء API")
        
        print(f"✅ تم الحصول على عدد الكروت بنجاح: {result}")

    def test_pre_creation_notification(self):
        """اختبار إشعار قبل إنشاء الكروت"""
        print("🧪 اختبار إشعار قبل إنشاء الكروت...")
        
        # تشغيل الدالة
        result = self.mock_app.send_lightning_pre_creation_notification(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id
        )
        
        # التحقق من النتائج
        self.assertTrue(result, "يجب أن يتم إرسال الإشعار بنجاح")
        self.assertEqual(len(self.notifications_sent), 1, "يجب أن يتم إرسال إشعار واحد")
        
        # التحقق من محتوى الإشعار
        notification = self.notifications_sent[0]
        self.assertIn("إشعار قبل إنشاء الكروت", notification['message'])
        self.assertIn("عدد الكروت الحالي: 100", notification['message'])
        self.assertIn("جاري بدء عملية إنشاء الكروت", notification['message'])
        
        print("✅ تم إرسال إشعار قبل إنشاء الكروت بنجاح")

    def test_pre_cleanup_notification(self):
        """اختبار إشعار قبل حذف الجدولة"""
        print("🧪 اختبار إشعار قبل حذف الجدولة...")
        
        # تشغيل الدالة
        result = self.mock_app.send_lightning_pre_cleanup_notification(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id
        )
        
        # التحقق من النتائج
        self.assertTrue(result, "يجب أن يتم إرسال الإشعار بنجاح")
        self.assertEqual(len(self.notifications_sent), 1, "يجب أن يتم إرسال إشعار واحد")
        
        # التحقق من محتوى الإشعار
        notification = self.notifications_sent[0]
        self.assertIn("إشعار قبل حذف الجدولة", notification['message'])
        self.assertIn("عدد الكروت بعد الإنشاء: 150", notification['message'])  # 100 + 50
        self.assertIn("تم إكمال إنشاء الكروت الجديدة بنجاح", notification['message'])
        
        print("✅ تم إرسال إشعار قبل حذف الجدولة بنجاح")

    def test_lightning_script_content_with_notifications(self):
        """اختبار إنشاء محتوى سكريبت البرق مع الإشعارات"""
        print("🧪 اختبار إنشاء محتوى سكريبت البرق مع الإشعارات...")

        # إعداد بيانات الاختبار
        credentials = self.mock_app.generated_credentials[:10]  # أول 10 كروت
        script_names = ['script1', 'script2', 'script3']

        # اختبار السكريبت الأول (يجب أن يحتوي على إشعار قبل الإنشاء)
        first_script = self.mock_app.create_lightning_script_content(
            credentials, 0, 3, script_names, 5
        )

        # التحقق من وجود إشعار قبل الإنشاء في السكريبت الأول
        self.assertIn("إرسال إشعار قبل إنشاء الكروت", first_script)
        self.assertIn("currentCardsCount", first_script)
        self.assertIn("telegram.org/bot", first_script)
        self.assertIn(self.mock_app.telegram_bot_token, first_script)
        self.assertIn(self.mock_app.telegram_chat_id, first_script)

        print("✅ السكريبت الأول يحتوي على إشعار قبل الإنشاء")

        # اختبار السكريبت الأخير (يجب أن يحتوي على إشعار قبل التنظيف)
        last_script = self.mock_app.create_lightning_script_content(
            credentials, 2, 3, script_names, 5
        )

        # التحقق من وجود إشعار قبل التنظيف في السكريبت الأخير
        self.assertIn("إرسال إشعار قبل حذف الجدولة", last_script)
        self.assertIn("finalCardsCount", last_script)
        self.assertIn("cleanupNotificationMessage", last_script)

        print("✅ السكريبت الأخير يحتوي على إشعار قبل التنظيف")

        # اختبار السكريبت الأوسط (لا يجب أن يحتوي على إشعارات)
        middle_script = self.mock_app.create_lightning_script_content(
            credentials, 1, 3, script_names, 5
        )

        # التحقق من عدم وجود إشعارات في السكريبت الأوسط
        self.assertNotIn("إرسال إشعار قبل إنشاء الكروت", middle_script)
        self.assertNotIn("إرسال إشعار قبل حذف الجدولة", middle_script)

        print("✅ السكريبت الأوسط لا يحتوي على إشعارات (صحيح)")

    def test_complete_lightning_workflow_with_notifications(self):
        """اختبار سير العمل الكامل للبرق مع الإشعارات"""
        print("🧪 اختبار سير العمل الكامل للبرق مع الإشعارات...")

        # محاكاة سير العمل الكامل

        # 1. إرسال إشعار قبل الإنشاء
        pre_creation_result = self.mock_app.send_lightning_pre_creation_notification(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id
        )
        self.assertTrue(pre_creation_result, "يجب أن يتم إرسال إشعار قبل الإنشاء")

        # 2. محاكاة إنشاء الكروت (تحديث العدد)
        initial_count = self.current_cards_count

        # 3. إرسال إشعار قبل التنظيف
        pre_cleanup_result = self.mock_app.send_lightning_pre_cleanup_notification(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id
        )
        self.assertTrue(pre_cleanup_result, "يجب أن يتم إرسال إشعار قبل التنظيف")

        # التحقق من إرسال الإشعارين
        self.assertEqual(len(self.notifications_sent), 2, "يجب أن يتم إرسال إشعارين")

        # التحقق من ترتيب الإشعارات
        first_notification = self.notifications_sent[0]
        second_notification = self.notifications_sent[1]

        self.assertIn("قبل إنشاء الكروت", first_notification['message'])
        self.assertIn(f"عدد الكروت الحالي: {initial_count}", first_notification['message'])

        self.assertIn("قبل حذف الجدولة", second_notification['message'])
        self.assertIn(f"عدد الكروت بعد الإنشاء: {initial_count + 50}", second_notification['message'])

        print("✅ تم إرسال الإشعارين بالترتيب الصحيح")
        print(f"   📊 العدد قبل الإنشاء: {initial_count}")
        print(f"   📊 العدد بعد الإنشاء: {initial_count + 50}")

    def test_notifications_only_for_user_manager(self):
        """اختبار أن الإشعارات تُرسل فقط لنظام User Manager"""
        print("🧪 اختبار أن الإشعارات خاصة بنظام User Manager فقط...")

        # تغيير نوع النظام إلى HotSpot
        self.mock_app.system_type = 'hotspot'

        # محاولة إنشاء سكريبت للهوت سبوت
        credentials = self.mock_app.generated_credentials[:5]
        script_names = ['hotspot_script1']

        hotspot_script = self.mock_app.create_lightning_script_content(
            credentials, 0, 1, script_names, 5
        )

        # التحقق من عدم وجود إشعارات في سكريبت الهوت سبوت
        self.assertNotIn("إرسال إشعار قبل إنشاء الكروت", hotspot_script)
        self.assertNotIn("إرسال إشعار قبل حذف الجدولة", hotspot_script)
        self.assertNotIn("telegram.org/bot", hotspot_script)

        print("✅ سكريبتات HotSpot لا تحتوي على إشعارات (صحيح)")

        # إعادة تعيين النظام إلى User Manager
        self.mock_app.system_type = 'user_manager'

    def test_notifications_with_missing_telegram_settings(self):
        """اختبار سلوك الإشعارات عند عدم وجود إعدادات Telegram"""
        print("🧪 اختبار سلوك الإشعارات عند عدم وجود إعدادات Telegram...")

        # حذف إعدادات Telegram
        original_token = self.mock_app.telegram_bot_token
        original_chat_id = self.mock_app.telegram_chat_id

        self.mock_app.telegram_bot_token = ''
        self.mock_app.telegram_chat_id = ''

        # محاولة إنشاء سكريبت بدون إعدادات Telegram
        credentials = self.mock_app.generated_credentials[:5]
        script_names = ['test_script']

        script_without_telegram = self.mock_app.create_lightning_script_content(
            credentials, 0, 1, script_names, 5
        )

        # التحقق من عدم وجود إشعارات عند عدم وجود إعدادات Telegram
        self.assertNotIn("إرسال إشعار قبل إنشاء الكروت", script_without_telegram)
        self.assertNotIn("telegram.org/bot", script_without_telegram)

        print("✅ لا يتم إضافة إشعارات عند عدم وجود إعدادات Telegram (صحيح)")

        # إعادة تعيين الإعدادات
        self.mock_app.telegram_bot_token = original_token
        self.mock_app.telegram_chat_id = original_chat_id

    def test_notification_message_format(self):
        """اختبار تنسيق رسائل الإشعارات"""
        print("🧪 اختبار تنسيق رسائل الإشعارات...")

        # إرسال إشعار قبل الإنشاء
        self.mock_app.send_lightning_pre_creation_notification(
            self.mock_app.telegram_bot_token,
            self.mock_app.telegram_chat_id
        )

        # التحقق من تنسيق الرسالة
        notification = self.notifications_sent[0]
        message = notification['message']

        # التحقق من وجود العناصر المطلوبة
        required_elements = [
            "⚡ البرق - User Manager",
            "📊 إشعار قبل إنشاء الكروت:",
            "🔢 عدد الكروت الحالي:",
            "📅 التاريخ:",
            "⏰ الوقت:",
            "⏳ جاري بدء عملية إنشاء الكروت الجديدة...",
            "🚀 البرق - أسرع طريقة لإنشاء الكروت في User Manager!"
        ]

        for element in required_elements:
            self.assertIn(element, message, f"يجب أن تحتوي الرسالة على: {element}")

        print("✅ تنسيق رسالة الإشعار صحيح")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار ميزة الإشعارات التلقائية في Lightning User Manager")
    print("=" * 80)

    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestLightningUserManagerNotifications)

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    print("=" * 80)

    # عرض النتائج
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت! ميزة الإشعارات التلقائية تعمل بشكل صحيح.")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
        return True
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")

        # عرض تفاصيل الأخطاء
        for test, error in result.failures + result.errors:
            print(f"\n❌ فشل الاختبار: {test}")
            print(f"   الخطأ: {error}")

        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
