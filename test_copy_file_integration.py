#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تكامل ميزة حذف المستخدمين بالإيميل في الملف المنسوخ
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن جميع الدوال والميزات تم إضافتها بشكل صحيح
"""

import os
import re

class TestCopyFileIntegration:
    """اختبار تكامل الملف المنسوخ"""
    
    def __init__(self):
        self.copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
        
    def test_button_addition(self):
        """اختبار إضافة الزر الجديد"""
        print("🔍 اختبار إضافة الزر الجديد...")
        
        if not os.path.exists(self.copy_file):
            print(f"❌ الملف غير موجود: {self.copy_file}")
            return False
        
        with open(self.copy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الزر الجديد
        button_pattern = r'🗑️ حذف يوزرات بالإيميل.*delete_users_by_email'
        if not re.search(button_pattern, content):
            print("❌ الزر الجديد غير موجود في القائمة")
            return False
        
        print("✅ تم العثور على الزر الجديد في القائمة")
        return True
    
    def test_callback_handling(self):
        """اختبار معالجة callback_data"""
        print("🔍 اختبار معالجة callback_data...")
        
        with open(self.copy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن معالجة callback_data
        patterns = [
            r'callback_data == "delete_users_by_email"',
            r'callback_data\.startswith\("confirm_delete_email_"\)',
            r'callback_data == "cancel_delete_email"'
        ]
        
        for pattern in patterns:
            if not re.search(pattern, content):
                print(f"❌ معالجة callback غير موجودة: {pattern}")
                return False
        
        print("✅ جميع معالجات callback_data موجودة")
        return True
    
    def test_function_definitions(self):
        """اختبار وجود تعريفات الدوال"""
        print("🔍 اختبار وجود تعريفات الدوال...")
        
        with open(self.copy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'handle_delete_users_by_email_request',
            'handle_email_pattern_input',
            'search_and_confirm_delete_users',
            'handle_confirm_delete_users_by_email',
            'handle_cancel_delete_users_by_email',
            'execute_delete_users_by_email'
        ]
        
        for func_name in required_functions:
            pattern = f'def {func_name}\\('
            if not re.search(pattern, content):
                print(f"❌ الدالة غير موجودة: {func_name}")
                return False
            print(f"✅ الدالة موجودة: {func_name}")
        
        return True
    
    def test_text_message_handling(self):
        """اختبار معالجة الرسائل النصية"""
        print("🔍 اختبار معالجة الرسائل النصية...")
        
        with open(self.copy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن معالجة نمط الإيميل في process_telegram_command
        pattern = r'waiting_for_email_pattern.*chat_id in self\.waiting_for_email_pattern'
        if not re.search(pattern, content):
            print("❌ معالجة نمط الإيميل غير موجودة في process_telegram_command")
            return False
        
        print("✅ معالجة الرسائل النصية موجودة")
        return True
    
    def test_code_structure(self):
        """اختبار بنية الكود"""
        print("🔍 اختبار بنية الكود...")
        
        with open(self.copy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود العناصر المهمة
        important_elements = [
            'waiting_for_email_pattern',
            'users_to_delete_by_email',
            'HotSpot فقط',
            'Comment فارغ',
            'نمط الإيميل',
            'تقرير عملية الحذف النهائي'
        ]
        
        for element in important_elements:
            if element not in content:
                print(f"❌ العنصر المهم غير موجود: {element}")
                return False
        
        print("✅ جميع العناصر المهمة موجودة")
        return True
    
    def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        print("🔍 اختبار معالجة الأخطاء...")
        
        with open(self.copy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن معالجة الأخطاء
        error_patterns = [
            r'except Exception as e:',
            r'logger\.error',
            r'فشل في الاتصال',
            r'حدث خطأ'
        ]
        
        error_count = 0
        for pattern in error_patterns:
            matches = re.findall(pattern, content)
            error_count += len(matches)
        
        if error_count < 10:  # يجب أن يكون هناك معالجة أخطاء كافية
            print(f"❌ معالجة الأخطاء غير كافية: {error_count} حالة")
            return False
        
        print(f"✅ معالجة الأخطاء كافية: {error_count} حالة")
        return True
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار تكامل الملف المنسوخ")
        print("="*60)
        
        tests = [
            ("إضافة الزر", self.test_button_addition),
            ("معالجة callback", self.test_callback_handling),
            ("تعريفات الدوال", self.test_function_definitions),
            ("معالجة الرسائل", self.test_text_message_handling),
            ("بنية الكود", self.test_code_structure),
            ("معالجة الأخطاء", self.test_error_handling)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 تشغيل: {test_name}")
                result = test_func()
                if result:
                    print(f"✅ نجح: {test_name}")
                    passed += 1
                else:
                    print(f"❌ فشل: {test_name}")
                    failed += 1
            except Exception as e:
                print(f"❌ خطأ في {test_name}: {str(e)}")
                failed += 1
        
        print("\n" + "="*60)
        print("📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("🎉 جميع الاختبارات نجحت! الملف المنسوخ جاهز للاستخدام.")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        
        return failed == 0

def main():
    """الدالة الرئيسية"""
    tester = TestCopyFileIntegration()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
