#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تشخيص مشكلة عدم ظهور زر حذف الكروت المرسلة بنجاح
Debug Test for Delete Successful Cards Button Issue

هذا الاختبار يحاكي الظروف التي يجب أن يظهر فيها الزر ويتحقق من كل شرط
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockLogger:
    """Mock للـ logger"""
    def info(self, message):
        print(f"[INFO] {message}")
    
    def error(self, message):
        print(f"[ERROR] {message}")
    
    def warning(self, message):
        print(f"[WARNING] {message}")

class MockApp:
    """محاكاة التطبيق لاختبار شروط ظهور الزر"""
    
    def __init__(self):
        self.logger = MockLogger()
        self.system_type = 'hotspot'
        self.telegram_bot_token = "test_token"
        self.telegram_chat_id = "123456789"
        
        # محاكاة إحصائيات البرق مع كروت ناجحة وفاشلة
        self.last_send_stats = {
            'success': 8,
            'failed': 2,
            'duplicates': 0,
            'total': 10,
            'successful_usernames': [
                'user_001', 'user_002', 'user_003', 'user_004',
                'user_005', 'user_006', 'user_007', 'user_008'
            ]
        }
        
        # محاكاة قائمة الكروت الناجحة
        self.lightning_successful_cards = [
            'user_001', 'user_002', 'user_003', 'user_004',
            'user_005', 'user_006', 'user_007', 'user_008'
        ]
        
        # محاكاة معلومات الكروت الناجحة
        from datetime import datetime
        self.lightning_successful_cards_info = {
            'timestamp': datetime.now().isoformat(),
            'total_successful': 8,
            'total_failed': 2,
            'total_cards': 10,
            'system_type': 'hotspot',
            'operation_type': 'lightning'
        }

    def test_button_conditions(self):
        """اختبار شروط ظهور زر حذف الكروت المرسلة بنجاح"""
        
        print("=" * 80)
        print("🧪 اختبار شروط ظهور زر حذف الكروت المرسلة بنجاح")
        print("=" * 80)
        
        # الحصول على الإحصائيات
        success_count = self.last_send_stats.get('success', 0)
        failed_count = self.last_send_stats.get('failed', 0)
        
        print(f"📊 الإحصائيات:")
        print(f"   - success_count: {success_count}")
        print(f"   - failed_count: {failed_count}")
        print(f"   - system_type: {self.system_type}")
        
        # اختبار كل شرط على حدة
        print(f"\n🔍 اختبار الشروط:")
        
        condition1 = failed_count > 0
        print(f"   1. failed_count > 0: {condition1} (failed_count={failed_count})")
        
        condition2 = success_count > 0
        print(f"   2. success_count > 0: {condition2} (success_count={success_count})")
        
        condition3 = hasattr(self, 'lightning_successful_cards')
        print(f"   3. hasattr(lightning_successful_cards): {condition3}")
        
        condition4 = bool(self.lightning_successful_cards) if condition3 else False
        if condition3:
            print(f"   4. bool(lightning_successful_cards): {condition4} (عدد={len(self.lightning_successful_cards)})")
        else:
            print(f"   4. bool(lightning_successful_cards): {condition4} (المتغير غير موجود)")
        
        # النتيجة النهائية
        show_delete_successful_button = condition1 and condition2 and condition3 and condition4
        
        print(f"\n✅ النتيجة النهائية:")
        print(f"   show_delete_successful_button = {show_delete_successful_button}")
        
        if show_delete_successful_button:
            print(f"   🎉 يجب أن يظهر زر حذف الكروت المرسلة بنجاح!")
            print(f"   📝 نص الزر: '🗑️ حذف الكروت المرسلة بنجاح من هذه العملية ({success_count})'")
            print(f"   🔗 callback_data: 'lightning_delete_successful_{success_count}'")
        else:
            print(f"   ❌ لن يظهر زر حذف الكروت المرسلة بنجاح")
            print(f"   🔍 الشروط غير المستوفاة:")
            if not condition1:
                print(f"      - لا توجد كروت فاشلة (failed_count={failed_count})")
            if not condition2:
                print(f"      - لا توجد كروت ناجحة (success_count={success_count})")
            if not condition3:
                print(f"      - متغير lightning_successful_cards غير موجود")
            if not condition4:
                print(f"      - قائمة lightning_successful_cards فارغة")
        
        return show_delete_successful_button

    def test_different_scenarios(self):
        """اختبار سيناريوهات مختلفة"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار سيناريوهات مختلفة")
        print("=" * 80)
        
        scenarios = [
            {
                'name': 'سيناريو 1: كروت ناجحة وفاشلة (يجب أن يظهر الزر)',
                'success': 8,
                'failed': 2,
                'has_list': True,
                'list_count': 8
            },
            {
                'name': 'سيناريو 2: كروت ناجحة فقط (لا يجب أن يظهر الزر)',
                'success': 10,
                'failed': 0,
                'has_list': True,
                'list_count': 10
            },
            {
                'name': 'سيناريو 3: كروت فاشلة فقط (لا يجب أن يظهر الزر)',
                'success': 0,
                'failed': 10,
                'has_list': False,
                'list_count': 0
            },
            {
                'name': 'سيناريو 4: كروت ناجحة وفاشلة لكن القائمة فارغة (لا يجب أن يظهر الزر)',
                'success': 8,
                'failed': 2,
                'has_list': True,
                'list_count': 0
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 {scenario['name']}")
            
            # تطبيق السيناريو
            success_count = scenario['success']
            failed_count = scenario['failed']
            
            if scenario['has_list']:
                self.lightning_successful_cards = ['user_' + str(j) for j in range(1, scenario['list_count'] + 1)]
            else:
                if hasattr(self, 'lightning_successful_cards'):
                    delattr(self, 'lightning_successful_cards')
            
            # اختبار الشروط
            condition1 = failed_count > 0
            condition2 = success_count > 0
            condition3 = hasattr(self, 'lightning_successful_cards')
            condition4 = bool(self.lightning_successful_cards) if condition3 else False
            
            show_button = condition1 and condition2 and condition3 and condition4
            
            print(f"   📊 success={success_count}, failed={failed_count}, list_count={scenario['list_count']}")
            print(f"   🔍 الشروط: {condition1} & {condition2} & {condition3} & {condition4} = {show_button}")
            print(f"   {'✅ يظهر الزر' if show_button else '❌ لا يظهر الزر'}")

    def test_system_type_condition(self):
        """اختبار شرط نوع النظام"""
        
        print("\n" + "=" * 80)
        print("🧪 اختبار شرط نوع النظام")
        print("=" * 80)
        
        system_types = ['hotspot', 'user_manager', 'unknown', None]
        
        for system_type in system_types:
            print(f"\n📋 اختبار system_type = '{system_type}'")
            
            self.system_type = system_type
            system_condition = getattr(self, 'system_type', '') == 'hotspot'
            
            print(f"   🔍 system_type == 'hotspot': {system_condition}")
            print(f"   {'✅ مناسب للهوت سبوت' if system_condition else '❌ غير مناسب للهوت سبوت'}")

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🚀 بدء اختبار تشخيص مشكلة زر حذف الكروت المرسلة بنجاح")
    
    # إنشاء محاكاة التطبيق
    mock_app = MockApp()
    
    # اختبار الشروط الأساسية
    result = mock_app.test_button_conditions()
    
    # اختبار سيناريوهات مختلفة
    mock_app.test_different_scenarios()
    
    # اختبار شرط نوع النظام
    mock_app.test_system_type_condition()
    
    print("\n" + "=" * 80)
    print("🎯 ملخص النتائج")
    print("=" * 80)
    
    if result:
        print("✅ في الظروف الطبيعية، يجب أن يظهر زر حذف الكروت المرسلة بنجاح")
        print("💡 إذا لم يظهر الزر في التطبيق الفعلي، تحقق من:")
        print("   1. أن دالة send_to_mikrotik_silent() تحفظ successful_usernames بشكل صحيح")
        print("   2. أن دالة send_lightning_hotspot_completion_notification() تستدعى بعد الإرسال")
        print("   3. أن last_send_stats تحتوي على الإحصائيات الصحيحة")
        print("   4. أن lightning_successful_cards تحتوي على قائمة الكروت الناجحة")
    else:
        print("❌ هناك مشكلة في الشروط - الزر لن يظهر")
    
    print("\n🔍 للتشخيص الإضافي، تحقق من رسائل السجل في التطبيق الفعلي")
    print("📝 ابحث عن رسائل تبدأ بـ '🔍 تشخيص' في ملف السجل")

if __name__ == '__main__':
    main()
