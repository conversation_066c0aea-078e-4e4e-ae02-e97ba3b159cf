#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توفر failed_cards_info بعد الإصلاح
"""

import re
import os

def test_failed_cards_info_fallback():
    """اختبار آلية الاحتياط لحفظ failed_cards_info"""
    print("🔍 اختبار آلية الاحتياط لحفظ failed_cards_info...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود آلية الاحتياط
    fallback_patterns = [
        'إصلاح: إنشاء failed_cards_info حتى لو لم تكن single_card_failed_cards موجودة',
        'استخدام قائمة failed_cards المحلية كبديل',
        'if failed_cards:  # استخدام قائمة failed_cards المحلية',
        'converted_failed_cards = []',
        'for failed_card in failed_cards:',
        'for cred in self.generated_credentials:',
        'if cred.get(\'username\') == failed_card.get(\'name\'):',
        'self.failed_cards_info = {',
        '\'failed_cards\': converted_failed_cards'
    ]
    
    missing_patterns = []
    for pattern in fallback_patterns:
        if pattern not in func_code:
            missing_patterns.append(pattern)
        else:
            print(f"✅ نمط الاحتياط موجود: {pattern[:50]}...")
    
    if missing_patterns:
        print(f"❌ أنماط الاحتياط المفقودة ({len(missing_patterns)}):")
        for pattern in missing_patterns:
            print(f"   - {pattern}")
        return False
    
    print("✅ جميع أنماط آلية الاحتياط موجودة")
    return True

def test_failed_cards_info_creation_conditions():
    """اختبار شروط إنشاء failed_cards_info"""
    print("\n🔍 اختبار شروط إنشاء failed_cards_info...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الشرط الأساسي
    main_condition = 'if failed_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\':'
    if main_condition in func_code:
        print("✅ الشرط الأساسي موجود: failed_count > 0 and system_type == 'hotspot'")
    else:
        print("❌ الشرط الأساسي غير موجود")
        return False
    
    # التحقق من الشرط الفرعي الأول (single_card_failed_cards)
    sub_condition1 = 'if hasattr(self, \'single_card_failed_cards\') and self.single_card_failed_cards:'
    if sub_condition1 in func_code:
        print("✅ الشرط الفرعي الأول موجود: single_card_failed_cards")
    else:
        print("❌ الشرط الفرعي الأول غير موجود")
        return False
    
    # التحقق من الشرط الفرعي الثاني (failed_cards كبديل)
    sub_condition2 = 'if failed_cards:  # استخدام قائمة failed_cards المحلية'
    if sub_condition2 in func_code:
        print("✅ الشرط الفرعي الثاني موجود: failed_cards كبديل")
    else:
        print("❌ الشرط الفرعي الثاني غير موجود")
        return False
    
    return True

def test_data_conversion_logic():
    """اختبار منطق تحويل البيانات"""
    print("\n🔍 اختبار منطق تحويل البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من منطق تحويل البيانات
    conversion_patterns = [
        'البحث عن الكرت الأصلي في generated_credentials',
        'original_card = None',
        'if cred.get(\'username\') == failed_card.get(\'name\'):',
        'original_card = cred',
        'break',
        'if original_card:',
        'converted_card = {',
        '\'username\': original_card.get(\'username\', \'\'),',
        '\'password\': original_card.get(\'password\', \'\'),',
        '\'error\': failed_card.get(\'error\', \'خطأ غير محدد\')',
        'converted_failed_cards.append(converted_card)'
    ]
    
    for pattern in conversion_patterns:
        if pattern not in func_code:
            print(f"❌ نمط تحويل البيانات غير موجود: {pattern}")
            return False
        print(f"✅ نمط تحويل البيانات موجود: {pattern[:40]}...")
    
    return True

def test_error_logging():
    """اختبار تسجيل الأخطاء"""
    print("\n🔍 اختبار تسجيل الأخطاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من رسائل السجلات
    logging_patterns = [
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد:',
        'لا توجد كروت فاشلة محفوظة في single_card_failed_cards، سيتم استخدام failed_cards المحلية',
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد (من failed_cards):',
        'لا توجد كروت فاشلة في أي من القوائم رغم وجود failed_count='
    ]
    
    for pattern in logging_patterns:
        if pattern not in func_code:
            print(f"❌ نمط السجلات غير موجود: {pattern[:50]}...")
            return False
        print(f"✅ نمط السجلات موجود: {pattern[:50]}...")
    
    return True

def test_diagnostic_messages():
    """اختبار الرسائل التشخيصية"""
    print("\n🔍 اختبار الرسائل التشخيصية...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_retry_failed_cards
    func_match = re.search(r'def handle_retry_failed_cards.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_retry_failed_cards")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الرسائل التشخيصية
    diagnostic_patterns = [
        'تشخيص إعادة المحاولة',
        'has_failed_info = hasattr(self, \'failed_cards_info\')',
        'failed_info_exists = has_failed_info and bool(self.failed_cards_info)',
        'تشخيص failed_cards_info: hasattr=',
        'محتوى failed_cards_info:',
        'عدد failed_cards:'
    ]
    
    for pattern in diagnostic_patterns:
        if pattern not in func_code:
            print(f"❌ نمط التشخيص غير موجود: {pattern}")
            return False
        print(f"✅ نمط التشخيص موجود: {pattern}")
    
    return True

def run_failed_cards_info_test():
    """تشغيل اختبار توفر failed_cards_info"""
    print("🚀 بدء اختبار توفر failed_cards_info بعد الإصلاح\n")
    
    tests = [
        test_failed_cards_info_fallback,
        test_failed_cards_info_creation_conditions,
        test_data_conversion_logic,
        test_error_logging,
        test_diagnostic_messages
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات توفر failed_cards_info نجحت!")
        print("✅ الإصلاح يضمن توفر failed_cards_info في جميع الحالات")
        print("\n🎯 ملخص الحل:")
        print("   • آلية احتياط مزدوجة لحفظ failed_cards_info")
        print("   • تحويل ذكي للبيانات من failed_cards إلى تنسيق مناسب")
        print("   • رسائل تشخيصية مفصلة لاستكشاف الأخطاء")
        print("   • تسجيل شامل لجميع مراحل العملية")
        print("\n💡 النتيجة المتوقعة:")
        print("   • لن تظهر رسالة 'لا يوجد معلومات محفوظة للكروت الفاشلة' بعد الآن")
        print("   • زر إعادة المحاولة سيظهر عند وجود كروت فاشلة")
        print("   • وظيفة إعادة المحاولة ستعمل بشكل صحيح")
        return True
    else:
        print("⚠️ بعض اختبارات توفر failed_cards_info فشلت")
        return False

if __name__ == "__main__":
    run_failed_cards_info_test()
