# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 13:12:38
# القالب: 10
# النظام: user_manager
# عدد الكروت: 400
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 400";

:local success 0;
:local errors 0;
:local total 400;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 400 مستخدم User Manager...";

# المستخدم 1: 0147408410
:do {
    /tool user-manager user add customer="adm8n" username="0147408410" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147408410";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147408410";
};

# المستخدم 2: 0129794418
:do {
    /tool user-manager user add customer="adm8n" username="0129794418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129794418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129794418";
};

# المستخدم 3: 0163019347
:do {
    /tool user-manager user add customer="adm8n" username="0163019347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163019347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163019347";
};

# المستخدم 4: 0150225699
:do {
    /tool user-manager user add customer="adm8n" username="0150225699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150225699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150225699";
};

# المستخدم 5: 0126183661
:do {
    /tool user-manager user add customer="adm8n" username="0126183661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126183661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126183661";
};

# المستخدم 6: 0114169110
:do {
    /tool user-manager user add customer="adm8n" username="0114169110" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114169110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114169110";
};

# المستخدم 7: 0101703756
:do {
    /tool user-manager user add customer="adm8n" username="0101703756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101703756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101703756";
};

# المستخدم 8: 0192535677
:do {
    /tool user-manager user add customer="adm8n" username="0192535677" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192535677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192535677";
};

# المستخدم 9: 0142848122
:do {
    /tool user-manager user add customer="adm8n" username="0142848122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142848122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142848122";
};

# المستخدم 10: 0161979016
:do {
    /tool user-manager user add customer="adm8n" username="0161979016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161979016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161979016";
};

# المستخدم 11: 0106081230
:do {
    /tool user-manager user add customer="adm8n" username="0106081230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106081230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106081230";
};

# المستخدم 12: 0100170774
:do {
    /tool user-manager user add customer="adm8n" username="0100170774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100170774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100170774";
};

# المستخدم 13: 0182862644
:do {
    /tool user-manager user add customer="adm8n" username="0182862644" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182862644";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182862644";
};

# المستخدم 14: 0188573567
:do {
    /tool user-manager user add customer="adm8n" username="0188573567" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188573567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188573567";
};

# المستخدم 15: 0116549765
:do {
    /tool user-manager user add customer="adm8n" username="0116549765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116549765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116549765";
};

# المستخدم 16: 0193322606
:do {
    /tool user-manager user add customer="adm8n" username="0193322606" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193322606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193322606";
};

# المستخدم 17: 0175460741
:do {
    /tool user-manager user add customer="adm8n" username="0175460741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175460741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175460741";
};

# المستخدم 18: 0150851935
:do {
    /tool user-manager user add customer="adm8n" username="0150851935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150851935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150851935";
};

# المستخدم 19: 0158654207
:do {
    /tool user-manager user add customer="adm8n" username="0158654207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158654207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158654207";
};

# المستخدم 20: 0132860766
:do {
    /tool user-manager user add customer="adm8n" username="0132860766" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132860766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132860766";
};

# المستخدم 21: 0173152187
:do {
    /tool user-manager user add customer="adm8n" username="0173152187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173152187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173152187";
};

# المستخدم 22: 0108499632
:do {
    /tool user-manager user add customer="adm8n" username="0108499632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108499632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108499632";
};

# المستخدم 23: 0175172459
:do {
    /tool user-manager user add customer="adm8n" username="0175172459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175172459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175172459";
};

# المستخدم 24: 0142438785
:do {
    /tool user-manager user add customer="adm8n" username="0142438785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142438785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142438785";
};

# المستخدم 25: 0123904507
:do {
    /tool user-manager user add customer="adm8n" username="0123904507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123904507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123904507";
};

# المستخدم 26: 0169507426
:do {
    /tool user-manager user add customer="adm8n" username="0169507426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169507426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169507426";
};

# المستخدم 27: 0155443637
:do {
    /tool user-manager user add customer="adm8n" username="0155443637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155443637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155443637";
};

# المستخدم 28: 0123154751
:do {
    /tool user-manager user add customer="adm8n" username="0123154751" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123154751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123154751";
};

# المستخدم 29: 0183188617
:do {
    /tool user-manager user add customer="adm8n" username="0183188617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183188617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183188617";
};

# المستخدم 30: 0140579601
:do {
    /tool user-manager user add customer="adm8n" username="0140579601" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140579601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140579601";
};

# المستخدم 31: 0198318557
:do {
    /tool user-manager user add customer="adm8n" username="0198318557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198318557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198318557";
};

# المستخدم 32: 0129680463
:do {
    /tool user-manager user add customer="adm8n" username="0129680463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129680463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129680463";
};

# المستخدم 33: 0119328525
:do {
    /tool user-manager user add customer="adm8n" username="0119328525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119328525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119328525";
};

# المستخدم 34: 0193066070
:do {
    /tool user-manager user add customer="adm8n" username="0193066070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193066070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193066070";
};

# المستخدم 35: 0148076146
:do {
    /tool user-manager user add customer="adm8n" username="0148076146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148076146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148076146";
};

# المستخدم 36: 0191546749
:do {
    /tool user-manager user add customer="adm8n" username="0191546749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191546749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191546749";
};

# المستخدم 37: 0196559810
:do {
    /tool user-manager user add customer="adm8n" username="0196559810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196559810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196559810";
};

# المستخدم 38: 0192908565
:do {
    /tool user-manager user add customer="adm8n" username="0192908565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192908565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192908565";
};

# المستخدم 39: 0144786752
:do {
    /tool user-manager user add customer="adm8n" username="0144786752" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144786752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144786752";
};

# المستخدم 40: 0120725505
:do {
    /tool user-manager user add customer="adm8n" username="0120725505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120725505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120725505";
};

# المستخدم 41: 0163157980
:do {
    /tool user-manager user add customer="adm8n" username="0163157980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163157980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163157980";
};

# المستخدم 42: 0111526678
:do {
    /tool user-manager user add customer="adm8n" username="0111526678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111526678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111526678";
};

# المستخدم 43: 0137010897
:do {
    /tool user-manager user add customer="adm8n" username="0137010897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137010897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137010897";
};

# المستخدم 44: 0149534079
:do {
    /tool user-manager user add customer="adm8n" username="0149534079" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149534079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149534079";
};

# المستخدم 45: 0151885699
:do {
    /tool user-manager user add customer="adm8n" username="0151885699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151885699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151885699";
};

# المستخدم 46: 0198845300
:do {
    /tool user-manager user add customer="adm8n" username="0198845300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198845300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198845300";
};

# المستخدم 47: 0170937663
:do {
    /tool user-manager user add customer="adm8n" username="0170937663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170937663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170937663";
};

# المستخدم 48: 0174535308
:do {
    /tool user-manager user add customer="adm8n" username="0174535308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174535308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174535308";
};

# المستخدم 49: 0131248565
:do {
    /tool user-manager user add customer="adm8n" username="0131248565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131248565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131248565";
};

# المستخدم 50: 0141491995
:do {
    /tool user-manager user add customer="adm8n" username="0141491995" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141491995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141491995";
};

# المستخدم 51: 0101251928
:do {
    /tool user-manager user add customer="adm8n" username="0101251928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101251928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101251928";
};

# المستخدم 52: 0133916674
:do {
    /tool user-manager user add customer="adm8n" username="0133916674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133916674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133916674";
};

# المستخدم 53: 0150667921
:do {
    /tool user-manager user add customer="adm8n" username="0150667921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150667921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150667921";
};

# المستخدم 54: 0143204828
:do {
    /tool user-manager user add customer="adm8n" username="0143204828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143204828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143204828";
};

# المستخدم 55: 0191670054
:do {
    /tool user-manager user add customer="adm8n" username="0191670054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191670054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191670054";
};

# المستخدم 56: 0192223961
:do {
    /tool user-manager user add customer="adm8n" username="0192223961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192223961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192223961";
};

# المستخدم 57: 0165937768
:do {
    /tool user-manager user add customer="adm8n" username="0165937768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165937768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165937768";
};

# المستخدم 58: 0113657950
:do {
    /tool user-manager user add customer="adm8n" username="0113657950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113657950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113657950";
};

# المستخدم 59: 0139663377
:do {
    /tool user-manager user add customer="adm8n" username="0139663377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139663377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139663377";
};

# المستخدم 60: 0116844392
:do {
    /tool user-manager user add customer="adm8n" username="0116844392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116844392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116844392";
};

# المستخدم 61: 0176511853
:do {
    /tool user-manager user add customer="adm8n" username="0176511853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176511853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176511853";
};

# المستخدم 62: 0130156937
:do {
    /tool user-manager user add customer="adm8n" username="0130156937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130156937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130156937";
};

# المستخدم 63: 0167958895
:do {
    /tool user-manager user add customer="adm8n" username="0167958895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167958895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167958895";
};

# المستخدم 64: 0173271410
:do {
    /tool user-manager user add customer="adm8n" username="0173271410" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173271410";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173271410";
};

# المستخدم 65: 0112270286
:do {
    /tool user-manager user add customer="adm8n" username="0112270286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112270286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112270286";
};

# المستخدم 66: 0114420153
:do {
    /tool user-manager user add customer="adm8n" username="0114420153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114420153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114420153";
};

# المستخدم 67: 0189898363
:do {
    /tool user-manager user add customer="adm8n" username="0189898363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189898363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189898363";
};

# المستخدم 68: 0124024693
:do {
    /tool user-manager user add customer="adm8n" username="0124024693" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124024693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124024693";
};

# المستخدم 69: 0102086731
:do {
    /tool user-manager user add customer="adm8n" username="0102086731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102086731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102086731";
};

# المستخدم 70: 0154523066
:do {
    /tool user-manager user add customer="adm8n" username="0154523066" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154523066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154523066";
};

# المستخدم 71: 0181437896
:do {
    /tool user-manager user add customer="adm8n" username="0181437896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181437896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181437896";
};

# المستخدم 72: 0106606360
:do {
    /tool user-manager user add customer="adm8n" username="0106606360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106606360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106606360";
};

# المستخدم 73: 0179819752
:do {
    /tool user-manager user add customer="adm8n" username="0179819752" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179819752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179819752";
};

# المستخدم 74: 0158308762
:do {
    /tool user-manager user add customer="adm8n" username="0158308762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158308762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158308762";
};

# المستخدم 75: 0169527226
:do {
    /tool user-manager user add customer="adm8n" username="0169527226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169527226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169527226";
};

# المستخدم 76: 0109847014
:do {
    /tool user-manager user add customer="adm8n" username="0109847014" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109847014";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109847014";
};

# المستخدم 77: 0176342009
:do {
    /tool user-manager user add customer="adm8n" username="0176342009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176342009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176342009";
};

# المستخدم 78: 0161555703
:do {
    /tool user-manager user add customer="adm8n" username="0161555703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161555703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161555703";
};

# المستخدم 79: 0190496075
:do {
    /tool user-manager user add customer="adm8n" username="0190496075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190496075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190496075";
};

# المستخدم 80: 0112036452
:do {
    /tool user-manager user add customer="adm8n" username="0112036452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112036452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112036452";
};

# المستخدم 81: 0147247013
:do {
    /tool user-manager user add customer="adm8n" username="0147247013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147247013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147247013";
};

# المستخدم 82: 0100544037
:do {
    /tool user-manager user add customer="adm8n" username="0100544037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100544037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100544037";
};

# المستخدم 83: 0132207569
:do {
    /tool user-manager user add customer="adm8n" username="0132207569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132207569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132207569";
};

# المستخدم 84: 0147967182
:do {
    /tool user-manager user add customer="adm8n" username="0147967182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147967182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147967182";
};

# المستخدم 85: 0115906804
:do {
    /tool user-manager user add customer="adm8n" username="0115906804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115906804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115906804";
};

# المستخدم 86: 0168481321
:do {
    /tool user-manager user add customer="adm8n" username="0168481321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168481321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168481321";
};

# المستخدم 87: 0104761874
:do {
    /tool user-manager user add customer="adm8n" username="0104761874" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104761874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104761874";
};

# المستخدم 88: 0191448898
:do {
    /tool user-manager user add customer="adm8n" username="0191448898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191448898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191448898";
};

# المستخدم 89: 0141813003
:do {
    /tool user-manager user add customer="adm8n" username="0141813003" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141813003";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141813003";
};

# المستخدم 90: 0172592864
:do {
    /tool user-manager user add customer="adm8n" username="0172592864" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172592864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172592864";
};

# المستخدم 91: 0173683793
:do {
    /tool user-manager user add customer="adm8n" username="0173683793" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173683793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173683793";
};

# المستخدم 92: 0124291568
:do {
    /tool user-manager user add customer="adm8n" username="0124291568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124291568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124291568";
};

# المستخدم 93: 0156896714
:do {
    /tool user-manager user add customer="adm8n" username="0156896714" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156896714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156896714";
};

# المستخدم 94: 0158070416
:do {
    /tool user-manager user add customer="adm8n" username="0158070416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158070416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158070416";
};

# المستخدم 95: 0101974766
:do {
    /tool user-manager user add customer="adm8n" username="0101974766" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101974766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101974766";
};

# المستخدم 96: 0197793422
:do {
    /tool user-manager user add customer="adm8n" username="0197793422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197793422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197793422";
};

# المستخدم 97: 0196527679
:do {
    /tool user-manager user add customer="adm8n" username="0196527679" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196527679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196527679";
};

# المستخدم 98: 0108495155
:do {
    /tool user-manager user add customer="adm8n" username="0108495155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108495155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108495155";
};

# المستخدم 99: 0166864573
:do {
    /tool user-manager user add customer="adm8n" username="0166864573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166864573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166864573";
};

# المستخدم 100: 0169602710
:do {
    /tool user-manager user add customer="adm8n" username="0169602710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169602710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169602710";
};

# المستخدم 101: 0136524541
:do {
    /tool user-manager user add customer="adm8n" username="0136524541" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136524541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136524541";
};

# المستخدم 102: 0178775339
:do {
    /tool user-manager user add customer="adm8n" username="0178775339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178775339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178775339";
};

# المستخدم 103: 0140348565
:do {
    /tool user-manager user add customer="adm8n" username="0140348565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140348565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140348565";
};

# المستخدم 104: 0197979746
:do {
    /tool user-manager user add customer="adm8n" username="0197979746" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197979746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197979746";
};

# المستخدم 105: 0145074495
:do {
    /tool user-manager user add customer="adm8n" username="0145074495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145074495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145074495";
};

# المستخدم 106: 0186457189
:do {
    /tool user-manager user add customer="adm8n" username="0186457189" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186457189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186457189";
};

# المستخدم 107: 0169589783
:do {
    /tool user-manager user add customer="adm8n" username="0169589783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169589783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169589783";
};

# المستخدم 108: 0102078822
:do {
    /tool user-manager user add customer="adm8n" username="0102078822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102078822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102078822";
};

# المستخدم 109: 0128471990
:do {
    /tool user-manager user add customer="adm8n" username="0128471990" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128471990";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128471990";
};

# المستخدم 110: 0159506464
:do {
    /tool user-manager user add customer="adm8n" username="0159506464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159506464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159506464";
};

# المستخدم 111: 0185080479
:do {
    /tool user-manager user add customer="adm8n" username="0185080479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185080479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185080479";
};

# المستخدم 112: 0157681351
:do {
    /tool user-manager user add customer="adm8n" username="0157681351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157681351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157681351";
};

# المستخدم 113: 0194511607
:do {
    /tool user-manager user add customer="adm8n" username="0194511607" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194511607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194511607";
};

# المستخدم 114: 0144553859
:do {
    /tool user-manager user add customer="adm8n" username="0144553859" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144553859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144553859";
};

# المستخدم 115: 0136287656
:do {
    /tool user-manager user add customer="adm8n" username="0136287656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136287656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136287656";
};

# المستخدم 116: 0148953829
:do {
    /tool user-manager user add customer="adm8n" username="0148953829" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148953829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148953829";
};

# المستخدم 117: 0151132843
:do {
    /tool user-manager user add customer="adm8n" username="0151132843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151132843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151132843";
};

# المستخدم 118: 0198754024
:do {
    /tool user-manager user add customer="adm8n" username="0198754024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198754024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198754024";
};

# المستخدم 119: 0129381392
:do {
    /tool user-manager user add customer="adm8n" username="0129381392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129381392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129381392";
};

# المستخدم 120: 0193667221
:do {
    /tool user-manager user add customer="adm8n" username="0193667221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193667221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193667221";
};

# المستخدم 121: 0123027667
:do {
    /tool user-manager user add customer="adm8n" username="0123027667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123027667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123027667";
};

# المستخدم 122: 0118486128
:do {
    /tool user-manager user add customer="adm8n" username="0118486128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118486128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118486128";
};

# المستخدم 123: 0109535626
:do {
    /tool user-manager user add customer="adm8n" username="0109535626" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109535626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109535626";
};

# المستخدم 124: 0125320711
:do {
    /tool user-manager user add customer="adm8n" username="0125320711" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125320711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125320711";
};

# المستخدم 125: 0136808935
:do {
    /tool user-manager user add customer="adm8n" username="0136808935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136808935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136808935";
};

# المستخدم 126: 0117547478
:do {
    /tool user-manager user add customer="adm8n" username="0117547478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117547478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117547478";
};

# المستخدم 127: 0100749609
:do {
    /tool user-manager user add customer="adm8n" username="0100749609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100749609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100749609";
};

# المستخدم 128: 0175778677
:do {
    /tool user-manager user add customer="adm8n" username="0175778677" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175778677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175778677";
};

# المستخدم 129: 0164324758
:do {
    /tool user-manager user add customer="adm8n" username="0164324758" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164324758";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164324758";
};

# المستخدم 130: 0172454992
:do {
    /tool user-manager user add customer="adm8n" username="0172454992" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172454992";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172454992";
};

# المستخدم 131: 0155016196
:do {
    /tool user-manager user add customer="adm8n" username="0155016196" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155016196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155016196";
};

# المستخدم 132: 0165076824
:do {
    /tool user-manager user add customer="adm8n" username="0165076824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165076824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165076824";
};

# المستخدم 133: 0199802757
:do {
    /tool user-manager user add customer="adm8n" username="0199802757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199802757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199802757";
};

# المستخدم 134: 0194425396
:do {
    /tool user-manager user add customer="adm8n" username="0194425396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194425396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194425396";
};

# المستخدم 135: 0182230738
:do {
    /tool user-manager user add customer="adm8n" username="0182230738" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182230738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182230738";
};

# المستخدم 136: 0102578954
:do {
    /tool user-manager user add customer="adm8n" username="0102578954" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102578954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102578954";
};

# المستخدم 137: 0160534401
:do {
    /tool user-manager user add customer="adm8n" username="0160534401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160534401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160534401";
};

# المستخدم 138: 0184971422
:do {
    /tool user-manager user add customer="adm8n" username="0184971422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184971422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184971422";
};

# المستخدم 139: 0122156970
:do {
    /tool user-manager user add customer="adm8n" username="0122156970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122156970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122156970";
};

# المستخدم 140: 0185762620
:do {
    /tool user-manager user add customer="adm8n" username="0185762620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185762620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185762620";
};

# المستخدم 141: 0140446285
:do {
    /tool user-manager user add customer="adm8n" username="0140446285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140446285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140446285";
};

# المستخدم 142: 0161649001
:do {
    /tool user-manager user add customer="adm8n" username="0161649001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161649001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161649001";
};

# المستخدم 143: 0138299068
:do {
    /tool user-manager user add customer="adm8n" username="0138299068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138299068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138299068";
};

# المستخدم 144: 0166713951
:do {
    /tool user-manager user add customer="adm8n" username="0166713951" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166713951";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166713951";
};

# المستخدم 145: 0103605401
:do {
    /tool user-manager user add customer="adm8n" username="0103605401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103605401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103605401";
};

# المستخدم 146: 0140331122
:do {
    /tool user-manager user add customer="adm8n" username="0140331122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140331122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140331122";
};

# المستخدم 147: 0130150181
:do {
    /tool user-manager user add customer="adm8n" username="0130150181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130150181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130150181";
};

# المستخدم 148: 0166904047
:do {
    /tool user-manager user add customer="adm8n" username="0166904047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166904047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166904047";
};

# المستخدم 149: 0145130269
:do {
    /tool user-manager user add customer="adm8n" username="0145130269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145130269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145130269";
};

# المستخدم 150: 0107815219
:do {
    /tool user-manager user add customer="adm8n" username="0107815219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107815219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107815219";
};

# المستخدم 151: 0196122788
:do {
    /tool user-manager user add customer="adm8n" username="0196122788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196122788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196122788";
};

# المستخدم 152: 0165589590
:do {
    /tool user-manager user add customer="adm8n" username="0165589590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165589590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165589590";
};

# المستخدم 153: 0153711715
:do {
    /tool user-manager user add customer="adm8n" username="0153711715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153711715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153711715";
};

# المستخدم 154: 0190133631
:do {
    /tool user-manager user add customer="adm8n" username="0190133631" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190133631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190133631";
};

# المستخدم 155: 0154894524
:do {
    /tool user-manager user add customer="adm8n" username="0154894524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154894524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154894524";
};

# المستخدم 156: 0184543821
:do {
    /tool user-manager user add customer="adm8n" username="0184543821" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184543821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184543821";
};

# المستخدم 157: 0184611767
:do {
    /tool user-manager user add customer="adm8n" username="0184611767" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184611767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184611767";
};

# المستخدم 158: 0171807134
:do {
    /tool user-manager user add customer="adm8n" username="0171807134" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171807134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171807134";
};

# المستخدم 159: 0121350580
:do {
    /tool user-manager user add customer="adm8n" username="0121350580" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121350580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121350580";
};

# المستخدم 160: 0128936845
:do {
    /tool user-manager user add customer="adm8n" username="0128936845" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128936845";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128936845";
};

# المستخدم 161: 0128881949
:do {
    /tool user-manager user add customer="adm8n" username="0128881949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128881949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128881949";
};

# المستخدم 162: 0144287391
:do {
    /tool user-manager user add customer="adm8n" username="0144287391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144287391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144287391";
};

# المستخدم 163: 0126566108
:do {
    /tool user-manager user add customer="adm8n" username="0126566108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126566108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126566108";
};

# المستخدم 164: 0169391091
:do {
    /tool user-manager user add customer="adm8n" username="0169391091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169391091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169391091";
};

# المستخدم 165: 0156773698
:do {
    /tool user-manager user add customer="adm8n" username="0156773698" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156773698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156773698";
};

# المستخدم 166: 0127161280
:do {
    /tool user-manager user add customer="adm8n" username="0127161280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127161280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127161280";
};

# المستخدم 167: 0156553431
:do {
    /tool user-manager user add customer="adm8n" username="0156553431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156553431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156553431";
};

# المستخدم 168: 0185177523
:do {
    /tool user-manager user add customer="adm8n" username="0185177523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185177523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185177523";
};

# المستخدم 169: 0149765426
:do {
    /tool user-manager user add customer="adm8n" username="0149765426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149765426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149765426";
};

# المستخدم 170: 0186875211
:do {
    /tool user-manager user add customer="adm8n" username="0186875211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186875211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186875211";
};

# المستخدم 171: 0188940353
:do {
    /tool user-manager user add customer="adm8n" username="0188940353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188940353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188940353";
};

# المستخدم 172: 0119521443
:do {
    /tool user-manager user add customer="adm8n" username="0119521443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119521443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119521443";
};

# المستخدم 173: 0162993994
:do {
    /tool user-manager user add customer="adm8n" username="0162993994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162993994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162993994";
};

# المستخدم 174: 0112629260
:do {
    /tool user-manager user add customer="adm8n" username="0112629260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112629260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112629260";
};

# المستخدم 175: 0147280195
:do {
    /tool user-manager user add customer="adm8n" username="0147280195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147280195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147280195";
};

# المستخدم 176: 0100094193
:do {
    /tool user-manager user add customer="adm8n" username="0100094193" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100094193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100094193";
};

# المستخدم 177: 0174162351
:do {
    /tool user-manager user add customer="adm8n" username="0174162351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174162351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174162351";
};

# المستخدم 178: 0188306400
:do {
    /tool user-manager user add customer="adm8n" username="0188306400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188306400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188306400";
};

# المستخدم 179: 0138371244
:do {
    /tool user-manager user add customer="adm8n" username="0138371244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138371244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138371244";
};

# المستخدم 180: 0141099882
:do {
    /tool user-manager user add customer="adm8n" username="0141099882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141099882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141099882";
};

# المستخدم 181: 0113856876
:do {
    /tool user-manager user add customer="adm8n" username="0113856876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113856876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113856876";
};

# المستخدم 182: 0116974063
:do {
    /tool user-manager user add customer="adm8n" username="0116974063" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116974063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116974063";
};

# المستخدم 183: 0128016064
:do {
    /tool user-manager user add customer="adm8n" username="0128016064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128016064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128016064";
};

# المستخدم 184: 0116619966
:do {
    /tool user-manager user add customer="adm8n" username="0116619966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116619966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116619966";
};

# المستخدم 185: 0143109696
:do {
    /tool user-manager user add customer="adm8n" username="0143109696" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143109696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143109696";
};

# المستخدم 186: 0127706741
:do {
    /tool user-manager user add customer="adm8n" username="0127706741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127706741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127706741";
};

# المستخدم 187: 0107385064
:do {
    /tool user-manager user add customer="adm8n" username="0107385064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107385064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107385064";
};

# المستخدم 188: 0161067486
:do {
    /tool user-manager user add customer="adm8n" username="0161067486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161067486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161067486";
};

# المستخدم 189: 0187713144
:do {
    /tool user-manager user add customer="adm8n" username="0187713144" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187713144";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187713144";
};

# المستخدم 190: 0142705007
:do {
    /tool user-manager user add customer="adm8n" username="0142705007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142705007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142705007";
};

# المستخدم 191: 0162242813
:do {
    /tool user-manager user add customer="adm8n" username="0162242813" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162242813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162242813";
};

# المستخدم 192: 0106681616
:do {
    /tool user-manager user add customer="adm8n" username="0106681616" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106681616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106681616";
};

# المستخدم 193: 0141672599
:do {
    /tool user-manager user add customer="adm8n" username="0141672599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141672599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141672599";
};

# المستخدم 194: 0111669884
:do {
    /tool user-manager user add customer="adm8n" username="0111669884" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111669884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111669884";
};

# المستخدم 195: 0159183441
:do {
    /tool user-manager user add customer="adm8n" username="0159183441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159183441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159183441";
};

# المستخدم 196: 0175178338
:do {
    /tool user-manager user add customer="adm8n" username="0175178338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175178338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175178338";
};

# المستخدم 197: 0190687745
:do {
    /tool user-manager user add customer="adm8n" username="0190687745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190687745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190687745";
};

# المستخدم 198: 0127397910
:do {
    /tool user-manager user add customer="adm8n" username="0127397910" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127397910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127397910";
};

# المستخدم 199: 0155419236
:do {
    /tool user-manager user add customer="adm8n" username="0155419236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155419236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155419236";
};

# المستخدم 200: 0119216996
:do {
    /tool user-manager user add customer="adm8n" username="0119216996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119216996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119216996";
};

# المستخدم 201: 0127869194
:do {
    /tool user-manager user add customer="adm8n" username="0127869194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127869194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127869194";
};

# المستخدم 202: 0183577392
:do {
    /tool user-manager user add customer="adm8n" username="0183577392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183577392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183577392";
};

# المستخدم 203: 0176584450
:do {
    /tool user-manager user add customer="adm8n" username="0176584450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176584450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176584450";
};

# المستخدم 204: 0124745763
:do {
    /tool user-manager user add customer="adm8n" username="0124745763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124745763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124745763";
};

# المستخدم 205: 0140533852
:do {
    /tool user-manager user add customer="adm8n" username="0140533852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140533852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140533852";
};

# المستخدم 206: 0118918733
:do {
    /tool user-manager user add customer="adm8n" username="0118918733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118918733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118918733";
};

# المستخدم 207: 0107212933
:do {
    /tool user-manager user add customer="adm8n" username="0107212933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107212933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107212933";
};

# المستخدم 208: 0126763716
:do {
    /tool user-manager user add customer="adm8n" username="0126763716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126763716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126763716";
};

# المستخدم 209: 0181759809
:do {
    /tool user-manager user add customer="adm8n" username="0181759809" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181759809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181759809";
};

# المستخدم 210: 0142859213
:do {
    /tool user-manager user add customer="adm8n" username="0142859213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142859213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142859213";
};

# المستخدم 211: 0187603810
:do {
    /tool user-manager user add customer="adm8n" username="0187603810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187603810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187603810";
};

# المستخدم 212: 0194147015
:do {
    /tool user-manager user add customer="adm8n" username="0194147015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194147015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194147015";
};

# المستخدم 213: 0145677568
:do {
    /tool user-manager user add customer="adm8n" username="0145677568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145677568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145677568";
};

# المستخدم 214: 0155421868
:do {
    /tool user-manager user add customer="adm8n" username="0155421868" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155421868";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155421868";
};

# المستخدم 215: 0119675205
:do {
    /tool user-manager user add customer="adm8n" username="0119675205" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119675205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119675205";
};

# المستخدم 216: 0189949731
:do {
    /tool user-manager user add customer="adm8n" username="0189949731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189949731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189949731";
};

# المستخدم 217: 0194538202
:do {
    /tool user-manager user add customer="adm8n" username="0194538202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194538202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194538202";
};

# المستخدم 218: 0107207363
:do {
    /tool user-manager user add customer="adm8n" username="0107207363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107207363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107207363";
};

# المستخدم 219: 0182212900
:do {
    /tool user-manager user add customer="adm8n" username="0182212900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182212900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182212900";
};

# المستخدم 220: 0176988101
:do {
    /tool user-manager user add customer="adm8n" username="0176988101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176988101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176988101";
};

# المستخدم 221: 0172640337
:do {
    /tool user-manager user add customer="adm8n" username="0172640337" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172640337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172640337";
};

# المستخدم 222: 0142083775
:do {
    /tool user-manager user add customer="adm8n" username="0142083775" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142083775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142083775";
};

# المستخدم 223: 0135111957
:do {
    /tool user-manager user add customer="adm8n" username="0135111957" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135111957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135111957";
};

# المستخدم 224: 0188471198
:do {
    /tool user-manager user add customer="adm8n" username="0188471198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188471198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188471198";
};

# المستخدم 225: 0105531100
:do {
    /tool user-manager user add customer="adm8n" username="0105531100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105531100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105531100";
};

# المستخدم 226: 0179608405
:do {
    /tool user-manager user add customer="adm8n" username="0179608405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179608405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179608405";
};

# المستخدم 227: 0142261246
:do {
    /tool user-manager user add customer="adm8n" username="0142261246" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142261246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142261246";
};

# المستخدم 228: 0108065924
:do {
    /tool user-manager user add customer="adm8n" username="0108065924" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108065924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108065924";
};

# المستخدم 229: 0134004140
:do {
    /tool user-manager user add customer="adm8n" username="0134004140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134004140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134004140";
};

# المستخدم 230: 0121925318
:do {
    /tool user-manager user add customer="adm8n" username="0121925318" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121925318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121925318";
};

# المستخدم 231: 0198689551
:do {
    /tool user-manager user add customer="adm8n" username="0198689551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198689551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198689551";
};

# المستخدم 232: 0157767422
:do {
    /tool user-manager user add customer="adm8n" username="0157767422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157767422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157767422";
};

# المستخدم 233: 0171587339
:do {
    /tool user-manager user add customer="adm8n" username="0171587339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171587339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171587339";
};

# المستخدم 234: 0132443443
:do {
    /tool user-manager user add customer="adm8n" username="0132443443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132443443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132443443";
};

# المستخدم 235: 0195612913
:do {
    /tool user-manager user add customer="adm8n" username="0195612913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195612913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195612913";
};

# المستخدم 236: 0123504047
:do {
    /tool user-manager user add customer="adm8n" username="0123504047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123504047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123504047";
};

# المستخدم 237: 0112245314
:do {
    /tool user-manager user add customer="adm8n" username="0112245314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112245314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112245314";
};

# المستخدم 238: 0165897917
:do {
    /tool user-manager user add customer="adm8n" username="0165897917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165897917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165897917";
};

# المستخدم 239: 0140931549
:do {
    /tool user-manager user add customer="adm8n" username="0140931549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140931549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140931549";
};

# المستخدم 240: 0191014579
:do {
    /tool user-manager user add customer="adm8n" username="0191014579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191014579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191014579";
};

# المستخدم 241: 0115158322
:do {
    /tool user-manager user add customer="adm8n" username="0115158322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115158322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115158322";
};

# المستخدم 242: 0118436997
:do {
    /tool user-manager user add customer="adm8n" username="0118436997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118436997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118436997";
};

# المستخدم 243: 0194782900
:do {
    /tool user-manager user add customer="adm8n" username="0194782900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194782900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194782900";
};

# المستخدم 244: 0128293875
:do {
    /tool user-manager user add customer="adm8n" username="0128293875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128293875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128293875";
};

# المستخدم 245: 0164401707
:do {
    /tool user-manager user add customer="adm8n" username="0164401707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164401707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164401707";
};

# المستخدم 246: 0185311108
:do {
    /tool user-manager user add customer="adm8n" username="0185311108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185311108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185311108";
};

# المستخدم 247: 0164707257
:do {
    /tool user-manager user add customer="adm8n" username="0164707257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164707257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164707257";
};

# المستخدم 248: 0154135914
:do {
    /tool user-manager user add customer="adm8n" username="0154135914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154135914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154135914";
};

# المستخدم 249: 0118340935
:do {
    /tool user-manager user add customer="adm8n" username="0118340935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118340935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118340935";
};

# المستخدم 250: 0192964928
:do {
    /tool user-manager user add customer="adm8n" username="0192964928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192964928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192964928";
};

# المستخدم 251: 0152610199
:do {
    /tool user-manager user add customer="adm8n" username="0152610199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152610199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152610199";
};

# المستخدم 252: 0107847049
:do {
    /tool user-manager user add customer="adm8n" username="0107847049" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107847049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107847049";
};

# المستخدم 253: 0154065875
:do {
    /tool user-manager user add customer="adm8n" username="0154065875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154065875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154065875";
};

# المستخدم 254: 0146586446
:do {
    /tool user-manager user add customer="adm8n" username="0146586446" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146586446";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146586446";
};

# المستخدم 255: 0194045994
:do {
    /tool user-manager user add customer="adm8n" username="0194045994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194045994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194045994";
};

# المستخدم 256: 0197276809
:do {
    /tool user-manager user add customer="adm8n" username="0197276809" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197276809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197276809";
};

# المستخدم 257: 0100713085
:do {
    /tool user-manager user add customer="adm8n" username="0100713085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100713085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100713085";
};

# المستخدم 258: 0189290398
:do {
    /tool user-manager user add customer="adm8n" username="0189290398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189290398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189290398";
};

# المستخدم 259: 0122609971
:do {
    /tool user-manager user add customer="adm8n" username="0122609971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122609971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122609971";
};

# المستخدم 260: 0183446141
:do {
    /tool user-manager user add customer="adm8n" username="0183446141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183446141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183446141";
};

# المستخدم 261: 0107910022
:do {
    /tool user-manager user add customer="adm8n" username="0107910022" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107910022";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107910022";
};

# المستخدم 262: 0157387845
:do {
    /tool user-manager user add customer="adm8n" username="0157387845" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157387845";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157387845";
};

# المستخدم 263: 0128987297
:do {
    /tool user-manager user add customer="adm8n" username="0128987297" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128987297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128987297";
};

# المستخدم 264: 0138595090
:do {
    /tool user-manager user add customer="adm8n" username="0138595090" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138595090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138595090";
};

# المستخدم 265: 0183063840
:do {
    /tool user-manager user add customer="adm8n" username="0183063840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183063840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183063840";
};

# المستخدم 266: 0179193835
:do {
    /tool user-manager user add customer="adm8n" username="0179193835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179193835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179193835";
};

# المستخدم 267: 0159434220
:do {
    /tool user-manager user add customer="adm8n" username="0159434220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159434220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159434220";
};

# المستخدم 268: 0174586731
:do {
    /tool user-manager user add customer="adm8n" username="0174586731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174586731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174586731";
};

# المستخدم 269: 0194795456
:do {
    /tool user-manager user add customer="adm8n" username="0194795456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194795456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194795456";
};

# المستخدم 270: 0136155492
:do {
    /tool user-manager user add customer="adm8n" username="0136155492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136155492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136155492";
};

# المستخدم 271: 0181384846
:do {
    /tool user-manager user add customer="adm8n" username="0181384846" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181384846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181384846";
};

# المستخدم 272: 0117562460
:do {
    /tool user-manager user add customer="adm8n" username="0117562460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117562460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117562460";
};

# المستخدم 273: 0177219621
:do {
    /tool user-manager user add customer="adm8n" username="0177219621" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177219621";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177219621";
};

# المستخدم 274: 0180249532
:do {
    /tool user-manager user add customer="adm8n" username="0180249532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180249532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180249532";
};

# المستخدم 275: 0171518527
:do {
    /tool user-manager user add customer="adm8n" username="0171518527" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171518527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171518527";
};

# المستخدم 276: 0105431075
:do {
    /tool user-manager user add customer="adm8n" username="0105431075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105431075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105431075";
};

# المستخدم 277: 0135714583
:do {
    /tool user-manager user add customer="adm8n" username="0135714583" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135714583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135714583";
};

# المستخدم 278: 0151791583
:do {
    /tool user-manager user add customer="adm8n" username="0151791583" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151791583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151791583";
};

# المستخدم 279: 0172901560
:do {
    /tool user-manager user add customer="adm8n" username="0172901560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172901560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172901560";
};

# المستخدم 280: 0156883942
:do {
    /tool user-manager user add customer="adm8n" username="0156883942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156883942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156883942";
};

# المستخدم 281: 0197221784
:do {
    /tool user-manager user add customer="adm8n" username="0197221784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197221784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197221784";
};

# المستخدم 282: 0130842888
:do {
    /tool user-manager user add customer="adm8n" username="0130842888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130842888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130842888";
};

# المستخدم 283: 0194867538
:do {
    /tool user-manager user add customer="adm8n" username="0194867538" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194867538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194867538";
};

# المستخدم 284: 0159976970
:do {
    /tool user-manager user add customer="adm8n" username="0159976970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159976970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159976970";
};

# المستخدم 285: 0184212763
:do {
    /tool user-manager user add customer="adm8n" username="0184212763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184212763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184212763";
};

# المستخدم 286: 0198617466
:do {
    /tool user-manager user add customer="adm8n" username="0198617466" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198617466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198617466";
};

# المستخدم 287: 0131184915
:do {
    /tool user-manager user add customer="adm8n" username="0131184915" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131184915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131184915";
};

# المستخدم 288: 0171037565
:do {
    /tool user-manager user add customer="adm8n" username="0171037565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171037565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171037565";
};

# المستخدم 289: 0157402589
:do {
    /tool user-manager user add customer="adm8n" username="0157402589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157402589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157402589";
};

# المستخدم 290: 0106002390
:do {
    /tool user-manager user add customer="adm8n" username="0106002390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106002390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106002390";
};

# المستخدم 291: 0135943043
:do {
    /tool user-manager user add customer="adm8n" username="0135943043" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135943043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135943043";
};

# المستخدم 292: 0187334276
:do {
    /tool user-manager user add customer="adm8n" username="0187334276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187334276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187334276";
};

# المستخدم 293: 0131111818
:do {
    /tool user-manager user add customer="adm8n" username="0131111818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131111818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131111818";
};

# المستخدم 294: 0153332615
:do {
    /tool user-manager user add customer="adm8n" username="0153332615" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153332615";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153332615";
};

# المستخدم 295: 0199161111
:do {
    /tool user-manager user add customer="adm8n" username="0199161111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199161111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199161111";
};

# المستخدم 296: 0193906890
:do {
    /tool user-manager user add customer="adm8n" username="0193906890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193906890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193906890";
};

# المستخدم 297: 0143741118
:do {
    /tool user-manager user add customer="adm8n" username="0143741118" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143741118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143741118";
};

# المستخدم 298: 0104369699
:do {
    /tool user-manager user add customer="adm8n" username="0104369699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104369699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104369699";
};

# المستخدم 299: 0199502156
:do {
    /tool user-manager user add customer="adm8n" username="0199502156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199502156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199502156";
};

# المستخدم 300: 0130015177
:do {
    /tool user-manager user add customer="adm8n" username="0130015177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130015177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130015177";
};

# المستخدم 301: 0154697371
:do {
    /tool user-manager user add customer="adm8n" username="0154697371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154697371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154697371";
};

# المستخدم 302: 0113178399
:do {
    /tool user-manager user add customer="adm8n" username="0113178399" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113178399";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113178399";
};

# المستخدم 303: 0137075188
:do {
    /tool user-manager user add customer="adm8n" username="0137075188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137075188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137075188";
};

# المستخدم 304: 0152214301
:do {
    /tool user-manager user add customer="adm8n" username="0152214301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152214301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152214301";
};

# المستخدم 305: 0138235904
:do {
    /tool user-manager user add customer="adm8n" username="0138235904" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138235904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138235904";
};

# المستخدم 306: 0149688305
:do {
    /tool user-manager user add customer="adm8n" username="0149688305" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149688305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149688305";
};

# المستخدم 307: 0141272721
:do {
    /tool user-manager user add customer="adm8n" username="0141272721" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141272721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141272721";
};

# المستخدم 308: 0138894022
:do {
    /tool user-manager user add customer="adm8n" username="0138894022" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138894022";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138894022";
};

# المستخدم 309: 0136677162
:do {
    /tool user-manager user add customer="adm8n" username="0136677162" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136677162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136677162";
};

# المستخدم 310: 0160243316
:do {
    /tool user-manager user add customer="adm8n" username="0160243316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160243316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160243316";
};

# المستخدم 311: 0122144146
:do {
    /tool user-manager user add customer="adm8n" username="0122144146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122144146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122144146";
};

# المستخدم 312: 0199924352
:do {
    /tool user-manager user add customer="adm8n" username="0199924352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199924352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199924352";
};

# المستخدم 313: 0144838043
:do {
    /tool user-manager user add customer="adm8n" username="0144838043" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144838043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144838043";
};

# المستخدم 314: 0131236547
:do {
    /tool user-manager user add customer="adm8n" username="0131236547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131236547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131236547";
};

# المستخدم 315: 0130214502
:do {
    /tool user-manager user add customer="adm8n" username="0130214502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130214502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130214502";
};

# المستخدم 316: 0106266504
:do {
    /tool user-manager user add customer="adm8n" username="0106266504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106266504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106266504";
};

# المستخدم 317: 0134632986
:do {
    /tool user-manager user add customer="adm8n" username="0134632986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134632986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134632986";
};

# المستخدم 318: 0123250041
:do {
    /tool user-manager user add customer="adm8n" username="0123250041" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123250041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123250041";
};

# المستخدم 319: 0123127357
:do {
    /tool user-manager user add customer="adm8n" username="0123127357" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123127357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123127357";
};

# المستخدم 320: 0106916875
:do {
    /tool user-manager user add customer="adm8n" username="0106916875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106916875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106916875";
};

# المستخدم 321: 0134428044
:do {
    /tool user-manager user add customer="adm8n" username="0134428044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134428044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134428044";
};

# المستخدم 322: 0176683897
:do {
    /tool user-manager user add customer="adm8n" username="0176683897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176683897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176683897";
};

# المستخدم 323: 0128729493
:do {
    /tool user-manager user add customer="adm8n" username="0128729493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128729493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128729493";
};

# المستخدم 324: 0143000680
:do {
    /tool user-manager user add customer="adm8n" username="0143000680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143000680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143000680";
};

# المستخدم 325: 0148682182
:do {
    /tool user-manager user add customer="adm8n" username="0148682182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148682182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148682182";
};

# المستخدم 326: 0166649830
:do {
    /tool user-manager user add customer="adm8n" username="0166649830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166649830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166649830";
};

# المستخدم 327: 0177477418
:do {
    /tool user-manager user add customer="adm8n" username="0177477418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177477418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177477418";
};

# المستخدم 328: 0165927487
:do {
    /tool user-manager user add customer="adm8n" username="0165927487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165927487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165927487";
};

# المستخدم 329: 0153945267
:do {
    /tool user-manager user add customer="adm8n" username="0153945267" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153945267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153945267";
};

# المستخدم 330: 0149608598
:do {
    /tool user-manager user add customer="adm8n" username="0149608598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149608598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149608598";
};

# المستخدم 331: 0178545130
:do {
    /tool user-manager user add customer="adm8n" username="0178545130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178545130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178545130";
};

# المستخدم 332: 0124987653
:do {
    /tool user-manager user add customer="adm8n" username="0124987653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124987653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124987653";
};

# المستخدم 333: 0150282290
:do {
    /tool user-manager user add customer="adm8n" username="0150282290" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150282290";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150282290";
};

# المستخدم 334: 0147466893
:do {
    /tool user-manager user add customer="adm8n" username="0147466893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147466893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147466893";
};

# المستخدم 335: 0173932501
:do {
    /tool user-manager user add customer="adm8n" username="0173932501" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173932501";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173932501";
};

# المستخدم 336: 0124113214
:do {
    /tool user-manager user add customer="adm8n" username="0124113214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124113214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124113214";
};

# المستخدم 337: 0176102977
:do {
    /tool user-manager user add customer="adm8n" username="0176102977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176102977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176102977";
};

# المستخدم 338: 0100961055
:do {
    /tool user-manager user add customer="adm8n" username="0100961055" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100961055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100961055";
};

# المستخدم 339: 0170642500
:do {
    /tool user-manager user add customer="adm8n" username="0170642500" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170642500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170642500";
};

# المستخدم 340: 0126780271
:do {
    /tool user-manager user add customer="adm8n" username="0126780271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126780271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126780271";
};

# المستخدم 341: 0172613023
:do {
    /tool user-manager user add customer="adm8n" username="0172613023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172613023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172613023";
};

# المستخدم 342: 0107652016
:do {
    /tool user-manager user add customer="adm8n" username="0107652016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107652016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107652016";
};

# المستخدم 343: 0198833179
:do {
    /tool user-manager user add customer="adm8n" username="0198833179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198833179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198833179";
};

# المستخدم 344: 0159977744
:do {
    /tool user-manager user add customer="adm8n" username="0159977744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159977744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159977744";
};

# المستخدم 345: 0111906364
:do {
    /tool user-manager user add customer="adm8n" username="0111906364" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111906364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111906364";
};

# المستخدم 346: 0101023153
:do {
    /tool user-manager user add customer="adm8n" username="0101023153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101023153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101023153";
};

# المستخدم 347: 0135464379
:do {
    /tool user-manager user add customer="adm8n" username="0135464379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135464379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135464379";
};

# المستخدم 348: 0166846544
:do {
    /tool user-manager user add customer="adm8n" username="0166846544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166846544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166846544";
};

# المستخدم 349: 0113374159
:do {
    /tool user-manager user add customer="adm8n" username="0113374159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113374159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113374159";
};

# المستخدم 350: 0157625590
:do {
    /tool user-manager user add customer="adm8n" username="0157625590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157625590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157625590";
};

# المستخدم 351: 0157345830
:do {
    /tool user-manager user add customer="adm8n" username="0157345830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157345830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157345830";
};

# المستخدم 352: 0155889894
:do {
    /tool user-manager user add customer="adm8n" username="0155889894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155889894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155889894";
};

# المستخدم 353: 0138394742
:do {
    /tool user-manager user add customer="adm8n" username="0138394742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138394742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138394742";
};

# المستخدم 354: 0115075272
:do {
    /tool user-manager user add customer="adm8n" username="0115075272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115075272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115075272";
};

# المستخدم 355: 0167706863
:do {
    /tool user-manager user add customer="adm8n" username="0167706863" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167706863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167706863";
};

# المستخدم 356: 0113390307
:do {
    /tool user-manager user add customer="adm8n" username="0113390307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113390307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113390307";
};

# المستخدم 357: 0189466188
:do {
    /tool user-manager user add customer="adm8n" username="0189466188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189466188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189466188";
};

# المستخدم 358: 0133577153
:do {
    /tool user-manager user add customer="adm8n" username="0133577153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133577153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133577153";
};

# المستخدم 359: 0191746284
:do {
    /tool user-manager user add customer="adm8n" username="0191746284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191746284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191746284";
};

# المستخدم 360: 0196176348
:do {
    /tool user-manager user add customer="adm8n" username="0196176348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196176348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196176348";
};

# المستخدم 361: 0111321974
:do {
    /tool user-manager user add customer="adm8n" username="0111321974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111321974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111321974";
};

# المستخدم 362: 0146553428
:do {
    /tool user-manager user add customer="adm8n" username="0146553428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146553428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146553428";
};

# المستخدم 363: 0123676852
:do {
    /tool user-manager user add customer="adm8n" username="0123676852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123676852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123676852";
};

# المستخدم 364: 0170377491
:do {
    /tool user-manager user add customer="adm8n" username="0170377491" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170377491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170377491";
};

# المستخدم 365: 0105467398
:do {
    /tool user-manager user add customer="adm8n" username="0105467398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105467398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105467398";
};

# المستخدم 366: 0153399256
:do {
    /tool user-manager user add customer="adm8n" username="0153399256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153399256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153399256";
};

# المستخدم 367: 0178619765
:do {
    /tool user-manager user add customer="adm8n" username="0178619765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178619765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178619765";
};

# المستخدم 368: 0102068617
:do {
    /tool user-manager user add customer="adm8n" username="0102068617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102068617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102068617";
};

# المستخدم 369: 0186209760
:do {
    /tool user-manager user add customer="adm8n" username="0186209760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186209760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186209760";
};

# المستخدم 370: 0119690540
:do {
    /tool user-manager user add customer="adm8n" username="0119690540" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119690540";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119690540";
};

# المستخدم 371: 0162231252
:do {
    /tool user-manager user add customer="adm8n" username="0162231252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162231252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162231252";
};

# المستخدم 372: 0124128887
:do {
    /tool user-manager user add customer="adm8n" username="0124128887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124128887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124128887";
};

# المستخدم 373: 0141164122
:do {
    /tool user-manager user add customer="adm8n" username="0141164122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141164122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141164122";
};

# المستخدم 374: 0177987293
:do {
    /tool user-manager user add customer="adm8n" username="0177987293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177987293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177987293";
};

# المستخدم 375: 0197940848
:do {
    /tool user-manager user add customer="adm8n" username="0197940848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197940848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197940848";
};

# المستخدم 376: 0156374810
:do {
    /tool user-manager user add customer="adm8n" username="0156374810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156374810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156374810";
};

# المستخدم 377: 0153728108
:do {
    /tool user-manager user add customer="adm8n" username="0153728108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153728108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153728108";
};

# المستخدم 378: 0172238745
:do {
    /tool user-manager user add customer="adm8n" username="0172238745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172238745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172238745";
};

# المستخدم 379: 0144159050
:do {
    /tool user-manager user add customer="adm8n" username="0144159050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144159050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144159050";
};

# المستخدم 380: 0165692528
:do {
    /tool user-manager user add customer="adm8n" username="0165692528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165692528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165692528";
};

# المستخدم 381: 0116359160
:do {
    /tool user-manager user add customer="adm8n" username="0116359160" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116359160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116359160";
};

# المستخدم 382: 0159607822
:do {
    /tool user-manager user add customer="adm8n" username="0159607822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159607822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159607822";
};

# المستخدم 383: 0170918124
:do {
    /tool user-manager user add customer="adm8n" username="0170918124" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170918124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170918124";
};

# المستخدم 384: 0132794417
:do {
    /tool user-manager user add customer="adm8n" username="0132794417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132794417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132794417";
};

# المستخدم 385: 0188313977
:do {
    /tool user-manager user add customer="adm8n" username="0188313977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188313977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188313977";
};

# المستخدم 386: 0169535641
:do {
    /tool user-manager user add customer="adm8n" username="0169535641" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169535641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169535641";
};

# المستخدم 387: 0127886435
:do {
    /tool user-manager user add customer="adm8n" username="0127886435" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127886435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127886435";
};

# المستخدم 388: 0172377554
:do {
    /tool user-manager user add customer="adm8n" username="0172377554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172377554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172377554";
};

# المستخدم 389: 0140127082
:do {
    /tool user-manager user add customer="adm8n" username="0140127082" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140127082";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140127082";
};

# المستخدم 390: 0103747667
:do {
    /tool user-manager user add customer="adm8n" username="0103747667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103747667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103747667";
};

# المستخدم 391: 0137322120
:do {
    /tool user-manager user add customer="adm8n" username="0137322120" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137322120";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137322120";
};

# المستخدم 392: 0119467557
:do {
    /tool user-manager user add customer="adm8n" username="0119467557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119467557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119467557";
};

# المستخدم 393: 0193069200
:do {
    /tool user-manager user add customer="adm8n" username="0193069200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193069200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193069200";
};

# المستخدم 394: 0113305581
:do {
    /tool user-manager user add customer="adm8n" username="0113305581" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113305581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113305581";
};

# المستخدم 395: 0126583278
:do {
    /tool user-manager user add customer="adm8n" username="0126583278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126583278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126583278";
};

# المستخدم 396: 0187299034
:do {
    /tool user-manager user add customer="adm8n" username="0187299034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187299034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187299034";
};

# المستخدم 397: 0160868677
:do {
    /tool user-manager user add customer="adm8n" username="0160868677" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160868677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160868677";
};

# المستخدم 398: 0185531518
:do {
    /tool user-manager user add customer="adm8n" username="0185531518" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185531518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185531518";
};

# المستخدم 399: 0193092535
:do {
    /tool user-manager user add customer="adm8n" username="0193092535" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193092535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193092535";
};

# المستخدم 400: 0169710989
:do {
    /tool user-manager user add customer="adm8n" username="0169710989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169710989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169710989";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
