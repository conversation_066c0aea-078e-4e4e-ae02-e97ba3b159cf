#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تكامل ميزة حذف المستخدمين بالإيميل مع مؤشر التقدم المباشر في الملف الأصلي
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن جميع الميزات تعمل في الملف الأصلي
"""

import re

class TestOriginalFileIntegration:
    """اختبار تكامل الملف الأصلي"""
    
    def __init__(self):
        self.original_file = "اخر حاجة  - كروت وبوت.py"
        
    def test_button_in_main_menu(self):
        """اختبار وجود الزر في القائمة الرئيسية"""
        print("🔍 اختبار وجود الزر في القائمة الرئيسية...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الزر الجديد
        button_pattern = r'🗑️ حذف يوزرات بالإيميل.*delete_users_by_email'
        if not re.search(button_pattern, content):
            print("❌ الزر الجديد غير موجود في القائمة")
            return False
        
        print("✅ تم العثور على الزر في القائمة الرئيسية")
        return True
    
    def test_progress_bar_functions(self):
        """اختبار وجود دوال مؤشر التقدم"""
        print("\n🔍 اختبار وجود دوال مؤشر التقدم...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'create_progress_bar',
            'estimate_remaining_time',
            'update_progress_message',
            'edit_telegram_message',
            'send_telegram_message_and_get_id',
            'create_safe_api_connection'
        ]
        
        for func_name in required_functions:
            func_pattern = f'def {func_name}\\('
            if not re.search(func_pattern, content):
                print(f"❌ الدالة غير موجودة: {func_name}")
                return False
            print(f"✅ الدالة موجودة: {func_name}")
        
        return True
    
    def test_callback_handling(self):
        """اختبار معالجة callback_data"""
        print("\n🔍 اختبار معالجة callback_data...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن معالجة callback_data
        patterns = [
            r'callback_data == "delete_users_by_email"',
            r'callback_data\.startswith\("confirm_delete_email_"\)',
            r'callback_data == "cancel_delete_email"'
        ]
        
        for pattern in patterns:
            if not re.search(pattern, content):
                print(f"❌ معالجة callback غير موجودة: {pattern}")
                return False
        
        print("✅ جميع معالجات callback_data موجودة")
        return True
    
    def test_text_message_handling(self):
        """اختبار معالجة الرسائل النصية"""
        print("\n🔍 اختبار معالجة الرسائل النصية...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن معالجة نمط الإيميل في process_telegram_command
        pattern = r'waiting_for_email_pattern.*chat_id in self\.waiting_for_email_pattern'
        if not re.search(pattern, content):
            print("❌ معالجة نمط الإيميل غير موجودة في process_telegram_command")
            return False
        
        print("✅ معالجة الرسائل النصية موجودة")
        return True
    
    def test_live_progress_integration(self):
        """اختبار تكامل مؤشر التقدم المباشر"""
        print("\n🔍 اختبار تكامل مؤشر التقدم المباشر...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة execute_delete_users_by_email
        func_match = re.search(r'def execute_delete_users_by_email.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على دالة execute_delete_users_by_email")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من التكامل مع مؤشر التقدم المباشر
        integration_elements = [
            'send_telegram_message_and_get_id',
            'progress_message_id',
            'update_progress_message',
            'update_frequency = 1 if len(users_to_delete) <= 20 else 5',
            'edit_telegram_message',
            'final_progress_bar = self.create_progress_bar',
            'create_safe_api_connection'
        ]
        
        for element in integration_elements:
            if element not in func_code:
                print(f"❌ عنصر التكامل غير موجود: {element}")
                return False
        
        print("✅ تكامل مؤشر التقدم المباشر موجود ومكتمل")
        return True
    
    def test_progress_message_format(self):
        """اختبار تنسيق رسالة التقدم"""
        print("\n🔍 اختبار تنسيق رسالة التقدم...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة update_progress_message
        func_match = re.search(r'def update_progress_message.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على دالة update_progress_message")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من عناصر الرسالة
        required_elements = [
            '🗑️ **جاري حذف المستخدمين**',
            '📧 **النمط:**',
            '📊 **التقدم:**',
            '📈 **الإحصائيات:**',
            '• **المعالج:**',
            '• **تم حذفه:** ✅',
            '• **فشل:** ❌',
            '⏱️ **الوقت:**',
            '• **المنقضي:**',
            '• **المتبقي:**',
            '🔄 **جاري المعالجة...**'
        ]
        
        for element in required_elements:
            if element not in func_code:
                print(f"❌ عنصر الرسالة غير موجود: {element}")
                return False
        
        print("✅ تنسيق رسالة التقدم صحيح ومكتمل")
        return True
    
    def test_safe_api_connection(self):
        """اختبار دالة الاتصال الآمن"""
        print("\n🔍 اختبار دالة الاتصال الآمن...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة create_safe_api_connection
        func_match = re.search(r'def create_safe_api_connection.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على دالة create_safe_api_connection")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من العناصر المهمة
        required_elements = [
            'hasattr(self, \'api_ip_entry\')',
            'hasattr(self, \'api_username_entry\')',
            'hasattr(self, \'api_password_entry\')',
            'routeros_api.RouterOsApiPool',
            'except Exception as',
            'self.logger.'
        ]
        
        for element in required_elements:
            if element not in func_code:
                print(f"❌ العنصر المطلوب غير موجود: {element}")
                return False
        
        print("✅ دالة الاتصال الآمن موجودة ومكتملة")
        return True
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار تكامل الملف الأصلي")
        print("="*70)
        
        tests = [
            ("وجود الزر في القائمة", self.test_button_in_main_menu),
            ("دوال مؤشر التقدم", self.test_progress_bar_functions),
            ("معالجة callback", self.test_callback_handling),
            ("معالجة الرسائل النصية", self.test_text_message_handling),
            ("تكامل مؤشر التقدم المباشر", self.test_live_progress_integration),
            ("تنسيق رسالة التقدم", self.test_progress_message_format),
            ("دالة الاتصال الآمن", self.test_safe_api_connection)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 تشغيل: {test_name}")
                result = test_func()
                if result:
                    print(f"✅ نجح: {test_name}")
                    passed += 1
                else:
                    print(f"❌ فشل: {test_name}")
                    failed += 1
            except Exception as e:
                print(f"❌ خطأ في {test_name}: {str(e)}")
                failed += 1
        
        print("\n" + "="*70)
        print("📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("🎉 جميع الاختبارات نجحت! الملف الأصلي جاهز للاستخدام.")
            print("\n🔥 الميزات المتاحة:")
            print("• ✅ زر 'حذف يوزرات بالإيميل' في القائمة الرئيسية")
            print("• ✅ مؤشر تقدم مباشر مع شريط نصي █░")
            print("• ✅ نسبة مئوية دقيقة للتقدم")
            print("• ✅ تقدير ذكي للوقت المتبقي")
            print("• ✅ إحصائيات مفصلة (نجح/فشل)")
            print("• ✅ تحديث مباشر للرسالة")
            print("• ✅ اتصال آمن مع MikroTik")
            print("• ✅ تقرير نهائي مفصل")
            print("\n🚀 الملف جاهز للاستخدام الفوري!")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        
        return failed == 0

def main():
    """الدالة الرئيسية"""
    tester = TestOriginalFileIntegration()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
