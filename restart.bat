@echo off
chcp 65001 >nul
REM ملف إعادة تشغيل محسن - تم إنشاؤه تلقائياً
REM أفضل طريقة Python: py

echo 🔄 جاري إعادة تشغيل البرنامج...
echo ⏳ انتظار 3 ثوانٍ للتأكد من إغلاق البرنامج...

REM انتظار 3 ثوانٍ
timeout /t 3 /nobreak >nul

echo 🚀 بدء تشغيل البرنامج الرئيسي...

REM الانتقال للمجلد الصحيح
cd /d "C:\Users\<USER>\Desktop\New folder (6)"

REM تشغيل البرنامج باستخدام أفضل طريقة
echo 🎯 تشغيل البرنامج باستخدام: py
start "" py "كروت وبوت.py"

echo ✅ تم إرسال أمر تشغيل البرنامج

echo ⏳ انتظار 4 ثوانٍ للتأكد من بدء البرنامج...
timeout /t 4 /nobreak >nul

echo 📱 تشغيل سكريبت إرسال رسالة التأكيد...

REM تشغيل سكريبت التأكيد
if exist "temp_send_confirmation.py" (
    echo 📱 تشغيل سكريبت إرسال التأكيد...
    start /wait "" py "temp_send_confirmation.py"
    echo ✅ تم تشغيل سكريبت التأكيد
) else (
    echo ⚠️ ملف إرسال التأكيد غير موجود
)

echo 🧹 تنظيف الملفات المؤقتة...

REM حذف الملفات المؤقتة
if exist "temp_send_confirmation.py" (
    del "temp_send_confirmation.py"
    echo ✅ تم حذف ملف إرسال التأكيد
)

if exist "temp_restart_info.json" (
    del "temp_restart_info.json"
    echo ✅ تم حذف ملف معلومات إعادة التشغيل
)

if exist "restart_config.json" (
    del "restart_config.json"
    echo ✅ تم حذف ملف إعدادات إعادة التشغيل
)

echo ✅ تم الانتهاء من عملية إعادة التشغيل بنجاح
echo 🎉 البرنامج جاهز للاستخدام

REM إنهاء ملف batch
exit /b 0
