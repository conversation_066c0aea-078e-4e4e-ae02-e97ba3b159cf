#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة connect_api
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن مشكلة المتغير host تم حلها
"""

import re

def test_connect_api_fix():
    """اختبار إصلاح دالة connect_api"""
    print("🔍 اختبار إصلاح دالة connect_api...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة connect_api
    connect_api_match = re.search(r'def connect_api\(self\):(.*?)(?=def|\Z)', content, re.DOTALL)
    if not connect_api_match:
        print("❌ لم يتم العثور على دالة connect_api")
        return False
    
    connect_api_code = connect_api_match.group(1)
    
    # التحقق من وجود تعريف افتراضي للمتغيرات
    if 'host = "غير محدد"' not in connect_api_code:
        print("❌ لم يتم العثور على تعريف افتراضي للمتغير host")
        return False
    
    if 'port = 8728' not in connect_api_code:
        print("❌ لم يتم العثور على تعريف افتراضي للمتغير port")
        return False
    
    if 'use_ssl = False' not in connect_api_code:
        print("❌ لم يتم العثور على تعريف افتراضي للمتغير use_ssl")
        return False
    
    # التحقق من ترتيب التعريفات
    lines = connect_api_code.split('\n')
    host_line = -1
    port_line = -1
    use_ssl_line = -1
    actual_host_line = -1
    
    for i, line in enumerate(lines):
        if 'host = "غير محدد"' in line:
            host_line = i
        elif 'port = 8728' in line:
            port_line = i
        elif 'use_ssl = False' in line:
            use_ssl_line = i
        elif 'host = self.api_ip_entry.get().strip()' in line:
            actual_host_line = i
    
    # التحقق من أن التعريفات الافتراضية تأتي قبل التعريفات الفعلية
    if host_line == -1 or port_line == -1 or use_ssl_line == -1:
        print("❌ بعض التعريفات الافتراضية مفقودة")
        return False
    
    if actual_host_line != -1 and host_line >= actual_host_line:
        print("❌ التعريف الافتراضي للـ host يجب أن يأتي قبل التعريف الفعلي")
        return False
    
    print("✅ تم إصلاح دالة connect_api بنجاح")
    print(f"  • تعريف افتراضي للـ host: السطر {host_line + 1}")
    print(f"  • تعريف افتراضي للـ port: السطر {port_line + 1}")
    print(f"  • تعريف افتراضي للـ use_ssl: السطر {use_ssl_line + 1}")
    
    return True

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n🔍 اختبار معالجة الأخطاء...")
    
    copy_file = "اخر حاجة  - كروت وبوت - Copy.py"
    
    with open(copy_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن استخدام المتغيرات في معالجة الأخطاء
    error_handling_patterns = [
        r'get_connection_troubleshooting_tips\(host, port\)',
        r'troubleshooting = self\.get_connection_troubleshooting_tips\(host, port\)'
    ]
    
    for pattern in error_handling_patterns:
        if re.search(pattern, content):
            print("✅ معالجة الأخطاء تستخدم المتغيرات بشكل صحيح")
            return True
    
    print("❌ لم يتم العثور على استخدام صحيح للمتغيرات في معالجة الأخطاء")
    return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح connect_api")
    print("="*50)
    
    tests = [
        ("إصلاح connect_api", test_connect_api_fix),
        ("معالجة الأخطاء", test_error_handling)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*50)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح المشكلة بنجاح!")
    else:
        print("⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
