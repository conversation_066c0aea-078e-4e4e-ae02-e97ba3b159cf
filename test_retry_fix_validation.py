#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق من إصلاح مشكلة إعادة المحاولة للكروت الفاشلة
"""

import re
import os

def test_improved_failed_cards_saving():
    """اختبار الإصلاح المحسن لحفظ الكروت الفاشلة"""
    print("🔍 اختبار الإصلاح المحسن لحفظ الكروت الفاشلة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من وجود الإصلاح الجديد
    fix_patterns = [
        'إصلاح: إنشاء failed_cards_info حتى لو لم تكن single_card_failed_cards موجودة',
        'استخدام قائمة failed_cards المحلية كبديل',
        'سيتم استخدام failed_cards المحلية',
        'if failed_cards:  # استخدام قائمة failed_cards المحلية',
        'تحويل failed_cards إلى تنسيق مناسب لإعادة المحاولة',
        'converted_failed_cards = []',
        'for failed_card in failed_cards:',
        'البحث عن الكرت الأصلي في generated_credentials',
        'for cred in self.generated_credentials:',
        'if cred.get(\'username\') == failed_card.get(\'name\'):',
        'converted_card = {',
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد (من failed_cards)'
    ]
    
    missing_patterns = []
    for pattern in fix_patterns:
        if pattern not in func_code:
            missing_patterns.append(pattern)
        else:
            print(f"✅ نمط الإصلاح موجود: {pattern[:50]}...")
    
    if missing_patterns:
        print(f"❌ أنماط الإصلاح المفقودة ({len(missing_patterns)}):")
        for pattern in missing_patterns:
            print(f"   - {pattern}")
        return False
    
    print("✅ جميع أنماط الإصلاح موجودة")
    return True

def test_fallback_logic():
    """اختبار منطق الاحتياط (fallback)"""
    print("\n🔍 اختبار منطق الاحتياط...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من منطق الاحتياط
    fallback_patterns = [
        'else:',  # الـ else block للشرط الفرعي
        'if failed_cards:',  # التحقق من وجود failed_cards
        'converted_failed_cards = []',  # إنشاء قائمة جديدة
        'for failed_card in failed_cards:',  # التكرار على failed_cards
        'for cred in self.generated_credentials:',  # البحث في الكروت الأصلية
        'if original_card:',  # التحقق من وجود الكرت الأصلي
        'converted_failed_cards.append(converted_card)',  # إضافة الكرت المحول
        '\'failed_cards\': converted_failed_cards'  # حفظ القائمة المحولة
    ]
    
    for pattern in fallback_patterns:
        if pattern not in func_code:
            print(f"❌ نمط الاحتياط غير موجود: {pattern}")
            return False
        print(f"✅ نمط الاحتياط موجود: {pattern}")
    
    return True

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n🔍 اختبار معالجة الأخطاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من معالجة الأخطاء
    error_patterns = [
        'لا توجد كروت فاشلة في أي من القوائم رغم وجود failed_count',
        'self.logger.error(f"❌ لا توجد كروت فاشلة في أي من القوائم',
        'self.logger.warning(f"⚠️ لا توجد كروت فاشلة محفوظة في single_card_failed_cards'
    ]
    
    for pattern in error_patterns:
        if pattern not in func_code:
            print(f"❌ نمط معالجة الأخطاء غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة الأخطاء موجود: {pattern[:50]}...")
    
    return True

def test_data_conversion():
    """اختبار تحويل البيانات"""
    print("\n🔍 اختبار تحويل البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من تحويل البيانات
    conversion_patterns = [
        'converted_card = {',
        '\'username\': original_card.get(\'username\', \'\'),',
        '\'password\': original_card.get(\'password\', \'\'),',
        '\'profile\': original_card.get(\'profile\', \'\'),',
        '\'comment\': original_card.get(\'comment\', \'\'),',
        '\'server\': server,',
        '\'limit_bytes\': original_card.get(\'limit_bytes\', \'\'),',
        '\'limit_unit\': original_card.get(\'limit_unit\', \'GB\'),',
        '\'days\': original_card.get(\'days\', \'\'),',
        '\'email_template\': original_card.get(\'email_template\', \'@pro.pro\'),',
        '\'error\': failed_card.get(\'error\', \'خطأ غير محدد\')'
    ]
    
    for pattern in conversion_patterns:
        if pattern not in func_code:
            print(f"❌ نمط تحويل البيانات غير موجود: {pattern}")
            return False
        print(f"✅ نمط تحويل البيانات موجود: {pattern[:40]}...")
    
    return True

def test_logging_improvements():
    """اختبار تحسينات السجلات"""
    print("\n🔍 اختبار تحسينات السجلات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من رسائل السجلات المحسنة
    logging_patterns = [
        'تم حفظ معلومات الكروت الفاشلة للكرت الواحد (من failed_cards)',
        'لا توجد كروت فاشلة محفوظة في single_card_failed_cards، سيتم استخدام failed_cards المحلية',
        'لا توجد كروت فاشلة في أي من القوائم رغم وجود failed_count'
    ]
    
    for pattern in logging_patterns:
        if pattern not in func_code:
            print(f"❌ نمط السجلات غير موجود: {pattern[:50]}...")
            return False
        print(f"✅ نمط السجلات موجود: {pattern[:50]}...")
    
    return True

def run_fix_validation():
    """تشغيل اختبار التحقق من الإصلاح"""
    print("🚀 بدء اختبار التحقق من إصلاح مشكلة إعادة المحاولة\n")
    
    tests = [
        test_improved_failed_cards_saving,
        test_fallback_logic,
        test_error_handling,
        test_data_conversion,
        test_logging_improvements
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات الإصلاح نجحت!")
        print("✅ الإصلاح مطبق بشكل صحيح")
        return True
    else:
        print("⚠️ بعض اختبارات الإصلاح فشلت")
        return False

if __name__ == "__main__":
    run_fix_validation()
