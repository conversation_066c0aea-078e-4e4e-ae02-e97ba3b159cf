# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 13:52:38
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0104334570
:do {
    /tool user-manager user add customer="adm8n" username="0104334570" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104334570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104334570";
};

# المستخدم 2: 0188157650
:do {
    /tool user-manager user add customer="adm8n" username="0188157650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188157650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188157650";
};

# المستخدم 3: 0167681093
:do {
    /tool user-manager user add customer="adm8n" username="0167681093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167681093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167681093";
};

# المستخدم 4: 0146525288
:do {
    /tool user-manager user add customer="adm8n" username="0146525288" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146525288";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146525288";
};

# المستخدم 5: 0195911611
:do {
    /tool user-manager user add customer="adm8n" username="0195911611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195911611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195911611";
};

# المستخدم 6: 0158711074
:do {
    /tool user-manager user add customer="adm8n" username="0158711074" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158711074";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158711074";
};

# المستخدم 7: 0118923784
:do {
    /tool user-manager user add customer="adm8n" username="0118923784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118923784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118923784";
};

# المستخدم 8: 0105832447
:do {
    /tool user-manager user add customer="adm8n" username="0105832447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105832447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105832447";
};

# المستخدم 9: 0170278252
:do {
    /tool user-manager user add customer="adm8n" username="0170278252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170278252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170278252";
};

# المستخدم 10: 0169598325
:do {
    /tool user-manager user add customer="adm8n" username="0169598325" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169598325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169598325";
};

# المستخدم 11: 0131860322
:do {
    /tool user-manager user add customer="adm8n" username="0131860322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131860322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131860322";
};

# المستخدم 12: 0166446816
:do {
    /tool user-manager user add customer="adm8n" username="0166446816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166446816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166446816";
};

# المستخدم 13: 0139583403
:do {
    /tool user-manager user add customer="adm8n" username="0139583403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139583403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139583403";
};

# المستخدم 14: 0127651738
:do {
    /tool user-manager user add customer="adm8n" username="0127651738" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127651738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127651738";
};

# المستخدم 15: 0168371075
:do {
    /tool user-manager user add customer="adm8n" username="0168371075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168371075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168371075";
};

# المستخدم 16: 0189736280
:do {
    /tool user-manager user add customer="adm8n" username="0189736280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189736280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189736280";
};

# المستخدم 17: 0138588136
:do {
    /tool user-manager user add customer="adm8n" username="0138588136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138588136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138588136";
};

# المستخدم 18: 0164456795
:do {
    /tool user-manager user add customer="adm8n" username="0164456795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164456795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164456795";
};

# المستخدم 19: 0137022094
:do {
    /tool user-manager user add customer="adm8n" username="0137022094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137022094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137022094";
};

# المستخدم 20: 0126812411
:do {
    /tool user-manager user add customer="adm8n" username="0126812411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126812411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126812411";
};

# المستخدم 21: 0166809423
:do {
    /tool user-manager user add customer="adm8n" username="0166809423" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166809423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166809423";
};

# المستخدم 22: 0100140560
:do {
    /tool user-manager user add customer="adm8n" username="0100140560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100140560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100140560";
};

# المستخدم 23: 0157266774
:do {
    /tool user-manager user add customer="adm8n" username="0157266774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157266774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157266774";
};

# المستخدم 24: 0152010475
:do {
    /tool user-manager user add customer="adm8n" username="0152010475" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152010475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152010475";
};

# المستخدم 25: 0147256128
:do {
    /tool user-manager user add customer="adm8n" username="0147256128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147256128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147256128";
};

# المستخدم 26: 0182217242
:do {
    /tool user-manager user add customer="adm8n" username="0182217242" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182217242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182217242";
};

# المستخدم 27: 0158854617
:do {
    /tool user-manager user add customer="adm8n" username="0158854617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158854617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158854617";
};

# المستخدم 28: 0120830218
:do {
    /tool user-manager user add customer="adm8n" username="0120830218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120830218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120830218";
};

# المستخدم 29: 0189176721
:do {
    /tool user-manager user add customer="adm8n" username="0189176721" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189176721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189176721";
};

# المستخدم 30: 0181522279
:do {
    /tool user-manager user add customer="adm8n" username="0181522279" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181522279";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181522279";
};

# المستخدم 31: 0121553498
:do {
    /tool user-manager user add customer="adm8n" username="0121553498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121553498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121553498";
};

# المستخدم 32: 0148248696
:do {
    /tool user-manager user add customer="adm8n" username="0148248696" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148248696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148248696";
};

# المستخدم 33: 0129426908
:do {
    /tool user-manager user add customer="adm8n" username="0129426908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129426908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129426908";
};

# المستخدم 34: 0196318968
:do {
    /tool user-manager user add customer="adm8n" username="0196318968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196318968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196318968";
};

# المستخدم 35: 0145163426
:do {
    /tool user-manager user add customer="adm8n" username="0145163426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145163426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145163426";
};

# المستخدم 36: 0198982406
:do {
    /tool user-manager user add customer="adm8n" username="0198982406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198982406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198982406";
};

# المستخدم 37: 0133623029
:do {
    /tool user-manager user add customer="adm8n" username="0133623029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133623029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133623029";
};

# المستخدم 38: 0143674720
:do {
    /tool user-manager user add customer="adm8n" username="0143674720" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143674720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143674720";
};

# المستخدم 39: 0133943592
:do {
    /tool user-manager user add customer="adm8n" username="0133943592" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133943592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133943592";
};

# المستخدم 40: 0136358915
:do {
    /tool user-manager user add customer="adm8n" username="0136358915" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136358915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136358915";
};

# المستخدم 41: 0180667157
:do {
    /tool user-manager user add customer="adm8n" username="0180667157" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180667157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180667157";
};

# المستخدم 42: 0148495508
:do {
    /tool user-manager user add customer="adm8n" username="0148495508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148495508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148495508";
};

# المستخدم 43: 0169269609
:do {
    /tool user-manager user add customer="adm8n" username="0169269609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169269609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169269609";
};

# المستخدم 44: 0105177565
:do {
    /tool user-manager user add customer="adm8n" username="0105177565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105177565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105177565";
};

# المستخدم 45: 0174035729
:do {
    /tool user-manager user add customer="adm8n" username="0174035729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174035729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174035729";
};

# المستخدم 46: 0110314948
:do {
    /tool user-manager user add customer="adm8n" username="0110314948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110314948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110314948";
};

# المستخدم 47: 0155078818
:do {
    /tool user-manager user add customer="adm8n" username="0155078818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155078818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155078818";
};

# المستخدم 48: 0140841375
:do {
    /tool user-manager user add customer="adm8n" username="0140841375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140841375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140841375";
};

# المستخدم 49: 0183199951
:do {
    /tool user-manager user add customer="adm8n" username="0183199951" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183199951";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183199951";
};

# المستخدم 50: 0146362130
:do {
    /tool user-manager user add customer="adm8n" username="0146362130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146362130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146362130";
};

# المستخدم 51: 0186846907
:do {
    /tool user-manager user add customer="adm8n" username="0186846907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186846907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186846907";
};

# المستخدم 52: 0151346673
:do {
    /tool user-manager user add customer="adm8n" username="0151346673" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151346673";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151346673";
};

# المستخدم 53: 0178986800
:do {
    /tool user-manager user add customer="adm8n" username="0178986800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178986800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178986800";
};

# المستخدم 54: 0130983999
:do {
    /tool user-manager user add customer="adm8n" username="0130983999" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130983999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130983999";
};

# المستخدم 55: 0129132103
:do {
    /tool user-manager user add customer="adm8n" username="0129132103" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129132103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129132103";
};

# المستخدم 56: 0114568401
:do {
    /tool user-manager user add customer="adm8n" username="0114568401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114568401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114568401";
};

# المستخدم 57: 0109543432
:do {
    /tool user-manager user add customer="adm8n" username="0109543432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109543432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109543432";
};

# المستخدم 58: 0140895511
:do {
    /tool user-manager user add customer="adm8n" username="0140895511" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140895511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140895511";
};

# المستخدم 59: 0148485770
:do {
    /tool user-manager user add customer="adm8n" username="0148485770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148485770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148485770";
};

# المستخدم 60: 0116945380
:do {
    /tool user-manager user add customer="adm8n" username="0116945380" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116945380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116945380";
};

# المستخدم 61: 0199603028
:do {
    /tool user-manager user add customer="adm8n" username="0199603028" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199603028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199603028";
};

# المستخدم 62: 0197324037
:do {
    /tool user-manager user add customer="adm8n" username="0197324037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197324037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197324037";
};

# المستخدم 63: 0141871094
:do {
    /tool user-manager user add customer="adm8n" username="0141871094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141871094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141871094";
};

# المستخدم 64: 0126238184
:do {
    /tool user-manager user add customer="adm8n" username="0126238184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126238184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126238184";
};

# المستخدم 65: 0180867484
:do {
    /tool user-manager user add customer="adm8n" username="0180867484" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180867484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180867484";
};

# المستخدم 66: 0102706606
:do {
    /tool user-manager user add customer="adm8n" username="0102706606" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102706606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102706606";
};

# المستخدم 67: 0131659332
:do {
    /tool user-manager user add customer="adm8n" username="0131659332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131659332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131659332";
};

# المستخدم 68: 0189834698
:do {
    /tool user-manager user add customer="adm8n" username="0189834698" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189834698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189834698";
};

# المستخدم 69: 0161080914
:do {
    /tool user-manager user add customer="adm8n" username="0161080914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161080914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161080914";
};

# المستخدم 70: 0145494582
:do {
    /tool user-manager user add customer="adm8n" username="0145494582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145494582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145494582";
};

# المستخدم 71: 0154668123
:do {
    /tool user-manager user add customer="adm8n" username="0154668123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154668123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154668123";
};

# المستخدم 72: 0100837153
:do {
    /tool user-manager user add customer="adm8n" username="0100837153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100837153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100837153";
};

# المستخدم 73: 0120998580
:do {
    /tool user-manager user add customer="adm8n" username="0120998580" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120998580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120998580";
};

# المستخدم 74: 0150108929
:do {
    /tool user-manager user add customer="adm8n" username="0150108929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150108929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150108929";
};

# المستخدم 75: 0149783260
:do {
    /tool user-manager user add customer="adm8n" username="0149783260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149783260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149783260";
};

# المستخدم 76: 0100746695
:do {
    /tool user-manager user add customer="adm8n" username="0100746695" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100746695";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100746695";
};

# المستخدم 77: 0127296664
:do {
    /tool user-manager user add customer="adm8n" username="0127296664" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127296664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127296664";
};

# المستخدم 78: 0162963651
:do {
    /tool user-manager user add customer="adm8n" username="0162963651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162963651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162963651";
};

# المستخدم 79: 0128550599
:do {
    /tool user-manager user add customer="adm8n" username="0128550599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128550599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128550599";
};

# المستخدم 80: 0165255533
:do {
    /tool user-manager user add customer="adm8n" username="0165255533" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165255533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165255533";
};

# المستخدم 81: 0189500557
:do {
    /tool user-manager user add customer="adm8n" username="0189500557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189500557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189500557";
};

# المستخدم 82: 0103035334
:do {
    /tool user-manager user add customer="adm8n" username="0103035334" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103035334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103035334";
};

# المستخدم 83: 0198522148
:do {
    /tool user-manager user add customer="adm8n" username="0198522148" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198522148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198522148";
};

# المستخدم 84: 0163520723
:do {
    /tool user-manager user add customer="adm8n" username="0163520723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163520723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163520723";
};

# المستخدم 85: 0114019643
:do {
    /tool user-manager user add customer="adm8n" username="0114019643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114019643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114019643";
};

# المستخدم 86: 0125248568
:do {
    /tool user-manager user add customer="adm8n" username="0125248568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125248568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125248568";
};

# المستخدم 87: 0105344223
:do {
    /tool user-manager user add customer="adm8n" username="0105344223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105344223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105344223";
};

# المستخدم 88: 0135321418
:do {
    /tool user-manager user add customer="adm8n" username="0135321418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135321418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135321418";
};

# المستخدم 89: 0166264442
:do {
    /tool user-manager user add customer="adm8n" username="0166264442" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166264442";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166264442";
};

# المستخدم 90: 0146897705
:do {
    /tool user-manager user add customer="adm8n" username="0146897705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146897705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146897705";
};

# المستخدم 91: 0131726199
:do {
    /tool user-manager user add customer="adm8n" username="0131726199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131726199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131726199";
};

# المستخدم 92: 0187663294
:do {
    /tool user-manager user add customer="adm8n" username="0187663294" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187663294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187663294";
};

# المستخدم 93: 0184790510
:do {
    /tool user-manager user add customer="adm8n" username="0184790510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184790510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184790510";
};

# المستخدم 94: 0134531277
:do {
    /tool user-manager user add customer="adm8n" username="0134531277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134531277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134531277";
};

# المستخدم 95: 0187646339
:do {
    /tool user-manager user add customer="adm8n" username="0187646339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187646339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187646339";
};

# المستخدم 96: 0157777530
:do {
    /tool user-manager user add customer="adm8n" username="0157777530" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157777530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157777530";
};

# المستخدم 97: 0137559924
:do {
    /tool user-manager user add customer="adm8n" username="0137559924" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137559924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137559924";
};

# المستخدم 98: 0115741417
:do {
    /tool user-manager user add customer="adm8n" username="0115741417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115741417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115741417";
};

# المستخدم 99: 0197660906
:do {
    /tool user-manager user add customer="adm8n" username="0197660906" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197660906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197660906";
};

# المستخدم 100: 0133431610
:do {
    /tool user-manager user add customer="adm8n" username="0133431610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133431610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133431610";
};

# المستخدم 101: 0115580927
:do {
    /tool user-manager user add customer="adm8n" username="0115580927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115580927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115580927";
};

# المستخدم 102: 0147024464
:do {
    /tool user-manager user add customer="adm8n" username="0147024464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147024464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147024464";
};

# المستخدم 103: 0143251113
:do {
    /tool user-manager user add customer="adm8n" username="0143251113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143251113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143251113";
};

# المستخدم 104: 0120268761
:do {
    /tool user-manager user add customer="adm8n" username="0120268761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120268761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120268761";
};

# المستخدم 105: 0123395407
:do {
    /tool user-manager user add customer="adm8n" username="0123395407" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123395407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123395407";
};

# المستخدم 106: 0182279860
:do {
    /tool user-manager user add customer="adm8n" username="0182279860" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182279860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182279860";
};

# المستخدم 107: 0122075779
:do {
    /tool user-manager user add customer="adm8n" username="0122075779" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122075779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122075779";
};

# المستخدم 108: 0115246595
:do {
    /tool user-manager user add customer="adm8n" username="0115246595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115246595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115246595";
};

# المستخدم 109: 0187269229
:do {
    /tool user-manager user add customer="adm8n" username="0187269229" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187269229";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187269229";
};

# المستخدم 110: 0197399659
:do {
    /tool user-manager user add customer="adm8n" username="0197399659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197399659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197399659";
};

# المستخدم 111: 0168065701
:do {
    /tool user-manager user add customer="adm8n" username="0168065701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168065701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168065701";
};

# المستخدم 112: 0182983272
:do {
    /tool user-manager user add customer="adm8n" username="0182983272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182983272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182983272";
};

# المستخدم 113: 0190161900
:do {
    /tool user-manager user add customer="adm8n" username="0190161900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190161900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190161900";
};

# المستخدم 114: 0174524388
:do {
    /tool user-manager user add customer="adm8n" username="0174524388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174524388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174524388";
};

# المستخدم 115: 0187255263
:do {
    /tool user-manager user add customer="adm8n" username="0187255263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187255263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187255263";
};

# المستخدم 116: 0159512971
:do {
    /tool user-manager user add customer="adm8n" username="0159512971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159512971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159512971";
};

# المستخدم 117: 0162648312
:do {
    /tool user-manager user add customer="adm8n" username="0162648312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162648312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162648312";
};

# المستخدم 118: 0119373657
:do {
    /tool user-manager user add customer="adm8n" username="0119373657" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119373657";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119373657";
};

# المستخدم 119: 0128081341
:do {
    /tool user-manager user add customer="adm8n" username="0128081341" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128081341";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128081341";
};

# المستخدم 120: 0148456963
:do {
    /tool user-manager user add customer="adm8n" username="0148456963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148456963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148456963";
};

# المستخدم 121: 0194777245
:do {
    /tool user-manager user add customer="adm8n" username="0194777245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194777245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194777245";
};

# المستخدم 122: 0104005120
:do {
    /tool user-manager user add customer="adm8n" username="0104005120" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104005120";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104005120";
};

# المستخدم 123: 0157650763
:do {
    /tool user-manager user add customer="adm8n" username="0157650763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157650763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157650763";
};

# المستخدم 124: 0187782181
:do {
    /tool user-manager user add customer="adm8n" username="0187782181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187782181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187782181";
};

# المستخدم 125: 0116169019
:do {
    /tool user-manager user add customer="adm8n" username="0116169019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116169019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116169019";
};

# المستخدم 126: 0118373952
:do {
    /tool user-manager user add customer="adm8n" username="0118373952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118373952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118373952";
};

# المستخدم 127: 0183769310
:do {
    /tool user-manager user add customer="adm8n" username="0183769310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183769310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183769310";
};

# المستخدم 128: 0192533878
:do {
    /tool user-manager user add customer="adm8n" username="0192533878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192533878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192533878";
};

# المستخدم 129: 0167986872
:do {
    /tool user-manager user add customer="adm8n" username="0167986872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167986872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167986872";
};

# المستخدم 130: 0102585578
:do {
    /tool user-manager user add customer="adm8n" username="0102585578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102585578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102585578";
};

# المستخدم 131: 0171913712
:do {
    /tool user-manager user add customer="adm8n" username="0171913712" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171913712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171913712";
};

# المستخدم 132: 0114955013
:do {
    /tool user-manager user add customer="adm8n" username="0114955013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114955013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114955013";
};

# المستخدم 133: 0111368306
:do {
    /tool user-manager user add customer="adm8n" username="0111368306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111368306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111368306";
};

# المستخدم 134: 0190670984
:do {
    /tool user-manager user add customer="adm8n" username="0190670984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190670984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190670984";
};

# المستخدم 135: 0101064403
:do {
    /tool user-manager user add customer="adm8n" username="0101064403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101064403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101064403";
};

# المستخدم 136: 0113999715
:do {
    /tool user-manager user add customer="adm8n" username="0113999715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113999715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113999715";
};

# المستخدم 137: 0188355274
:do {
    /tool user-manager user add customer="adm8n" username="0188355274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188355274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188355274";
};

# المستخدم 138: 0161108899
:do {
    /tool user-manager user add customer="adm8n" username="0161108899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161108899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161108899";
};

# المستخدم 139: 0189957660
:do {
    /tool user-manager user add customer="adm8n" username="0189957660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189957660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189957660";
};

# المستخدم 140: 0195064198
:do {
    /tool user-manager user add customer="adm8n" username="0195064198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195064198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195064198";
};

# المستخدم 141: 0102227730
:do {
    /tool user-manager user add customer="adm8n" username="0102227730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102227730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102227730";
};

# المستخدم 142: 0157324106
:do {
    /tool user-manager user add customer="adm8n" username="0157324106" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157324106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157324106";
};

# المستخدم 143: 0120174367
:do {
    /tool user-manager user add customer="adm8n" username="0120174367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120174367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120174367";
};

# المستخدم 144: 0145928661
:do {
    /tool user-manager user add customer="adm8n" username="0145928661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145928661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145928661";
};

# المستخدم 145: 0157055858
:do {
    /tool user-manager user add customer="adm8n" username="0157055858" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157055858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157055858";
};

# المستخدم 146: 0162452324
:do {
    /tool user-manager user add customer="adm8n" username="0162452324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162452324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162452324";
};

# المستخدم 147: 0118397653
:do {
    /tool user-manager user add customer="adm8n" username="0118397653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118397653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118397653";
};

# المستخدم 148: 0113216448
:do {
    /tool user-manager user add customer="adm8n" username="0113216448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113216448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113216448";
};

# المستخدم 149: 0170106650
:do {
    /tool user-manager user add customer="adm8n" username="0170106650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170106650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170106650";
};

# المستخدم 150: 0122856605
:do {
    /tool user-manager user add customer="adm8n" username="0122856605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122856605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122856605";
};

# المستخدم 151: 0146980465
:do {
    /tool user-manager user add customer="adm8n" username="0146980465" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146980465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146980465";
};

# المستخدم 152: 0199547907
:do {
    /tool user-manager user add customer="adm8n" username="0199547907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199547907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199547907";
};

# المستخدم 153: 0147688109
:do {
    /tool user-manager user add customer="adm8n" username="0147688109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147688109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147688109";
};

# المستخدم 154: 0191510409
:do {
    /tool user-manager user add customer="adm8n" username="0191510409" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191510409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191510409";
};

# المستخدم 155: 0182624358
:do {
    /tool user-manager user add customer="adm8n" username="0182624358" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182624358";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182624358";
};

# المستخدم 156: 0191418897
:do {
    /tool user-manager user add customer="adm8n" username="0191418897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191418897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191418897";
};

# المستخدم 157: 0171600887
:do {
    /tool user-manager user add customer="adm8n" username="0171600887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171600887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171600887";
};

# المستخدم 158: 0128249318
:do {
    /tool user-manager user add customer="adm8n" username="0128249318" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128249318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128249318";
};

# المستخدم 159: 0150804705
:do {
    /tool user-manager user add customer="adm8n" username="0150804705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150804705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150804705";
};

# المستخدم 160: 0168427087
:do {
    /tool user-manager user add customer="adm8n" username="0168427087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168427087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168427087";
};

# المستخدم 161: 0126475718
:do {
    /tool user-manager user add customer="adm8n" username="0126475718" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126475718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126475718";
};

# المستخدم 162: 0140620228
:do {
    /tool user-manager user add customer="adm8n" username="0140620228" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140620228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140620228";
};

# المستخدم 163: 0111737896
:do {
    /tool user-manager user add customer="adm8n" username="0111737896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111737896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111737896";
};

# المستخدم 164: 0141701366
:do {
    /tool user-manager user add customer="adm8n" username="0141701366" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141701366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141701366";
};

# المستخدم 165: 0153709199
:do {
    /tool user-manager user add customer="adm8n" username="0153709199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153709199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153709199";
};

# المستخدم 166: 0142239046
:do {
    /tool user-manager user add customer="adm8n" username="0142239046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142239046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142239046";
};

# المستخدم 167: 0131702411
:do {
    /tool user-manager user add customer="adm8n" username="0131702411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131702411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131702411";
};

# المستخدم 168: 0184684804
:do {
    /tool user-manager user add customer="adm8n" username="0184684804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184684804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184684804";
};

# المستخدم 169: 0197805046
:do {
    /tool user-manager user add customer="adm8n" username="0197805046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197805046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197805046";
};

# المستخدم 170: 0151208449
:do {
    /tool user-manager user add customer="adm8n" username="0151208449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151208449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151208449";
};

# المستخدم 171: 0190018497
:do {
    /tool user-manager user add customer="adm8n" username="0190018497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190018497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190018497";
};

# المستخدم 172: 0174007731
:do {
    /tool user-manager user add customer="adm8n" username="0174007731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174007731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174007731";
};

# المستخدم 173: 0171994708
:do {
    /tool user-manager user add customer="adm8n" username="0171994708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171994708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171994708";
};

# المستخدم 174: 0166214759
:do {
    /tool user-manager user add customer="adm8n" username="0166214759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166214759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166214759";
};

# المستخدم 175: 0131419233
:do {
    /tool user-manager user add customer="adm8n" username="0131419233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131419233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131419233";
};

# المستخدم 176: 0100024927
:do {
    /tool user-manager user add customer="adm8n" username="0100024927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100024927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100024927";
};

# المستخدم 177: 0126857917
:do {
    /tool user-manager user add customer="adm8n" username="0126857917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126857917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126857917";
};

# المستخدم 178: 0109222765
:do {
    /tool user-manager user add customer="adm8n" username="0109222765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109222765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109222765";
};

# المستخدم 179: 0127129881
:do {
    /tool user-manager user add customer="adm8n" username="0127129881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127129881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127129881";
};

# المستخدم 180: 0186738518
:do {
    /tool user-manager user add customer="adm8n" username="0186738518" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186738518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186738518";
};

# المستخدم 181: 0157064799
:do {
    /tool user-manager user add customer="adm8n" username="0157064799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157064799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157064799";
};

# المستخدم 182: 0129382756
:do {
    /tool user-manager user add customer="adm8n" username="0129382756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129382756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129382756";
};

# المستخدم 183: 0105108950
:do {
    /tool user-manager user add customer="adm8n" username="0105108950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105108950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105108950";
};

# المستخدم 184: 0103664727
:do {
    /tool user-manager user add customer="adm8n" username="0103664727" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103664727";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103664727";
};

# المستخدم 185: 0198648296
:do {
    /tool user-manager user add customer="adm8n" username="0198648296" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198648296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198648296";
};

# المستخدم 186: 0174824230
:do {
    /tool user-manager user add customer="adm8n" username="0174824230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174824230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174824230";
};

# المستخدم 187: 0198130272
:do {
    /tool user-manager user add customer="adm8n" username="0198130272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198130272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198130272";
};

# المستخدم 188: 0150759661
:do {
    /tool user-manager user add customer="adm8n" username="0150759661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150759661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150759661";
};

# المستخدم 189: 0162498314
:do {
    /tool user-manager user add customer="adm8n" username="0162498314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162498314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162498314";
};

# المستخدم 190: 0163237179
:do {
    /tool user-manager user add customer="adm8n" username="0163237179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163237179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163237179";
};

# المستخدم 191: 0110841638
:do {
    /tool user-manager user add customer="adm8n" username="0110841638" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110841638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110841638";
};

# المستخدم 192: 0152876930
:do {
    /tool user-manager user add customer="adm8n" username="0152876930" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152876930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152876930";
};

# المستخدم 193: 0102351376
:do {
    /tool user-manager user add customer="adm8n" username="0102351376" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102351376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102351376";
};

# المستخدم 194: 0100153288
:do {
    /tool user-manager user add customer="adm8n" username="0100153288" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100153288";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100153288";
};

# المستخدم 195: 0122150690
:do {
    /tool user-manager user add customer="adm8n" username="0122150690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122150690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122150690";
};

# المستخدم 196: 0146468252
:do {
    /tool user-manager user add customer="adm8n" username="0146468252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146468252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146468252";
};

# المستخدم 197: 0145315719
:do {
    /tool user-manager user add customer="adm8n" username="0145315719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145315719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145315719";
};

# المستخدم 198: 0120602747
:do {
    /tool user-manager user add customer="adm8n" username="0120602747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120602747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120602747";
};

# المستخدم 199: 0158022581
:do {
    /tool user-manager user add customer="adm8n" username="0158022581" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158022581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158022581";
};

# المستخدم 200: 0140221626
:do {
    /tool user-manager user add customer="adm8n" username="0140221626" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140221626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140221626";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
