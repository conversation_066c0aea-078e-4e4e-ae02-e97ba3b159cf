#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة حذف الكروت المرسلة للميكروتيك عند وجود فشل في الإرسال
Test Auto Delete Failed Cards Feature

هذا الاختبار يتحقق من:
1. حذف الكروت الناجحة عند وجود كروت فاشلة في عملية البرق
2. عدم الحذف عند عدم وجود كروت فاشلة
3. عدم الحذف عند عدم وجود كروت ناجحة
4. العمل فقط مع نظام الهوت سبوت
5. العمل فقط مع عمليات البرق
"""

import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

class TestAutoDeleteFailedCards(unittest.TestCase):
    """اختبار ميزة حذف الكروت المرسلة عند وجود فشل"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء mock للتطبيق الرئيسي
        self.mock_app = Mock()
        
        # إعداد logger
        self.mock_app.logger = Mock()
        
        # إعداد نوع النظام
        self.mock_app.system_type = 'hotspot'
        
        # إعداد بيانات الكروت المولدة
        self.mock_app.generated_credentials = [
            {'username': 'user001', 'password': 'pass001'},
            {'username': 'user002', 'password': 'pass002'},
            {'username': 'user003', 'password': 'pass003'},
            {'username': 'user004', 'password': 'pass004'},
            {'username': 'user005', 'password': 'pass005'},
        ]
        
        # إضافة الدوال المطلوبة
        self.mock_app.delete_successful_cards_from_mikrotik = self.delete_successful_cards_from_mikrotik

    def mock_connect_api(self):
        """Mock للاتصال بـ MikroTik API"""
        mock_api = Mock()
        
        # محاكاة قائمة المستخدمين الموجودين
        mock_users = [
            {'id': '*1', 'name': 'user001'},
            {'id': '*2', 'name': 'user002'},
            {'id': '*3', 'name': 'user003'},
        ]
        
        # محاكاة resource للمستخدمين
        mock_resource = Mock()
        
        def mock_get(name=None):
            if name:
                return [user for user in mock_users if user['name'] == name]
            return mock_users
        
        mock_resource.get = mock_get
        mock_resource.remove = Mock()
        
        mock_api.get_resource.return_value = mock_resource
        mock_api.disconnect = Mock()
        
        return mock_api

    def delete_successful_cards_from_mikrotik(self, successful_usernames, api):
        """حذف الكروت الناجحة من MikroTik عند وجود كروت فاشلة في عملية البرق"""
        try:
            if not successful_usernames:
                self.mock_app.logger.info("🗑️ لا توجد كروت ناجحة لحذفها")
                return 0
            
            deleted_count = 0
            failed_count = 0
            
            self.mock_app.logger.info(f"🗑️ بدء حذف {len(successful_usernames)} كرت ناجح من MikroTik...")
            
            for username in successful_usernames:
                try:
                    # البحث عن المستخدم في HotSpot
                    users = api.get_resource('/ip/hotspot/user').get(name=username)
                    
                    if users:
                        # حذف المستخدم
                        user_id = users[0]['id']
                        api.get_resource('/ip/hotspot/user').remove(user_id)
                        deleted_count += 1
                        self.mock_app.logger.debug(f"✅ تم حذف المستخدم: {username}")
                    else:
                        # المستخدم غير موجود
                        failed_count += 1
                        self.mock_app.logger.warning(f"⚠️ المستخدم غير موجود: {username}")
                        
                except Exception as user_error:
                    failed_count += 1
                    self.mock_app.logger.error(f"❌ فشل في حذف المستخدم {username}: {str(user_error)}")
            
            success_rate = (deleted_count / len(successful_usernames)) * 100
            self.mock_app.logger.info(f"🗑️ نتائج الحذف: تم حذف {deleted_count} من {len(successful_usernames)} كرت (معدل النجاح: {success_rate:.1f}%)")
            
            if failed_count > 0:
                self.mock_app.logger.warning(f"⚠️ فشل في حذف {failed_count} كرت من MikroTik")
            
            return deleted_count
            
        except Exception as e:
            self.mock_app.logger.error(f"❌ خطأ في حذف الكروت الناجحة من MikroTik: {str(e)}")
            return 0

    def test_delete_when_failures_exist(self):
        """اختبار حذف الكروت الناجحة عند وجود كروت فاشلة"""
        
        # إعداد البيانات
        successful_usernames = ['user001', 'user002', 'user003']
        success_count = 3
        error_count = 2  # يوجد كروت فاشلة
        system_type = 'hotspot'
        
        # محاكاة API
        mock_api = self.mock_connect_api()
        
        # التحقق من الشرط
        should_delete = (error_count > 0 and success_count > 0 and system_type == 'hotspot')
        
        self.assertTrue(should_delete, "يجب حذف الكروت الناجحة عند وجود كروت فاشلة")
        
        if should_delete:
            # تنفيذ الحذف
            deleted_count = self.mock_app.delete_successful_cards_from_mikrotik(successful_usernames, mock_api)
            
            # التحقق من النتيجة
            self.assertEqual(deleted_count, 3, "يجب حذف جميع الكروت الناجحة")
        
        print("✅ اختبار حذف الكروت عند وجود فشل نجح")

    def test_no_delete_when_no_failures(self):
        """اختبار عدم حذف الكروت عند عدم وجود كروت فاشلة"""
        
        # إعداد البيانات - بدون كروت فاشلة
        successful_usernames = ['user001', 'user002', 'user003']
        success_count = 3
        error_count = 0  # لا توجد كروت فاشلة
        system_type = 'hotspot'
        
        # التحقق من الشرط
        should_delete = (error_count > 0 and success_count > 0 and system_type == 'hotspot')
        
        self.assertFalse(should_delete, "لا يجب حذف الكروت عند عدم وجود فشل")
        
        print("✅ اختبار عدم حذف الكروت عند عدم وجود فشل نجح")

    def test_no_delete_when_no_success(self):
        """اختبار عدم حذف الكروت عند عدم وجود كروت ناجحة"""
        
        # إعداد البيانات - بدون كروت ناجحة
        successful_usernames = []
        success_count = 0  # لا توجد كروت ناجحة
        error_count = 5
        system_type = 'hotspot'
        
        # التحقق من الشرط
        should_delete = (error_count > 0 and success_count > 0 and system_type == 'hotspot')
        
        self.assertFalse(should_delete, "لا يجب حذف الكروت عند عدم وجود كروت ناجحة")
        
        print("✅ اختبار عدم حذف الكروت عند عدم وجود نجاح نجح")

    def test_no_delete_with_user_manager(self):
        """اختبار عدم حذف الكروت مع نظام User Manager"""
        
        # إعداد البيانات - نظام User Manager
        successful_usernames = ['user001', 'user002', 'user003']
        success_count = 3
        error_count = 2
        system_type = 'user_manager'  # نظام User Manager
        
        # التحقق من الشرط
        should_delete = (error_count > 0 and success_count > 0 and system_type == 'hotspot')
        
        self.assertFalse(should_delete, "لا يجب حذف الكروت مع نظام User Manager")
        
        print("✅ اختبار عدم حذف الكروت مع User Manager نجح")

    def test_delete_function_with_empty_list(self):
        """اختبار دالة الحذف مع قائمة فارغة"""
        
        # إعداد البيانات
        successful_usernames = []
        mock_api = self.mock_connect_api()
        
        # تنفيذ الاختبار
        deleted_count = self.mock_app.delete_successful_cards_from_mikrotik(successful_usernames, mock_api)
        
        # التحقق من النتيجة
        self.assertEqual(deleted_count, 0, "يجب إرجاع 0 عند عدم وجود كروت للحذف")
        
        print("✅ اختبار دالة الحذف مع قائمة فارغة نجح")

    def test_delete_function_with_non_existent_users(self):
        """اختبار دالة الحذف مع مستخدمين غير موجودين"""
        
        # إعداد البيانات - مستخدمين غير موجودين
        successful_usernames = ['user999', 'user888']
        mock_api = self.mock_connect_api()
        
        # تنفيذ الاختبار
        deleted_count = self.mock_app.delete_successful_cards_from_mikrotik(successful_usernames, mock_api)
        
        # التحقق من النتيجة
        self.assertEqual(deleted_count, 0, "يجب إرجاع 0 عند عدم وجود المستخدمين في MikroTik")
        
        print("✅ اختبار دالة الحذف مع مستخدمين غير موجودين نجح")

    def test_delete_function_mixed_results(self):
        """اختبار دالة الحذف مع نتائج مختلطة (بعض المستخدمين موجودين وبعضهم لا)"""
        
        # إعداد البيانات - مزيج من المستخدمين الموجودين وغير الموجودين
        successful_usernames = ['user001', 'user999', 'user002', 'user888', 'user003']
        mock_api = self.mock_connect_api()
        
        # تنفيذ الاختبار
        deleted_count = self.mock_app.delete_successful_cards_from_mikrotik(successful_usernames, mock_api)
        
        # التحقق من النتيجة (3 مستخدمين موجودين من أصل 5)
        self.assertEqual(deleted_count, 3, "يجب حذف المستخدمين الموجودين فقط")
        
        print("✅ اختبار دالة الحذف مع نتائج مختلطة نجح")

    def test_conditions_matrix(self):
        """اختبار مصفوفة شاملة لجميع الحالات"""
        
        # مصفوفة اختبار شاملة
        test_matrix = [
            # (success_count, error_count, system_type, expected_result, description)
            (3, 2, 'hotspot', True, 'نجاح + فشل + هوت سبوت = حذف'),
            (0, 2, 'hotspot', False, 'لا نجاح + فشل + هوت سبوت = لا حذف'),
            (3, 0, 'hotspot', False, 'نجاح + لا فشل + هوت سبوت = لا حذف'),
            (3, 2, 'user_manager', False, 'نجاح + فشل + يوزر منجر = لا حذف'),
            (0, 0, 'hotspot', False, 'لا نجاح + لا فشل + هوت سبوت = لا حذف'),
            (5, 1, 'hotspot', True, 'نجاح كثير + فشل قليل + هوت سبوت = حذف'),
            (1, 10, 'hotspot', True, 'نجاح قليل + فشل كثير + هوت سبوت = حذف'),
        ]
        
        for success_count, error_count, system_type, expected, description in test_matrix:
            should_delete = (error_count > 0 and success_count > 0 and system_type == 'hotspot')
            
            self.assertEqual(should_delete, expected, 
                           f"فشل الاختبار للحالة: {description}")
        
        print("✅ اختبار مصفوفة شاملة لجميع الحالات نجح")

    def test_lightning_specific_conditions(self):
        """اختبار الشروط الخاصة بعملية البرق"""
        
        # اختبار أن الميزة تعمل فقط مع البرق
        # (في التطبيق الفعلي، هذا يحدث في دالة send_to_mikrotik_silent التي تُستخدم للبرق فقط)
        
        # محاكاة سيناريو البرق
        lightning_conditions = {
            'is_lightning': True,  # عملية البرق
            'is_hotspot': True,    # نظام الهوت سبوت
            'has_success': True,   # يوجد كروت ناجحة
            'has_failure': True    # يوجد كروت فاشلة
        }
        
        # التحقق من أن جميع الشروط مستوفاة للبرق
        all_conditions_met = all(lightning_conditions.values())
        
        self.assertTrue(all_conditions_met, "يجب استيفاء جميع شروط البرق")
        
        # محاكاة سيناريو غير البرق (الطريقة العادية)
        regular_conditions = {
            'is_lightning': False,  # ليس عملية البرق
            'is_hotspot': True,     # نظام الهوت سبوت
            'has_success': True,    # يوجد كروت ناجحة
            'has_failure': True     # يوجد كروت فاشلة
        }
        
        # التحقق من أن الشروط غير مستوفاة للطريقة العادية
        all_conditions_met_regular = all(regular_conditions.values())
        
        self.assertFalse(all_conditions_met_regular, "لا يجب استيفاء شروط الطريقة العادية")
        
        print("✅ اختبار الشروط الخاصة بعملية البرق نجح")

if __name__ == '__main__':
    print("🧪 بدء اختبارات ميزة حذف الكروت المرسلة عند وجود فشل...")
    print("=" * 70)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 70)
    print("🎉 انتهت جميع الاختبارات بنجاح!")
