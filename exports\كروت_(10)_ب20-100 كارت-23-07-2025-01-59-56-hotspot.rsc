# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-23 01:59:58
# القالب: 10
# النظام: hotspot
# عدد الكروت: 100
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 100";

:local success 0;
:local errors 0;
:local total 100;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 100 مستخدم Hotspot...";

# المستخدم 1: 0154832267
:do {
    /ip hotspot user add name="0154832267" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154832267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154832267";
};

# المستخدم 2: 0164228123
:do {
    /ip hotspot user add name="0164228123" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164228123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164228123";
};

# المستخدم 3: 0155785131
:do {
    /ip hotspot user add name="0155785131" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155785131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155785131";
};

# المستخدم 4: 0155153459
:do {
    /ip hotspot user add name="0155153459" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155153459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155153459";
};

# المستخدم 5: 0190476780
:do {
    /ip hotspot user add name="0190476780" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190476780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190476780";
};

# المستخدم 6: 0107458503
:do {
    /ip hotspot user add name="0107458503" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107458503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107458503";
};

# المستخدم 7: 0108756063
:do {
    /ip hotspot user add name="0108756063" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108756063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108756063";
};

# المستخدم 8: 0101213095
:do {
    /ip hotspot user add name="0101213095" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101213095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101213095";
};

# المستخدم 9: 0170263753
:do {
    /ip hotspot user add name="0170263753" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170263753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170263753";
};

# المستخدم 10: 0190060118
:do {
    /ip hotspot user add name="0190060118" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190060118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190060118";
};

# المستخدم 11: 0108955570
:do {
    /ip hotspot user add name="0108955570" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108955570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108955570";
};

# المستخدم 12: 0123019310
:do {
    /ip hotspot user add name="0123019310" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123019310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123019310";
};

# المستخدم 13: 0161543736
:do {
    /ip hotspot user add name="0161543736" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161543736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161543736";
};

# المستخدم 14: 0111556713
:do {
    /ip hotspot user add name="0111556713" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111556713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111556713";
};

# المستخدم 15: 0194529939
:do {
    /ip hotspot user add name="0194529939" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194529939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194529939";
};

# المستخدم 16: 0148205831
:do {
    /ip hotspot user add name="0148205831" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148205831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148205831";
};

# المستخدم 17: 0173138453
:do {
    /ip hotspot user add name="0173138453" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173138453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173138453";
};

# المستخدم 18: 0131380618
:do {
    /ip hotspot user add name="0131380618" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131380618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131380618";
};

# المستخدم 19: 0110160702
:do {
    /ip hotspot user add name="0110160702" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110160702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110160702";
};

# المستخدم 20: 0147243699
:do {
    /ip hotspot user add name="0147243699" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147243699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147243699";
};

# المستخدم 21: 0183504015
:do {
    /ip hotspot user add name="0183504015" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183504015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183504015";
};

# المستخدم 22: 0187035177
:do {
    /ip hotspot user add name="0187035177" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187035177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187035177";
};

# المستخدم 23: 0142989127
:do {
    /ip hotspot user add name="0142989127" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142989127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142989127";
};

# المستخدم 24: 0189140415
:do {
    /ip hotspot user add name="0189140415" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189140415";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189140415";
};

# المستخدم 25: 0112396569
:do {
    /ip hotspot user add name="0112396569" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112396569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112396569";
};

# المستخدم 26: 0108086841
:do {
    /ip hotspot user add name="0108086841" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108086841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108086841";
};

# المستخدم 27: 0176138756
:do {
    /ip hotspot user add name="0176138756" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176138756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176138756";
};

# المستخدم 28: 0126685325
:do {
    /ip hotspot user add name="0126685325" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126685325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126685325";
};

# المستخدم 29: 0148438726
:do {
    /ip hotspot user add name="0148438726" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148438726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148438726";
};

# المستخدم 30: 0171201899
:do {
    /ip hotspot user add name="0171201899" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171201899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171201899";
};

# المستخدم 31: 0123508882
:do {
    /ip hotspot user add name="0123508882" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123508882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123508882";
};

# المستخدم 32: 0100504058
:do {
    /ip hotspot user add name="0100504058" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100504058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100504058";
};

# المستخدم 33: 0163755888
:do {
    /ip hotspot user add name="0163755888" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163755888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163755888";
};

# المستخدم 34: 0115984353
:do {
    /ip hotspot user add name="0115984353" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115984353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115984353";
};

# المستخدم 35: 0199212315
:do {
    /ip hotspot user add name="0199212315" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199212315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199212315";
};

# المستخدم 36: 0159161794
:do {
    /ip hotspot user add name="0159161794" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159161794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159161794";
};

# المستخدم 37: 0138513438
:do {
    /ip hotspot user add name="0138513438" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138513438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138513438";
};

# المستخدم 38: 0135011688
:do {
    /ip hotspot user add name="0135011688" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135011688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135011688";
};

# المستخدم 39: 0121526790
:do {
    /ip hotspot user add name="0121526790" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121526790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121526790";
};

# المستخدم 40: 0100868465
:do {
    /ip hotspot user add name="0100868465" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100868465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100868465";
};

# المستخدم 41: 0149498706
:do {
    /ip hotspot user add name="0149498706" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149498706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149498706";
};

# المستخدم 42: 0141206832
:do {
    /ip hotspot user add name="0141206832" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141206832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141206832";
};

# المستخدم 43: 0152638155
:do {
    /ip hotspot user add name="0152638155" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152638155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152638155";
};

# المستخدم 44: 0161195967
:do {
    /ip hotspot user add name="0161195967" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161195967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161195967";
};

# المستخدم 45: 0145403085
:do {
    /ip hotspot user add name="0145403085" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145403085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145403085";
};

# المستخدم 46: 0162654935
:do {
    /ip hotspot user add name="0162654935" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162654935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162654935";
};

# المستخدم 47: 0164034980
:do {
    /ip hotspot user add name="0164034980" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164034980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164034980";
};

# المستخدم 48: 0129368255
:do {
    /ip hotspot user add name="0129368255" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129368255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129368255";
};

# المستخدم 49: 0185057881
:do {
    /ip hotspot user add name="0185057881" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185057881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185057881";
};

# المستخدم 50: 0195829617
:do {
    /ip hotspot user add name="0195829617" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195829617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195829617";
};

# المستخدم 51: 0155676627
:do {
    /ip hotspot user add name="0155676627" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155676627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155676627";
};

# المستخدم 52: 0180844975
:do {
    /ip hotspot user add name="0180844975" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180844975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180844975";
};

# المستخدم 53: 0161523775
:do {
    /ip hotspot user add name="0161523775" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161523775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161523775";
};

# المستخدم 54: 0143094147
:do {
    /ip hotspot user add name="0143094147" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143094147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143094147";
};

# المستخدم 55: 0121534555
:do {
    /ip hotspot user add name="0121534555" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121534555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121534555";
};

# المستخدم 56: 0169847629
:do {
    /ip hotspot user add name="0169847629" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169847629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169847629";
};

# المستخدم 57: 0188090778
:do {
    /ip hotspot user add name="0188090778" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188090778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188090778";
};

# المستخدم 58: 0109643797
:do {
    /ip hotspot user add name="0109643797" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109643797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109643797";
};

# المستخدم 59: 0152271663
:do {
    /ip hotspot user add name="0152271663" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152271663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152271663";
};

# المستخدم 60: 0131375895
:do {
    /ip hotspot user add name="0131375895" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131375895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131375895";
};

# المستخدم 61: 0143254802
:do {
    /ip hotspot user add name="0143254802" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143254802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143254802";
};

# المستخدم 62: 0194340114
:do {
    /ip hotspot user add name="0194340114" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194340114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194340114";
};

# المستخدم 63: 0178801956
:do {
    /ip hotspot user add name="0178801956" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178801956";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178801956";
};

# المستخدم 64: 0165821591
:do {
    /ip hotspot user add name="0165821591" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165821591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165821591";
};

# المستخدم 65: 0139002771
:do {
    /ip hotspot user add name="0139002771" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139002771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139002771";
};

# المستخدم 66: 0134319925
:do {
    /ip hotspot user add name="0134319925" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134319925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134319925";
};

# المستخدم 67: 0130827830
:do {
    /ip hotspot user add name="0130827830" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130827830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130827830";
};

# المستخدم 68: 0163444941
:do {
    /ip hotspot user add name="0163444941" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163444941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163444941";
};

# المستخدم 69: 0110757952
:do {
    /ip hotspot user add name="0110757952" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110757952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110757952";
};

# المستخدم 70: 0107511054
:do {
    /ip hotspot user add name="0107511054" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107511054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107511054";
};

# المستخدم 71: 0148156944
:do {
    /ip hotspot user add name="0148156944" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148156944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148156944";
};

# المستخدم 72: 0164499899
:do {
    /ip hotspot user add name="0164499899" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164499899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164499899";
};

# المستخدم 73: 0132304069
:do {
    /ip hotspot user add name="0132304069" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132304069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132304069";
};

# المستخدم 74: 0128621052
:do {
    /ip hotspot user add name="0128621052" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128621052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128621052";
};

# المستخدم 75: 0135599526
:do {
    /ip hotspot user add name="0135599526" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135599526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135599526";
};

# المستخدم 76: 0104127844
:do {
    /ip hotspot user add name="0104127844" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104127844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104127844";
};

# المستخدم 77: 0196266188
:do {
    /ip hotspot user add name="0196266188" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196266188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196266188";
};

# المستخدم 78: 0161332811
:do {
    /ip hotspot user add name="0161332811" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161332811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161332811";
};

# المستخدم 79: 0187405182
:do {
    /ip hotspot user add name="0187405182" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187405182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187405182";
};

# المستخدم 80: 0181122961
:do {
    /ip hotspot user add name="0181122961" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181122961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181122961";
};

# المستخدم 81: 0119130405
:do {
    /ip hotspot user add name="0119130405" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119130405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119130405";
};

# المستخدم 82: 0152021333
:do {
    /ip hotspot user add name="0152021333" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152021333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152021333";
};

# المستخدم 83: 0139122089
:do {
    /ip hotspot user add name="0139122089" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139122089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139122089";
};

# المستخدم 84: 0144504958
:do {
    /ip hotspot user add name="0144504958" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144504958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144504958";
};

# المستخدم 85: 0185613380
:do {
    /ip hotspot user add name="0185613380" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185613380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185613380";
};

# المستخدم 86: 0107326958
:do {
    /ip hotspot user add name="0107326958" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107326958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107326958";
};

# المستخدم 87: 0169596357
:do {
    /ip hotspot user add name="0169596357" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169596357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169596357";
};

# المستخدم 88: 0110088212
:do {
    /ip hotspot user add name="0110088212" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110088212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110088212";
};

# المستخدم 89: 0105371494
:do {
    /ip hotspot user add name="0105371494" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105371494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105371494";
};

# المستخدم 90: 0126126697
:do {
    /ip hotspot user add name="0126126697" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126126697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126126697";
};

# المستخدم 91: 0122901739
:do {
    /ip hotspot user add name="0122901739" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122901739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122901739";
};

# المستخدم 92: 0156449949
:do {
    /ip hotspot user add name="0156449949" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156449949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156449949";
};

# المستخدم 93: 0169313457
:do {
    /ip hotspot user add name="0169313457" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169313457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169313457";
};

# المستخدم 94: 0110160977
:do {
    /ip hotspot user add name="0110160977" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110160977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110160977";
};

# المستخدم 95: 0156939504
:do {
    /ip hotspot user add name="0156939504" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156939504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156939504";
};

# المستخدم 96: 0165517337
:do {
    /ip hotspot user add name="0165517337" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165517337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165517337";
};

# المستخدم 97: 0129143072
:do {
    /ip hotspot user add name="0129143072" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129143072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129143072";
};

# المستخدم 98: 0109526325
:do {
    /ip hotspot user add name="0109526325" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109526325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109526325";
};

# المستخدم 99: 0157224299
:do {
    /ip hotspot user add name="0157224299" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157224299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157224299";
};

# المستخدم 100: 0181283274
:do {
    /ip hotspot user add name="0181283274" password="" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181283274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181283274";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
