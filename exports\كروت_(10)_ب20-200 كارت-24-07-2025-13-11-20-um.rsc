# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 13:11:20
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0130782760
:do {
    /tool user-manager user add customer="adm8n" username="0130782760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130782760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130782760";
};

# المستخدم 2: 0120027478
:do {
    /tool user-manager user add customer="adm8n" username="0120027478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120027478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120027478";
};

# المستخدم 3: 0118318061
:do {
    /tool user-manager user add customer="adm8n" username="0118318061" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118318061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118318061";
};

# المستخدم 4: 0181711373
:do {
    /tool user-manager user add customer="adm8n" username="0181711373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181711373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181711373";
};

# المستخدم 5: 0162517599
:do {
    /tool user-manager user add customer="adm8n" username="0162517599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162517599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162517599";
};

# المستخدم 6: 0108141263
:do {
    /tool user-manager user add customer="adm8n" username="0108141263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108141263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108141263";
};

# المستخدم 7: 0152446485
:do {
    /tool user-manager user add customer="adm8n" username="0152446485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152446485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152446485";
};

# المستخدم 8: 0106469283
:do {
    /tool user-manager user add customer="adm8n" username="0106469283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106469283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106469283";
};

# المستخدم 9: 0126438977
:do {
    /tool user-manager user add customer="adm8n" username="0126438977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126438977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126438977";
};

# المستخدم 10: 0198040499
:do {
    /tool user-manager user add customer="adm8n" username="0198040499" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198040499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198040499";
};

# المستخدم 11: 0192657850
:do {
    /tool user-manager user add customer="adm8n" username="0192657850" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192657850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192657850";
};

# المستخدم 12: 0194285295
:do {
    /tool user-manager user add customer="adm8n" username="0194285295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194285295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194285295";
};

# المستخدم 13: 0119792530
:do {
    /tool user-manager user add customer="adm8n" username="0119792530" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119792530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119792530";
};

# المستخدم 14: 0145925286
:do {
    /tool user-manager user add customer="adm8n" username="0145925286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145925286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145925286";
};

# المستخدم 15: 0180801087
:do {
    /tool user-manager user add customer="adm8n" username="0180801087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180801087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180801087";
};

# المستخدم 16: 0120730236
:do {
    /tool user-manager user add customer="adm8n" username="0120730236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120730236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120730236";
};

# المستخدم 17: 0125360693
:do {
    /tool user-manager user add customer="adm8n" username="0125360693" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125360693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125360693";
};

# المستخدم 18: 0172253196
:do {
    /tool user-manager user add customer="adm8n" username="0172253196" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172253196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172253196";
};

# المستخدم 19: 0134486967
:do {
    /tool user-manager user add customer="adm8n" username="0134486967" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134486967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134486967";
};

# المستخدم 20: 0188941966
:do {
    /tool user-manager user add customer="adm8n" username="0188941966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188941966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188941966";
};

# المستخدم 21: 0133268823
:do {
    /tool user-manager user add customer="adm8n" username="0133268823" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133268823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133268823";
};

# المستخدم 22: 0138414428
:do {
    /tool user-manager user add customer="adm8n" username="0138414428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138414428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138414428";
};

# المستخدم 23: 0196261824
:do {
    /tool user-manager user add customer="adm8n" username="0196261824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196261824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196261824";
};

# المستخدم 24: 0166965037
:do {
    /tool user-manager user add customer="adm8n" username="0166965037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166965037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166965037";
};

# المستخدم 25: 0180627113
:do {
    /tool user-manager user add customer="adm8n" username="0180627113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180627113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180627113";
};

# المستخدم 26: 0145139212
:do {
    /tool user-manager user add customer="adm8n" username="0145139212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145139212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145139212";
};

# المستخدم 27: 0107206835
:do {
    /tool user-manager user add customer="adm8n" username="0107206835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107206835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107206835";
};

# المستخدم 28: 0129944608
:do {
    /tool user-manager user add customer="adm8n" username="0129944608" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129944608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129944608";
};

# المستخدم 29: 0124052919
:do {
    /tool user-manager user add customer="adm8n" username="0124052919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124052919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124052919";
};

# المستخدم 30: 0142329373
:do {
    /tool user-manager user add customer="adm8n" username="0142329373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142329373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142329373";
};

# المستخدم 31: 0186548264
:do {
    /tool user-manager user add customer="adm8n" username="0186548264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186548264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186548264";
};

# المستخدم 32: 0147750835
:do {
    /tool user-manager user add customer="adm8n" username="0147750835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147750835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147750835";
};

# المستخدم 33: 0117504387
:do {
    /tool user-manager user add customer="adm8n" username="0117504387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117504387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117504387";
};

# المستخدم 34: 0181247908
:do {
    /tool user-manager user add customer="adm8n" username="0181247908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181247908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181247908";
};

# المستخدم 35: 0137641549
:do {
    /tool user-manager user add customer="adm8n" username="0137641549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137641549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137641549";
};

# المستخدم 36: 0186575706
:do {
    /tool user-manager user add customer="adm8n" username="0186575706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186575706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186575706";
};

# المستخدم 37: 0107337907
:do {
    /tool user-manager user add customer="adm8n" username="0107337907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107337907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107337907";
};

# المستخدم 38: 0109372794
:do {
    /tool user-manager user add customer="adm8n" username="0109372794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109372794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109372794";
};

# المستخدم 39: 0135586505
:do {
    /tool user-manager user add customer="adm8n" username="0135586505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135586505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135586505";
};

# المستخدم 40: 0125575216
:do {
    /tool user-manager user add customer="adm8n" username="0125575216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125575216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125575216";
};

# المستخدم 41: 0112769920
:do {
    /tool user-manager user add customer="adm8n" username="0112769920" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112769920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112769920";
};

# المستخدم 42: 0133892281
:do {
    /tool user-manager user add customer="adm8n" username="0133892281" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133892281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133892281";
};

# المستخدم 43: 0125518257
:do {
    /tool user-manager user add customer="adm8n" username="0125518257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125518257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125518257";
};

# المستخدم 44: 0137188176
:do {
    /tool user-manager user add customer="adm8n" username="0137188176" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137188176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137188176";
};

# المستخدم 45: 0174467417
:do {
    /tool user-manager user add customer="adm8n" username="0174467417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174467417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174467417";
};

# المستخدم 46: 0127782962
:do {
    /tool user-manager user add customer="adm8n" username="0127782962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127782962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127782962";
};

# المستخدم 47: 0164111503
:do {
    /tool user-manager user add customer="adm8n" username="0164111503" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164111503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164111503";
};

# المستخدم 48: 0104009325
:do {
    /tool user-manager user add customer="adm8n" username="0104009325" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104009325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104009325";
};

# المستخدم 49: 0176126208
:do {
    /tool user-manager user add customer="adm8n" username="0176126208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176126208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176126208";
};

# المستخدم 50: 0152540179
:do {
    /tool user-manager user add customer="adm8n" username="0152540179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152540179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152540179";
};

# المستخدم 51: 0176831094
:do {
    /tool user-manager user add customer="adm8n" username="0176831094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176831094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176831094";
};

# المستخدم 52: 0132630664
:do {
    /tool user-manager user add customer="adm8n" username="0132630664" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132630664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132630664";
};

# المستخدم 53: 0159437682
:do {
    /tool user-manager user add customer="adm8n" username="0159437682" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159437682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159437682";
};

# المستخدم 54: 0136147898
:do {
    /tool user-manager user add customer="adm8n" username="0136147898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136147898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136147898";
};

# المستخدم 55: 0125554171
:do {
    /tool user-manager user add customer="adm8n" username="0125554171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125554171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125554171";
};

# المستخدم 56: 0138665850
:do {
    /tool user-manager user add customer="adm8n" username="0138665850" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138665850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138665850";
};

# المستخدم 57: 0162200627
:do {
    /tool user-manager user add customer="adm8n" username="0162200627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162200627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162200627";
};

# المستخدم 58: 0130785094
:do {
    /tool user-manager user add customer="adm8n" username="0130785094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130785094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130785094";
};

# المستخدم 59: 0195878971
:do {
    /tool user-manager user add customer="adm8n" username="0195878971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195878971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195878971";
};

# المستخدم 60: 0199159010
:do {
    /tool user-manager user add customer="adm8n" username="0199159010" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199159010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199159010";
};

# المستخدم 61: 0194175224
:do {
    /tool user-manager user add customer="adm8n" username="0194175224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194175224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194175224";
};

# المستخدم 62: 0190866561
:do {
    /tool user-manager user add customer="adm8n" username="0190866561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190866561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190866561";
};

# المستخدم 63: 0161033843
:do {
    /tool user-manager user add customer="adm8n" username="0161033843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161033843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161033843";
};

# المستخدم 64: 0110526444
:do {
    /tool user-manager user add customer="adm8n" username="0110526444" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110526444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110526444";
};

# المستخدم 65: 0126245475
:do {
    /tool user-manager user add customer="adm8n" username="0126245475" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126245475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126245475";
};

# المستخدم 66: 0140227440
:do {
    /tool user-manager user add customer="adm8n" username="0140227440" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140227440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140227440";
};

# المستخدم 67: 0199682512
:do {
    /tool user-manager user add customer="adm8n" username="0199682512" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199682512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199682512";
};

# المستخدم 68: 0167179291
:do {
    /tool user-manager user add customer="adm8n" username="0167179291" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167179291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167179291";
};

# المستخدم 69: 0135484073
:do {
    /tool user-manager user add customer="adm8n" username="0135484073" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135484073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135484073";
};

# المستخدم 70: 0110632946
:do {
    /tool user-manager user add customer="adm8n" username="0110632946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110632946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110632946";
};

# المستخدم 71: 0150206301
:do {
    /tool user-manager user add customer="adm8n" username="0150206301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150206301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150206301";
};

# المستخدم 72: 0187926969
:do {
    /tool user-manager user add customer="adm8n" username="0187926969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187926969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187926969";
};

# المستخدم 73: 0146362563
:do {
    /tool user-manager user add customer="adm8n" username="0146362563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146362563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146362563";
};

# المستخدم 74: 0167484841
:do {
    /tool user-manager user add customer="adm8n" username="0167484841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167484841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167484841";
};

# المستخدم 75: 0171849052
:do {
    /tool user-manager user add customer="adm8n" username="0171849052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171849052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171849052";
};

# المستخدم 76: 0153600938
:do {
    /tool user-manager user add customer="adm8n" username="0153600938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153600938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153600938";
};

# المستخدم 77: 0195172164
:do {
    /tool user-manager user add customer="adm8n" username="0195172164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195172164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195172164";
};

# المستخدم 78: 0128924741
:do {
    /tool user-manager user add customer="adm8n" username="0128924741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128924741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128924741";
};

# المستخدم 79: 0171047062
:do {
    /tool user-manager user add customer="adm8n" username="0171047062" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171047062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171047062";
};

# المستخدم 80: 0195738072
:do {
    /tool user-manager user add customer="adm8n" username="0195738072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195738072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195738072";
};

# المستخدم 81: 0132872949
:do {
    /tool user-manager user add customer="adm8n" username="0132872949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132872949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132872949";
};

# المستخدم 82: 0196288182
:do {
    /tool user-manager user add customer="adm8n" username="0196288182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196288182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196288182";
};

# المستخدم 83: 0160409201
:do {
    /tool user-manager user add customer="adm8n" username="0160409201" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160409201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160409201";
};

# المستخدم 84: 0135229360
:do {
    /tool user-manager user add customer="adm8n" username="0135229360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135229360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135229360";
};

# المستخدم 85: 0137338749
:do {
    /tool user-manager user add customer="adm8n" username="0137338749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137338749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137338749";
};

# المستخدم 86: 0167414804
:do {
    /tool user-manager user add customer="adm8n" username="0167414804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167414804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167414804";
};

# المستخدم 87: 0140120245
:do {
    /tool user-manager user add customer="adm8n" username="0140120245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140120245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140120245";
};

# المستخدم 88: 0151494921
:do {
    /tool user-manager user add customer="adm8n" username="0151494921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151494921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151494921";
};

# المستخدم 89: 0100520375
:do {
    /tool user-manager user add customer="adm8n" username="0100520375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100520375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100520375";
};

# المستخدم 90: 0117846622
:do {
    /tool user-manager user add customer="adm8n" username="0117846622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117846622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117846622";
};

# المستخدم 91: 0160487654
:do {
    /tool user-manager user add customer="adm8n" username="0160487654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160487654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160487654";
};

# المستخدم 92: 0103536478
:do {
    /tool user-manager user add customer="adm8n" username="0103536478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103536478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103536478";
};

# المستخدم 93: 0125015481
:do {
    /tool user-manager user add customer="adm8n" username="0125015481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125015481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125015481";
};

# المستخدم 94: 0117439846
:do {
    /tool user-manager user add customer="adm8n" username="0117439846" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117439846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117439846";
};

# المستخدم 95: 0159293454
:do {
    /tool user-manager user add customer="adm8n" username="0159293454" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159293454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159293454";
};

# المستخدم 96: 0155710199
:do {
    /tool user-manager user add customer="adm8n" username="0155710199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155710199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155710199";
};

# المستخدم 97: 0103295835
:do {
    /tool user-manager user add customer="adm8n" username="0103295835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103295835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103295835";
};

# المستخدم 98: 0188975045
:do {
    /tool user-manager user add customer="adm8n" username="0188975045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188975045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188975045";
};

# المستخدم 99: 0182199738
:do {
    /tool user-manager user add customer="adm8n" username="0182199738" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182199738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182199738";
};

# المستخدم 100: 0139106703
:do {
    /tool user-manager user add customer="adm8n" username="0139106703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139106703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139106703";
};

# المستخدم 101: 0188168324
:do {
    /tool user-manager user add customer="adm8n" username="0188168324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188168324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188168324";
};

# المستخدم 102: 0150242546
:do {
    /tool user-manager user add customer="adm8n" username="0150242546" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150242546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150242546";
};

# المستخدم 103: 0167235369
:do {
    /tool user-manager user add customer="adm8n" username="0167235369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167235369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167235369";
};

# المستخدم 104: 0109576861
:do {
    /tool user-manager user add customer="adm8n" username="0109576861" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109576861";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109576861";
};

# المستخدم 105: 0112862745
:do {
    /tool user-manager user add customer="adm8n" username="0112862745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112862745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112862745";
};

# المستخدم 106: 0177295280
:do {
    /tool user-manager user add customer="adm8n" username="0177295280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177295280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177295280";
};

# المستخدم 107: 0149037077
:do {
    /tool user-manager user add customer="adm8n" username="0149037077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149037077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149037077";
};

# المستخدم 108: 0142827561
:do {
    /tool user-manager user add customer="adm8n" username="0142827561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142827561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142827561";
};

# المستخدم 109: 0130142799
:do {
    /tool user-manager user add customer="adm8n" username="0130142799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130142799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130142799";
};

# المستخدم 110: 0101887794
:do {
    /tool user-manager user add customer="adm8n" username="0101887794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101887794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101887794";
};

# المستخدم 111: 0145280486
:do {
    /tool user-manager user add customer="adm8n" username="0145280486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145280486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145280486";
};

# المستخدم 112: 0165411540
:do {
    /tool user-manager user add customer="adm8n" username="0165411540" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165411540";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165411540";
};

# المستخدم 113: 0102839824
:do {
    /tool user-manager user add customer="adm8n" username="0102839824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102839824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102839824";
};

# المستخدم 114: 0133127689
:do {
    /tool user-manager user add customer="adm8n" username="0133127689" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133127689";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133127689";
};

# المستخدم 115: 0108724867
:do {
    /tool user-manager user add customer="adm8n" username="0108724867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108724867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108724867";
};

# المستخدم 116: 0138554496
:do {
    /tool user-manager user add customer="adm8n" username="0138554496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138554496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138554496";
};

# المستخدم 117: 0155862028
:do {
    /tool user-manager user add customer="adm8n" username="0155862028" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155862028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155862028";
};

# المستخدم 118: 0192858653
:do {
    /tool user-manager user add customer="adm8n" username="0192858653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192858653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192858653";
};

# المستخدم 119: 0152460800
:do {
    /tool user-manager user add customer="adm8n" username="0152460800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152460800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152460800";
};

# المستخدم 120: 0188665706
:do {
    /tool user-manager user add customer="adm8n" username="0188665706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188665706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188665706";
};

# المستخدم 121: 0159515983
:do {
    /tool user-manager user add customer="adm8n" username="0159515983" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159515983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159515983";
};

# المستخدم 122: 0126073629
:do {
    /tool user-manager user add customer="adm8n" username="0126073629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126073629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126073629";
};

# المستخدم 123: 0189657846
:do {
    /tool user-manager user add customer="adm8n" username="0189657846" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189657846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189657846";
};

# المستخدم 124: 0107909620
:do {
    /tool user-manager user add customer="adm8n" username="0107909620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107909620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107909620";
};

# المستخدم 125: 0153141219
:do {
    /tool user-manager user add customer="adm8n" username="0153141219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153141219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153141219";
};

# المستخدم 126: 0164407028
:do {
    /tool user-manager user add customer="adm8n" username="0164407028" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164407028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164407028";
};

# المستخدم 127: 0128888895
:do {
    /tool user-manager user add customer="adm8n" username="0128888895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128888895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128888895";
};

# المستخدم 128: 0184484842
:do {
    /tool user-manager user add customer="adm8n" username="0184484842" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184484842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184484842";
};

# المستخدم 129: 0191440361
:do {
    /tool user-manager user add customer="adm8n" username="0191440361" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191440361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191440361";
};

# المستخدم 130: 0101973949
:do {
    /tool user-manager user add customer="adm8n" username="0101973949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101973949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101973949";
};

# المستخدم 131: 0174384229
:do {
    /tool user-manager user add customer="adm8n" username="0174384229" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174384229";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174384229";
};

# المستخدم 132: 0157223832
:do {
    /tool user-manager user add customer="adm8n" username="0157223832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157223832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157223832";
};

# المستخدم 133: 0101269773
:do {
    /tool user-manager user add customer="adm8n" username="0101269773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101269773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101269773";
};

# المستخدم 134: 0142727601
:do {
    /tool user-manager user add customer="adm8n" username="0142727601" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142727601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142727601";
};

# المستخدم 135: 0105675282
:do {
    /tool user-manager user add customer="adm8n" username="0105675282" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105675282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105675282";
};

# المستخدم 136: 0174774770
:do {
    /tool user-manager user add customer="adm8n" username="0174774770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174774770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174774770";
};

# المستخدم 137: 0135302887
:do {
    /tool user-manager user add customer="adm8n" username="0135302887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135302887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135302887";
};

# المستخدم 138: 0146294040
:do {
    /tool user-manager user add customer="adm8n" username="0146294040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146294040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146294040";
};

# المستخدم 139: 0157237617
:do {
    /tool user-manager user add customer="adm8n" username="0157237617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157237617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157237617";
};

# المستخدم 140: 0163967356
:do {
    /tool user-manager user add customer="adm8n" username="0163967356" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163967356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163967356";
};

# المستخدم 141: 0157361569
:do {
    /tool user-manager user add customer="adm8n" username="0157361569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157361569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157361569";
};

# المستخدم 142: 0171392267
:do {
    /tool user-manager user add customer="adm8n" username="0171392267" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171392267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171392267";
};

# المستخدم 143: 0195755168
:do {
    /tool user-manager user add customer="adm8n" username="0195755168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195755168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195755168";
};

# المستخدم 144: 0159358760
:do {
    /tool user-manager user add customer="adm8n" username="0159358760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159358760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159358760";
};

# المستخدم 145: 0136806871
:do {
    /tool user-manager user add customer="adm8n" username="0136806871" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136806871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136806871";
};

# المستخدم 146: 0123162876
:do {
    /tool user-manager user add customer="adm8n" username="0123162876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123162876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123162876";
};

# المستخدم 147: 0138195081
:do {
    /tool user-manager user add customer="adm8n" username="0138195081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138195081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138195081";
};

# المستخدم 148: 0192853505
:do {
    /tool user-manager user add customer="adm8n" username="0192853505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192853505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192853505";
};

# المستخدم 149: 0119486000
:do {
    /tool user-manager user add customer="adm8n" username="0119486000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119486000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119486000";
};

# المستخدم 150: 0190183691
:do {
    /tool user-manager user add customer="adm8n" username="0190183691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190183691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190183691";
};

# المستخدم 151: 0131561912
:do {
    /tool user-manager user add customer="adm8n" username="0131561912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131561912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131561912";
};

# المستخدم 152: 0169377880
:do {
    /tool user-manager user add customer="adm8n" username="0169377880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169377880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169377880";
};

# المستخدم 153: 0120673837
:do {
    /tool user-manager user add customer="adm8n" username="0120673837" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120673837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120673837";
};

# المستخدم 154: 0148929264
:do {
    /tool user-manager user add customer="adm8n" username="0148929264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148929264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148929264";
};

# المستخدم 155: 0126707813
:do {
    /tool user-manager user add customer="adm8n" username="0126707813" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126707813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126707813";
};

# المستخدم 156: 0145425393
:do {
    /tool user-manager user add customer="adm8n" username="0145425393" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145425393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145425393";
};

# المستخدم 157: 0123319855
:do {
    /tool user-manager user add customer="adm8n" username="0123319855" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123319855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123319855";
};

# المستخدم 158: 0182069005
:do {
    /tool user-manager user add customer="adm8n" username="0182069005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182069005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182069005";
};

# المستخدم 159: 0168755509
:do {
    /tool user-manager user add customer="adm8n" username="0168755509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168755509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168755509";
};

# المستخدم 160: 0158140364
:do {
    /tool user-manager user add customer="adm8n" username="0158140364" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158140364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158140364";
};

# المستخدم 161: 0147900390
:do {
    /tool user-manager user add customer="adm8n" username="0147900390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147900390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147900390";
};

# المستخدم 162: 0125769579
:do {
    /tool user-manager user add customer="adm8n" username="0125769579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125769579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125769579";
};

# المستخدم 163: 0129828008
:do {
    /tool user-manager user add customer="adm8n" username="0129828008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129828008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129828008";
};

# المستخدم 164: 0197780411
:do {
    /tool user-manager user add customer="adm8n" username="0197780411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197780411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197780411";
};

# المستخدم 165: 0141837849
:do {
    /tool user-manager user add customer="adm8n" username="0141837849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141837849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141837849";
};

# المستخدم 166: 0144952184
:do {
    /tool user-manager user add customer="adm8n" username="0144952184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144952184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144952184";
};

# المستخدم 167: 0127148024
:do {
    /tool user-manager user add customer="adm8n" username="0127148024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127148024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127148024";
};

# المستخدم 168: 0175411920
:do {
    /tool user-manager user add customer="adm8n" username="0175411920" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175411920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175411920";
};

# المستخدم 169: 0189959040
:do {
    /tool user-manager user add customer="adm8n" username="0189959040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189959040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189959040";
};

# المستخدم 170: 0172778235
:do {
    /tool user-manager user add customer="adm8n" username="0172778235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172778235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172778235";
};

# المستخدم 171: 0141964542
:do {
    /tool user-manager user add customer="adm8n" username="0141964542" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141964542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141964542";
};

# المستخدم 172: 0144751043
:do {
    /tool user-manager user add customer="adm8n" username="0144751043" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144751043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144751043";
};

# المستخدم 173: 0183632665
:do {
    /tool user-manager user add customer="adm8n" username="0183632665" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183632665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183632665";
};

# المستخدم 174: 0143234954
:do {
    /tool user-manager user add customer="adm8n" username="0143234954" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143234954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143234954";
};

# المستخدم 175: 0127983105
:do {
    /tool user-manager user add customer="adm8n" username="0127983105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127983105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127983105";
};

# المستخدم 176: 0180502177
:do {
    /tool user-manager user add customer="adm8n" username="0180502177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180502177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180502177";
};

# المستخدم 177: 0159847573
:do {
    /tool user-manager user add customer="adm8n" username="0159847573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159847573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159847573";
};

# المستخدم 178: 0135636412
:do {
    /tool user-manager user add customer="adm8n" username="0135636412" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135636412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135636412";
};

# المستخدم 179: 0159380687
:do {
    /tool user-manager user add customer="adm8n" username="0159380687" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159380687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159380687";
};

# المستخدم 180: 0117949068
:do {
    /tool user-manager user add customer="adm8n" username="0117949068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117949068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117949068";
};

# المستخدم 181: 0107292496
:do {
    /tool user-manager user add customer="adm8n" username="0107292496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107292496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107292496";
};

# المستخدم 182: 0124521595
:do {
    /tool user-manager user add customer="adm8n" username="0124521595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124521595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124521595";
};

# المستخدم 183: 0124887276
:do {
    /tool user-manager user add customer="adm8n" username="0124887276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124887276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124887276";
};

# المستخدم 184: 0114580153
:do {
    /tool user-manager user add customer="adm8n" username="0114580153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114580153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114580153";
};

# المستخدم 185: 0144385305
:do {
    /tool user-manager user add customer="adm8n" username="0144385305" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144385305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144385305";
};

# المستخدم 186: 0119494195
:do {
    /tool user-manager user add customer="adm8n" username="0119494195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119494195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119494195";
};

# المستخدم 187: 0143480338
:do {
    /tool user-manager user add customer="adm8n" username="0143480338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143480338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143480338";
};

# المستخدم 188: 0171062894
:do {
    /tool user-manager user add customer="adm8n" username="0171062894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171062894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171062894";
};

# المستخدم 189: 0141396980
:do {
    /tool user-manager user add customer="adm8n" username="0141396980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141396980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141396980";
};

# المستخدم 190: 0197940069
:do {
    /tool user-manager user add customer="adm8n" username="0197940069" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197940069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197940069";
};

# المستخدم 191: 0195340441
:do {
    /tool user-manager user add customer="adm8n" username="0195340441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195340441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195340441";
};

# المستخدم 192: 0129518401
:do {
    /tool user-manager user add customer="adm8n" username="0129518401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129518401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129518401";
};

# المستخدم 193: 0119332072
:do {
    /tool user-manager user add customer="adm8n" username="0119332072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119332072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119332072";
};

# المستخدم 194: 0154452780
:do {
    /tool user-manager user add customer="adm8n" username="0154452780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154452780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154452780";
};

# المستخدم 195: 0152911839
:do {
    /tool user-manager user add customer="adm8n" username="0152911839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152911839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152911839";
};

# المستخدم 196: 0159914794
:do {
    /tool user-manager user add customer="adm8n" username="0159914794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159914794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159914794";
};

# المستخدم 197: 0186828949
:do {
    /tool user-manager user add customer="adm8n" username="0186828949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186828949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186828949";
};

# المستخدم 198: 0112205382
:do {
    /tool user-manager user add customer="adm8n" username="0112205382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112205382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112205382";
};

# المستخدم 199: 0111039560
:do {
    /tool user-manager user add customer="adm8n" username="0111039560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111039560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111039560";
};

# المستخدم 200: 0153623690
:do {
    /tool user-manager user add customer="adm8n" username="0153623690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153623690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153623690";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
