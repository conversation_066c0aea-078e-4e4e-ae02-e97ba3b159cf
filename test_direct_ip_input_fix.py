#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح وظيفة الكتابة المباشرة للـ IP
Test Direct IP Input Fix
"""

import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

class TestDirectIPInputFix(unittest.TestCase):
    """اختبارات إصلاح وظيفة الكتابة المباشرة للـ IP"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء mock للبرنامج الرئيسي
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.api_ip_entry = Mock()
        self.mock_app.api_username_entry = Mock()
        self.mock_app.api_password_entry = Mock()
        self.mock_app.api_port_entry = Mock()
        self.mock_app.use_ssl_var = Mock()
        self.mock_app.send_telegram_message_direct = Mock()
        self.mock_app.send_telegram_message_with_keyboard = Mock()
        
        # محاكاة القيم الحالية
        self.mock_app.api_ip_entry.get.return_value = "***********"
        self.mock_app.api_username_entry.get.return_value = "admin"
        self.mock_app.api_password_entry.get.return_value = "password"
        self.mock_app.api_port_entry.get.return_value = "8728"
        self.mock_app.use_ssl_var.get.return_value = False
        
    def test_direct_ip_detection_function(self):
        """اختبار دالة اكتشاف IP مباشر"""
        print("🧪 اختبار دالة اكتشاف IP مباشر...")
        
        # محاكاة دالة اكتشاف IP مباشر
        def mock_is_direct_ip_input(text):
            text = text.strip()
            
            # تجاهل النصوص القصيرة أو الطويلة
            if len(text) < 7 or len(text) > 50:
                return False
            
            # تجاهل النصوص مع مسافات
            if ' ' in text:
                return False
            
            # تجاهل الأوامر
            if text.startswith('/'):
                return False
            
            # تجاهل النصوص العربية
            arabic_chars = 'ابتثجحخدذرزسشصضطظعغفقكلمنهوي'
            if any(char in arabic_chars for char in text):
                return False
            
            # فحص IP address
            if text.count('.') == 3:
                parts = text.split('.')
                try:
                    for part in parts:
                        num = int(part)
                        if num < 0 or num > 255:
                            return False
                    return True
                except ValueError:
                    return False
            
            # فحص domain/hostname
            if '.' in text and len(text) > 3:
                allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-')
                if all(c in allowed_chars for c in text):
                    if not text.startswith('.') and not text.endswith('.') and '..' not in text:
                        return True
            
            return False
        
        # اختبار IP addresses صحيحة
        valid_ips = [
            "***********00", "********", "**********", 
            "*******", "*******", "************"
        ]
        
        for ip in valid_ips:
            result = mock_is_direct_ip_input(ip)
            self.assertTrue(result, f"IP صحيح يجب أن يُكتشف: {ip}")
        
        # اختبار domains صحيحة
        valid_domains = [
            "router.local", "mikrotik.company.com", 
            "server.example.org", "host.domain.net"
        ]
        
        for domain in valid_domains:
            result = mock_is_direct_ip_input(domain)
            self.assertTrue(result, f"Domain صحيح يجب أن يُكتشف: {domain}")
        
        # اختبار نصوص يجب تجاهلها
        invalid_inputs = [
            "/start", "/newip", "hello world", "مرحبا", 
            "abc", "999.999.999.999", "192.168.1", 
            "invalid..host", "", "   ", "192.168.1.256",
            "test message", "كيف الحال"
        ]
        
        for invalid in invalid_inputs:
            result = mock_is_direct_ip_input(invalid)
            self.assertFalse(result, f"نص غير صحيح يجب تجاهله: {invalid}")
        
        print("✅ نجح اختبار دالة اكتشاف IP مباشر")
    
    def test_direct_ip_message_processing(self):
        """اختبار معالجة رسائل IP المباشر"""
        print("🧪 اختبار معالجة رسائل IP المباشر...")
        
        # محاكاة معالجة رسائل IP المباشر
        def mock_process_direct_ip_message(text, current_host):
            # فحص إذا كان IP مباشر
            if not mock_is_direct_ip_input(text):
                return {"action": "send_main_menu", "reason": "not_direct_ip"}
            
            # التحقق من وجود تغيير
            if current_host == text:
                return {
                    "action": "send_no_change_message",
                    "ip": text,
                    "message": f"Host الحالي هو بالفعل: {text}"
                }
            
            # إنشاء رسالة تأكيد
            return {
                "action": "send_confirmation",
                "ip": text,
                "current_host": current_host,
                "keyboard": {
                    "inline_keyboard": [
                        [
                            {"text": "✅ تطبيق فوراً", "callback_data": f"quick_apply_{text}"},
                            {"text": "❌ إلغاء", "callback_data": f"cancel_direct_{text}"}
                        ],
                        [{"text": "🔍 عرض التفاصيل", "callback_data": f"show_details_{text}"}]
                    ]
                }
            }
        
        def mock_is_direct_ip_input(text):
            # نسخة مبسطة للاختبار
            return len(text) >= 7 and '.' in text and not text.startswith('/') and ' ' not in text
        
        # اختبار IP صحيح مع تغيير
        result_valid = mock_process_direct_ip_message("***********00", "***********")
        self.assertEqual(result_valid["action"], "send_confirmation")
        self.assertEqual(result_valid["ip"], "***********00")
        self.assertIn("inline_keyboard", result_valid["keyboard"])
        
        # اختبار IP بدون تغيير
        result_no_change = mock_process_direct_ip_message("***********", "***********")
        self.assertEqual(result_no_change["action"], "send_no_change_message")
        self.assertEqual(result_no_change["ip"], "***********")
        
        # اختبار نص ليس IP
        result_not_ip = mock_process_direct_ip_message("hello world", "***********")
        self.assertEqual(result_not_ip["action"], "send_main_menu")
        self.assertEqual(result_not_ip["reason"], "not_direct_ip")
        
        print("✅ نجح اختبار معالجة رسائل IP المباشر")
    
    def test_direct_ip_callback_handling(self):
        """اختبار معالجة callback queries للـ IP المباشر"""
        print("🧪 اختبار معالجة callback queries للـ IP المباشر...")
        
        # محاكاة معالجة callback queries للـ IP المباشر
        def mock_handle_direct_ip_callbacks(callback_data):
            if callback_data.startswith("quick_apply_"):
                ip = callback_data.replace("quick_apply_", "")
                return {
                    "action": "execute_host_change_with_restart",
                    "ip": ip,
                    "message": "تطبيق فوري مع إعادة تشغيل"
                }
            
            elif callback_data.startswith("cancel_direct_"):
                ip = callback_data.replace("cancel_direct_", "")
                return {
                    "action": "send_cancel_message",
                    "ip": ip,
                    "message": "تم إلغاء تغيير Host"
                }
            
            elif callback_data.startswith("show_details_"):
                ip = callback_data.replace("show_details_", "")
                return {
                    "action": "handle_quick_host_confirmation",
                    "ip": ip,
                    "message": "عرض تفاصيل التأكيد"
                }
            
            elif callback_data.startswith("confirm_host_"):
                ip = callback_data.replace("confirm_host_", "")
                return {
                    "action": "execute_host_change_with_restart",
                    "ip": ip,
                    "message": "تأكيد تغيير Host"
                }
            
            elif callback_data.startswith("cancel_host_"):
                return {
                    "action": "send_cancel_message",
                    "message": "إلغاء تغيير Host"
                }
            
            return {"action": "unknown"}
        
        # اختبار callback data للتطبيق السريع
        quick_apply_callbacks = [
            ("quick_apply_***********00", "***********00"),
            ("quick_apply_********", "********"),
            ("quick_apply_router.local", "router.local")
        ]
        
        for callback, expected_ip in quick_apply_callbacks:
            result = mock_handle_direct_ip_callbacks(callback)
            self.assertEqual(result["action"], "execute_host_change_with_restart")
            self.assertEqual(result["ip"], expected_ip)
        
        # اختبار callback data للإلغاء
        cancel_callbacks = [
            ("cancel_direct_***********00", "***********00"),
            ("cancel_direct_mikrotik.local", "mikrotik.local")
        ]
        
        for callback, expected_ip in cancel_callbacks:
            result = mock_handle_direct_ip_callbacks(callback)
            self.assertEqual(result["action"], "send_cancel_message")
            self.assertEqual(result["ip"], expected_ip)
        
        # اختبار callback data لعرض التفاصيل
        details_callbacks = [
            ("show_details_**********", "**********"),
            ("show_details_server.company.com", "server.company.com")
        ]
        
        for callback, expected_ip in details_callbacks:
            result = mock_handle_direct_ip_callbacks(callback)
            self.assertEqual(result["action"], "handle_quick_host_confirmation")
            self.assertEqual(result["ip"], expected_ip)
        
        # اختبار callback data للتأكيد والإلغاء
        confirm_cancel_callbacks = [
            ("confirm_host_***********", "execute_host_change_with_restart", "***********"),
            ("cancel_host_***********", "send_cancel_message", None)
        ]
        
        for callback, expected_action, expected_ip in confirm_cancel_callbacks:
            result = mock_handle_direct_ip_callbacks(callback)
            self.assertEqual(result["action"], expected_action)
            if expected_ip:
                self.assertEqual(result["ip"], expected_ip)
        
        print("✅ نجح اختبار معالجة callback queries للـ IP المباشر")
    
    def test_network_type_detection_for_direct_ip(self):
        """اختبار اكتشاف نوع الشبكة للـ IP المباشر"""
        print("🧪 اختبار اكتشاف نوع الشبكة للـ IP المباشر...")
        
        # محاكاة دالة اكتشاف نوع الشبكة
        def mock_detect_network_type_for_direct_ip(ip_text):
            if ip_text.count('.') == 3:
                try:
                    first_octet = int(ip_text.split('.')[0])
                    if first_octet == 192:
                        return "شبكة محلية (192.x.x.x)"
                    elif first_octet == 10:
                        return "شبكة خاصة (10.x.x.x)"
                    elif first_octet == 172:
                        return "شبكة مؤسسية (172.x.x.x)"
                    elif first_octet == 127:
                        return "شبكة محلية (localhost)"
                    else:
                        return "شبكة عامة"
                except:
                    return "نوع غير محدد"
            else:
                return "DDNS أو Hostname"
        
        # اختبار أنواع الشبكات المختلفة
        network_tests = [
            ("***********00", "شبكة محلية (192.x.x.x)"),
            ("************", "شبكة محلية (192.x.x.x)"),
            ("********", "شبكة خاصة (10.x.x.x)"),
            ("***********", "شبكة خاصة (10.x.x.x)"),
            ("**********", "شبكة مؤسسية (172.x.x.x)"),
            ("************", "شبكة مؤسسية (172.x.x.x)"),
            ("127.0.0.1", "شبكة محلية (localhost)"),
            ("*******", "شبكة عامة"),
            ("*******", "شبكة عامة"),
            ("router.local", "DDNS أو Hostname"),
            ("mikrotik.company.com", "DDNS أو Hostname")
        ]
        
        for ip, expected_type in network_tests:
            result = mock_detect_network_type_for_direct_ip(ip)
            self.assertEqual(result, expected_type, f"نوع شبكة خاطئ لـ {ip}")
        
        print("✅ نجح اختبار اكتشاف نوع الشبكة للـ IP المباشر")
    
    def test_complete_direct_ip_workflow(self):
        """اختبار سير العمل الكامل للـ IP المباشر"""
        print("🧪 اختبار سير العمل الكامل للـ IP المباشر...")
        
        # محاكاة سير العمل الكامل
        def mock_complete_direct_ip_workflow(user_input, current_host):
            workflow_steps = []
            
            try:
                # 1. فحص إذا كان IP مباشر
                workflow_steps.append("check_direct_ip")
                
                # محاكاة فحص IP مباشر
                if len(user_input) < 7 or ' ' in user_input or user_input.startswith('/'):
                    return {
                        "success": False,
                        "reason": "not_direct_ip",
                        "steps": workflow_steps,
                        "action": "send_main_menu"
                    }
                
                workflow_steps.append("direct_ip_detected")
                
                # 2. التحقق من وجود تغيير
                workflow_steps.append("check_change")
                if current_host == user_input:
                    return {
                        "success": False,
                        "reason": "no_change",
                        "steps": workflow_steps,
                        "action": "send_no_change_message"
                    }
                
                workflow_steps.append("change_detected")
                
                # 3. إنشاء رسالة تأكيد
                workflow_steps.append("create_confirmation")
                
                # 4. انتظار اختيار المستخدم
                workflow_steps.append("wait_user_choice")
                
                # 5. تطبيق التغيير (محاكاة اختيار "تطبيق فوراً")
                workflow_steps.append("apply_change")
                
                # 6. حفظ الإعدادات
                workflow_steps.append("save_settings")
                
                # 7. إعادة التشغيل
                workflow_steps.append("restart_program")
                
                return {
                    "success": True,
                    "final_host": user_input,
                    "steps": workflow_steps,
                    "action": "execute_host_change_with_restart",
                    "message": "تم تغيير Host بنجاح"
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "steps": workflow_steps
                }
        
        # اختبار سير عمل ناجح
        result_success = mock_complete_direct_ip_workflow("***********00", "***********")
        self.assertTrue(result_success["success"], "يجب أن ينجح سير العمل")
        self.assertEqual(result_success["final_host"], "***********00")
        self.assertEqual(result_success["action"], "execute_host_change_with_restart")
        
        expected_steps = [
            "check_direct_ip", "direct_ip_detected", "check_change",
            "change_detected", "create_confirmation", "wait_user_choice",
            "apply_change", "save_settings", "restart_program"
        ]
        self.assertEqual(result_success["steps"], expected_steps)
        
        # اختبار نص ليس IP مباشر
        result_not_ip = mock_complete_direct_ip_workflow("hello world", "***********")
        self.assertFalse(result_not_ip["success"])
        self.assertEqual(result_not_ip["reason"], "not_direct_ip")
        self.assertEqual(result_not_ip["action"], "send_main_menu")
        
        # اختبار عدم وجود تغيير
        result_no_change = mock_complete_direct_ip_workflow("***********", "***********")
        self.assertFalse(result_no_change["success"])
        self.assertEqual(result_no_change["reason"], "no_change")
        self.assertEqual(result_no_change["action"], "send_no_change_message")
        
        print("✅ نجح اختبار سير العمل الكامل للـ IP المباشر")

def run_direct_ip_input_fix_tests():
    """تشغيل جميع اختبارات إصلاح الكتابة المباشرة للـ IP"""
    print("🌐 بدء اختبارات إصلاح الكتابة المباشرة للـ IP")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("=" * 60)
    print("✅ انتهت جميع الاختبارات")
    
    print("\n📋 ملخص الإصلاحات المختبرة:")
    print("• ✅ دالة اكتشاف IP مباشر")
    print("• ✅ معالجة رسائل IP المباشر")
    print("• ✅ معالجة callback queries للـ IP المباشر")
    print("• ✅ اكتشاف نوع الشبكة للـ IP المباشر")
    print("• ✅ سير العمل الكامل للـ IP المباشر")

if __name__ == "__main__":
    run_direct_ip_input_fix_tests()
