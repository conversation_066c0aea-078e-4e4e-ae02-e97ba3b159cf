# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-19 18:28:31
# القالب: 20-marawan
# النظام: hotspot
# عدد الكروت: 600
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 20-marawan";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 600";

:local success 0;
:local errors 0;
:local total 600;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 600 مستخدم Hotspot...";

# المستخدم 1: 2007810888
:do {
    /ip hotspot user add name="2007810888" password="07992301" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007810888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007810888";
};

# المستخدم 2: 2008562863
:do {
    /ip hotspot user add name="2008562863" password="36516408" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008562863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008562863";
};

# المستخدم 3: 2034668698
:do {
    /ip hotspot user add name="2034668698" password="94280569" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034668698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034668698";
};

# المستخدم 4: 2064269696
:do {
    /ip hotspot user add name="2064269696" password="09295243" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064269696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064269696";
};

# المستخدم 5: 2001821254
:do {
    /ip hotspot user add name="2001821254" password="72299652" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001821254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001821254";
};

# المستخدم 6: 2095808335
:do {
    /ip hotspot user add name="2095808335" password="06404112" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095808335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095808335";
};

# المستخدم 7: 2055595765
:do {
    /ip hotspot user add name="2055595765" password="03580996" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055595765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055595765";
};

# المستخدم 8: 2019072241
:do {
    /ip hotspot user add name="2019072241" password="95551436" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019072241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019072241";
};

# المستخدم 9: 2017554693
:do {
    /ip hotspot user add name="2017554693" password="28562139" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017554693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017554693";
};

# المستخدم 10: 2007279381
:do {
    /ip hotspot user add name="2007279381" password="67016029" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007279381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007279381";
};

# المستخدم 11: 2041105791
:do {
    /ip hotspot user add name="2041105791" password="59592140" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041105791";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041105791";
};

# المستخدم 12: 2078107779
:do {
    /ip hotspot user add name="2078107779" password="16677953" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078107779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078107779";
};

# المستخدم 13: 2038371781
:do {
    /ip hotspot user add name="2038371781" password="37622906" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038371781";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038371781";
};

# المستخدم 14: 2079022133
:do {
    /ip hotspot user add name="2079022133" password="33750677" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079022133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079022133";
};

# المستخدم 15: 2083404000
:do {
    /ip hotspot user add name="2083404000" password="84596784" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083404000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083404000";
};

# المستخدم 16: 2034650790
:do {
    /ip hotspot user add name="2034650790" password="21449123" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034650790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034650790";
};

# المستخدم 17: 2003139614
:do {
    /ip hotspot user add name="2003139614" password="74093058" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003139614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003139614";
};

# المستخدم 18: 2080997860
:do {
    /ip hotspot user add name="2080997860" password="62802846" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080997860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080997860";
};

# المستخدم 19: 2084805282
:do {
    /ip hotspot user add name="2084805282" password="48918694" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084805282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084805282";
};

# المستخدم 20: 2070161749
:do {
    /ip hotspot user add name="2070161749" password="77309649" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070161749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070161749";
};

# المستخدم 21: 2063783600
:do {
    /ip hotspot user add name="2063783600" password="48245290" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063783600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063783600";
};

# المستخدم 22: 2027388925
:do {
    /ip hotspot user add name="2027388925" password="00663652" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027388925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027388925";
};

# المستخدم 23: 2058084086
:do {
    /ip hotspot user add name="2058084086" password="23251927" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058084086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058084086";
};

# المستخدم 24: 2000311390
:do {
    /ip hotspot user add name="2000311390" password="36749850" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000311390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000311390";
};

# المستخدم 25: 2050274310
:do {
    /ip hotspot user add name="2050274310" password="89511811" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050274310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050274310";
};

# المستخدم 26: 2034239468
:do {
    /ip hotspot user add name="2034239468" password="64267209" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034239468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034239468";
};

# المستخدم 27: 2073202538
:do {
    /ip hotspot user add name="2073202538" password="87962569" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073202538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073202538";
};

# المستخدم 28: 2042176360
:do {
    /ip hotspot user add name="2042176360" password="43386626" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042176360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042176360";
};

# المستخدم 29: 2026866823
:do {
    /ip hotspot user add name="2026866823" password="33433671" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026866823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026866823";
};

# المستخدم 30: 2061766673
:do {
    /ip hotspot user add name="2061766673" password="34306089" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061766673";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061766673";
};

# المستخدم 31: 2090835103
:do {
    /ip hotspot user add name="2090835103" password="73335511" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090835103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090835103";
};

# المستخدم 32: 2079059025
:do {
    /ip hotspot user add name="2079059025" password="38817267" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079059025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079059025";
};

# المستخدم 33: 2008056416
:do {
    /ip hotspot user add name="2008056416" password="79329657" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008056416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008056416";
};

# المستخدم 34: 2005872146
:do {
    /ip hotspot user add name="2005872146" password="01843272" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005872146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005872146";
};

# المستخدم 35: 2079735661
:do {
    /ip hotspot user add name="2079735661" password="70736760" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079735661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079735661";
};

# المستخدم 36: 2036786772
:do {
    /ip hotspot user add name="2036786772" password="85608796" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036786772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036786772";
};

# المستخدم 37: 2081655475
:do {
    /ip hotspot user add name="2081655475" password="29916862" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081655475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081655475";
};

# المستخدم 38: 2024232809
:do {
    /ip hotspot user add name="2024232809" password="78638739" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024232809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024232809";
};

# المستخدم 39: 2038834205
:do {
    /ip hotspot user add name="2038834205" password="33226590" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038834205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038834205";
};

# المستخدم 40: 2079874303
:do {
    /ip hotspot user add name="2079874303" password="40249638" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079874303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079874303";
};

# المستخدم 41: 2097423064
:do {
    /ip hotspot user add name="2097423064" password="11504469" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097423064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097423064";
};

# المستخدم 42: 2045796643
:do {
    /ip hotspot user add name="2045796643" password="79869797" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045796643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045796643";
};

# المستخدم 43: 2061098797
:do {
    /ip hotspot user add name="2061098797" password="63487970" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061098797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061098797";
};

# المستخدم 44: 2085911452
:do {
    /ip hotspot user add name="2085911452" password="53337434" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085911452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085911452";
};

# المستخدم 45: 2058264628
:do {
    /ip hotspot user add name="2058264628" password="53216741" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058264628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058264628";
};

# المستخدم 46: 2084664749
:do {
    /ip hotspot user add name="2084664749" password="21416249" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084664749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084664749";
};

# المستخدم 47: 2076555866
:do {
    /ip hotspot user add name="2076555866" password="37857138" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076555866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076555866";
};

# المستخدم 48: 2075871938
:do {
    /ip hotspot user add name="2075871938" password="24517844" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075871938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075871938";
};

# المستخدم 49: 2068035774
:do {
    /ip hotspot user add name="2068035774" password="47169992" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068035774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068035774";
};

# المستخدم 50: 2034173026
:do {
    /ip hotspot user add name="2034173026" password="57382523" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034173026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034173026";
};

# المستخدم 51: 2008926171
:do {
    /ip hotspot user add name="2008926171" password="52776264" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008926171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008926171";
};

# المستخدم 52: 2042829870
:do {
    /ip hotspot user add name="2042829870" password="35169493" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042829870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042829870";
};

# المستخدم 53: 2003972747
:do {
    /ip hotspot user add name="2003972747" password="99007256" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003972747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003972747";
};

# المستخدم 54: 2060774881
:do {
    /ip hotspot user add name="2060774881" password="90760741" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060774881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060774881";
};

# المستخدم 55: 2064977352
:do {
    /ip hotspot user add name="2064977352" password="85150574" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064977352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064977352";
};

# المستخدم 56: 2001781832
:do {
    /ip hotspot user add name="2001781832" password="22879134" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001781832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001781832";
};

# المستخدم 57: 2008666852
:do {
    /ip hotspot user add name="2008666852" password="08710297" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008666852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008666852";
};

# المستخدم 58: 2027862257
:do {
    /ip hotspot user add name="2027862257" password="86620316" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027862257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027862257";
};

# المستخدم 59: 2012066437
:do {
    /ip hotspot user add name="2012066437" password="61446729" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012066437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012066437";
};

# المستخدم 60: 2045677454
:do {
    /ip hotspot user add name="2045677454" password="32441373" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045677454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045677454";
};

# المستخدم 61: 2080077975
:do {
    /ip hotspot user add name="2080077975" password="76834467" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080077975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080077975";
};

# المستخدم 62: 2034047151
:do {
    /ip hotspot user add name="2034047151" password="33953714" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034047151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034047151";
};

# المستخدم 63: 2067569969
:do {
    /ip hotspot user add name="2067569969" password="74786766" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067569969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067569969";
};

# المستخدم 64: 2092040473
:do {
    /ip hotspot user add name="2092040473" password="08915373" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092040473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092040473";
};

# المستخدم 65: 2052967004
:do {
    /ip hotspot user add name="2052967004" password="14354561" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052967004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052967004";
};

# المستخدم 66: 2030669355
:do {
    /ip hotspot user add name="2030669355" password="82470028" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030669355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030669355";
};

# المستخدم 67: 2012873748
:do {
    /ip hotspot user add name="2012873748" password="25886679" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012873748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012873748";
};

# المستخدم 68: 2012702194
:do {
    /ip hotspot user add name="2012702194" password="59792943" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012702194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012702194";
};

# المستخدم 69: 2098989882
:do {
    /ip hotspot user add name="2098989882" password="06706988" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098989882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098989882";
};

# المستخدم 70: 2048854234
:do {
    /ip hotspot user add name="2048854234" password="10690189" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048854234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048854234";
};

# المستخدم 71: 2019355150
:do {
    /ip hotspot user add name="2019355150" password="44773761" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019355150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019355150";
};

# المستخدم 72: 2035908221
:do {
    /ip hotspot user add name="2035908221" password="15475112" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035908221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035908221";
};

# المستخدم 73: 2063810605
:do {
    /ip hotspot user add name="2063810605" password="10188828" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063810605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063810605";
};

# المستخدم 74: 2014589404
:do {
    /ip hotspot user add name="2014589404" password="83939839" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014589404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014589404";
};

# المستخدم 75: 2051442034
:do {
    /ip hotspot user add name="2051442034" password="29105174" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051442034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051442034";
};

# المستخدم 76: 2034647372
:do {
    /ip hotspot user add name="2034647372" password="65500311" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034647372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034647372";
};

# المستخدم 77: 2017105505
:do {
    /ip hotspot user add name="2017105505" password="96194273" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017105505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017105505";
};

# المستخدم 78: 2049688361
:do {
    /ip hotspot user add name="2049688361" password="94589253" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049688361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049688361";
};

# المستخدم 79: 2003693062
:do {
    /ip hotspot user add name="2003693062" password="01180795" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003693062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003693062";
};

# المستخدم 80: 2050842594
:do {
    /ip hotspot user add name="2050842594" password="10058597" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050842594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050842594";
};

# المستخدم 81: 2047846683
:do {
    /ip hotspot user add name="2047846683" password="61884359" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047846683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047846683";
};

# المستخدم 82: 2026106224
:do {
    /ip hotspot user add name="2026106224" password="21091125" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026106224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026106224";
};

# المستخدم 83: 2002824234
:do {
    /ip hotspot user add name="2002824234" password="72343954" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002824234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002824234";
};

# المستخدم 84: 2061365136
:do {
    /ip hotspot user add name="2061365136" password="25131080" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061365136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061365136";
};

# المستخدم 85: 2079763920
:do {
    /ip hotspot user add name="2079763920" password="15854446" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079763920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079763920";
};

# المستخدم 86: 2056001548
:do {
    /ip hotspot user add name="2056001548" password="56302723" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056001548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056001548";
};

# المستخدم 87: 2094334218
:do {
    /ip hotspot user add name="2094334218" password="95273508" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094334218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094334218";
};

# المستخدم 88: 2087295516
:do {
    /ip hotspot user add name="2087295516" password="66772220" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087295516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087295516";
};

# المستخدم 89: 2076673284
:do {
    /ip hotspot user add name="2076673284" password="19206886" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076673284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076673284";
};

# المستخدم 90: 2069210890
:do {
    /ip hotspot user add name="2069210890" password="44508382" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069210890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069210890";
};

# المستخدم 91: 2031620551
:do {
    /ip hotspot user add name="2031620551" password="24289814" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031620551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031620551";
};

# المستخدم 92: 2029821827
:do {
    /ip hotspot user add name="2029821827" password="53745142" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029821827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029821827";
};

# المستخدم 93: 2095482956
:do {
    /ip hotspot user add name="2095482956" password="18073756" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095482956";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095482956";
};

# المستخدم 94: 2081156346
:do {
    /ip hotspot user add name="2081156346" password="95486541" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081156346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081156346";
};

# المستخدم 95: 2056342874
:do {
    /ip hotspot user add name="2056342874" password="28070226" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056342874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056342874";
};

# المستخدم 96: 2062681653
:do {
    /ip hotspot user add name="2062681653" password="28784727" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062681653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062681653";
};

# المستخدم 97: 2075810609
:do {
    /ip hotspot user add name="2075810609" password="86657377" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075810609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075810609";
};

# المستخدم 98: 2093499319
:do {
    /ip hotspot user add name="2093499319" password="76124925" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093499319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093499319";
};

# المستخدم 99: 2004889806
:do {
    /ip hotspot user add name="2004889806" password="80891577" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004889806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004889806";
};

# المستخدم 100: 2097199925
:do {
    /ip hotspot user add name="2097199925" password="89986160" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097199925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097199925";
};

# المستخدم 101: 2011778975
:do {
    /ip hotspot user add name="2011778975" password="18748917" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011778975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011778975";
};

# المستخدم 102: 2012799558
:do {
    /ip hotspot user add name="2012799558" password="42767148" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012799558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012799558";
};

# المستخدم 103: 2091437793
:do {
    /ip hotspot user add name="2091437793" password="61956372" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091437793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091437793";
};

# المستخدم 104: 2023355272
:do {
    /ip hotspot user add name="2023355272" password="63988362" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023355272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023355272";
};

# المستخدم 105: 2086203935
:do {
    /ip hotspot user add name="2086203935" password="86193718" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086203935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086203935";
};

# المستخدم 106: 2089466393
:do {
    /ip hotspot user add name="2089466393" password="72953473" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089466393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089466393";
};

# المستخدم 107: 2069877323
:do {
    /ip hotspot user add name="2069877323" password="80321954" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069877323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069877323";
};

# المستخدم 108: 2056130618
:do {
    /ip hotspot user add name="2056130618" password="79320877" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056130618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056130618";
};

# المستخدم 109: 2089847344
:do {
    /ip hotspot user add name="2089847344" password="14839821" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089847344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089847344";
};

# المستخدم 110: 2051125728
:do {
    /ip hotspot user add name="2051125728" password="44937487" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051125728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051125728";
};

# المستخدم 111: 2079916394
:do {
    /ip hotspot user add name="2079916394" password="05983069" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079916394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079916394";
};

# المستخدم 112: 2046340680
:do {
    /ip hotspot user add name="2046340680" password="76186925" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046340680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046340680";
};

# المستخدم 113: 2095360628
:do {
    /ip hotspot user add name="2095360628" password="59525684" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095360628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095360628";
};

# المستخدم 114: 2021014755
:do {
    /ip hotspot user add name="2021014755" password="17877303" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021014755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021014755";
};

# المستخدم 115: 2089422179
:do {
    /ip hotspot user add name="2089422179" password="82309816" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089422179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089422179";
};

# المستخدم 116: 2046644373
:do {
    /ip hotspot user add name="2046644373" password="93626498" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046644373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046644373";
};

# المستخدم 117: 2009905886
:do {
    /ip hotspot user add name="2009905886" password="08805510" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009905886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009905886";
};

# المستخدم 118: 2026594599
:do {
    /ip hotspot user add name="2026594599" password="83910698" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026594599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026594599";
};

# المستخدم 119: 2059364464
:do {
    /ip hotspot user add name="2059364464" password="62215234" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059364464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059364464";
};

# المستخدم 120: 2094407199
:do {
    /ip hotspot user add name="2094407199" password="71947948" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094407199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094407199";
};

# المستخدم 121: 2087215481
:do {
    /ip hotspot user add name="2087215481" password="62634676" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087215481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087215481";
};

# المستخدم 122: 2012160553
:do {
    /ip hotspot user add name="2012160553" password="08287418" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012160553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012160553";
};

# المستخدم 123: 2098359605
:do {
    /ip hotspot user add name="2098359605" password="61609245" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098359605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098359605";
};

# المستخدم 124: 2024469105
:do {
    /ip hotspot user add name="2024469105" password="35345831" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024469105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024469105";
};

# المستخدم 125: 2013255792
:do {
    /ip hotspot user add name="2013255792" password="61013008" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013255792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013255792";
};

# المستخدم 126: 2057849392
:do {
    /ip hotspot user add name="2057849392" password="32367768" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057849392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057849392";
};

# المستخدم 127: 2043707686
:do {
    /ip hotspot user add name="2043707686" password="86772314" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043707686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043707686";
};

# المستخدم 128: 2074368940
:do {
    /ip hotspot user add name="2074368940" password="40028826" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074368940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074368940";
};

# المستخدم 129: 2030370684
:do {
    /ip hotspot user add name="2030370684" password="21558320" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030370684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030370684";
};

# المستخدم 130: 2019810186
:do {
    /ip hotspot user add name="2019810186" password="33481774" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019810186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019810186";
};

# المستخدم 131: 2095631138
:do {
    /ip hotspot user add name="2095631138" password="15402235" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095631138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095631138";
};

# المستخدم 132: 2062239541
:do {
    /ip hotspot user add name="2062239541" password="86936177" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062239541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062239541";
};

# المستخدم 133: 2065676720
:do {
    /ip hotspot user add name="2065676720" password="42694604" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065676720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065676720";
};

# المستخدم 134: 2068993858
:do {
    /ip hotspot user add name="2068993858" password="09096848" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068993858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068993858";
};

# المستخدم 135: 2062285016
:do {
    /ip hotspot user add name="2062285016" password="99694913" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062285016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062285016";
};

# المستخدم 136: 2094309546
:do {
    /ip hotspot user add name="2094309546" password="32732116" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094309546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094309546";
};

# المستخدم 137: 2000053035
:do {
    /ip hotspot user add name="2000053035" password="16683503" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000053035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000053035";
};

# المستخدم 138: 2040837363
:do {
    /ip hotspot user add name="2040837363" password="32005922" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040837363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040837363";
};

# المستخدم 139: 2072662917
:do {
    /ip hotspot user add name="2072662917" password="96507576" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072662917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072662917";
};

# المستخدم 140: 2064434191
:do {
    /ip hotspot user add name="2064434191" password="13505921" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064434191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064434191";
};

# المستخدم 141: 2040720429
:do {
    /ip hotspot user add name="2040720429" password="16064156" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040720429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040720429";
};

# المستخدم 142: 2076682835
:do {
    /ip hotspot user add name="2076682835" password="13970895" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076682835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076682835";
};

# المستخدم 143: 2091114492
:do {
    /ip hotspot user add name="2091114492" password="75428447" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091114492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091114492";
};

# المستخدم 144: 2007225763
:do {
    /ip hotspot user add name="2007225763" password="14792726" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007225763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007225763";
};

# المستخدم 145: 2039075315
:do {
    /ip hotspot user add name="2039075315" password="51681291" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039075315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039075315";
};

# المستخدم 146: 2005608878
:do {
    /ip hotspot user add name="2005608878" password="73073744" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005608878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005608878";
};

# المستخدم 147: 2043891231
:do {
    /ip hotspot user add name="2043891231" password="96570234" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043891231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043891231";
};

# المستخدم 148: 2022023804
:do {
    /ip hotspot user add name="2022023804" password="37556544" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022023804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022023804";
};

# المستخدم 149: 2033852461
:do {
    /ip hotspot user add name="2033852461" password="29126133" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033852461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033852461";
};

# المستخدم 150: 2076116486
:do {
    /ip hotspot user add name="2076116486" password="04018618" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076116486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076116486";
};

# المستخدم 151: 2061653412
:do {
    /ip hotspot user add name="2061653412" password="37889105" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061653412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061653412";
};

# المستخدم 152: 2073624099
:do {
    /ip hotspot user add name="2073624099" password="55555347" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073624099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073624099";
};

# المستخدم 153: 2070420093
:do {
    /ip hotspot user add name="2070420093" password="21774844" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070420093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070420093";
};

# المستخدم 154: 2029574721
:do {
    /ip hotspot user add name="2029574721" password="38454762" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029574721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029574721";
};

# المستخدم 155: 2002418601
:do {
    /ip hotspot user add name="2002418601" password="69806233" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002418601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002418601";
};

# المستخدم 156: 2018834880
:do {
    /ip hotspot user add name="2018834880" password="43291412" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018834880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018834880";
};

# المستخدم 157: 2059615101
:do {
    /ip hotspot user add name="2059615101" password="18440635" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059615101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059615101";
};

# المستخدم 158: 2042713284
:do {
    /ip hotspot user add name="2042713284" password="08820523" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042713284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042713284";
};

# المستخدم 159: 2054288425
:do {
    /ip hotspot user add name="2054288425" password="49552628" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054288425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054288425";
};

# المستخدم 160: 2005171964
:do {
    /ip hotspot user add name="2005171964" password="33920330" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005171964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005171964";
};

# المستخدم 161: 2089784890
:do {
    /ip hotspot user add name="2089784890" password="53838098" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089784890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089784890";
};

# المستخدم 162: 2032897642
:do {
    /ip hotspot user add name="2032897642" password="03101276" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032897642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032897642";
};

# المستخدم 163: 2063195196
:do {
    /ip hotspot user add name="2063195196" password="23572146" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063195196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063195196";
};

# المستخدم 164: 2053119723
:do {
    /ip hotspot user add name="2053119723" password="68511837" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053119723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053119723";
};

# المستخدم 165: 2021766392
:do {
    /ip hotspot user add name="2021766392" password="97645675" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021766392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021766392";
};

# المستخدم 166: 2083169472
:do {
    /ip hotspot user add name="2083169472" password="29800594" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083169472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083169472";
};

# المستخدم 167: 2083011900
:do {
    /ip hotspot user add name="2083011900" password="61695601" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083011900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083011900";
};

# المستخدم 168: 2041780012
:do {
    /ip hotspot user add name="2041780012" password="39980067" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041780012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041780012";
};

# المستخدم 169: 2094276112
:do {
    /ip hotspot user add name="2094276112" password="22010048" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094276112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094276112";
};

# المستخدم 170: 2018188906
:do {
    /ip hotspot user add name="2018188906" password="41454352" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018188906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018188906";
};

# المستخدم 171: 2078159439
:do {
    /ip hotspot user add name="2078159439" password="18745248" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078159439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078159439";
};

# المستخدم 172: 2092839419
:do {
    /ip hotspot user add name="2092839419" password="04283116" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092839419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092839419";
};

# المستخدم 173: 2030044172
:do {
    /ip hotspot user add name="2030044172" password="62732359" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030044172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030044172";
};

# المستخدم 174: 2017202938
:do {
    /ip hotspot user add name="2017202938" password="44664275" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017202938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017202938";
};

# المستخدم 175: 2061781948
:do {
    /ip hotspot user add name="2061781948" password="89256565" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061781948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061781948";
};

# المستخدم 176: 2097930641
:do {
    /ip hotspot user add name="2097930641" password="26282731" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097930641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097930641";
};

# المستخدم 177: 2040468708
:do {
    /ip hotspot user add name="2040468708" password="08618682" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040468708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040468708";
};

# المستخدم 178: 2021715751
:do {
    /ip hotspot user add name="2021715751" password="16997108" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021715751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021715751";
};

# المستخدم 179: 2027269273
:do {
    /ip hotspot user add name="2027269273" password="24650721" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027269273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027269273";
};

# المستخدم 180: 2029488548
:do {
    /ip hotspot user add name="2029488548" password="14427561" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029488548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029488548";
};

# المستخدم 181: 2095871868
:do {
    /ip hotspot user add name="2095871868" password="11494556" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095871868";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095871868";
};

# المستخدم 182: 2042594636
:do {
    /ip hotspot user add name="2042594636" password="32076972" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042594636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042594636";
};

# المستخدم 183: 2067936820
:do {
    /ip hotspot user add name="2067936820" password="67940212" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067936820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067936820";
};

# المستخدم 184: 2024132535
:do {
    /ip hotspot user add name="2024132535" password="21959672" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024132535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024132535";
};

# المستخدم 185: 2044533827
:do {
    /ip hotspot user add name="2044533827" password="61567097" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044533827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044533827";
};

# المستخدم 186: 2030731359
:do {
    /ip hotspot user add name="2030731359" password="66278421" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030731359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030731359";
};

# المستخدم 187: 2030593528
:do {
    /ip hotspot user add name="2030593528" password="17165331" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030593528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030593528";
};

# المستخدم 188: 2010042778
:do {
    /ip hotspot user add name="2010042778" password="01347364" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010042778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010042778";
};

# المستخدم 189: 2016450423
:do {
    /ip hotspot user add name="2016450423" password="04514214" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016450423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016450423";
};

# المستخدم 190: 2012219199
:do {
    /ip hotspot user add name="2012219199" password="67050578" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012219199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012219199";
};

# المستخدم 191: 2022745179
:do {
    /ip hotspot user add name="2022745179" password="50518743" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022745179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022745179";
};

# المستخدم 192: 2003972449
:do {
    /ip hotspot user add name="2003972449" password="80428596" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003972449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003972449";
};

# المستخدم 193: 2026117676
:do {
    /ip hotspot user add name="2026117676" password="53192279" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026117676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026117676";
};

# المستخدم 194: 2073080498
:do {
    /ip hotspot user add name="2073080498" password="52799765" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073080498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073080498";
};

# المستخدم 195: 2069392048
:do {
    /ip hotspot user add name="2069392048" password="57452299" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069392048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069392048";
};

# المستخدم 196: 2008730485
:do {
    /ip hotspot user add name="2008730485" password="77311047" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008730485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008730485";
};

# المستخدم 197: 2028586649
:do {
    /ip hotspot user add name="2028586649" password="27613111" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028586649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028586649";
};

# المستخدم 198: 2058558099
:do {
    /ip hotspot user add name="2058558099" password="40681494" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058558099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058558099";
};

# المستخدم 199: 2019276133
:do {
    /ip hotspot user add name="2019276133" password="50192096" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019276133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019276133";
};

# المستخدم 200: 2092235481
:do {
    /ip hotspot user add name="2092235481" password="51038511" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092235481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092235481";
};

# المستخدم 201: 2047694511
:do {
    /ip hotspot user add name="2047694511" password="04531633" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047694511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047694511";
};

# المستخدم 202: 2049014026
:do {
    /ip hotspot user add name="2049014026" password="46563714" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049014026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049014026";
};

# المستخدم 203: 2045717484
:do {
    /ip hotspot user add name="2045717484" password="55599725" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045717484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045717484";
};

# المستخدم 204: 2031709960
:do {
    /ip hotspot user add name="2031709960" password="45264169" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031709960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031709960";
};

# المستخدم 205: 2056065316
:do {
    /ip hotspot user add name="2056065316" password="30262729" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056065316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056065316";
};

# المستخدم 206: 2041599440
:do {
    /ip hotspot user add name="2041599440" password="52467829" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041599440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041599440";
};

# المستخدم 207: 2092495477
:do {
    /ip hotspot user add name="2092495477" password="74145858" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092495477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092495477";
};

# المستخدم 208: 2006409153
:do {
    /ip hotspot user add name="2006409153" password="24221159" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006409153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006409153";
};

# المستخدم 209: 2090676071
:do {
    /ip hotspot user add name="2090676071" password="24908905" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090676071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090676071";
};

# المستخدم 210: 2069722906
:do {
    /ip hotspot user add name="2069722906" password="79946773" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069722906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069722906";
};

# المستخدم 211: 2088178899
:do {
    /ip hotspot user add name="2088178899" password="50156144" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088178899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088178899";
};

# المستخدم 212: 2086274086
:do {
    /ip hotspot user add name="2086274086" password="07015582" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086274086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086274086";
};

# المستخدم 213: 2076564499
:do {
    /ip hotspot user add name="2076564499" password="15851705" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076564499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076564499";
};

# المستخدم 214: 2071909482
:do {
    /ip hotspot user add name="2071909482" password="35353070" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071909482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071909482";
};

# المستخدم 215: 2052600099
:do {
    /ip hotspot user add name="2052600099" password="60081495" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052600099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052600099";
};

# المستخدم 216: 2074770140
:do {
    /ip hotspot user add name="2074770140" password="96465749" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074770140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074770140";
};

# المستخدم 217: 2062551498
:do {
    /ip hotspot user add name="2062551498" password="28039190" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062551498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062551498";
};

# المستخدم 218: 2013947865
:do {
    /ip hotspot user add name="2013947865" password="63071675" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013947865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013947865";
};

# المستخدم 219: 2056363324
:do {
    /ip hotspot user add name="2056363324" password="99580394" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056363324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056363324";
};

# المستخدم 220: 2063473800
:do {
    /ip hotspot user add name="2063473800" password="38357828" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063473800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063473800";
};

# المستخدم 221: 2093141225
:do {
    /ip hotspot user add name="2093141225" password="61672559" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093141225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093141225";
};

# المستخدم 222: 2076295652
:do {
    /ip hotspot user add name="2076295652" password="47885025" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076295652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076295652";
};

# المستخدم 223: 2089984484
:do {
    /ip hotspot user add name="2089984484" password="33489245" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089984484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089984484";
};

# المستخدم 224: 2073893841
:do {
    /ip hotspot user add name="2073893841" password="85777575" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073893841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073893841";
};

# المستخدم 225: 2063423796
:do {
    /ip hotspot user add name="2063423796" password="37719979" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063423796";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063423796";
};

# المستخدم 226: 2046895510
:do {
    /ip hotspot user add name="2046895510" password="74304528" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046895510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046895510";
};

# المستخدم 227: 2023654193
:do {
    /ip hotspot user add name="2023654193" password="25980607" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023654193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023654193";
};

# المستخدم 228: 2085026954
:do {
    /ip hotspot user add name="2085026954" password="86868267" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085026954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085026954";
};

# المستخدم 229: 2001043516
:do {
    /ip hotspot user add name="2001043516" password="31249381" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001043516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001043516";
};

# المستخدم 230: 2078586395
:do {
    /ip hotspot user add name="2078586395" password="97918494" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078586395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078586395";
};

# المستخدم 231: 2008348162
:do {
    /ip hotspot user add name="2008348162" password="59740509" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008348162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008348162";
};

# المستخدم 232: 2056402649
:do {
    /ip hotspot user add name="2056402649" password="50189542" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056402649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056402649";
};

# المستخدم 233: 2070862542
:do {
    /ip hotspot user add name="2070862542" password="60393112" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070862542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070862542";
};

# المستخدم 234: 2018347714
:do {
    /ip hotspot user add name="2018347714" password="72679242" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018347714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018347714";
};

# المستخدم 235: 2097862781
:do {
    /ip hotspot user add name="2097862781" password="56439470" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097862781";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097862781";
};

# المستخدم 236: 2036740119
:do {
    /ip hotspot user add name="2036740119" password="69059125" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036740119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036740119";
};

# المستخدم 237: 2006931712
:do {
    /ip hotspot user add name="2006931712" password="02862371" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006931712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006931712";
};

# المستخدم 238: 2014788898
:do {
    /ip hotspot user add name="2014788898" password="32927652" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014788898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014788898";
};

# المستخدم 239: 2010488394
:do {
    /ip hotspot user add name="2010488394" password="36952367" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010488394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010488394";
};

# المستخدم 240: 2038143427
:do {
    /ip hotspot user add name="2038143427" password="93936641" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038143427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038143427";
};

# المستخدم 241: 2068365101
:do {
    /ip hotspot user add name="2068365101" password="22545069" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068365101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068365101";
};

# المستخدم 242: 2060926286
:do {
    /ip hotspot user add name="2060926286" password="39177819" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060926286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060926286";
};

# المستخدم 243: 2022575320
:do {
    /ip hotspot user add name="2022575320" password="20664712" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022575320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022575320";
};

# المستخدم 244: 2071176565
:do {
    /ip hotspot user add name="2071176565" password="51040521" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071176565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071176565";
};

# المستخدم 245: 2023760690
:do {
    /ip hotspot user add name="2023760690" password="00516488" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023760690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023760690";
};

# المستخدم 246: 2090266701
:do {
    /ip hotspot user add name="2090266701" password="62831303" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090266701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090266701";
};

# المستخدم 247: 2080509654
:do {
    /ip hotspot user add name="2080509654" password="92716795" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080509654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080509654";
};

# المستخدم 248: 2099696394
:do {
    /ip hotspot user add name="2099696394" password="88740054" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099696394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099696394";
};

# المستخدم 249: 2051497857
:do {
    /ip hotspot user add name="2051497857" password="25508958" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051497857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051497857";
};

# المستخدم 250: 2028858766
:do {
    /ip hotspot user add name="2028858766" password="11510342" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028858766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028858766";
};

# المستخدم 251: 2059054627
:do {
    /ip hotspot user add name="2059054627" password="05244748" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059054627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059054627";
};

# المستخدم 252: 2093877716
:do {
    /ip hotspot user add name="2093877716" password="43676870" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093877716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093877716";
};

# المستخدم 253: 2003147332
:do {
    /ip hotspot user add name="2003147332" password="45031171" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003147332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003147332";
};

# المستخدم 254: 2081543728
:do {
    /ip hotspot user add name="2081543728" password="30465038" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081543728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081543728";
};

# المستخدم 255: 2090782453
:do {
    /ip hotspot user add name="2090782453" password="08359759" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090782453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090782453";
};

# المستخدم 256: 2067620950
:do {
    /ip hotspot user add name="2067620950" password="90030868" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067620950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067620950";
};

# المستخدم 257: 2066645342
:do {
    /ip hotspot user add name="2066645342" password="62338240" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066645342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066645342";
};

# المستخدم 258: 2067826037
:do {
    /ip hotspot user add name="2067826037" password="48214597" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067826037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067826037";
};

# المستخدم 259: 2013485389
:do {
    /ip hotspot user add name="2013485389" password="27103424" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013485389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013485389";
};

# المستخدم 260: 2080160059
:do {
    /ip hotspot user add name="2080160059" password="39881363" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080160059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080160059";
};

# المستخدم 261: 2098871543
:do {
    /ip hotspot user add name="2098871543" password="02393399" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098871543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098871543";
};

# المستخدم 262: 2068073897
:do {
    /ip hotspot user add name="2068073897" password="50001302" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068073897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068073897";
};

# المستخدم 263: 2037868731
:do {
    /ip hotspot user add name="2037868731" password="85951004" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037868731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037868731";
};

# المستخدم 264: 2075201683
:do {
    /ip hotspot user add name="2075201683" password="38836365" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075201683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075201683";
};

# المستخدم 265: 2076916362
:do {
    /ip hotspot user add name="2076916362" password="85235119" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076916362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076916362";
};

# المستخدم 266: 2010319871
:do {
    /ip hotspot user add name="2010319871" password="61489422" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010319871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010319871";
};

# المستخدم 267: 2067986978
:do {
    /ip hotspot user add name="2067986978" password="46193641" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067986978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067986978";
};

# المستخدم 268: 2025677017
:do {
    /ip hotspot user add name="2025677017" password="10143052" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025677017";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025677017";
};

# المستخدم 269: 2046136722
:do {
    /ip hotspot user add name="2046136722" password="42909396" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046136722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046136722";
};

# المستخدم 270: 2014730792
:do {
    /ip hotspot user add name="2014730792" password="54519526" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014730792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014730792";
};

# المستخدم 271: 2016975330
:do {
    /ip hotspot user add name="2016975330" password="51616665" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016975330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016975330";
};

# المستخدم 272: 2010620814
:do {
    /ip hotspot user add name="2010620814" password="37229812" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010620814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010620814";
};

# المستخدم 273: 2083479580
:do {
    /ip hotspot user add name="2083479580" password="28271766" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083479580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083479580";
};

# المستخدم 274: 2055420775
:do {
    /ip hotspot user add name="2055420775" password="05990192" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055420775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055420775";
};

# المستخدم 275: 2045852058
:do {
    /ip hotspot user add name="2045852058" password="71286383" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045852058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045852058";
};

# المستخدم 276: 2048491751
:do {
    /ip hotspot user add name="2048491751" password="30502878" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048491751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048491751";
};

# المستخدم 277: 2030125858
:do {
    /ip hotspot user add name="2030125858" password="83623011" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030125858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030125858";
};

# المستخدم 278: 2032029937
:do {
    /ip hotspot user add name="2032029937" password="79882231" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032029937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032029937";
};

# المستخدم 279: 2087359628
:do {
    /ip hotspot user add name="2087359628" password="65480085" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087359628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087359628";
};

# المستخدم 280: 2028674297
:do {
    /ip hotspot user add name="2028674297" password="27716871" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028674297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028674297";
};

# المستخدم 281: 2010593345
:do {
    /ip hotspot user add name="2010593345" password="59175326" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010593345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010593345";
};

# المستخدم 282: 2051556877
:do {
    /ip hotspot user add name="2051556877" password="54541425" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051556877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051556877";
};

# المستخدم 283: 2059595569
:do {
    /ip hotspot user add name="2059595569" password="62406784" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059595569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059595569";
};

# المستخدم 284: 2023108392
:do {
    /ip hotspot user add name="2023108392" password="73439482" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023108392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023108392";
};

# المستخدم 285: 2026511201
:do {
    /ip hotspot user add name="2026511201" password="15945273" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026511201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026511201";
};

# المستخدم 286: 2017137302
:do {
    /ip hotspot user add name="2017137302" password="18433385" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017137302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017137302";
};

# المستخدم 287: 2072054770
:do {
    /ip hotspot user add name="2072054770" password="39717973" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072054770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072054770";
};

# المستخدم 288: 2023251325
:do {
    /ip hotspot user add name="2023251325" password="68210627" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023251325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023251325";
};

# المستخدم 289: 2020058087
:do {
    /ip hotspot user add name="2020058087" password="85237473" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020058087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020058087";
};

# المستخدم 290: 2008093804
:do {
    /ip hotspot user add name="2008093804" password="26035807" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008093804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008093804";
};

# المستخدم 291: 2088818270
:do {
    /ip hotspot user add name="2088818270" password="52900446" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088818270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088818270";
};

# المستخدم 292: 2097573548
:do {
    /ip hotspot user add name="2097573548" password="86063446" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097573548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097573548";
};

# المستخدم 293: 2079953797
:do {
    /ip hotspot user add name="2079953797" password="71489712" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079953797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079953797";
};

# المستخدم 294: 2036344108
:do {
    /ip hotspot user add name="2036344108" password="03738154" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036344108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036344108";
};

# المستخدم 295: 2086833269
:do {
    /ip hotspot user add name="2086833269" password="34177671" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086833269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086833269";
};

# المستخدم 296: 2028902688
:do {
    /ip hotspot user add name="2028902688" password="43838976" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028902688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028902688";
};

# المستخدم 297: 2075844123
:do {
    /ip hotspot user add name="2075844123" password="52101483" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075844123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075844123";
};

# المستخدم 298: 2083416732
:do {
    /ip hotspot user add name="2083416732" password="48293844" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083416732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083416732";
};

# المستخدم 299: 2062365695
:do {
    /ip hotspot user add name="2062365695" password="26910049" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062365695";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062365695";
};

# المستخدم 300: 2046742558
:do {
    /ip hotspot user add name="2046742558" password="85535983" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046742558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046742558";
};

# المستخدم 301: 2056910081
:do {
    /ip hotspot user add name="2056910081" password="06926397" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056910081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056910081";
};

# المستخدم 302: 2087759799
:do {
    /ip hotspot user add name="2087759799" password="34822587" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087759799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087759799";
};

# المستخدم 303: 2092396711
:do {
    /ip hotspot user add name="2092396711" password="88822893" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092396711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092396711";
};

# المستخدم 304: 2033712666
:do {
    /ip hotspot user add name="2033712666" password="14809625" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033712666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033712666";
};

# المستخدم 305: 2053532934
:do {
    /ip hotspot user add name="2053532934" password="11163755" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053532934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053532934";
};

# المستخدم 306: 2074406706
:do {
    /ip hotspot user add name="2074406706" password="46671012" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074406706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074406706";
};

# المستخدم 307: 2093917157
:do {
    /ip hotspot user add name="2093917157" password="51232627" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093917157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093917157";
};

# المستخدم 308: 2005581626
:do {
    /ip hotspot user add name="2005581626" password="36985576" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005581626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005581626";
};

# المستخدم 309: 2089273410
:do {
    /ip hotspot user add name="2089273410" password="33796610" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089273410";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089273410";
};

# المستخدم 310: 2036216319
:do {
    /ip hotspot user add name="2036216319" password="25796912" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036216319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036216319";
};

# المستخدم 311: 2088363824
:do {
    /ip hotspot user add name="2088363824" password="42781228" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088363824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088363824";
};

# المستخدم 312: 2012387496
:do {
    /ip hotspot user add name="2012387496" password="62906066" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012387496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012387496";
};

# المستخدم 313: 2062134354
:do {
    /ip hotspot user add name="2062134354" password="31615290" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062134354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062134354";
};

# المستخدم 314: 2017072033
:do {
    /ip hotspot user add name="2017072033" password="11063596" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017072033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017072033";
};

# المستخدم 315: 2061926424
:do {
    /ip hotspot user add name="2061926424" password="02024880" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061926424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061926424";
};

# المستخدم 316: 2052437015
:do {
    /ip hotspot user add name="2052437015" password="66695795" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052437015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052437015";
};

# المستخدم 317: 2071404878
:do {
    /ip hotspot user add name="2071404878" password="44636444" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071404878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071404878";
};

# المستخدم 318: 2058792762
:do {
    /ip hotspot user add name="2058792762" password="33858427" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058792762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058792762";
};

# المستخدم 319: 2002307397
:do {
    /ip hotspot user add name="2002307397" password="16018807" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002307397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002307397";
};

# المستخدم 320: 2020074071
:do {
    /ip hotspot user add name="2020074071" password="67738178" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020074071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020074071";
};

# المستخدم 321: 2054784565
:do {
    /ip hotspot user add name="2054784565" password="95371844" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054784565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054784565";
};

# المستخدم 322: 2005180729
:do {
    /ip hotspot user add name="2005180729" password="91121521" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005180729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005180729";
};

# المستخدم 323: 2043103114
:do {
    /ip hotspot user add name="2043103114" password="40115044" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043103114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043103114";
};

# المستخدم 324: 2032242391
:do {
    /ip hotspot user add name="2032242391" password="45221085" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032242391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032242391";
};

# المستخدم 325: 2006746566
:do {
    /ip hotspot user add name="2006746566" password="38105443" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006746566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006746566";
};

# المستخدم 326: 2095338819
:do {
    /ip hotspot user add name="2095338819" password="71206571" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095338819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095338819";
};

# المستخدم 327: 2071110986
:do {
    /ip hotspot user add name="2071110986" password="45751399" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071110986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071110986";
};

# المستخدم 328: 2059741628
:do {
    /ip hotspot user add name="2059741628" password="34823867" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059741628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059741628";
};

# المستخدم 329: 2035380963
:do {
    /ip hotspot user add name="2035380963" password="49661941" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035380963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035380963";
};

# المستخدم 330: 2000565493
:do {
    /ip hotspot user add name="2000565493" password="90883129" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000565493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000565493";
};

# المستخدم 331: 2013179134
:do {
    /ip hotspot user add name="2013179134" password="21297582" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013179134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013179134";
};

# المستخدم 332: 2037356145
:do {
    /ip hotspot user add name="2037356145" password="97358970" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037356145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037356145";
};

# المستخدم 333: 2068668604
:do {
    /ip hotspot user add name="2068668604" password="89549943" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068668604";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068668604";
};

# المستخدم 334: 2007308767
:do {
    /ip hotspot user add name="2007308767" password="78327603" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007308767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007308767";
};

# المستخدم 335: 2092254282
:do {
    /ip hotspot user add name="2092254282" password="98052560" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092254282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092254282";
};

# المستخدم 336: 2075882778
:do {
    /ip hotspot user add name="2075882778" password="37911199" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075882778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075882778";
};

# المستخدم 337: 2074225538
:do {
    /ip hotspot user add name="2074225538" password="99052143" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074225538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074225538";
};

# المستخدم 338: 2078536011
:do {
    /ip hotspot user add name="2078536011" password="60460674" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078536011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078536011";
};

# المستخدم 339: 2053150529
:do {
    /ip hotspot user add name="2053150529" password="36801784" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053150529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053150529";
};

# المستخدم 340: 2077682514
:do {
    /ip hotspot user add name="2077682514" password="69265062" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077682514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077682514";
};

# المستخدم 341: 2015341274
:do {
    /ip hotspot user add name="2015341274" password="74872155" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015341274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015341274";
};

# المستخدم 342: 2047212799
:do {
    /ip hotspot user add name="2047212799" password="17374337" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047212799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047212799";
};

# المستخدم 343: 2013931779
:do {
    /ip hotspot user add name="2013931779" password="12685748" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013931779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013931779";
};

# المستخدم 344: 2086725546
:do {
    /ip hotspot user add name="2086725546" password="58850678" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086725546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086725546";
};

# المستخدم 345: 2078175505
:do {
    /ip hotspot user add name="2078175505" password="11736548" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078175505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078175505";
};

# المستخدم 346: 2047384173
:do {
    /ip hotspot user add name="2047384173" password="37668306" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047384173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047384173";
};

# المستخدم 347: 2086839225
:do {
    /ip hotspot user add name="2086839225" password="26093371" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086839225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086839225";
};

# المستخدم 348: 2069193338
:do {
    /ip hotspot user add name="2069193338" password="70162202" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069193338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069193338";
};

# المستخدم 349: 2049410757
:do {
    /ip hotspot user add name="2049410757" password="13835013" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049410757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049410757";
};

# المستخدم 350: 2054992115
:do {
    /ip hotspot user add name="2054992115" password="49799596" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054992115";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054992115";
};

# المستخدم 351: 2048317060
:do {
    /ip hotspot user add name="2048317060" password="15119561" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048317060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048317060";
};

# المستخدم 352: 2045582874
:do {
    /ip hotspot user add name="2045582874" password="03508341" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045582874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045582874";
};

# المستخدم 353: 2009573346
:do {
    /ip hotspot user add name="2009573346" password="27279100" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009573346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009573346";
};

# المستخدم 354: 2065207400
:do {
    /ip hotspot user add name="2065207400" password="30751615" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065207400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065207400";
};

# المستخدم 355: 2011746731
:do {
    /ip hotspot user add name="2011746731" password="84901057" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011746731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011746731";
};

# المستخدم 356: 2054946465
:do {
    /ip hotspot user add name="2054946465" password="43939398" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054946465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054946465";
};

# المستخدم 357: 2023174448
:do {
    /ip hotspot user add name="2023174448" password="15965722" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023174448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023174448";
};

# المستخدم 358: 2066260426
:do {
    /ip hotspot user add name="2066260426" password="42903928" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066260426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066260426";
};

# المستخدم 359: 2054306074
:do {
    /ip hotspot user add name="2054306074" password="36564851" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054306074";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054306074";
};

# المستخدم 360: 2035562956
:do {
    /ip hotspot user add name="2035562956" password="21400511" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035562956";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035562956";
};

# المستخدم 361: 2069934019
:do {
    /ip hotspot user add name="2069934019" password="23561059" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069934019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069934019";
};

# المستخدم 362: 2059830433
:do {
    /ip hotspot user add name="2059830433" password="12222438" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059830433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059830433";
};

# المستخدم 363: 2089171365
:do {
    /ip hotspot user add name="2089171365" password="81613383" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089171365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089171365";
};

# المستخدم 364: 2084798275
:do {
    /ip hotspot user add name="2084798275" password="09381747" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084798275";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084798275";
};

# المستخدم 365: 2056173579
:do {
    /ip hotspot user add name="2056173579" password="61119871" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056173579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056173579";
};

# المستخدم 366: 2044763583
:do {
    /ip hotspot user add name="2044763583" password="66590895" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044763583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044763583";
};

# المستخدم 367: 2014797731
:do {
    /ip hotspot user add name="2014797731" password="06468556" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014797731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014797731";
};

# المستخدم 368: 2071074459
:do {
    /ip hotspot user add name="2071074459" password="73173859" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071074459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071074459";
};

# المستخدم 369: 2083197324
:do {
    /ip hotspot user add name="2083197324" password="11347161" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083197324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083197324";
};

# المستخدم 370: 2071002696
:do {
    /ip hotspot user add name="2071002696" password="01056302" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071002696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071002696";
};

# المستخدم 371: 2014417209
:do {
    /ip hotspot user add name="2014417209" password="58787575" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014417209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014417209";
};

# المستخدم 372: 2081728244
:do {
    /ip hotspot user add name="2081728244" password="35683026" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081728244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081728244";
};

# المستخدم 373: 2008022061
:do {
    /ip hotspot user add name="2008022061" password="22532361" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008022061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008022061";
};

# المستخدم 374: 2018237798
:do {
    /ip hotspot user add name="2018237798" password="91753967" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018237798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018237798";
};

# المستخدم 375: 2002391829
:do {
    /ip hotspot user add name="2002391829" password="74678471" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002391829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002391829";
};

# المستخدم 376: 2051624031
:do {
    /ip hotspot user add name="2051624031" password="67291485" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051624031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051624031";
};

# المستخدم 377: 2078403371
:do {
    /ip hotspot user add name="2078403371" password="51375200" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078403371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078403371";
};

# المستخدم 378: 2069339192
:do {
    /ip hotspot user add name="2069339192" password="26610518" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069339192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069339192";
};

# المستخدم 379: 2071395665
:do {
    /ip hotspot user add name="2071395665" password="89753511" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071395665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071395665";
};

# المستخدم 380: 2073785887
:do {
    /ip hotspot user add name="2073785887" password="70625726" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073785887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073785887";
};

# المستخدم 381: 2086394673
:do {
    /ip hotspot user add name="2086394673" password="51851003" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086394673";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086394673";
};

# المستخدم 382: 2024307497
:do {
    /ip hotspot user add name="2024307497" password="94163559" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024307497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024307497";
};

# المستخدم 383: 2052967227
:do {
    /ip hotspot user add name="2052967227" password="43392136" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052967227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052967227";
};

# المستخدم 384: 2037239876
:do {
    /ip hotspot user add name="2037239876" password="11614654" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037239876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037239876";
};

# المستخدم 385: 2023415266
:do {
    /ip hotspot user add name="2023415266" password="63932032" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023415266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023415266";
};

# المستخدم 386: 2041342505
:do {
    /ip hotspot user add name="2041342505" password="08522343" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041342505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041342505";
};

# المستخدم 387: 2046131685
:do {
    /ip hotspot user add name="2046131685" password="75859155" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046131685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046131685";
};

# المستخدم 388: 2051358806
:do {
    /ip hotspot user add name="2051358806" password="96934321" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051358806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051358806";
};

# المستخدم 389: 2018696132
:do {
    /ip hotspot user add name="2018696132" password="38312944" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018696132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018696132";
};

# المستخدم 390: 2054825881
:do {
    /ip hotspot user add name="2054825881" password="25081900" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054825881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054825881";
};

# المستخدم 391: 2043654206
:do {
    /ip hotspot user add name="2043654206" password="42714619" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043654206";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043654206";
};

# المستخدم 392: 2090659952
:do {
    /ip hotspot user add name="2090659952" password="69470680" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090659952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090659952";
};

# المستخدم 393: 2040772107
:do {
    /ip hotspot user add name="2040772107" password="90794349" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040772107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040772107";
};

# المستخدم 394: 2002580099
:do {
    /ip hotspot user add name="2002580099" password="02487360" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002580099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002580099";
};

# المستخدم 395: 2033748723
:do {
    /ip hotspot user add name="2033748723" password="84445039" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033748723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033748723";
};

# المستخدم 396: 2008695795
:do {
    /ip hotspot user add name="2008695795" password="56498752" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008695795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008695795";
};

# المستخدم 397: 2048793865
:do {
    /ip hotspot user add name="2048793865" password="51260237" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048793865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048793865";
};

# المستخدم 398: 2019485823
:do {
    /ip hotspot user add name="2019485823" password="30381452" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019485823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019485823";
};

# المستخدم 399: 2084322134
:do {
    /ip hotspot user add name="2084322134" password="71479801" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084322134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084322134";
};

# المستخدم 400: 2069761077
:do {
    /ip hotspot user add name="2069761077" password="66311112" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069761077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069761077";
};

# المستخدم 401: 2028776661
:do {
    /ip hotspot user add name="2028776661" password="97206767" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028776661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028776661";
};

# المستخدم 402: 2092453620
:do {
    /ip hotspot user add name="2092453620" password="48296069" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092453620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092453620";
};

# المستخدم 403: 2084552138
:do {
    /ip hotspot user add name="2084552138" password="75895433" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084552138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084552138";
};

# المستخدم 404: 2063874479
:do {
    /ip hotspot user add name="2063874479" password="91177576" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063874479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063874479";
};

# المستخدم 405: 2085083794
:do {
    /ip hotspot user add name="2085083794" password="08406882" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085083794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085083794";
};

# المستخدم 406: 2022334610
:do {
    /ip hotspot user add name="2022334610" password="36678844" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022334610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022334610";
};

# المستخدم 407: 2031852494
:do {
    /ip hotspot user add name="2031852494" password="95619080" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031852494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031852494";
};

# المستخدم 408: 2010111922
:do {
    /ip hotspot user add name="2010111922" password="06723340" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010111922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010111922";
};

# المستخدم 409: 2072698398
:do {
    /ip hotspot user add name="2072698398" password="79900877" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072698398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072698398";
};

# المستخدم 410: 2069043181
:do {
    /ip hotspot user add name="2069043181" password="34662276" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069043181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069043181";
};

# المستخدم 411: 2057310600
:do {
    /ip hotspot user add name="2057310600" password="78359531" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057310600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057310600";
};

# المستخدم 412: 2001706372
:do {
    /ip hotspot user add name="2001706372" password="32823155" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001706372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001706372";
};

# المستخدم 413: 2079337600
:do {
    /ip hotspot user add name="2079337600" password="67800954" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079337600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079337600";
};

# المستخدم 414: 2046703448
:do {
    /ip hotspot user add name="2046703448" password="99088196" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046703448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046703448";
};

# المستخدم 415: 2044072049
:do {
    /ip hotspot user add name="2044072049" password="18299820" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044072049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044072049";
};

# المستخدم 416: 2021880069
:do {
    /ip hotspot user add name="2021880069" password="87037695" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021880069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021880069";
};

# المستخدم 417: 2025470828
:do {
    /ip hotspot user add name="2025470828" password="76291032" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025470828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025470828";
};

# المستخدم 418: 2029543499
:do {
    /ip hotspot user add name="2029543499" password="19695143" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029543499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029543499";
};

# المستخدم 419: 2035051274
:do {
    /ip hotspot user add name="2035051274" password="90817269" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035051274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035051274";
};

# المستخدم 420: 2032191023
:do {
    /ip hotspot user add name="2032191023" password="70512424" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032191023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032191023";
};

# المستخدم 421: 2055612449
:do {
    /ip hotspot user add name="2055612449" password="78419339" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055612449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055612449";
};

# المستخدم 422: 2012903811
:do {
    /ip hotspot user add name="2012903811" password="70747112" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012903811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012903811";
};

# المستخدم 423: 2010918486
:do {
    /ip hotspot user add name="2010918486" password="50574900" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010918486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010918486";
};

# المستخدم 424: 2058744682
:do {
    /ip hotspot user add name="2058744682" password="87886824" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058744682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058744682";
};

# المستخدم 425: 2046288898
:do {
    /ip hotspot user add name="2046288898" password="37517732" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046288898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046288898";
};

# المستخدم 426: 2080879662
:do {
    /ip hotspot user add name="2080879662" password="87261226" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080879662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080879662";
};

# المستخدم 427: 2006143453
:do {
    /ip hotspot user add name="2006143453" password="80475223" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006143453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006143453";
};

# المستخدم 428: 2087711765
:do {
    /ip hotspot user add name="2087711765" password="65839450" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087711765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087711765";
};

# المستخدم 429: 2007289559
:do {
    /ip hotspot user add name="2007289559" password="19058390" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007289559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007289559";
};

# المستخدم 430: 2023080043
:do {
    /ip hotspot user add name="2023080043" password="53473004" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023080043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023080043";
};

# المستخدم 431: 2086830310
:do {
    /ip hotspot user add name="2086830310" password="27293923" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086830310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086830310";
};

# المستخدم 432: 2049014631
:do {
    /ip hotspot user add name="2049014631" password="16031408" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049014631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049014631";
};

# المستخدم 433: 2006756763
:do {
    /ip hotspot user add name="2006756763" password="78705974" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006756763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006756763";
};

# المستخدم 434: 2091957348
:do {
    /ip hotspot user add name="2091957348" password="02043752" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091957348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091957348";
};

# المستخدم 435: 2037565155
:do {
    /ip hotspot user add name="2037565155" password="26433029" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037565155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037565155";
};

# المستخدم 436: 2065309154
:do {
    /ip hotspot user add name="2065309154" password="76897133" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065309154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065309154";
};

# المستخدم 437: 2014869119
:do {
    /ip hotspot user add name="2014869119" password="59450728" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014869119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014869119";
};

# المستخدم 438: 2034635836
:do {
    /ip hotspot user add name="2034635836" password="50886628" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034635836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034635836";
};

# المستخدم 439: 2097107492
:do {
    /ip hotspot user add name="2097107492" password="51544413" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097107492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097107492";
};

# المستخدم 440: 2049317488
:do {
    /ip hotspot user add name="2049317488" password="92982309" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049317488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049317488";
};

# المستخدم 441: 2061753158
:do {
    /ip hotspot user add name="2061753158" password="01802837" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061753158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061753158";
};

# المستخدم 442: 2017629033
:do {
    /ip hotspot user add name="2017629033" password="55843735" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017629033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017629033";
};

# المستخدم 443: 2049198780
:do {
    /ip hotspot user add name="2049198780" password="83247167" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049198780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049198780";
};

# المستخدم 444: 2092849610
:do {
    /ip hotspot user add name="2092849610" password="10554581" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092849610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092849610";
};

# المستخدم 445: 2058883503
:do {
    /ip hotspot user add name="2058883503" password="46951598" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058883503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058883503";
};

# المستخدم 446: 2016967632
:do {
    /ip hotspot user add name="2016967632" password="05787014" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016967632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016967632";
};

# المستخدم 447: 2001018088
:do {
    /ip hotspot user add name="2001018088" password="98029265" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001018088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001018088";
};

# المستخدم 448: 2062591712
:do {
    /ip hotspot user add name="2062591712" password="23219904" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062591712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062591712";
};

# المستخدم 449: 2056650753
:do {
    /ip hotspot user add name="2056650753" password="96929766" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056650753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056650753";
};

# المستخدم 450: 2037648439
:do {
    /ip hotspot user add name="2037648439" password="82153551" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037648439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037648439";
};

# المستخدم 451: 2086570285
:do {
    /ip hotspot user add name="2086570285" password="96788628" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086570285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086570285";
};

# المستخدم 452: 2075100013
:do {
    /ip hotspot user add name="2075100013" password="01940552" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075100013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075100013";
};

# المستخدم 453: 2032558236
:do {
    /ip hotspot user add name="2032558236" password="01023915" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032558236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032558236";
};

# المستخدم 454: 2018675154
:do {
    /ip hotspot user add name="2018675154" password="36453632" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018675154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018675154";
};

# المستخدم 455: 2003137397
:do {
    /ip hotspot user add name="2003137397" password="21417653" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003137397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003137397";
};

# المستخدم 456: 2034230164
:do {
    /ip hotspot user add name="2034230164" password="82266450" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034230164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034230164";
};

# المستخدم 457: 2053903913
:do {
    /ip hotspot user add name="2053903913" password="95970405" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053903913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053903913";
};

# المستخدم 458: 2030213716
:do {
    /ip hotspot user add name="2030213716" password="95950509" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030213716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030213716";
};

# المستخدم 459: 2004826449
:do {
    /ip hotspot user add name="2004826449" password="22905700" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004826449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004826449";
};

# المستخدم 460: 2001580264
:do {
    /ip hotspot user add name="2001580264" password="77569818" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001580264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001580264";
};

# المستخدم 461: 2067827875
:do {
    /ip hotspot user add name="2067827875" password="41884653" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067827875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067827875";
};

# المستخدم 462: 2049747697
:do {
    /ip hotspot user add name="2049747697" password="05054941" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049747697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049747697";
};

# المستخدم 463: 2003570466
:do {
    /ip hotspot user add name="2003570466" password="96770440" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003570466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003570466";
};

# المستخدم 464: 2044705171
:do {
    /ip hotspot user add name="2044705171" password="87642167" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044705171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044705171";
};

# المستخدم 465: 2040862000
:do {
    /ip hotspot user add name="2040862000" password="30872742" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040862000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040862000";
};

# المستخدم 466: 2024139400
:do {
    /ip hotspot user add name="2024139400" password="10155775" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024139400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024139400";
};

# المستخدم 467: 2025504777
:do {
    /ip hotspot user add name="2025504777" password="41877498" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025504777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025504777";
};

# المستخدم 468: 2015644751
:do {
    /ip hotspot user add name="2015644751" password="03068552" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015644751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015644751";
};

# المستخدم 469: 2039038348
:do {
    /ip hotspot user add name="2039038348" password="26413250" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039038348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039038348";
};

# المستخدم 470: 2005749645
:do {
    /ip hotspot user add name="2005749645" password="84310363" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005749645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005749645";
};

# المستخدم 471: 2007836028
:do {
    /ip hotspot user add name="2007836028" password="29452015" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007836028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007836028";
};

# المستخدم 472: 2092336425
:do {
    /ip hotspot user add name="2092336425" password="69727836" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092336425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092336425";
};

# المستخدم 473: 2049806208
:do {
    /ip hotspot user add name="2049806208" password="99013880" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049806208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049806208";
};

# المستخدم 474: 2019408059
:do {
    /ip hotspot user add name="2019408059" password="46975314" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019408059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019408059";
};

# المستخدم 475: 2063587498
:do {
    /ip hotspot user add name="2063587498" password="53870113" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063587498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063587498";
};

# المستخدم 476: 2043404309
:do {
    /ip hotspot user add name="2043404309" password="44089341" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043404309";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043404309";
};

# المستخدم 477: 2087454281
:do {
    /ip hotspot user add name="2087454281" password="91852260" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087454281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087454281";
};

# المستخدم 478: 2025915764
:do {
    /ip hotspot user add name="2025915764" password="01429748" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025915764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025915764";
};

# المستخدم 479: 2031592439
:do {
    /ip hotspot user add name="2031592439" password="87112096" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031592439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031592439";
};

# المستخدم 480: 2056691610
:do {
    /ip hotspot user add name="2056691610" password="76520492" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056691610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056691610";
};

# المستخدم 481: 2036392575
:do {
    /ip hotspot user add name="2036392575" password="54250495" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036392575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036392575";
};

# المستخدم 482: 2037208909
:do {
    /ip hotspot user add name="2037208909" password="20042166" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037208909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037208909";
};

# المستخدم 483: 2003747143
:do {
    /ip hotspot user add name="2003747143" password="18032507" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003747143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003747143";
};

# المستخدم 484: 2093783919
:do {
    /ip hotspot user add name="2093783919" password="47978697" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093783919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093783919";
};

# المستخدم 485: 2064815512
:do {
    /ip hotspot user add name="2064815512" password="50498776" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064815512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064815512";
};

# المستخدم 486: 2034379055
:do {
    /ip hotspot user add name="2034379055" password="58122881" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034379055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034379055";
};

# المستخدم 487: 2080123668
:do {
    /ip hotspot user add name="2080123668" password="22630789" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080123668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080123668";
};

# المستخدم 488: 2027282996
:do {
    /ip hotspot user add name="2027282996" password="51328575" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027282996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027282996";
};

# المستخدم 489: 2028416470
:do {
    /ip hotspot user add name="2028416470" password="97782011" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028416470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028416470";
};

# المستخدم 490: 2059650498
:do {
    /ip hotspot user add name="2059650498" password="51308676" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059650498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059650498";
};

# المستخدم 491: 2029548192
:do {
    /ip hotspot user add name="2029548192" password="31266630" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029548192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029548192";
};

# المستخدم 492: 2021039885
:do {
    /ip hotspot user add name="2021039885" password="25476529" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021039885";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021039885";
};

# المستخدم 493: 2033914922
:do {
    /ip hotspot user add name="2033914922" password="07865615" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033914922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033914922";
};

# المستخدم 494: 2078496227
:do {
    /ip hotspot user add name="2078496227" password="37682788" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078496227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078496227";
};

# المستخدم 495: 2095390873
:do {
    /ip hotspot user add name="2095390873" password="77927581" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095390873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095390873";
};

# المستخدم 496: 2037293738
:do {
    /ip hotspot user add name="2037293738" password="35517811" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037293738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037293738";
};

# المستخدم 497: 2016239718
:do {
    /ip hotspot user add name="2016239718" password="20175692" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016239718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016239718";
};

# المستخدم 498: 2011430855
:do {
    /ip hotspot user add name="2011430855" password="62170379" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011430855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011430855";
};

# المستخدم 499: 2086827439
:do {
    /ip hotspot user add name="2086827439" password="03583206" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086827439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086827439";
};

# المستخدم 500: 2056401081
:do {
    /ip hotspot user add name="2056401081" password="11185427" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056401081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056401081";
};

# المستخدم 501: 2074252954
:do {
    /ip hotspot user add name="2074252954" password="39059522" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074252954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074252954";
};

# المستخدم 502: 2098069899
:do {
    /ip hotspot user add name="2098069899" password="50584715" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098069899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098069899";
};

# المستخدم 503: 2059552338
:do {
    /ip hotspot user add name="2059552338" password="39997561" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059552338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059552338";
};

# المستخدم 504: 2003881698
:do {
    /ip hotspot user add name="2003881698" password="08348933" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003881698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003881698";
};

# المستخدم 505: 2040144495
:do {
    /ip hotspot user add name="2040144495" password="04391651" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040144495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040144495";
};

# المستخدم 506: 2066533495
:do {
    /ip hotspot user add name="2066533495" password="84581008" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066533495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066533495";
};

# المستخدم 507: 2073503664
:do {
    /ip hotspot user add name="2073503664" password="86772384" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073503664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073503664";
};

# المستخدم 508: 2001882726
:do {
    /ip hotspot user add name="2001882726" password="03124736" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001882726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001882726";
};

# المستخدم 509: 2020834208
:do {
    /ip hotspot user add name="2020834208" password="56186820" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020834208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020834208";
};

# المستخدم 510: 2031225486
:do {
    /ip hotspot user add name="2031225486" password="73383193" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031225486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031225486";
};

# المستخدم 511: 2041753409
:do {
    /ip hotspot user add name="2041753409" password="46820001" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041753409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041753409";
};

# المستخدم 512: 2075423862
:do {
    /ip hotspot user add name="2075423862" password="73107640" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075423862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075423862";
};

# المستخدم 513: 2071654706
:do {
    /ip hotspot user add name="2071654706" password="97310190" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071654706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071654706";
};

# المستخدم 514: 2082103134
:do {
    /ip hotspot user add name="2082103134" password="90047149" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082103134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082103134";
};

# المستخدم 515: 2079407419
:do {
    /ip hotspot user add name="2079407419" password="18803883" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079407419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079407419";
};

# المستخدم 516: 2025900466
:do {
    /ip hotspot user add name="2025900466" password="88286015" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025900466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025900466";
};

# المستخدم 517: 2056102919
:do {
    /ip hotspot user add name="2056102919" password="67573089" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056102919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056102919";
};

# المستخدم 518: 2059094315
:do {
    /ip hotspot user add name="2059094315" password="33173466" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059094315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059094315";
};

# المستخدم 519: 2017110957
:do {
    /ip hotspot user add name="2017110957" password="17789493" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017110957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017110957";
};

# المستخدم 520: 2082918143
:do {
    /ip hotspot user add name="2082918143" password="71484232" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082918143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082918143";
};

# المستخدم 521: 2091112138
:do {
    /ip hotspot user add name="2091112138" password="39493063" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091112138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091112138";
};

# المستخدم 522: 2033077708
:do {
    /ip hotspot user add name="2033077708" password="37277448" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033077708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033077708";
};

# المستخدم 523: 2090438413
:do {
    /ip hotspot user add name="2090438413" password="41738366" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090438413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090438413";
};

# المستخدم 524: 2093276677
:do {
    /ip hotspot user add name="2093276677" password="14461232" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093276677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093276677";
};

# المستخدم 525: 2068955886
:do {
    /ip hotspot user add name="2068955886" password="58927972" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068955886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068955886";
};

# المستخدم 526: 2024013524
:do {
    /ip hotspot user add name="2024013524" password="13861139" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024013524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024013524";
};

# المستخدم 527: 2040437601
:do {
    /ip hotspot user add name="2040437601" password="79455158" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040437601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040437601";
};

# المستخدم 528: 2083896479
:do {
    /ip hotspot user add name="2083896479" password="86683983" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083896479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083896479";
};

# المستخدم 529: 2089659633
:do {
    /ip hotspot user add name="2089659633" password="51258566" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089659633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089659633";
};

# المستخدم 530: 2097975922
:do {
    /ip hotspot user add name="2097975922" password="96413400" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097975922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097975922";
};

# المستخدم 531: 2015721245
:do {
    /ip hotspot user add name="2015721245" password="89474357" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015721245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015721245";
};

# المستخدم 532: 2089735000
:do {
    /ip hotspot user add name="2089735000" password="10614839" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089735000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089735000";
};

# المستخدم 533: 2028386034
:do {
    /ip hotspot user add name="2028386034" password="86202031" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028386034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028386034";
};

# المستخدم 534: 2050274396
:do {
    /ip hotspot user add name="2050274396" password="39644908" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050274396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050274396";
};

# المستخدم 535: 2054979239
:do {
    /ip hotspot user add name="2054979239" password="30326632" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054979239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054979239";
};

# المستخدم 536: 2018961819
:do {
    /ip hotspot user add name="2018961819" password="33160709" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018961819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018961819";
};

# المستخدم 537: 2035075438
:do {
    /ip hotspot user add name="2035075438" password="36874112" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035075438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035075438";
};

# المستخدم 538: 2055063382
:do {
    /ip hotspot user add name="2055063382" password="48572542" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055063382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055063382";
};

# المستخدم 539: 2064722910
:do {
    /ip hotspot user add name="2064722910" password="34024558" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064722910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064722910";
};

# المستخدم 540: 2095606876
:do {
    /ip hotspot user add name="2095606876" password="12313069" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095606876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095606876";
};

# المستخدم 541: 2008640233
:do {
    /ip hotspot user add name="2008640233" password="23247012" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008640233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008640233";
};

# المستخدم 542: 2099091126
:do {
    /ip hotspot user add name="2099091126" password="15690092" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099091126";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099091126";
};

# المستخدم 543: 2085195388
:do {
    /ip hotspot user add name="2085195388" password="75906975" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085195388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085195388";
};

# المستخدم 544: 2090252582
:do {
    /ip hotspot user add name="2090252582" password="04761612" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090252582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090252582";
};

# المستخدم 545: 2083385837
:do {
    /ip hotspot user add name="2083385837" password="14037887" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083385837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083385837";
};

# المستخدم 546: 2002964177
:do {
    /ip hotspot user add name="2002964177" password="87706544" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002964177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002964177";
};

# المستخدم 547: 2068987468
:do {
    /ip hotspot user add name="2068987468" password="20506968" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068987468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068987468";
};

# المستخدم 548: 2012587937
:do {
    /ip hotspot user add name="2012587937" password="45952123" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012587937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012587937";
};

# المستخدم 549: 2010717862
:do {
    /ip hotspot user add name="2010717862" password="03731473" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010717862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010717862";
};

# المستخدم 550: 2093470380
:do {
    /ip hotspot user add name="2093470380" password="92759624" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093470380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093470380";
};

# المستخدم 551: 2025627348
:do {
    /ip hotspot user add name="2025627348" password="49465278" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025627348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025627348";
};

# المستخدم 552: 2076946161
:do {
    /ip hotspot user add name="2076946161" password="97693193" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076946161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076946161";
};

# المستخدم 553: 2098499132
:do {
    /ip hotspot user add name="2098499132" password="78175346" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098499132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098499132";
};

# المستخدم 554: 2053688389
:do {
    /ip hotspot user add name="2053688389" password="55084813" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053688389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053688389";
};

# المستخدم 555: 2020220558
:do {
    /ip hotspot user add name="2020220558" password="12460561" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020220558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020220558";
};

# المستخدم 556: 2031689951
:do {
    /ip hotspot user add name="2031689951" password="78257464" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031689951";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031689951";
};

# المستخدم 557: 2002545321
:do {
    /ip hotspot user add name="2002545321" password="75768237" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002545321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002545321";
};

# المستخدم 558: 2042290464
:do {
    /ip hotspot user add name="2042290464" password="24867474" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042290464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042290464";
};

# المستخدم 559: 2000699752
:do {
    /ip hotspot user add name="2000699752" password="00065168" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000699752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000699752";
};

# المستخدم 560: 2061993037
:do {
    /ip hotspot user add name="2061993037" password="37263003" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061993037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061993037";
};

# المستخدم 561: 2010016836
:do {
    /ip hotspot user add name="2010016836" password="91381107" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010016836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010016836";
};

# المستخدم 562: 2089810296
:do {
    /ip hotspot user add name="2089810296" password="81993028" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089810296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089810296";
};

# المستخدم 563: 2099553680
:do {
    /ip hotspot user add name="2099553680" password="60778294" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099553680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099553680";
};

# المستخدم 564: 2039445218
:do {
    /ip hotspot user add name="2039445218" password="09197599" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039445218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039445218";
};

# المستخدم 565: 2042432844
:do {
    /ip hotspot user add name="2042432844" password="66933655" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042432844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042432844";
};

# المستخدم 566: 2088362721
:do {
    /ip hotspot user add name="2088362721" password="36109989" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088362721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088362721";
};

# المستخدم 567: 2091865341
:do {
    /ip hotspot user add name="2091865341" password="61349326" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091865341";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091865341";
};

# المستخدم 568: 2065812200
:do {
    /ip hotspot user add name="2065812200" password="18577324" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065812200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065812200";
};

# المستخدم 569: 2011541862
:do {
    /ip hotspot user add name="2011541862" password="35762175" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011541862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011541862";
};

# المستخدم 570: 2051313366
:do {
    /ip hotspot user add name="2051313366" password="16349350" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051313366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051313366";
};

# المستخدم 571: 2038294444
:do {
    /ip hotspot user add name="2038294444" password="70835423" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038294444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038294444";
};

# المستخدم 572: 2051497162
:do {
    /ip hotspot user add name="2051497162" password="37109873" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051497162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051497162";
};

# المستخدم 573: 2080120571
:do {
    /ip hotspot user add name="2080120571" password="25594308" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080120571";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080120571";
};

# المستخدم 574: 2011726018
:do {
    /ip hotspot user add name="2011726018" password="97688869" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011726018";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011726018";
};

# المستخدم 575: 2083448004
:do {
    /ip hotspot user add name="2083448004" password="20439431" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083448004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083448004";
};

# المستخدم 576: 2084794269
:do {
    /ip hotspot user add name="2084794269" password="05937537" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084794269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084794269";
};

# المستخدم 577: 2083873999
:do {
    /ip hotspot user add name="2083873999" password="61987394" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083873999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083873999";
};

# المستخدم 578: 2009460536
:do {
    /ip hotspot user add name="2009460536" password="31475587" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009460536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009460536";
};

# المستخدم 579: 2000208935
:do {
    /ip hotspot user add name="2000208935" password="01634189" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000208935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000208935";
};

# المستخدم 580: 2053638784
:do {
    /ip hotspot user add name="2053638784" password="60052397" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053638784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053638784";
};

# المستخدم 581: 2067518754
:do {
    /ip hotspot user add name="2067518754" password="20484420" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067518754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067518754";
};

# المستخدم 582: 2068959705
:do {
    /ip hotspot user add name="2068959705" password="58568288" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068959705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068959705";
};

# المستخدم 583: 2095617389
:do {
    /ip hotspot user add name="2095617389" password="19908138" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095617389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095617389";
};

# المستخدم 584: 2043531481
:do {
    /ip hotspot user add name="2043531481" password="89715619" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043531481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043531481";
};

# المستخدم 585: 2071972369
:do {
    /ip hotspot user add name="2071972369" password="30082590" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071972369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071972369";
};

# المستخدم 586: 2066776077
:do {
    /ip hotspot user add name="2066776077" password="82268385" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066776077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066776077";
};

# المستخدم 587: 2016579283
:do {
    /ip hotspot user add name="2016579283" password="95834100" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016579283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016579283";
};

# المستخدم 588: 2027334519
:do {
    /ip hotspot user add name="2027334519" password="87247152" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027334519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027334519";
};

# المستخدم 589: 2072260324
:do {
    /ip hotspot user add name="2072260324" password="88579548" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072260324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072260324";
};

# المستخدم 590: 2094095015
:do {
    /ip hotspot user add name="2094095015" password="55517760" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094095015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094095015";
};

# المستخدم 591: 2041745087
:do {
    /ip hotspot user add name="2041745087" password="00607493" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041745087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041745087";
};

# المستخدم 592: 2051289515
:do {
    /ip hotspot user add name="2051289515" password="45617174" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051289515";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051289515";
};

# المستخدم 593: 2088446189
:do {
    /ip hotspot user add name="2088446189" password="49508493" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088446189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088446189";
};

# المستخدم 594: 2022789211
:do {
    /ip hotspot user add name="2022789211" password="52982426" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022789211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022789211";
};

# المستخدم 595: 2099959544
:do {
    /ip hotspot user add name="2099959544" password="39924053" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099959544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099959544";
};

# المستخدم 596: 2021600288
:do {
    /ip hotspot user add name="2021600288" password="20206798" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021600288";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021600288";
};

# المستخدم 597: 2004435566
:do {
    /ip hotspot user add name="2004435566" password="39286256" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004435566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004435566";
};

# المستخدم 598: 2050489214
:do {
    /ip hotspot user add name="2050489214" password="38416594" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050489214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050489214";
};

# المستخدم 599: 2013415289
:do {
    /ip hotspot user add name="2013415289" password="75087953" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013415289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013415289";
};

# المستخدم 600: 2058589457
:do {
    /ip hotspot user add name="2058589457" password="76617717" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058589457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058589457";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 20-marawan";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
