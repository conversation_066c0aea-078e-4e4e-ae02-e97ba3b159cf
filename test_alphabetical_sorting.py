#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة الترتيب الأبجدي للقوالب في برنامج مولد كروت MikroTik
Test Alphabetical Sorting Feature for Templates in MikroTik Card Generator

هذا الاختبار يتحقق من:
1. ترتيب القوالب أبجدياً تصاعدياً (من أ إلى ي)
2. دعم الأسماء العربية والإنجليزية معاً
3. ترتيب الأرقام قبل الحروف
4. عمل الترتيب في جميع أنواع القوالب
"""

import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch

class TestAlphabeticalSorting(unittest.TestCase):
    """اختبار ميزة الترتيب الأبجدي للقوالب"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء mock للتطبيق الرئيسي
        self.mock_app = Mock()
        
        # إضافة دالة الترتيب الأبجدي
        self.mock_app.sort_templates_alphabetically = self.sort_templates_alphabetically
        
        # إعداد logger
        self.mock_app.logger = Mock()

    def sort_templates_alphabetically(self, template_names):
        """ترتيب أسماء القوالب ترتيباً أبجدياً مع دعم العربية والإنجليزية"""
        try:
            def arabic_sort_key(name):
                """مفتاح الترتيب للنصوص العربية والإنجليزية"""
                # تحويل النص لحروف صغيرة للترتيب
                name_lower = name.lower()
                
                # إنشاء مفتاح ترتيب يضع الأرقام أولاً، ثم العربية، ثم الإنجليزية
                sort_key = []
                for char in name_lower:
                    if char.isdigit():
                        # الأرقام تأتي أولاً
                        sort_key.append((0, char))
                    elif '\u0600' <= char <= '\u06FF':
                        # الحروف العربية
                        sort_key.append((1, char))
                    elif 'a' <= char <= 'z':
                        # الحروف الإنجليزية
                        sort_key.append((2, char))
                    else:
                        # رموز أخرى
                        sort_key.append((3, char))
                
                return sort_key
            
            # ترتيب القوالب باستخدام المفتاح المخصص
            sorted_names = sorted(template_names, key=arabic_sort_key)
            
            return sorted_names
            
        except Exception as e:
            # في حالة الخطأ، إرجاع القائمة كما هي
            return template_names

    def test_arabic_names_sorting(self):
        """اختبار ترتيب الأسماء العربية"""
        
        # قائمة أسماء عربية غير مرتبة
        arabic_names = [
            "قالب_مدرسة",
            "قالب_أطفال", 
            "قالب_جامعة",
            "قالب_بيت",
            "قالب_تجاري",
            "قالب_ثانوية"
        ]
        
        # الترتيب المتوقع
        expected_order = [
            "قالب_أطفال",
            "قالب_بيت", 
            "قالب_تجاري",
            "قالب_ثانوية",
            "قالب_جامعة",
            "قالب_مدرسة"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(arabic_names)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, expected_order)
        print("✅ اختبار ترتيب الأسماء العربية نجح")

    def test_english_names_sorting(self):
        """اختبار ترتيب الأسماء الإنجليزية"""
        
        # قائمة أسماء إنجليزية غير مرتبة
        english_names = [
            "Template_School",
            "Template_Admin",
            "Template_Hotel",
            "Template_Cafe",
            "Template_Business"
        ]
        
        # الترتيب المتوقع
        expected_order = [
            "Template_Admin",
            "Template_Business",
            "Template_Cafe",
            "Template_Hotel",
            "Template_School"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(english_names)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, expected_order)
        print("✅ اختبار ترتيب الأسماء الإنجليزية نجح")

    def test_mixed_names_sorting(self):
        """اختبار ترتيب الأسماء المختلطة (عربية وإنجليزية)"""
        
        # قائمة أسماء مختلطة غير مرتبة
        mixed_names = [
            "Template_Hotel",
            "قالب_مدرسة",
            "Admin_Template",
            "قالب_أطفال",
            "Business_Template",
            "قالب_جامعة"
        ]
        
        # الترتيب المتوقع (العربية أولاً، ثم الإنجليزية)
        expected_order = [
            "قالب_أطفال",
            "قالب_جامعة", 
            "قالب_مدرسة",
            "Admin_Template",
            "Business_Template",
            "Template_Hotel"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(mixed_names)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, expected_order)
        print("✅ اختبار ترتيب الأسماء المختلطة نجح")

    def test_numeric_names_sorting(self):
        """اختبار ترتيب الأسماء التي تحتوي على أرقام"""
        
        # قائمة أسماء تحتوي على أرقام غير مرتبة
        numeric_names = [
            "قالب_10",
            "Template_5",
            "قالب_1",
            "Template_20",
            "قالب_2",
            "Template_15"
        ]
        
        # الترتيب المتوقع (الأرقام أولاً، ثم حسب القيمة الرقمية)
        expected_order = [
            "قالب_1",
            "قالب_10", 
            "قالب_2",
            "Template_15",
            "Template_20",
            "Template_5"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(numeric_names)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, expected_order)
        print("✅ اختبار ترتيب الأسماء الرقمية نجح")

    def test_case_insensitive_sorting(self):
        """اختبار الترتيب غير الحساس لحالة الأحرف"""
        
        # قائمة أسماء بحالات أحرف مختلفة
        case_names = [
            "template_hotel",
            "TEMPLATE_ADMIN", 
            "Template_Cafe",
            "TEMPLATE_BUSINESS",
            "template_school"
        ]
        
        # الترتيب المتوقع (غير حساس لحالة الأحرف)
        expected_order = [
            "TEMPLATE_ADMIN",
            "TEMPLATE_BUSINESS",
            "Template_Cafe",
            "template_hotel",
            "template_school"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(case_names)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, expected_order)
        print("✅ اختبار الترتيب غير الحساس لحالة الأحرف نجح")

    def test_empty_list_sorting(self):
        """اختبار ترتيب قائمة فارغة"""
        
        # قائمة فارغة
        empty_list = []
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(empty_list)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, [])
        print("✅ اختبار ترتيب القائمة الفارغة نجح")

    def test_single_item_sorting(self):
        """اختبار ترتيب قائمة بعنصر واحد"""
        
        # قائمة بعنصر واحد
        single_item = ["قالب_وحيد"]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(single_item)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, ["قالب_وحيد"])
        print("✅ اختبار ترتيب القائمة بعنصر واحد نجح")

    def test_duplicate_names_sorting(self):
        """اختبار ترتيب قائمة تحتوي على أسماء مكررة"""
        
        # قائمة تحتوي على أسماء مكررة
        duplicate_names = [
            "قالب_مدرسة",
            "قالب_أطفال",
            "قالب_مدرسة",  # مكرر
            "قالب_جامعة",
            "قالب_أطفال"   # مكرر
        ]
        
        # الترتيب المتوقع (مع الاحتفاظ بالمكررات)
        expected_order = [
            "قالب_أطفال",
            "قالب_أطفال",
            "قالب_جامعة",
            "قالب_مدرسة",
            "قالب_مدرسة"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(duplicate_names)
        
        # التحقق من النتيجة
        self.assertEqual(sorted_names, expected_order)
        print("✅ اختبار ترتيب الأسماء المكررة نجح")

    def test_special_characters_sorting(self):
        """اختبار ترتيب الأسماء التي تحتوي على رموز خاصة"""
        
        # قائمة أسماء تحتوي على رموز خاصة
        special_names = [
            "قالب-مدرسة",
            "قالب_أطفال",
            "قالب@جامعة",
            "قالب.بيت",
            "قالب+تجاري"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(special_names)
        
        # التحقق من أن الترتيب تم بدون أخطاء
        self.assertEqual(len(sorted_names), len(special_names))
        self.assertTrue(all(name in sorted_names for name in special_names))
        print("✅ اختبار ترتيب الأسماء بالرموز الخاصة نجح")

    def test_real_world_templates(self):
        """اختبار ترتيب قوالب حقيقية من البرنامج"""
        
        # قائمة قوالب حقيقية من البرنامج
        real_templates = [
            "10",
            "20", 
            "قالب_كافيه",
            "قالب_فندق",
            "Template_Admin",
            "قالب_مدرسة_ابتدائية",
            "5GB_Monthly",
            "قالب_جامعة_كبيرة",
            "Basic_Template"
        ]
        
        # تطبيق الترتيب
        sorted_names = self.mock_app.sort_templates_alphabetically(real_templates)
        
        # التحقق من أن الأرقام تأتي أولاً
        self.assertTrue(sorted_names[0] in ["10", "20", "5GB_Monthly"])
        
        # التحقق من أن العربية تأتي قبل الإنجليزية
        arabic_found = False
        english_found = False
        for name in sorted_names:
            if any('\u0600' <= char <= '\u06FF' for char in name):
                arabic_found = True
                if english_found:
                    self.fail("الأسماء العربية يجب أن تأتي قبل الإنجليزية")
            elif any('a' <= char.lower() <= 'z' for char in name):
                english_found = True
        
        print("✅ اختبار ترتيب القوالب الحقيقية نجح")
        print(f"الترتيب النهائي: {sorted_names}")

if __name__ == '__main__':
    print("🧪 بدء اختبارات الترتيب الأبجدي للقوالب...")
    print("=" * 50)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 50)
    print("🎉 انتهت جميع الاختبارات بنجاح!")
