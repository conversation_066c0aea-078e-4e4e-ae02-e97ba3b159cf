# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 14:20:03
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0115852852
:do {
    /tool user-manager user add customer="adm8n" username="0115852852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115852852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115852852";
};

# المستخدم 2: 0124191272
:do {
    /tool user-manager user add customer="adm8n" username="0124191272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124191272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124191272";
};

# المستخدم 3: 0141841544
:do {
    /tool user-manager user add customer="adm8n" username="0141841544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141841544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141841544";
};

# المستخدم 4: 0149478044
:do {
    /tool user-manager user add customer="adm8n" username="0149478044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149478044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149478044";
};

# المستخدم 5: 0145696596
:do {
    /tool user-manager user add customer="adm8n" username="0145696596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145696596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145696596";
};

# المستخدم 6: 0187410609
:do {
    /tool user-manager user add customer="adm8n" username="0187410609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187410609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187410609";
};

# المستخدم 7: 0112061587
:do {
    /tool user-manager user add customer="adm8n" username="0112061587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112061587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112061587";
};

# المستخدم 8: 0121616039
:do {
    /tool user-manager user add customer="adm8n" username="0121616039" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121616039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121616039";
};

# المستخدم 9: 0133522417
:do {
    /tool user-manager user add customer="adm8n" username="0133522417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133522417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133522417";
};

# المستخدم 10: 0154464985
:do {
    /tool user-manager user add customer="adm8n" username="0154464985" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154464985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154464985";
};

# المستخدم 11: 0101345362
:do {
    /tool user-manager user add customer="adm8n" username="0101345362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101345362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101345362";
};

# المستخدم 12: 0170673522
:do {
    /tool user-manager user add customer="adm8n" username="0170673522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170673522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170673522";
};

# المستخدم 13: 0185131730
:do {
    /tool user-manager user add customer="adm8n" username="0185131730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185131730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185131730";
};

# المستخدم 14: 0170090060
:do {
    /tool user-manager user add customer="adm8n" username="0170090060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170090060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170090060";
};

# المستخدم 15: 0151871494
:do {
    /tool user-manager user add customer="adm8n" username="0151871494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151871494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151871494";
};

# المستخدم 16: 0136212674
:do {
    /tool user-manager user add customer="adm8n" username="0136212674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136212674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136212674";
};

# المستخدم 17: 0133992467
:do {
    /tool user-manager user add customer="adm8n" username="0133992467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133992467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133992467";
};

# المستخدم 18: 0177400243
:do {
    /tool user-manager user add customer="adm8n" username="0177400243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177400243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177400243";
};

# المستخدم 19: 0174410560
:do {
    /tool user-manager user add customer="adm8n" username="0174410560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174410560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174410560";
};

# المستخدم 20: 0158371731
:do {
    /tool user-manager user add customer="adm8n" username="0158371731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158371731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158371731";
};

# المستخدم 21: 0154993197
:do {
    /tool user-manager user add customer="adm8n" username="0154993197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154993197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154993197";
};

# المستخدم 22: 0160229482
:do {
    /tool user-manager user add customer="adm8n" username="0160229482" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160229482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160229482";
};

# المستخدم 23: 0145801228
:do {
    /tool user-manager user add customer="adm8n" username="0145801228" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145801228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145801228";
};

# المستخدم 24: 0122578654
:do {
    /tool user-manager user add customer="adm8n" username="0122578654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122578654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122578654";
};

# المستخدم 25: 0103732136
:do {
    /tool user-manager user add customer="adm8n" username="0103732136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103732136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103732136";
};

# المستخدم 26: 0158599838
:do {
    /tool user-manager user add customer="adm8n" username="0158599838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158599838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158599838";
};

# المستخدم 27: 0154355576
:do {
    /tool user-manager user add customer="adm8n" username="0154355576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154355576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154355576";
};

# المستخدم 28: 0100541525
:do {
    /tool user-manager user add customer="adm8n" username="0100541525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100541525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100541525";
};

# المستخدم 29: 0161071743
:do {
    /tool user-manager user add customer="adm8n" username="0161071743" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161071743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161071743";
};

# المستخدم 30: 0170948383
:do {
    /tool user-manager user add customer="adm8n" username="0170948383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170948383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170948383";
};

# المستخدم 31: 0119999283
:do {
    /tool user-manager user add customer="adm8n" username="0119999283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119999283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119999283";
};

# المستخدم 32: 0122079659
:do {
    /tool user-manager user add customer="adm8n" username="0122079659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122079659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122079659";
};

# المستخدم 33: 0115498698
:do {
    /tool user-manager user add customer="adm8n" username="0115498698" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115498698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115498698";
};

# المستخدم 34: 0101337738
:do {
    /tool user-manager user add customer="adm8n" username="0101337738" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101337738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101337738";
};

# المستخدم 35: 0167251949
:do {
    /tool user-manager user add customer="adm8n" username="0167251949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167251949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167251949";
};

# المستخدم 36: 0186021587
:do {
    /tool user-manager user add customer="adm8n" username="0186021587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186021587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186021587";
};

# المستخدم 37: 0145690455
:do {
    /tool user-manager user add customer="adm8n" username="0145690455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145690455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145690455";
};

# المستخدم 38: 0146371932
:do {
    /tool user-manager user add customer="adm8n" username="0146371932" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146371932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146371932";
};

# المستخدم 39: 0180119292
:do {
    /tool user-manager user add customer="adm8n" username="0180119292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180119292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180119292";
};

# المستخدم 40: 0166500399
:do {
    /tool user-manager user add customer="adm8n" username="0166500399" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166500399";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166500399";
};

# المستخدم 41: 0120238058
:do {
    /tool user-manager user add customer="adm8n" username="0120238058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120238058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120238058";
};

# المستخدم 42: 0158263295
:do {
    /tool user-manager user add customer="adm8n" username="0158263295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158263295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158263295";
};

# المستخدم 43: 0122491128
:do {
    /tool user-manager user add customer="adm8n" username="0122491128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122491128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122491128";
};

# المستخدم 44: 0179122075
:do {
    /tool user-manager user add customer="adm8n" username="0179122075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179122075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179122075";
};

# المستخدم 45: 0101247674
:do {
    /tool user-manager user add customer="adm8n" username="0101247674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101247674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101247674";
};

# المستخدم 46: 0189471610
:do {
    /tool user-manager user add customer="adm8n" username="0189471610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189471610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189471610";
};

# المستخدم 47: 0176791773
:do {
    /tool user-manager user add customer="adm8n" username="0176791773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176791773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176791773";
};

# المستخدم 48: 0152222522
:do {
    /tool user-manager user add customer="adm8n" username="0152222522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152222522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152222522";
};

# المستخدم 49: 0120289034
:do {
    /tool user-manager user add customer="adm8n" username="0120289034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120289034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120289034";
};

# المستخدم 50: 0195522408
:do {
    /tool user-manager user add customer="adm8n" username="0195522408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195522408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195522408";
};

# المستخدم 51: 0188208066
:do {
    /tool user-manager user add customer="adm8n" username="0188208066" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188208066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188208066";
};

# المستخدم 52: 0112233289
:do {
    /tool user-manager user add customer="adm8n" username="0112233289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112233289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112233289";
};

# المستخدم 53: 0105235282
:do {
    /tool user-manager user add customer="adm8n" username="0105235282" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105235282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105235282";
};

# المستخدم 54: 0100368935
:do {
    /tool user-manager user add customer="adm8n" username="0100368935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100368935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100368935";
};

# المستخدم 55: 0191736006
:do {
    /tool user-manager user add customer="adm8n" username="0191736006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191736006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191736006";
};

# المستخدم 56: 0196219338
:do {
    /tool user-manager user add customer="adm8n" username="0196219338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196219338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196219338";
};

# المستخدم 57: 0114665569
:do {
    /tool user-manager user add customer="adm8n" username="0114665569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114665569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114665569";
};

# المستخدم 58: 0127730147
:do {
    /tool user-manager user add customer="adm8n" username="0127730147" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127730147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127730147";
};

# المستخدم 59: 0141068558
:do {
    /tool user-manager user add customer="adm8n" username="0141068558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141068558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141068558";
};

# المستخدم 60: 0102616585
:do {
    /tool user-manager user add customer="adm8n" username="0102616585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102616585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102616585";
};

# المستخدم 61: 0189908583
:do {
    /tool user-manager user add customer="adm8n" username="0189908583" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189908583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189908583";
};

# المستخدم 62: 0117163696
:do {
    /tool user-manager user add customer="adm8n" username="0117163696" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117163696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117163696";
};

# المستخدم 63: 0168601213
:do {
    /tool user-manager user add customer="adm8n" username="0168601213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168601213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168601213";
};

# المستخدم 64: 0187515535
:do {
    /tool user-manager user add customer="adm8n" username="0187515535" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187515535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187515535";
};

# المستخدم 65: 0191241868
:do {
    /tool user-manager user add customer="adm8n" username="0191241868" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191241868";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191241868";
};

# المستخدم 66: 0106077635
:do {
    /tool user-manager user add customer="adm8n" username="0106077635" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106077635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106077635";
};

# المستخدم 67: 0174481271
:do {
    /tool user-manager user add customer="adm8n" username="0174481271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174481271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174481271";
};

# المستخدم 68: 0172270517
:do {
    /tool user-manager user add customer="adm8n" username="0172270517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172270517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172270517";
};

# المستخدم 69: 0159298709
:do {
    /tool user-manager user add customer="adm8n" username="0159298709" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159298709";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159298709";
};

# المستخدم 70: 0101771623
:do {
    /tool user-manager user add customer="adm8n" username="0101771623" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101771623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101771623";
};

# المستخدم 71: 0117527680
:do {
    /tool user-manager user add customer="adm8n" username="0117527680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117527680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117527680";
};

# المستخدم 72: 0152667390
:do {
    /tool user-manager user add customer="adm8n" username="0152667390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152667390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152667390";
};

# المستخدم 73: 0107651540
:do {
    /tool user-manager user add customer="adm8n" username="0107651540" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107651540";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107651540";
};

# المستخدم 74: 0162183632
:do {
    /tool user-manager user add customer="adm8n" username="0162183632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162183632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162183632";
};

# المستخدم 75: 0170849071
:do {
    /tool user-manager user add customer="adm8n" username="0170849071" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170849071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170849071";
};

# المستخدم 76: 0161782118
:do {
    /tool user-manager user add customer="adm8n" username="0161782118" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161782118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161782118";
};

# المستخدم 77: 0171410392
:do {
    /tool user-manager user add customer="adm8n" username="0171410392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171410392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171410392";
};

# المستخدم 78: 0129207707
:do {
    /tool user-manager user add customer="adm8n" username="0129207707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129207707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129207707";
};

# المستخدم 79: 0115132310
:do {
    /tool user-manager user add customer="adm8n" username="0115132310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115132310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115132310";
};

# المستخدم 80: 0137839835
:do {
    /tool user-manager user add customer="adm8n" username="0137839835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137839835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137839835";
};

# المستخدم 81: 0155932279
:do {
    /tool user-manager user add customer="adm8n" username="0155932279" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155932279";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155932279";
};

# المستخدم 82: 0119558640
:do {
    /tool user-manager user add customer="adm8n" username="0119558640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119558640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119558640";
};

# المستخدم 83: 0123733825
:do {
    /tool user-manager user add customer="adm8n" username="0123733825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123733825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123733825";
};

# المستخدم 84: 0172228066
:do {
    /tool user-manager user add customer="adm8n" username="0172228066" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172228066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172228066";
};

# المستخدم 85: 0127439866
:do {
    /tool user-manager user add customer="adm8n" username="0127439866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127439866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127439866";
};

# المستخدم 86: 0172036007
:do {
    /tool user-manager user add customer="adm8n" username="0172036007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172036007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172036007";
};

# المستخدم 87: 0161091425
:do {
    /tool user-manager user add customer="adm8n" username="0161091425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161091425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161091425";
};

# المستخدم 88: 0179114028
:do {
    /tool user-manager user add customer="adm8n" username="0179114028" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179114028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179114028";
};

# المستخدم 89: 0112765580
:do {
    /tool user-manager user add customer="adm8n" username="0112765580" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112765580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112765580";
};

# المستخدم 90: 0100705328
:do {
    /tool user-manager user add customer="adm8n" username="0100705328" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100705328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100705328";
};

# المستخدم 91: 0177731745
:do {
    /tool user-manager user add customer="adm8n" username="0177731745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177731745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177731745";
};

# المستخدم 92: 0132788815
:do {
    /tool user-manager user add customer="adm8n" username="0132788815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132788815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132788815";
};

# المستخدم 93: 0115953487
:do {
    /tool user-manager user add customer="adm8n" username="0115953487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115953487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115953487";
};

# المستخدم 94: 0129559325
:do {
    /tool user-manager user add customer="adm8n" username="0129559325" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129559325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129559325";
};

# المستخدم 95: 0142125511
:do {
    /tool user-manager user add customer="adm8n" username="0142125511" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142125511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142125511";
};

# المستخدم 96: 0156876223
:do {
    /tool user-manager user add customer="adm8n" username="0156876223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156876223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156876223";
};

# المستخدم 97: 0109260092
:do {
    /tool user-manager user add customer="adm8n" username="0109260092" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109260092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109260092";
};

# المستخدم 98: 0145965449
:do {
    /tool user-manager user add customer="adm8n" username="0145965449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145965449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145965449";
};

# المستخدم 99: 0111340641
:do {
    /tool user-manager user add customer="adm8n" username="0111340641" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111340641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111340641";
};

# المستخدم 100: 0150487801
:do {
    /tool user-manager user add customer="adm8n" username="0150487801" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150487801";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150487801";
};

# المستخدم 101: 0144079379
:do {
    /tool user-manager user add customer="adm8n" username="0144079379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144079379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144079379";
};

# المستخدم 102: 0118141056
:do {
    /tool user-manager user add customer="adm8n" username="0118141056" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118141056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118141056";
};

# المستخدم 103: 0173573665
:do {
    /tool user-manager user add customer="adm8n" username="0173573665" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173573665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173573665";
};

# المستخدم 104: 0183754568
:do {
    /tool user-manager user add customer="adm8n" username="0183754568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183754568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183754568";
};

# المستخدم 105: 0130212083
:do {
    /tool user-manager user add customer="adm8n" username="0130212083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130212083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130212083";
};

# المستخدم 106: 0117827827
:do {
    /tool user-manager user add customer="adm8n" username="0117827827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117827827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117827827";
};

# المستخدم 107: 0170332582
:do {
    /tool user-manager user add customer="adm8n" username="0170332582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170332582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170332582";
};

# المستخدم 108: 0199606140
:do {
    /tool user-manager user add customer="adm8n" username="0199606140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199606140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199606140";
};

# المستخدم 109: 0131620417
:do {
    /tool user-manager user add customer="adm8n" username="0131620417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131620417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131620417";
};

# المستخدم 110: 0174616954
:do {
    /tool user-manager user add customer="adm8n" username="0174616954" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174616954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174616954";
};

# المستخدم 111: 0149261773
:do {
    /tool user-manager user add customer="adm8n" username="0149261773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149261773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149261773";
};

# المستخدم 112: 0184490273
:do {
    /tool user-manager user add customer="adm8n" username="0184490273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184490273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184490273";
};

# المستخدم 113: 0184045480
:do {
    /tool user-manager user add customer="adm8n" username="0184045480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184045480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184045480";
};

# المستخدم 114: 0148391048
:do {
    /tool user-manager user add customer="adm8n" username="0148391048" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148391048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148391048";
};

# المستخدم 115: 0122800527
:do {
    /tool user-manager user add customer="adm8n" username="0122800527" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122800527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122800527";
};

# المستخدم 116: 0135232496
:do {
    /tool user-manager user add customer="adm8n" username="0135232496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135232496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135232496";
};

# المستخدم 117: 0108189522
:do {
    /tool user-manager user add customer="adm8n" username="0108189522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108189522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108189522";
};

# المستخدم 118: 0163181810
:do {
    /tool user-manager user add customer="adm8n" username="0163181810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163181810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163181810";
};

# المستخدم 119: 0107060927
:do {
    /tool user-manager user add customer="adm8n" username="0107060927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107060927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107060927";
};

# المستخدم 120: 0109942901
:do {
    /tool user-manager user add customer="adm8n" username="0109942901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109942901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109942901";
};

# المستخدم 121: 0161412708
:do {
    /tool user-manager user add customer="adm8n" username="0161412708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161412708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161412708";
};

# المستخدم 122: 0138590509
:do {
    /tool user-manager user add customer="adm8n" username="0138590509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138590509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138590509";
};

# المستخدم 123: 0123432522
:do {
    /tool user-manager user add customer="adm8n" username="0123432522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123432522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123432522";
};

# المستخدم 124: 0117261381
:do {
    /tool user-manager user add customer="adm8n" username="0117261381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117261381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117261381";
};

# المستخدم 125: 0162392396
:do {
    /tool user-manager user add customer="adm8n" username="0162392396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162392396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162392396";
};

# المستخدم 126: 0159604008
:do {
    /tool user-manager user add customer="adm8n" username="0159604008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159604008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159604008";
};

# المستخدم 127: 0171398986
:do {
    /tool user-manager user add customer="adm8n" username="0171398986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171398986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171398986";
};

# المستخدم 128: 0144174413
:do {
    /tool user-manager user add customer="adm8n" username="0144174413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144174413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144174413";
};

# المستخدم 129: 0197575455
:do {
    /tool user-manager user add customer="adm8n" username="0197575455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197575455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197575455";
};

# المستخدم 130: 0151226496
:do {
    /tool user-manager user add customer="adm8n" username="0151226496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151226496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151226496";
};

# المستخدم 131: 0193781178
:do {
    /tool user-manager user add customer="adm8n" username="0193781178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193781178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193781178";
};

# المستخدم 132: 0111978746
:do {
    /tool user-manager user add customer="adm8n" username="0111978746" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111978746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111978746";
};

# المستخدم 133: 0169846697
:do {
    /tool user-manager user add customer="adm8n" username="0169846697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169846697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169846697";
};

# المستخدم 134: 0117876828
:do {
    /tool user-manager user add customer="adm8n" username="0117876828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117876828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117876828";
};

# المستخدم 135: 0177432761
:do {
    /tool user-manager user add customer="adm8n" username="0177432761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177432761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177432761";
};

# المستخدم 136: 0119896505
:do {
    /tool user-manager user add customer="adm8n" username="0119896505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119896505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119896505";
};

# المستخدم 137: 0153071031
:do {
    /tool user-manager user add customer="adm8n" username="0153071031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153071031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153071031";
};

# المستخدم 138: 0100388293
:do {
    /tool user-manager user add customer="adm8n" username="0100388293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100388293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100388293";
};

# المستخدم 139: 0168136126
:do {
    /tool user-manager user add customer="adm8n" username="0168136126" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168136126";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168136126";
};

# المستخدم 140: 0145597168
:do {
    /tool user-manager user add customer="adm8n" username="0145597168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145597168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145597168";
};

# المستخدم 141: 0158047074
:do {
    /tool user-manager user add customer="adm8n" username="0158047074" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158047074";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158047074";
};

# المستخدم 142: 0193078872
:do {
    /tool user-manager user add customer="adm8n" username="0193078872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193078872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193078872";
};

# المستخدم 143: 0127150978
:do {
    /tool user-manager user add customer="adm8n" username="0127150978" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127150978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127150978";
};

# المستخدم 144: 0121858189
:do {
    /tool user-manager user add customer="adm8n" username="0121858189" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121858189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121858189";
};

# المستخدم 145: 0168281949
:do {
    /tool user-manager user add customer="adm8n" username="0168281949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168281949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168281949";
};

# المستخدم 146: 0187425489
:do {
    /tool user-manager user add customer="adm8n" username="0187425489" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187425489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187425489";
};

# المستخدم 147: 0159530060
:do {
    /tool user-manager user add customer="adm8n" username="0159530060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159530060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159530060";
};

# المستخدم 148: 0164115656
:do {
    /tool user-manager user add customer="adm8n" username="0164115656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164115656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164115656";
};

# المستخدم 149: 0190127225
:do {
    /tool user-manager user add customer="adm8n" username="0190127225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190127225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190127225";
};

# المستخدم 150: 0157494094
:do {
    /tool user-manager user add customer="adm8n" username="0157494094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157494094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157494094";
};

# المستخدم 151: 0114696671
:do {
    /tool user-manager user add customer="adm8n" username="0114696671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114696671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114696671";
};

# المستخدم 152: 0167107472
:do {
    /tool user-manager user add customer="adm8n" username="0167107472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167107472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167107472";
};

# المستخدم 153: 0156418767
:do {
    /tool user-manager user add customer="adm8n" username="0156418767" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156418767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156418767";
};

# المستخدم 154: 0143007033
:do {
    /tool user-manager user add customer="adm8n" username="0143007033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143007033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143007033";
};

# المستخدم 155: 0143685358
:do {
    /tool user-manager user add customer="adm8n" username="0143685358" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143685358";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143685358";
};

# المستخدم 156: 0177928791
:do {
    /tool user-manager user add customer="adm8n" username="0177928791" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177928791";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177928791";
};

# المستخدم 157: 0148897887
:do {
    /tool user-manager user add customer="adm8n" username="0148897887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148897887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148897887";
};

# المستخدم 158: 0135807511
:do {
    /tool user-manager user add customer="adm8n" username="0135807511" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135807511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135807511";
};

# المستخدم 159: 0179684387
:do {
    /tool user-manager user add customer="adm8n" username="0179684387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179684387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179684387";
};

# المستخدم 160: 0195340853
:do {
    /tool user-manager user add customer="adm8n" username="0195340853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195340853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195340853";
};

# المستخدم 161: 0173843646
:do {
    /tool user-manager user add customer="adm8n" username="0173843646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173843646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173843646";
};

# المستخدم 162: 0136939856
:do {
    /tool user-manager user add customer="adm8n" username="0136939856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136939856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136939856";
};

# المستخدم 163: 0159011949
:do {
    /tool user-manager user add customer="adm8n" username="0159011949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159011949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159011949";
};

# المستخدم 164: 0144450441
:do {
    /tool user-manager user add customer="adm8n" username="0144450441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144450441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144450441";
};

# المستخدم 165: 0169149122
:do {
    /tool user-manager user add customer="adm8n" username="0169149122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169149122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169149122";
};

# المستخدم 166: 0139673065
:do {
    /tool user-manager user add customer="adm8n" username="0139673065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139673065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139673065";
};

# المستخدم 167: 0140488195
:do {
    /tool user-manager user add customer="adm8n" username="0140488195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140488195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140488195";
};

# المستخدم 168: 0172552641
:do {
    /tool user-manager user add customer="adm8n" username="0172552641" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172552641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172552641";
};

# المستخدم 169: 0198249289
:do {
    /tool user-manager user add customer="adm8n" username="0198249289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198249289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198249289";
};

# المستخدم 170: 0144363643
:do {
    /tool user-manager user add customer="adm8n" username="0144363643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144363643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144363643";
};

# المستخدم 171: 0129784268
:do {
    /tool user-manager user add customer="adm8n" username="0129784268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129784268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129784268";
};

# المستخدم 172: 0167060617
:do {
    /tool user-manager user add customer="adm8n" username="0167060617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167060617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167060617";
};

# المستخدم 173: 0169110704
:do {
    /tool user-manager user add customer="adm8n" username="0169110704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169110704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169110704";
};

# المستخدم 174: 0156712645
:do {
    /tool user-manager user add customer="adm8n" username="0156712645" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156712645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156712645";
};

# المستخدم 175: 0112840079
:do {
    /tool user-manager user add customer="adm8n" username="0112840079" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112840079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112840079";
};

# المستخدم 176: 0168136823
:do {
    /tool user-manager user add customer="adm8n" username="0168136823" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168136823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168136823";
};

# المستخدم 177: 0173522432
:do {
    /tool user-manager user add customer="adm8n" username="0173522432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173522432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173522432";
};

# المستخدم 178: 0112364164
:do {
    /tool user-manager user add customer="adm8n" username="0112364164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112364164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112364164";
};

# المستخدم 179: 0109756889
:do {
    /tool user-manager user add customer="adm8n" username="0109756889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109756889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109756889";
};

# المستخدم 180: 0129925862
:do {
    /tool user-manager user add customer="adm8n" username="0129925862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129925862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129925862";
};

# المستخدم 181: 0150339448
:do {
    /tool user-manager user add customer="adm8n" username="0150339448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150339448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150339448";
};

# المستخدم 182: 0112921918
:do {
    /tool user-manager user add customer="adm8n" username="0112921918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112921918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112921918";
};

# المستخدم 183: 0175220596
:do {
    /tool user-manager user add customer="adm8n" username="0175220596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175220596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175220596";
};

# المستخدم 184: 0184229487
:do {
    /tool user-manager user add customer="adm8n" username="0184229487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184229487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184229487";
};

# المستخدم 185: 0166164741
:do {
    /tool user-manager user add customer="adm8n" username="0166164741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166164741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166164741";
};

# المستخدم 186: 0131560150
:do {
    /tool user-manager user add customer="adm8n" username="0131560150" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131560150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131560150";
};

# المستخدم 187: 0118466353
:do {
    /tool user-manager user add customer="adm8n" username="0118466353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118466353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118466353";
};

# المستخدم 188: 0166815091
:do {
    /tool user-manager user add customer="adm8n" username="0166815091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166815091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166815091";
};

# المستخدم 189: 0117437908
:do {
    /tool user-manager user add customer="adm8n" username="0117437908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117437908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117437908";
};

# المستخدم 190: 0182330077
:do {
    /tool user-manager user add customer="adm8n" username="0182330077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182330077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182330077";
};

# المستخدم 191: 0100813344
:do {
    /tool user-manager user add customer="adm8n" username="0100813344" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100813344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100813344";
};

# المستخدم 192: 0132297374
:do {
    /tool user-manager user add customer="adm8n" username="0132297374" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132297374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132297374";
};

# المستخدم 193: 0102371433
:do {
    /tool user-manager user add customer="adm8n" username="0102371433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102371433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102371433";
};

# المستخدم 194: 0152305849
:do {
    /tool user-manager user add customer="adm8n" username="0152305849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152305849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152305849";
};

# المستخدم 195: 0158836859
:do {
    /tool user-manager user add customer="adm8n" username="0158836859" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158836859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158836859";
};

# المستخدم 196: 0157386668
:do {
    /tool user-manager user add customer="adm8n" username="0157386668" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157386668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157386668";
};

# المستخدم 197: 0196662324
:do {
    /tool user-manager user add customer="adm8n" username="0196662324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196662324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196662324";
};

# المستخدم 198: 0196391667
:do {
    /tool user-manager user add customer="adm8n" username="0196391667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196391667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196391667";
};

# المستخدم 199: 0144640625
:do {
    /tool user-manager user add customer="adm8n" username="0144640625" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144640625";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144640625";
};

# المستخدم 200: 0182829084
:do {
    /tool user-manager user add customer="adm8n" username="0182829084" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182829084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182829084";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
